name: Upload S3
description: 'Upload directory to S3'
author: '<PERSON><PERSON><PERSON>'
inputs:
  aws_key_id:
    description: 'aws key id'
    required: true
  aws_secret_access_key:
    description: 'aws secret access key'
    required: true
  aws_bucket:
    description: 'aws bucket name'
    required: true
  source_dir:
    required: true
    description: 'directory to upload'
  destination_dir:
    required: false
    default: /
    description: 'destination directory for upload'
  endpoint:
    required: false
    description: 'endpoint URI to send requests'
outputs:
  object_key:
    description: 'object key'
  object_locations:
    description: 'object locations'
runs:
  using: 'node20'
  main: 'dist/index.js'