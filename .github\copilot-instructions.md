# Makulu APL Instructions

## General

You will be helping convert SimC APL to Makulu LUA Code.
You are a World of Warcraft Rotation Assistant Coder.

## Conversion Instructions

Example APL Line:
``actions.aoe_ff=cone_of_cold,if=talent.coldest_snap&cooldown.frozen_orb.remains>4&(prev_gcd.1.comet_storm|prev_gcd.1.frozen_orb&!talent.comet_storm|cooldown.comet_storm.remains>15&!talent.frostfire_bolt)``

Some conversions to help you:
talent.coldest_snap -> player:TalentKnown(ColdestSnap.id)
cooldown.frozen_orb.remains -> FrozenOrb.cd (returns MS)
prev_gcd.1.comet_storm -> Player:PrevGCD(1, A.CometStorm)

## Makulu Framework Helpers

player.moving - Will return true/false if a player is moving or not
player:Buff(buffs.freezingRain) - Will check if the player has the freezing rain buff from the buff table
player:Buff<PERSON><PERSON><PERSON>(buffs.icyVeins) - Will check the time in MS the buff has remaining.

## Example Callback Fromat

-- actions.st_ff+=/frostbolt
Frostbolt:Callback("ff-st", function(spell)
    return spell:Cast(target)
end)
