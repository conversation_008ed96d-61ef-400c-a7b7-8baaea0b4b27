name: Run Docker Container

on:
  push:
    branches: [ feat/ci_build ]
  pull_request:
      branches: [ main ]

jobs:
  docker-job:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Cache Docker image
      uses: actions/cache@v3
      id: cache
      with:
        path: /tmp/docker-image.tar
        key: ${{ runner.os }}-docker-${{ hashFiles('AutoBundle/**') }}-${{ hashFiles('CI/Dockerfile') }}

    - name: Load cached Docker image
      if: steps.cache.outputs.cache-hit == 'true'
      run: docker load -i /tmp/docker-image.tar

    - name: Build Docker image
      if: steps.cache.outputs.cache-hit != 'true'
      run: |
        docker build -t user/app:latest -f CI/Dockerfile .
        docker save -o /tmp/docker-image.tar user/app:latest

    - name: Run docker container
      run: docker run --name bundler -v ${{ github.workspace }}/Rotations:/usr/src/profiles -v ${{ github.workspace}}/out:/usr/src/bundler/out  user/app:latest

    - uses: ./.github/actions/s3Upload
      if: github.ref == 'refs/heads/feat/ci_build'
      with:
        aws_key_id: ${{ secrets.AWS_ACCESS_KEY }}
        aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws_bucket: ${{ secrets.AWS_S3_BUCKET }}
        destination_dir: 'bundled/'
        source_dir: 'out/'

    - uses: sarisia/actions-status-discord@v1
      if: github.ref == 'refs/heads/feat/ci_build'
      with:
        webhook: ${{ secrets.DISCORD_WEBHOOK }}
        color: 0x0000ff
        username: Makulu Builder
        nodetail: true
        title: New version of `Makulu Rotations` is ready!
        avatar_url: ${{ secrets.DISCORD_PP }}
        description: |
          An awesome new update is ready 😎
          Click [here](https://makulu-deployments.s3.eu-west-1.amazonaws.com/deployments-main/bundled/Makulu_AIO.lua) to download, or import directly in the GGL app!

    - uses: sarisia/actions-status-discord@v1
      if: failure()
      with:
        webhook: ${{ secrets.DISCORD_WEBHOOK }}
        color: 0xff0000 # Red color for failure
        username: Makulu Builder
        nodetail: true
        title: Ruh roh
        avatar_url: ${{ secrets.DISCORD_PP }}
        image: https://media.tenor.com/KGRLk_Dfub0AAAAC/scooby-doo-woof.gif
        description: |
          The build failed that aint good
