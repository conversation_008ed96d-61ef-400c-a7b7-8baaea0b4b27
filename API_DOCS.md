### Table of Contents

1. [Overview](#overview)
2. [Core Systems](#core-systems)
   - [Unit System](#unit-system)
   - [Spell System](#spell-system)
   - [Cache System](#cache-system)
   - [MultiUnits System](#multiunits-system)
   - [MakuluAware UI System](#makuluaware-ui-system)
3. [Framework Modules](#framework-modules)
   - [Damage Tracker](#damage-tracker)
   - [DR Tracker](#dr-tracker)
   - [Boss Mods Integration](#boss-mods-integration)
   - [Arena State](#arena-state)
4. [Real-World Examples](#real-world-examples)
   - [Combat Rotation Structure](#combat-rotation-structure)
   - [Common Patterns](#common-patterns)
5. [Role-Specific Patterns](#role-specific-patterns)
   - [Tank Rotations](#tank-rotations)
   - [Healer Rotations](#healer-rotations)
   - [DPS Rotations](#dps-rotations)
6. [PvP and Arena Combat](#pvp-and-arena-combat)
   - [Arena Unit Management](#arena-unit-management)
   - [Interrupt Management](#interrupt-management)
7. [Troubleshooting & Development Tools](#troubleshooting--development-tools)
   - [Debug Information Display](#debug-information-display)
8. [Getting Started](#getting-started)
   - [Basic Setup](#basic-setup)
   - [Common Gotchas](#common-gotchas)
9. [Common Data Structures](#common-data-structures)
   - [Buff and Debuff Tables](#buff-and-debuff-tables)
   - [Interrupt Tables](#interrupt-tables)

## Overview

The Makulu Framework is a comprehensive World of Warcraft combat rotation framework that provides robust APIs for unit tracking, spell management, and combat optimization. The framework is designed with performance in mind, utilizing an intelligent caching system to minimize overhead during combat rotations. It supports both PvE and PvP content with specialized utilities for each.

## Core Systems

### Unit System

The Unit system provides a comprehensive API for tracking and managing units in the game, including players, targets, and party members.

```lua
-- Access pre-defined constant units
local player = ConstUnit.player
local target = ConstUnit.target
local focus = ConstUnit.focus

-- Create a new unit tracker for a specific unit
local customUnit = MakUnit:new("targettarget")

-- Create unit from GUID
local unitByGuid = MakUnit:newGUID(guid, unitId)
```

#### Key Unit Methods

##### State & Information
- `unit:Exists()` - Check if unit exists
- `unit:Name()` - Get unit name
- `unit:Guid()` - Get unit GUID
- `unit:UnitToken()` - Get the unit token from GUID
- `unit:ClassID()` - Get unit class ID
- `unit:SpecID()` - Get unit specialization ID
- `unit:NpcId()` - Get NPC ID for creature units

##### Health & Resources
- `unit:Health()` - Get unit health percentage
- `unit:HealthMax()` - Get unit maximum health
- `unit:HealthActual()` - Get unit current health
- `unit:Shield()` - Get unit's current absorb shield amount
- `unit:GetPower(powerType)` - Get unit's power (mana, rage, energy, etc.)
- `unit:GetPowerMax(powerType)` - Get unit's maximum power

##### Direct Property Access
- `unit.hp` - Direct access to unit's health percentage
- `unit.healthMax` - Direct access to unit's maximum health
- `unit.health` - Direct access to unit's current health
- `unit.hpRaw` - Direct access to unit's raw health value
- `unit.guid` - Direct access to unit's GUID
- `unit.moving` - Boolean indicating if unit is moving
- `unit.casting` - Boolean indicating if unit is casting
- `unit.exists` - Boolean indicating if unit exists
- `unit.dead` - Boolean indicating if unit is dead
- `unit.class` - Unit's class ID
- `unit.spec` - Unit's specialization ID
- `unit.npcId` - Unit's NPC ID for creature units

##### Power Type Helpers
- `unit:GetMana()` - Get current mana
- `unit:GetManaMax()` - Get maximum mana
- `unit:GetManaPct()` - Get mana percentage
- `unit:GetManaDeficit()` - Get mana deficit

The framework also includes similar helper methods for all power types including:
- `unit:GetRage()`, `unit:GetRagePct()`, `unit:GetRageMax()`
- `unit:GetEnergy()`, `unit:GetEnergyPct()`, `unit:GetEnergyMax()`
- `unit:GetFocus()`, `unit:GetFocusPct()`, `unit:GetFocusMax()`
- `unit:GetComboPoints()` (also aliased as `unit:GetCp()`)
- Similar methods for: `runicPower`, `soulShards`, `lunarPower`, `holyPower`, `maelstrom`, `chi`, `insanity`, `arcaneCharges`, `fury`, `pain`, `essence`

##### Combat State
- `unit:InCombat()` - Check if unit is in combat
- `unit:CanAttack()` - Check if unit can be attacked
- `unit:IsDeadOrGhost()` - Check if unit is dead or ghost
- `unit:TTD()` - Get estimated time to die
- `unit:DPS()` - Get unit's current DPS (damage dealt or received)
- `unit:CombatTime()` - Get time in current combat

##### Role & Type Checks
- `unit:IsHealer()` - Check if unit is a healer
- `unit:IsTank()` - Check if unit is a tank
- `unit:IsMelee()` - Check if unit is melee
- `unit:IsCaster()` - Check if unit is a caster
- `unit:IsPlayer()` - Check if unit is a player
- `unit:IsPet()` - Check if unit is a pet
- `unit:IsTotem()` - Check if unit is a totem
- `unit:IsBoss()` - Check if unit is a boss
- `unit:IsDummy()` - Check if unit is a training dummy
- `unit:IsUnit(otherUnit)` - Check if this unit is the same as another unit
- `unit:IsMe()` - Check if unit is the player character
- `unit:IsTarget()` - Check if unit is the current target
- `unit:IsFriendly()` - Check if unit is friendly to the player

##### Set Bonuses
- `unit:SetPieces()` - Get number of equipped tier set items
- `unit:Has2Set()` - Check if unit has 2-piece tier set bonus
- `unit:Has4Set()` - Check if unit has 4-piece tier set bonus

##### Buffs & Debuffs
- `unit:HasBuff(spell, onlyOurs)` - Check if unit has specific buff
- `unit:Buff(spell, onlyOurs)` - Alias for HasBuff
- `unit:HasDeBuff(spell, onlyOurs)` - Check if unit has specific debuff
- `unit:Debuff(spell, onlyOurs)` - Alias for HasDeBuff
- `unit:BuffRemains(spell, onlyOurs)` - Get remaining time on buff in milliseconds
- `unit:DebuffRemains(spell, onlyOurs)` - Get remaining time on debuff in milliseconds
- `unit:HasBuffCount(spell, onlyOurs)` - Get stack count of a buff
- `unit:HasDeBuffCount(spell, onlyOurs)` - Get stack count of a debuff
- `unit:BuffDuration(spell, onlyOurs)` - Get total duration of a buff
- `unit:DebuffDuration(spell, onlyOurs)` - Get total duration of a debuff
- `unit:DebuffPandemic(spell)` - Check if debuff is in pandemic refresh range (30% of duration left)
- `unit:HasBuffRemain(spell, remaining, onlyOurs)` - Check if buff has at least remaining time left
- `unit:HasDeBuffRemain(spell, remaining, onlyOurs)` - Check if debuff has at least remaining time left

##### Status Effects
- `unit:Poisoned()` - Check if unit is poisoned
- `unit:Cursed()` - Check if unit is cursed
- `unit:Bleeding()` - Check if unit is bleeding
- `unit:Diseased()` - Check if unit is diseased
- `unit:Magicked()` - Check if unit is affected by a magic debuff
- `unit:Enraged()` - Check if unit is enraged
- `unit:Feigned()` - Check if unit is feigning death
- `unit:Bloodlust()` - Check if unit has Bloodlust or equivalent
- `unit:Sated()` - Check if unit is Sated (has Bloodlust debuff)
- `unit:SatedRemains()` - Get remaining time on Sated debuff

##### Immunities & Control
- `unit:IsTotalImmune()` - Check if unit is totally immune
- `unit:IsPhysImmune()` - Check if unit is immune to physical damage
- `unit:IsMagicImmune()` - Check if unit is immune to magic damage
- `unit:IsKickImmune()` - Check if unit is immune to interrupts
- `unit:IsCCImmune()` - Check if unit is immune to crowd control
- `unit:IsSlowImmune()` - Check if unit is immune to slows
- `unit:InCC()` - Check if unit is crowd controlled
- `unit:InStun()` - Check if unit is stunned
- `unit:InBreakableCC()` - Check if unit is in breakable crowd control
- `unit:CCRemains()` - Get remaining time on crowd control effects
- `unit:IsRooted()` - Check if unit is rooted
- `unit:IsSlowed()` - Check if unit is slowed

##### Movement & Position
- `unit:Distance()` - Get distance to unit in yards
- `unit:MeleeRange()` - Check if unit is in melee range
- `unit:MeleeRangeOf(unit)` - Check if this unit is in melee range of another unit
- `unit:IsMoving()` - Check if unit is moving
- `unit:Speed()` - Get unit's movement speed
- `unit:IsMounted()` - Check if unit is mounted (player only)
- `unit:IsStealthed()` - Check if unit is stealthed (player only)
- `unit:RaidMarker()` - Get raid target marker (1-8) or 0 if none

##### Casting & Channeling
- `unit:CastInfo()` - Get detailed information about current cast
- `unit:ChannelInfo()` - Get detailed information about current channel
- `unit:IsCasting()` - Check if unit is casting
- `unit:IsChanneling()` - Check if unit is channeling
- `unit:IsCastOrChanneling()` - Check if unit is casting or channeling
- `unit:IsSafeToKick()` - Check if it's safe to interrupt the unit's cast
- `unit:IsSafeToKickCC()` - Check if it's safe to use CC on the casting unit
- `unit:PvpKick(kickList)` - Check if unit should be kicked based on PvP rules
- `unit:CanCastWhileMoving()` - Check if unit can cast while moving
- `unit:Interrupted()` - Check if unit was recently interrupted

##### Diminishing Returns
- `unit:StunDr()` - Get stun DR status
- `unit:StunDrRemains()` - Get time until stun DR resets
- `unit:IncapDr()` - Get incapacitate DR status
- `unit:IncapDrRemains()` - Get time until incapacitate DR resets

*Similar DR methods exist for all DR categories*

##### Targeting
- `unit:Target()` - Get the unit's current target as a Unit object

##### Talent & Gear
- `unit:TalentKnown(id)` - Check if unit has a specific talent
- `unit:TalentRank(id)` - Get the rank of a talent
- `unit:TalentKnownInt(id)` - Get talent known status as an integer (1/0)
- `unit:TrinketReady(id)` - Check if trinket is ready to use (1 or 2)
- `unit:TrinketCD(id)` - Get cooldown remaining on trinket in milliseconds

### Spell System

The Spell system manages spell casting, cooldowns, and spell state tracking.

```lua
-- Define action IDs with optional spell info
local ActionID = {
    Frostbolt = { ID = 116, MAKULU_INFO = { 
        damageType = "magic",
        targeted = true,
        ignoreMoving = false
    }},
    IceBlock = { ID = 45438, MAKULU_INFO = { 
        offGcd = true,
        ignoreCasting = true 
    }}
}

-- Create spell objects from action IDs
local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end

-- Build Makulu framework spells
local M = buildMakuluFrameworkSpells(A)
```

#### Spell Properties

When defining spell information, the following properties can be specified:

- `targeted` - Does the spell require a target (default: true)
- `offGcd` - Is the spell off the global cooldown (default: false)
- `ignoreCasting` - Can the spell be used while casting other spells (default: false)
- `ignoreFacing` - Does the spell ignore facing requirements (default: false)
- `ignoreRange` - Does the spell ignore range requirements (default: false)
- `ignoreMoving` - Can the spell be cast while moving (default: false)
- `ignoreLos` - Does the spell ignore line of sight (default: false)
- `ignoreResource` - Does the spell ignore resource requirements (default: false)
- `cc` - Is the spell a crowd control ability (default: false)
- `heal` - Is the spell a healing ability (default: false)
- `channel` - Is the spell a channeled spell (default: false)
- `damageType` - Spell damage type: "physical" or "magic" (default: "magic")
- `pet` - Is the spell a pet ability (default: false)

#### Key Spell Methods

##### State & Information
- `spell:IsKnown()` - Check if spell is known
- `spell:SpellInfo()` - Get spell information
- `spell:Cost()` - Get spell cost
- `spell:CastTime()` - Get spell cast time
- `spell:Range()` - Get spell range
- `spell:HasRange()` - Check if spell has range requirement

##### Cooldown & Charges
- `spell:Cooldown()` - Get spell cooldown in milliseconds
- `spell:cd` - Alias for Cooldown()
- `spell:Charges()` - Get current charges of the spell
- `spell:MaxCharges()` - Get maximum charges of the spell
- `spell:Fraction()` - Get spell charge fraction (includes partial recharges)
- `spell:TimeToFullCharges()` - Get time until all charges are available
- `spell:SpellGcd()` - Get spell's base GCD

##### Direct Property Access
- `spell.id` - Direct access to spell ID
- `spell.cd` - Direct access to cooldown in milliseconds
- `spell.charges` - Direct access to current charges
- `spell.maxCharges` - Direct access to maximum charges
- `spell.known` - Boolean indicating if spell is known
- `spell.ready` - Boolean indicating if spell is ready to use
- `spell.usable` - Boolean indicating if spell can be used
- `spell.cost` - Direct access to spell cost
- `spell.castTime` - Direct access to spell cast time in milliseconds
- `spell.range` - Direct access to spell range in yards

##### Usage
- `spell:ReadyToUse()` - Check if spell is ready to use
- `spell:Usable(target)` - Check if spell can be used on target
- `spell:Cast(target, skipSet, spellOverride)` - Cast spell on target
- `spell:InRange(target)` - Check if target is in range for spell
- `spell:IsImmune(target)` - Check if target is immune to this spell
- `spell:Used()` - Get time since spell was last used

##### Callback System
- `spell:Callback(key, callbackFunction)` - Register a callback function for the spell

The callback system allows you to define different conditions when a spell should be used:

```lua
-- Define callback for Rake
Rake:Callback("open", function(spell)
    -- Use Rake when in stealth for the damage bonus
    if player:HasBuff(buffs.prowl) or player:HasBuff(buffs.suddenAmbush) then
        return spell:Cast(target)
    end
    return false
end)

Rake:Callback("maintain", function(spell)
    -- Maintain Rake on target if it's missing or about to expire
    if not target:HasDeBuff(debuffs.rake) or target:DebuffRemains(debuffs.rake) < 4200 then
        return spell:Cast(target)
    end
    return false
end)

-- Use the callbacks in rotation
local function runRotation()
    if Rake("open") then return true end
    if Rake("maintain") then return true end
    
    return false
end
```

You can call the spell directly with the callback key to execute that specific callback:

```lua
-- These will run the corresponding callback and return its result
Rake("open")
Rake("maintain")
```

#### Constant Spells

The framework provides a set of predefined utility spells for common actions like targeting and using trinkets.

```lua
-- Use constant spells with callback system
TabTarget()       -- Tab targeting
TargetArena1()    -- Target arena1
FocusArena2()     -- Set focus to arena2
Trinket1()        -- Use first trinket
HealthStone()     -- Use healthstone
```

Available constant spells:
- Targeting: `TabTarget`, `TargetMouseOver`, `TargetLastTarget`
- Arena targeting: `TargetArena1` through `TargetArena5` 
- Focus setting: `FocusArena1` through `FocusArena5`, `FocusParty1` through `FocusParty4`, `FocusPlayer`
- Items: `StopCasting`, `Trinket1`, `Trinket2`, `HealthStone`, `HealthPotion`, `Potion`
- Racial abilities: `WillToSurvive`, `Stoneform`, `Shadowmeld`, `EscapeArtist`, etc.

#### Framework Utilities

- `MakuluFramework.gcd()` - Get current global cooldown in milliseconds
- `MakuluFramework.maxGcd()` - Get max global cooldown in milliseconds

### Cache System

The Cache system provides performance optimization through intelligent caching of frequently accessed data.

```lua
-- Get a standard cache cell (reset every frame)
local cache = MakuluFramework.Cache:getCell()

-- Get a constant cache cell (persists between frames)
local constCache = MakuluFramework.Cache:getConstCacheCell()

-- Get a combat cache cell (persists during combat)
local combatCache = MakuluFramework.Cache:getCombatCacheCell()
```

#### Cache Types
- **Regular Cache**: Reset every frame, for standard per-frame calculations
- **Const Cache**: Persistent across frames, for data that rarely changes
- **Combat Cache**: Only reset when leaving combat, for combat-specific data

#### Key Cache Methods
- `cache:Get(key)` - Get cached value
- `cache:GetOrSet(key, callback)` - Get cached value or compute and cache new value
- `cache:GetOrSetTimeout(key, callback, delay)` - Get cached value with timeout
- `cache:Set(key, value)` - Explicitly set a cached value

#### Cache Usage Examples

```lua
-- Simple cache lookup
local cachedValue = cache:GetOrSet("myKey", function() 
    return ExpensiveCalculation()
end)

-- With timeout (value valid for 5 seconds)
local timedValue = cache:GetOrSetTimeout("timedKey", function() 
    return SlowOperation()
end, 5000) 
```

### MultiUnits System

The MultiUnits system provides powerful functionality for handling groups of units, such as party members, arena opponents, and enemies.

```lua
-- Access the MultiUnits module
local multiUnits = MakuluFramework.MultiUnits

-- Available unit collections
local party = multiUnits.party        -- Party/raid members
local enemies = multiUnits.enemies    -- Enemy units
local activeEnemies = multiUnits.activeEnemies -- Combat-active enemies
local enemyPlayers = multiUnits.enemyPlayers  -- Enemy players
local friendlyPlayers = multiUnits.friendlyPlayers -- Friendly players
local totems = multiUnits.totems      -- Totem units
local arena = multiUnits.arena        -- Arena opponents
```

#### Group Methods

Groups provide collection operations for sets of units:

##### Core Group Operations
- `group:ForEach(func, stop)` - Execute function for each member
- `group:Any(func)` - Check if any member matches condition
- `group:Find(func)` - Find first member matching condition
- `group:Filter(func)` - Create new group with matching members
- `group:Size()` - Get total number of members

##### Aggregation Methods
- `group:Highest(func)` - Get member with highest value from function
- `group:Lowest(func)` - Get member with lowest value from function
- `group:Sum(func)` - Sum values from function across members
- `group:Average(func)` - Average values from function across members
- `group:Count(func)` - Count members matching condition

#### MultiUnits Usage Examples

```lua
-- Find enemies below 20% health
local lowHealthEnemies = enemies:Filter(function(unit)
    return unit:Health() < 20 and not unit:HasBuff("Immunity")
end)

-- Get the lowest health party member
local lowestHealthAlly = party:Lowest(function(unit)
    return unit:Health()
end)

-- Count enemies in melee range
local meleeCount = enemies:Count(function(unit)
    return unit:MeleeRange()
end)
```

### MakuluAware UI System

The MakuluAware system provides UI utilities for displaying information to the player.

```lua
-- Enable the system (typically done at the rotation start)
Aware:enable()

-- Display a message
Aware:ShowText("Interrupt Incoming!", 3, true)

-- Display message with icon
Aware:ShowText("Use Defensive!", 5, true, 135988) -- Using spell icon ID
```

#### Key MakuluAware Methods

- `Aware:enable()` - Enable the message system
- `Aware:disable()` - Disable the message system
- `Aware:ShowText(text, length, isPriority, icon)` - Show a text message
- `Aware:BumpText(text, length)` - Refresh an existing message

## Framework Modules

### Damage Tracker

The Damage Tracker module monitors and records damage taken and dealt by units.

```lua
-- Access the damage tracker
local damageTracker = MakuluFramework.DamageTracker
```

#### Key Damage Tracker Methods

- `damageTracker:GetTimeToDie(unit)` - Estimate time to death for a unit
- `damageTracker:GetDmgPerSec(unit)` - Get unit's current DPS or damage taken per second
- `damageTracker:GetRecentDamageTaken(seconds)` - Get recent damage taken by player
- `damageTracker:GetRecentDamageDealt(seconds)` - Get recent damage dealt by player

### DR Tracker

The DR (Diminishing Returns) Tracker monitors crowd control DR status for tracked units.

```lua
-- Access the DR tracker via unit methods
local drStatus = unit:StunDr() -- Get DR status for stuns
local drRemaining = unit:StunDrRemains() -- Get time until DR resets
```

#### DR Categories

- `stun` - Stuns
- `incap` - Incapacitates
- `fear` - Fears
- `horror` - Horrors
- `silence` - Silences
- `disarm` - Disarms
- `root` - Roots
- `disorient` - Disorients

### Boss Mods Integration

The Boss Mods module integrates with DBM and BigWigs to provide timers for upcoming boss mechanics.

```lua
-- Access the Boss Mods module
local bossMods = MakuluFramework.BossMods
```

#### Key Boss Mods Methods

- `bossMods:GetNextCast(spellId, timeThreshold)` - Get time until next boss cast
- `bossMods:ShouldDefensive(timeThreshold)` - Check if defensive cooldown should be used
- `bossMods:IncomingDamage(timeThreshold)` - Get information about incoming damage

### Arena State

The Arena State module provides specialized functionality for arena combat.

```lua
-- Access the Arena State module
local arenaState = MakuluFramework.ArenaState
```

#### Key Arena State Methods

- `arenaState:InArena()` - Check if player is in arena
- `arenaState:GetEnemySpec(position)` - Get enemy spec at arena position
- `arenaState:GetEnemyClass(position)` - Get enemy class at arena position
- `arenaState:GetTeamComposition()` - Get composition of both teams

## Real-World Examples

### Combat Rotation Structure

Combat rotations in the Makulu Framework typically follow this structure:

```lua
-- Basic file structure
if not MakuluValidCheck() then return true end
if not Makulu_magic_number == 2347956243324 then return true end

-- Check for correct specialization
if GetSpecializationInfo(GetSpecialization()) ~= 103 then return end

-- Import key framework components
local FrameworkStart = MakuluFramework.start
local FrameworkEnd = MakuluFramework.endFunc
local MakUnit = MakuluFramework.Unit
local MakSpell = MakuluFramework.Spell
local TableToLocal = MakuluFramework.tableToLocal
local ConstUnit = MakuluFramework.ConstUnits
local cacheContext = MakuluFramework.Cache
local Aware = MakuluFramework.Aware

local Action = _G.Action
local ActionUnit = Action.Unit
local Player = Action.Player
local MultiUnits = Action.MultiUnits

-- Define spell IDs and attributes
local ActionID = {
    -- Offensive abilities
    Rake = { ID = 1822, MAKULU_INFO = { damageType = "physical" } },
    Rip = { ID = 1079, MAKULU_INFO = { damageType = "physical" } },
    Shred = { ID = 5221, MAKULU_INFO = { damageType = "physical" } },
    
    -- Cooldowns
    TigersFury = { ID = 5217 },
    Berserk = { ID = 106951 },
    
    -- Defensive abilities
    Barkskin = { ID = 22812 },
    
    -- Forms
    CatForm = { ID = 768 },
    BearForm = { ID = 5487 },
    
    -- Utility abilities
    Prowl = { ID = 5215 },
    SkullBash = { ID = 106839, MAKULU_INFO = { damageType = "physical" } },
    
    -- Talents (for checks)
    BloodtalonsTalent = { ID = 319439, Hidden = true },
}

-- Create Action objects
local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end

-- Build Makulu framework spells and make them directly accessible
local M = buildMakuluFrameworkSpells(A)
TableToLocal(M, getfenv(1))

-- Enable UI awareness system
Aware:enable()

-- Create local variables for common units
local player = ConstUnit.player
local target = ConstUnit.target
local focus = ConstUnit.focus
local mouseover = ConstUnit.mouseover

-- Define buff and debuff tables for easier reference
local buffs = {
    catForm = 768,
    prowl = 5215,
    tigersFury = 5217,
    bloodtalons = 145152,
    berserk = 106951,
}

local debuffs = {
    rake = 155722,
    rip = 1079,
}

-- Callback functions for spells
Rake:Callback("maintain", function(spell)
    if not target:HasDeBuff(debuffs.rake) or target:DebuffRemains(debuffs.rake) < 4200 then
        return spell:Cast(target)
    end
    return false
end)

Rip:Callback("maintain", function(spell)
    -- Only use Rip with 5 combo points
    if player:GetComboPoints() >= 5 and 
       (not target:HasDeBuff(debuffs.rip) or target:DebuffRemains(debuffs.rip) < 7200) then
        return spell:Cast(target)
    end
    return false
end)

-- Main rotation logic
local function runFeral()
    -- Check if we're in the right form
    if not player:HasBuff(buffs.catForm) then
        return CatForm:Cast(player)
    end
    
    -- Maintain bleeds
    if Rake("maintain") then return true end
    if Rip("maintain") then return true end
    
    -- Builder
    return Shred:Cast(target)
end

-- Register the rotation
MakuluFramework.registerRotation({
    spec = 103, -- Feral Druid spec ID
    name = "Feral Druid",
    callback = runFeral
})
```

### Common Patterns

#### Target Validation

```lua
local function isValidTarget(unit)
    return unit:Exists() and 
           not unit:IsDeadOrGhost() and 
           unit:CanAttack() and 
           unit:Los()
end

-- Usage
if isValidTarget(target) and player.hp > 20 then
    Frostbolt:Cast(target)
end
```

#### AoE Detection

```lua
local function shouldUseAoE()
    local enemyCount = enemies:Count(function(unit)
        return unit:Distance() < 8 -- Within 8 yards
    end)
    
    return enemyCount >= 3
end

-- Usage
if shouldUseAoE() then
    Blizzard:Cast(target)
else
    Frostbolt:Cast(target)
end
```

#### Defensive Cooldown Management

```lua
local function shouldUseDefensive()
    -- Health-based check
    if player:Health() < 40 then return true end
    
    -- Incoming damage check
    local recentDamage = MakuluFramework.DamageTracker:GetRecentDamageTaken(2)
    if recentDamage > player:HealthMax() * 0.3 then return true end
    
    -- Boss ability check
    if MakuluFramework.BossMods:ShouldDefensive(1500) then return true end
    
    return false
end

-- Usage
if shouldUseDefensive() then
    IceBlock:Cast(player)
end
```

## Role-Specific Patterns

### Tank Rotations

Tank rotations focus on maintaining survivability while generating threat:

```lua
-- Example tank defensive logic
local function useTankDefensives()
    local defensiveSpell = nil
      -- Check if heavy damage is incoming
    if MakuluFramework.BossMods:ShouldDefensive(1500) then
        defensiveSpell = ShieldWall -- Major defensive
    -- Check if health is dropping quickly
    elseif player.hp < 35 then
        defensiveSpell = LastStand -- Emergency defensive
    -- Check if taking sustained damage
    elseif player.hp < 60 and activeEnemies:Size() > 2 then
        defensiveSpell = Ignore -- Medium defensive
    end
    
    if defensiveSpell and defensiveSpell:ReadyToUse() then
        return defensiveSpell:Cast(player)
    end
    
    return false
end

-- Active mitigation logic
local function useActiveMitigation()    -- If we have excess resources, use mitigation
    if player:GetRagePct() > 60 then
        return ShieldBlock:Cast(player)
    end
    
    return false
end
```

### Healer Rotations

Healer rotations prioritize keeping allies alive:

```lua
-- Find the most injured ally
local function getMostInjuredAlly()
    return party:Lowest(function(unit)
        if unit:Exists() and not unit:IsDeadOrGhost() then
            return unit:Health()
        end
        return 100
    end)
end

-- Emergency healing logic
local function handleEmergencyHealing()
    local criticalThreshold = 30
    
    local criticalAlly = party:Find(function(unit)
        return unit:Exists() and 
               not unit:IsDeadOrGhost() and
               unit:Health() < criticalThreshold
    end)
      if criticalAlly then
        return FlashHeal:Cast(criticalAlly)
    end
    
    return false
end

-- HoT management
local function manageHoTs()
    local hotTarget = party:Find(function(unit)
        return unit:Exists() and 
               not unit:IsDeadOrGhost() and
               unit.hp < 90 and
               not unit:HasBuff(Rejuvenation)
    end)
    
    if hotTarget then
        return Rejuvenation:Cast(hotTarget)
    end
    
    return false
end
```

### DPS Rotations

DPS rotations focus on maximizing damage output:

```lua
-- Example priority-based DPS rotation
local function singleTargetDps()
    -- Maintain debuffs
    if not target:HasDeBuff(Moonfire) or target:DebuffRemains(Moonfire) < 3000 then
        return Moonfire:Cast(target)
    end
    
    -- Use procs
    if player:HasBuff(ClearCasting) then
        return Starfire:Cast(target)
    end
    
    -- Use cooldowns
    if target.hp > 80 and not player:HasBuff(Bloodlust) then
        return Celestial:Cast(player)
    end
    
    -- Filler spell
    return Wrath:Cast(target)
end

-- AoE rotation
local function aoeRotation()
    local enemyCount = enemies:Count(function(unit)
        return unit:Distance() < 10
    end)
    
    if enemyCount >= 3 then
        return Starfall:Cast(player)
    end
    
    return singleTargetDps()
end
```

## PvP and Arena Combat

### Arena Unit Management

```lua
-- Track arena cooldowns
local function trackArenaInterrupts()
    -- Create a cache for tracking interrupt usage
    local cache = MakuluFramework.Cache:getCombatCacheCell()
    local kickUsed = cache:GetOrSet("kicksUsed", {})
    
    for i = 1, 3 do
        local arenaUnit = ConstUnit["arena" .. i]
        if arenaUnit:Exists() then
            local unitKey = arenaUnit:Guid()
            
            -- Check if they're casting something important
            if arenaUnit:IsCasting() and arenaUnit:PvpKick(MakLists.importantCasts) then
                -- Check if we've already seen them use kick recently
                if not kickUsed[unitKey] or kickUsed[unitKey] < GetTime() - 15 then
                    Aware:ShowText("Arena" .. i .. " has kick available!", 3, true)
                end
            end
            
            -- Track when they use their interrupt
            if arenaUnit:HasBuff(MakLists.kickUsed) then
                kickUsed[unitKey] = GetTime()
                Aware:ShowText("Arena" .. i .. " used kick!", 3, true)
            end
        end
    end
end
```

### Interrupt Management

```lua
-- Smart interrupt handling
local function handleInterrupts()
    local interruptTarget = enemies:Find(function(unit)
        return unit:IsCasting() and 
               unit:IsSafeToKick() and 
               unit:PvpKick(MakLists.importantCasts)
    end)
      if interruptTarget and CounterSpell:ReadyToUse() then
        return CounterSpell:Cast(interruptTarget)
    end
    
    return false
end
```

## Troubleshooting & Development Tools

### Debug Information Display

```lua
-- Display debug information
local function showDebugInfo()
    local debugCache = MakuluFramework.Cache:getCell()
    
    -- Only update debug info every 0.5 seconds
    local shouldUpdate = debugCache:GetOrSetTimeout("debugUpdate", function() 
        return true 
    end, 500)
    
    if shouldUpdate then
        local debugText = "Target: " .. (target:Name() or "None") .. "\n"
        debugText = debugText .. "Health: " .. target:Health() .. "%\n"
        debugText = debugText .. "TTD: " .. (target:TTD() / 1000) .. "s\n"
        debugText = debugText .. "Casting: " .. tostring(target:IsCasting()) .. "\n"
        
        Aware:ShowText(debugText, 0.6, false)
    end
end
```

## Getting Started

### Basic Setup

To create a new rotation using the Makulu Framework:

1. Set up the spell definitions with proper spell IDs and attributes
2. Create the action and spell tables
3. Define your rotation logic using callbacks and condition checks
4. Register the rotation with the framework

```lua
-- Basic rotation template
if GetSpecializationInfo(GetSpecialization()) ~= YOUR_SPEC_ID then return end

local MakUnit = MakuluFramework.Unit
local MakSpell = MakuluFramework.Spell
local ConstUnit = MakuluFramework.ConstUnits
local Aware = MakuluFramework.Aware

-- Define your spells
local ActionID = {
    -- Spell definitions go here
}

-- Create spell objects
local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end

-- TableToLocal will make spells available directly without M. prefix
TableToLocal(buildMakuluFrameworkSpells(A), getfenv(1))

-- Enable UI system
Aware:enable()

-- Set up commonly used units
local player = ConstUnit.player
local target = ConstUnit.target

-- Define your rotation logic
local function runRotation()
    -- Rotation logic goes here using direct spell names without M. prefix
    if target.exists and not target.dead and player.hp > 20 then
        return Frostbolt:Cast(target)
    end
end

-- Register the rotation
MakuluFramework.registerRotation({
    spec = YOUR_SPEC_ID,
    name = "Your Rotation Name",
    callback = runRotation
})
```

### Common Gotchas

1. **Unit Existence Checks**: Always check if a unit exists before accessing its properties
   ```lua
   if target:Exists() and not target:IsDeadOrGhost() then
       -- Safe to use target
   end
   ```

2. **Spell Range Checks**: Ensure targets are in range for spells
   ```lua
   if spell:InRange(target) then
       -- Safe to cast
   end
   ```

3. **Resource Management**: Verify resource availability
   ```lua
   if player.mana > 20 then
       -- Safe to cast mana-intensive spell
   end
   ```

4. **Performance Optimization**: Use caching for expensive operations
   ```lua
   local cache = MakuluFramework.Cache:getCell()
   local enemyCount = cache:GetOrSet("nearbyEnemies", function()
       return enemies:Count(function(unit) return unit:Distance() < 8 end)
   end)
   ```

5. **Error Handling**: Provide meaningful error messages with Aware
   ```lua
   if not spell:IsKnown() then
       Aware:ShowText("Missing required spell: " .. spell:SpellInfo().name, 3, true)
       return false
   end
   ```

## Common Data Structures

### Buff and Debuff Tables

The Makulu Framework commonly uses buff and debuff tables to make code more readable and maintainable:

```lua
-- Example buff definitions
local buffs = {
    arenaPreparation = 32727,
    powerInfusion = 10060,
    clearcasting = 135700,
    incarnation = 102543,
    shadowmeld = 58984,
    prowl = 5215,
    catForm = 768,
    bearForm = 5487,
    tigersFury = 5217,
}

-- Example debuff definitions
local debuffs = {
    thrashCat = 405233,
    moonfireCat = 155625,
    rake = 155722,
    rip = 1079,
    adaptiveSwarm = 391889,
}

-- Usage with unit methods
if player:HasBuff(buffs.tigersFury) then
    -- Do something when Tiger's Fury is active
end

if not target:HasDeBuff(debuffs.rip) or target:DebuffRemains(debuffs.rip) < 5000 then
    -- Apply or refresh Rip if it's missing or about to expire
end
```

### Interrupt Tables

Interrupt tables define spells that can be used to interrupt enemy casts:

```lua
local interrupts = {
    { spell = SkullBash },
    { spell = MightyBash, isCC = true, distance = 8 },
    { spell = IncapacitatingRoar, isCC = true, aoe = true, distance = 3 },
}

-- Function to handle interrupts
local function handleInterrupts()
    -- Find a casting enemy that should be interrupted
    local interruptTarget = enemies:Find(function(unit)
        return unit:IsCasting() and 
               unit:IsSafeToKick() and 
               unit:PvpKick(MakLists.importantCasts)
    end)
    
    if interruptTarget then
        -- Try each interrupt in order
        for _, interrupt in ipairs(interrupts) do
            if interrupt.spell:ReadyToUse() and 
               (not interrupt.distance or interruptTarget:Distance() <= interrupt.distance) then
                return interrupt.spell:Cast(interruptTarget)
            end
        end
    end
    
    return false
end
```
