BindPadVars = {
	["tab"] = 2,
	["version"] = 1.3,
	["PROFILE_Tichondrius_Goblindznts"] = {
		{
			["CharacterSpecificTab1"] = {
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Quaking Palm",
					["action"] = "CLICK BindPadKey:Quaking Palm",
					["macrotext"] = "/cast [@mouseover,harm][]Quaking Palm",
				}, -- [1]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "1",
					["action"] = "CLICK BindPadKey:1",
					["macrotext"] = "",
				}, -- [2]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "2",
					["action"] = "CLICK BindPadKey:2",
					["macrotext"] = "/cast Hyper Organic Light Originator",
				}, -- [3]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "3",
					["action"] = "CLICK BindPadKey:3",
					["macrotext"] = "",
				}, -- [4]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "4",
					["action"] = "CLICK BindPadKey:4",
					["macrotext"] = "",
				}, -- [5]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "5",
					["action"] = "CLICK BindPadKey:5",
					["macrotext"] = "",
				}, -- [6]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "6",
					["action"] = "CLICK BindPadKey:6",
					["macrotext"] = "/cast [@mouseover,harm][]Admonishment\n/cast [@mouseover,harm][]Provoke",
				}, -- [7]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Strike of tge Windlord",
					["action"] = "CLICK BindPadKey:Strike of tge Windlord",
					["macrotext"] = "/cast Strike of the Windlord",
				}, -- [8]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Dispel Member1",
					["action"] = "CLICK BindPadKey:Dispel Member1",
					["macrotext"] = "/run GetLOS(UnitExists(\"raid1\") and \"raid1\" or \"party1\")\n/stopcasting\n/cast [@raid1,exists][@party1,exists]Detox",
				}, -- [9]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "7",
					["action"] = "CLICK BindPadKey:7",
					["macrotext"] = "",
				}, -- [10]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Blackout Kick",
					["action"] = "CLICK BindPadKey:Blackout Kick",
					["macrotext"] = "/cast Blackout Kick",
				}, -- [11]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Roll",
					["action"] = "CLICK BindPadKey:Roll",
					["macrotext"] = "/cast Chi Torpedo\n/cast Roll",
				}, -- [12]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Vivify",
					["action"] = "CLICK BindPadKey:Vivify",
					["macrotext"] = "/cast [@mouseover,help][]Vivify",
				}, -- [13]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "8",
					["action"] = "CLICK BindPadKey:8",
					["macrotext"] = "/cast [@mouseover,harm][]Crackling Jade Lightning",
				}, -- [14]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Storm, Earth, and Fire | Serenity",
					["action"] = "CLICK BindPadKey:Storm, Earth, and Fire | Serenity",
					["macrotext"] = "/cast Storm, Earth, and Fire\n/cast Storm, Earth, and Fire: Fixate\n/cast Serenity",
				}, -- [15]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "9",
					["action"] = "CLICK BindPadKey:9",
					["macrotext"] = "/cast Spinning Crane Kick",
				}, -- [16]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Cisable",
					["action"] = "CLICK BindPadKey:Cisable",
					["macrotext"] = "/cast Disable",
				}, -- [17]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Tiger Palm",
					["action"] = "CLICK BindPadKey:Tiger Palm",
					["macrotext"] = "/cast Tiger Palm",
				}, -- [18]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Taunt Psts",
					["action"] = "CLICK BindPadKey:Taunt Psts",
					["macrotext"] = "/stopcasting\n/cast [@arenapet1,harm]Admonishment\n/cast [@arenapet2,harm]Admonishment\n/cast [@arenapet3,harm]Admonishment\n/cast [@arenapet1,harm]Provoke\n/cast [@arenapet2,harm]Provoke\n/cast [@arenapet3,harm]Provoke",
				}, -- [19]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "10",
					["action"] = "CLICK BindPadKey:10",
					["macrotext"] = "/cast Stoneform",
				}, -- [20]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "11",
					["action"] = "CLICK BindPadKey:11",
					["macrotext"] = "/cast Arcane Torrent",
				}, -- [21]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Grapple Weapon Arena3",
					["action"] = "CLICK BindPadKey:Grapple Weapon Arena3",
					["macrotext"] = "/stopcasting\n/cast [@arena3]Grapple Weapon",
				}, -- [22]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Risiny Sun Kick",
					["action"] = "CLICK BindPadKey:Risiny Sun Kick",
					["macrotext"] = "/cast Rising Sun Kick",
				}, -- [23]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "12",
					["action"] = "CLICK BindPadKey:12",
					["macrotext"] = "/cast Chi Wave\n/cast Chi Burst",
				}, -- [24]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Tiger's Lust Member1",
					["action"] = "CLICK BindPadKey:Tiger's Lust Member1",
					["macrotext"] = "/run GetLOS(UnitExists(\"raid1\") and \"raid1\" or \"party1\")\n/stopcasting\n/cast [@raid1,exists][@party1,exists]Tiger\'s Lust",
				}, -- [25]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "13",
					["action"] = "CLICK BindPadKey:13",
					["macrotext"] = "/run GetLOS(UnitExists(\"raid2\") and \"raid2\" or \"party2\")\n/stopcasting\n/cast [@raid2,exists][@party2,exists]Tiger\'s Lust",
				}, -- [26]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "14",
					["action"] = "CLICK BindPadKey:14",
					["macrotext"] = "",
				}, -- [27]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Tiger's Lust Member3",
					["action"] = "CLICK BindPadKey:Tiger's Lust Member3",
					["macrotext"] = "/run GetLOS(UnitExists(\"raid3\") and \"raid3\" or \"party3\")\n/stopcasting\n/cast [@raid3,exists][@party3,exists]Tiger\'s Lust",
				}, -- [28]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "15",
					["action"] = "CLICK BindPadKey:15",
					["macrotext"] = "/cast Expel Harm",
				}, -- [29]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Dispel",
					["action"] = "CLICK BindPadKey:Dispel",
					["macrotext"] = "/stopcasting\n/cast [@mouseover,help][]Detox",
				}, -- [30]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Touch of Death",
					["action"] = "CLICK BindPadKey:Touch of Death",
					["macrotext"] = "/cast [@mouseover,harm][]Touch of Death",
				}, -- [31]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Dispel Mekber2",
					["action"] = "CLICK BindPadKey:Dispel Mekber2",
					["macrotext"] = "/run GetLOS(UnitExists(\"raid2\") and \"raid2\" or \"party2\")\n/stopcasting\n/cast [@raid2,exists][@party2,exists]Detox",
				}, -- [32]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Faeline Stomp",
					["action"] = "CLICK BindPadKey:Faeline Stomp",
					["macrotext"] = "/cast Faeline Stomp",
				}, -- [33]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Leg Sweep",
					["action"] = "CLICK BindPadKey:Leg Sweep",
					["macrotext"] = "/stopcasting\n/cast Leg Sweep",
				}, -- [34]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "16",
					["action"] = "CLICK BindPadKey:16",
					["macrotext"] = "",
				}, -- [35]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Patalysis Focus",
					["action"] = "CLICK BindPadKey:Patalysis Focus",
					["macrotext"] = "/stopcasting\n/cast [@focus]Paralysis",
				}, -- [36]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "17",
					["action"] = "CLICK BindPadKey:17",
					["macrotext"] = "",
				}, -- [37]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "18",
					["action"] = "CLICK BindPadKey:18",
					["macrotext"] = "/stopcasting\n/cast [@arena1]Paralysis",
				}, -- [38]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "19",
					["action"] = "CLICK BindPadKey:19",
					["macrotext"] = "/stopcasting\n/cast [@arena2]Paralysis",
				}, -- [39]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Transcendence",
					["action"] = "CLICK BindPadKey:Transcendence",
					["macrotext"] = "/cast Transcendence",
				}, -- [40]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Oaralysis Arena3",
					["action"] = "CLICK BindPadKey:Oaralysis Arena3",
					["macrotext"] = "/stopcasting\n/cast [@arena3]Paralysis",
				}, -- [41]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Paralysis",
					["action"] = "CLICK BindPadKey:Paralysis",
					["macrotext"] = "/stopcasting\n/cast [@mouseover,harm][]Paralysis",
				}, -- [42]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Interrupt Focus",
					["action"] = "CLICK BindPadKey:Interrupt Focus",
					["macrotext"] = "/stopcasting\n/cast [@focus]Spear Hand Strike",
				}, -- [43]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Interrupt Arena1",
					["action"] = "CLICK BindPadKey:Interrupt Arena1",
					["macrotext"] = "/stopcasting\n/cast [@arena1]Spear Hand Strike",
				}, -- [44]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Interrupt Arena2",
					["action"] = "CLICK BindPadKey:Interrupt Arena2",
					["macrotext"] = "/stopcasting\n/cast [@arena2]Spear Hand Strike",
				}, -- [45]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Interrupt Arena3",
					["action"] = "CLICK BindPadKey:Interrupt Arena3",
					["macrotext"] = "/stopcasting\n/cast [@arena3]Spear Hand Strike",
				}, -- [46]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "20",
					["action"] = "CLICK BindPadKey:20",
					["macrotext"] = "",
				}, -- [47]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Dispel Member3",
					["action"] = "CLICK BindPadKey:Dispel Member3",
					["macrotext"] = "/run GetLOS(UnitExists(\"raid3\") and \"raid3\" or \"party3\")\n/stopcasting\n/cast [@raid3,exists][@party3,exists]Detox",
				}, -- [48]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Tiger's Lust",
					["action"] = "CLICK BindPadKey:Tiger's Lust",
					["macrotext"] = "/stopcasting\n/cast [@mouseover,help][]Tiger\'s Lust",
				}, -- [49]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Ring of Peace",
					["action"] = "CLICK BindPadKey:Ring of Peace",
					["macrotext"] = "/cast [combat,@player][]Ring of Peace",
				}, -- [50]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Diffuse Magic",
					["action"] = "CLICK BindPadKey:Diffuse Magic",
					["macrotext"] = "/cast Diffuse Magic",
				}, -- [51]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "21",
					["action"] = "CLICK BindPadKey:21",
					["macrotext"] = "/cast [@payer]Summon Jade Serpent Statue",
				}, -- [52]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "22",
					["action"] = "CLICK BindPadKey:22",
					["macrotext"] = "/cast [@player]Summon White Tiger Statue",
				}, -- [53]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "23",
					["action"] = "CLICK BindPadKey:23",
					["macrotext"] = "/cast [@player]Summon Black Ox Statue",
				}, -- [54]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Taunt Black Ox Statue",
					["action"] = "CLICK BindPadKey:Taunt Black Ox Statue",
					["macrotext"] = "/stopcasting\n/target Black Ox Statue\n/cast Admonishment\n/cast Provoke\n/targetlasttarget\n/click TotemFrameTotem1 RightButton",
				}, -- [55]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Fists of Fury",
					["action"] = "CLICK BindPadKey:Fists of Fury",
					["macrotext"] = "/cast [nochanneling]Fists of Fury",
				}, -- [56]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "24",
					["action"] = "CLICK BindPadKey:24",
					["macrotext"] = "/cast Flying Serpent Kick",
				}, -- [57]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Interrupt",
					["action"] = "CLICK BindPadKey:Interrupt",
					["macrotext"] = "/stopcasting\n/cast [@mouseover,harm][]Spear Hand Strike",
				}, -- [58]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Touvh of Karma",
					["action"] = "CLICK BindPadKey:Touvh of Karma",
					["macrotext"] = "/cast Touch of Karma",
				}, -- [59]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "25",
					["action"] = "CLICK BindPadKey:25",
					["macrotext"] = "/cast Rushing Jade Wind",
				}, -- [60]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Resuscitate",
					["action"] = "CLICK BindPadKey:Resuscitate",
					["macrotext"] = "/cast [spec:2,@mouseover,raid][spec:2,@target,raid][spec:2,@target,noexists,group]Reawaken",
				}, -- [61]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Bonedust Brew",
					["action"] = "CLICK BindPadKey:Bonedust Brew",
					["macrotext"] = "/startattack\n/cast Bonedust Brew",
				}, -- [62]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Blood Fury",
					["action"] = "CLICK BindPadKey:Blood Fury",
					["macrotext"] = "/cast Blood Fury",
				}, -- [63]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "26",
					["action"] = "CLICK BindPadKey:26",
					["macrotext"] = "",
				}, -- [64]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Mighty Ox Kick",
					["action"] = "CLICK BindPadKey:Mighty Ox Kick",
					["macrotext"] = "/cast Mighty Ox Kick",
				}, -- [65]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Tigereye Brew",
					["action"] = "CLICK BindPadKey:Tigereye Brew",
					["macrotext"] = "/cast Tigereye Brew",
				}, -- [66]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "27",
					["action"] = "CLICK BindPadKey:27",
					["macrotext"] = "/stopcasting\n/cast [@mouseover,harm][]Grapple Weapon",
				}, -- [67]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Grapple Weapon Arena1",
					["action"] = "CLICK BindPadKey:Grapple Weapon Arena1",
					["macrotext"] = "/stopcasting\n/cast [@arena1]Grapple Weapon",
				}, -- [68]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "28",
					["action"] = "CLICK BindPadKey:28",
					["macrotext"] = "/stopcasting\n/cast [@arena2]Grapple Weapon",
				}, -- [69]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "29",
					["action"] = "CLICK BindPadKey:29",
					["macrotext"] = "/cast [@mouseover,help][]Soothing Mist",
				}, -- [70]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Transcendence: Transfer",
					["action"] = "CLICK BindPadKey:Transcendence: Transfer",
					["macrotext"] = "/stopcasting\n/cast Transcendence: Transfer",
				}, -- [71]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Fortifying Brew",
					["action"] = "CLICK BindPadKey:Fortifying Brew",
					["macrotext"] = "/stopcasting\n/cast Fortifying Brew",
				}, -- [72]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Shadowmeld",
					["action"] = "CLICK BindPadKey:Shadowmeld",
					["macrotext"] = "/cast Shadowmeld",
				}, -- [73]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "30",
					["action"] = "CLICK BindPadKey:30",
					["macrotext"] = "",
				}, -- [74]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Escape Artist",
					["action"] = "CLICK BindPadKey:Escape Artist",
					["macrotext"] = "/cast Escape Artist",
				}, -- [75]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "31",
					["action"] = "CLICK BindPadKey:31",
					["macrotext"] = "/cast [@mouseover,help][]Gift of the Naaru",
				}, -- [76]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "32",
					["action"] = "CLICK BindPadKey:32",
					["macrotext"] = "",
				}, -- [77]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Whirling Dragon Punch",
					["action"] = "CLICK BindPadKey:Whirling Dragon Punch",
					["macrotext"] = "/cast Whirling Dragon Punch",
				}, -- [78]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Will of the Forsaken",
					["action"] = "CLICK BindPadKey:Will of the Forsaken",
					["macrotext"] = "/cast Will of the Forsaken",
				}, -- [79]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "War Stomp",
					["action"] = "CLICK BindPadKey:War Stomp",
					["macrotext"] = "/cast War Stomp",
				}, -- [80]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Berserking",
					["action"] = "CLICK BindPadKey:Berserking",
					["macrotext"] = "/cast Berserking",
				}, -- [81]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Invoke Xuen, the White Tiger",
					["action"] = "CLICK BindPadKey:Invoke Xuen, the White Tiger",
					["macrotext"] = "/cast Invoke Xuen, the White Tiger",
				}, -- [82]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Haymaker",
					["action"] = "CLICK BindPadKey:Haymaker",
					["macrotext"] = "/cast [@mouseover,harm][]Haymaker",
				}, -- [83]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "33",
					["action"] = "CLICK BindPadKey:33",
					["macrotext"] = "",
				}, -- [84]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Spatial Tift",
					["action"] = "CLICK BindPadKey:Spatial Tift",
					["macrotext"] = "/cast Spatial Rift",
				}, -- [85]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Fireblood",
					["action"] = "CLICK BindPadKey:Fireblood",
					["macrotext"] = "/cast Fireblood",
				}, -- [86]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "34",
					["action"] = "CLICK BindPadKey:34",
					["macrotext"] = "",
				}, -- [87]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "35",
					["action"] = "CLICK BindPadKey:35",
					["macrotext"] = "/cast Dampen Harm",
				}, -- [88]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Bull Rush",
					["action"] = "CLICK BindPadKey:Bull Rush",
					["macrotext"] = "/cast Bull Rush",
				}, -- [89]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "36",
					["action"] = "CLICK BindPadKey:36",
					["macrotext"] = "/cast Ancestral Call",
				}, -- [90]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Human Racial",
					["action"] = "CLICK BindPadKey:Human Racial",
					["macrotext"] = "/cast Human Racial",
				}, -- [91]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "37",
					["action"] = "CLICK BindPadKey:37",
					["macrotext"] = "/cast Regeneratin",
				}, -- [92]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Bag of Tricks",
					["action"] = "CLICK BindPadKey:Bag of Tricks",
					["macrotext"] = "/cast [nocombat]Rummage Your Bag",
				}, -- [93]
				{
					["type"] = "CLICK",
					["texture"] = 132089,
					["name"] = "Arcane Pulse",
					["action"] = "CLICK BindPadKey:Arcane Pulse",
					["macrotext"] = "/cast Arcane Pulse",
				}, -- [94]
				["numSlot"] = 175,
			},
			["CharacterSpecificTab2"] = {
				["numSlot"] = 49,
			},
			["AllKeyBindings"] = {
				["TAB"] = "TARGETNEARESTENEMY",
				["CTRL-MOUSEWHEELDOWN"] = "VEHICLEAIMDECREMENT",
				["SHIFT-C"] = "ITEMCOMPARISONCYCLING",
				["CTRL-MOUSEWHEELUP"] = "VEHICLEAIMINCREMENT",
				["NUMPAD1"] = "CLICK BindPadKey:Patalysis Focus",
				["NUMPAD3"] = "CLICK BindPadKey:19",
				["CTRL-F5"] = "SHAPESHIFTBUTTON5",
				["CTRL-F3"] = "SHAPESHIFTBUTTON3",
				["CTRL-4"] = "BONUSACTIONBUTTON4",
				["MOUSEWHEELDOWN"] = "CAMERAZOOMOUT",
				["SHIFT-P"] = "TOGGLECOLLECTIONS",
				["MOUSEWHEELUP"] = "CAMERAZOOMIN",
				["HOME"] = "PREVVIEW",
				["END"] = "NEXTVIEW",
				["CTRL-0"] = "BONUSACTIONBUTTON10",
				["SHIFT-F3"] = "TARGETPARTYPET2",
				["PRINTSCREEN"] = "SCREENSHOT",
				["F2"] = "TARGETPARTYMEMBER1",
				["NUMPAD0"] = "CLICK BindPadKey:Paralysis",
				["DOWN"] = "MOVEBACKWARD",
				["NUMPAD5"] = "CLICK BindPadKey:Transcendence",
				["CTRL-2"] = "BONUSACTIONBUTTON2",
				["LEFT"] = "TURNLEFT",
				["CTRL-SHIFT-PAGEDOWN"] = "COMBATLOGBOTTOM",
				["CTRL-PAGEUP"] = "COMBATLOGPAGEUP",
				["CTRL--"] = "MASTERVOLUMEDOWN",
				["F12"] = "TOGGLEBACKPACK",
				["CTRL-PAGEDOWN"] = "COMBATLOGPAGEDOWN",
				["CTRL-="] = "MASTERVOLUMEUP",
				["CTRL-S"] = "TOGGLESOUND",
				["CTRL-M"] = "TOGGLEMUSIC",
				["F11"] = "TOGGLEBAG4",
				["NUMPADMINUS"] = "MINIMAPZOOMOUT",
				["NUMPADPLUS"] = "MINIMAPZOOMIN",
				["B"] = "OPENALLBAGS",
				["CTRL-8"] = "BONUSACTIONBUTTON8",
				["CTRL-I"] = "TOGGLEDUNGEONSANDRAIDS",
				["NUMLOCK"] = "TOGGLEAUTORUN",
				["CTRL-9"] = "BONUSACTIONBUTTON9",
				["I"] = "TOGGLEGROUPFINDER",
				["F10"] = "TOGGLEBAG3",
				["ENTER"] = "OPENCHAT",
				["UP"] = "MOVEFORWARD",
				["T"] = "TOGGLECHATTAB",
				["/"] = "OPENCHATSLASH",
				["PAGEDOWN"] = "CHATPAGEDOWN",
				["SHIFT-SPACE"] = "TOGGLEWORLDSTATESCORES",
				["SHIFT-M"] = "TOGGLEBATTLEFIELDMINIMAP",
				["F1"] = "TARGETSELF",
				["CTRL-6"] = "BONUSACTIONBUTTON6",
				["L"] = "TOGGLEQUESTLOG",
				["F5"] = "TARGETPARTYMEMBER4",
				["SHIFT-Y"] = "TOGGLESTATISTICS",
				["CTRL-F9"] = "SHAPESHIFTBUTTON9",
				["SHIFT-T"] = "PETATTACK",
				["Y"] = "TOGGLEACHIEVEMENT",
				["1"] = "ACTIONBUTTON1",
				["SHIFT-I"] = "TOGGLEPETBOOK",
				["SPACE"] = "JUMP",
				["CTRL-7"] = "BONUSACTIONBUTTON7",
				["RIGHT"] = "TURNRIGHT",
				["BUTTON4"] = "TOGGLEAUTORUN",
				["CTRL-F8"] = "SHAPESHIFTBUTTON8",
				["A"] = "STRAFELEFT",
				["SHIFT-J"] = "TOGGLEENCOUNTERJOURNAL",
				["F9"] = "TOGGLEBAG2",
				["F3"] = "TARGETPARTYMEMBER2",
				["SHIFT-B"] = "TOGGLEBACKPACK",
				["CTRL-R"] = "TOGGLEFPS",
				["CTRL-F10"] = "SHAPESHIFTBUTTON10",
				["F"] = "INTERACTTARGET",
				["CTRL-V"] = "ALLNAMEPLATES",
				["CTRL-F2"] = "SHAPESHIFTBUTTON2",
				["INSERT"] = "PITCHUP",
				["ALT-Z"] = "TOGGLEUI",
				["-"] = "ACTIONBUTTON11",
				["SHIFT-F5"] = "TARGETPARTYPET4",
				["CTRL-F1"] = "SHAPESHIFTBUTTON1",
				["NUMPADDIVIDE"] = "TOGGLERUN",
				["CTRL-5"] = "BONUSACTIONBUTTON5",
				["0"] = "CLICK BindPadKey:Dispel Member3",
				["3"] = "ACTIONBUTTON3",
				["2"] = "ACTIONBUTTON2",
				["5"] = "CLICK BindPadKey:Tiger's Lust Member3",
				["4"] = "ACTIONBUTTON4",
				["7"] = "CLICK BindPadKey:Dispel",
				["6"] = "CLICK BindPadKey:Cisable",
				["9"] = "CLICK BindPadKey:Dispel Mekber2",
				["8"] = "CLICK BindPadKey:Dispel Member1",
				["CTRL-F4"] = "SHAPESHIFTBUTTON4",
				["SHIFT-F1"] = "TARGETPET",
				["="] = "ACTIONBUTTON12",
				["F4"] = "TARGETPARTYMEMBER3",
				["F8"] = "TOGGLEBAG1",
				["CTRL-3"] = "BONUSACTIONBUTTON3",
				["SHIFT-R"] = "REPLY2",
				["SHIFT-PAGEDOWN"] = "CHATBOTTOM",
				["C"] = "TOGGLECHARACTER0",
				["CTRL-F7"] = "SHAPESHIFTBUTTON7",
				["SHIFT-TAB"] = "TARGETPREVIOUSENEMY",
				["D"] = "STRAFERIGHT",
				["G"] = "TOGGLEPINGLISTENER",
				["SHIFT-F4"] = "TARGETPARTYPET3",
				["CTRL-F6"] = "SHAPESHIFTBUTTON6",
				["H"] = "TOGGLECHARACTER4",
				["K"] = "TOGGLEPROFESSIONBOOK",
				["J"] = "TOGGLEGUILDTAB",
				["M"] = "TOGGLEWORLDMAP",
				["CTRL-SHIFT-TAB"] = "TARGETPREVIOUSFRIEND",
				["O"] = "TOGGLESOCIAL",
				["N"] = "TOGGLETALENTS",
				["BUTTON3"] = "MOVEANDSTEER",
				["P"] = "TOGGLESPELLBOOK",
				["S"] = "MULTIACTIONBAR1BUTTON1",
				["R"] = "REPLY",
				["U"] = "TOGGLECHARACTER2",
				["CTRL-TAB"] = "TARGETNEARESTFRIEND",
				["W"] = "MOVEFORWARD",
				["V"] = "NAMEPLATES",
				["ESCAPE"] = "TOGGLEGAMEMENU",
				["X"] = "SITORSTAND",
				["CTRL-1"] = "BONUSACTIONBUTTON1",
				["Z"] = "TOGGLESHEATH",
				["SHIFT-F2"] = "TARGETPARTYPET1",
				["PAGEUP"] = "CHATPAGEUP",
				["SHIFT-V"] = "FRIENDNAMEPLATES",
				["DELETE"] = "PITCHDOWN",
				["SHIFT-i"] = "CLICK BindPadKey:35",
				["SHIFT-b"] = "CLICK BindPadKey:21",
				["n"] = "CLICK BindPadKey:Tiger's Lust Member1",
				["SHIFT-NUMPAD5"] = "CLICK BindPadKey:Quaking Palm",
				["SHIFT-NUMPAD7"] = "CLICK BindPadKey:Bag of Tricks",
				["SHIFT-h"] = "CLICK BindPadKey:Taunt Black Ox Statue",
				["o"] = "CLICK BindPadKey:Resuscitate",
				["NUMPAD9"] = "CLICK BindPadKey:Interrupt Arena1",
				["k"] = "CLICK BindPadKey:29",
				["v"] = "CLICK BindPadKey:6",
				["NUMPAD7"] = "CLICK BindPadKey:Interrupt",
				["SHIFT-u"] = "CLICK BindPadKey:Interrupt Arena3",
				["p"] = "CLICK BindPadKey:Roll",
				["SHIFT-."] = "CLICK BindPadKey:Storm, Earth, and Fire | Serenity",
				["j"] = "CLICK BindPadKey:13",
				["SHIFT-["] = "CLICK BindPadKey:12",
				["SHIFT-6"] = "CLICK BindPadKey:Bonedust Brew",
				["SHIFT-NUMPAD1"] = "CLICK BindPadKey:Grapple Weapon Arena1",
				["SHIFT-l"] = "CLICK BindPadKey:Ring of Peace",
				["l"] = "CLICK BindPadKey:8",
				["h"] = "CLICK BindPadKey:Touch of Death",
				["NUMPAD2"] = "CLICK BindPadKey:18",
				["SHIFT-v"] = "CLICK BindPadKey:22",
				["SHIFT-,"] = "CLICK BindPadKey:Interrupt Arena2",
				["SHIFT-p"] = "CLICK BindPadKey:Fortifying Brew",
				[";"] = "CLICK BindPadKey:Taunt Psts",
				["SHIFT-NUMPAD4"] = "CLICK BindPadKey:31",
				["SHIFT-NUMPAD2"] = "CLICK BindPadKey:28",
				["SHIFT-NUMPAD3"] = "CLICK BindPadKey:Grapple Weapon Arena3",
				["y"] = "CLICK BindPadKey:Leg Sweep",
				["SHIFT-NUMPAD0"] = "CLICK BindPadKey:27",
				["SHIFT-k"] = "CLICK BindPadKey:Touvh of Karma",
				["SHIFT-n"] = "CLICK BindPadKey:Strike of tge Windlord",
				["SHIFT-0"] = "CLICK BindPadKey:Tigereye Brew",
				["SHIFT-8"] = "CLICK BindPadKey:Faeline Stomp",
				["SHIFT-m"] = "CLICK BindPadKey:24",
				["SHIFT-7"] = "CLICK BindPadKey:Whirling Dragon Punch",
				["NUMPAD4"] = "CLICK BindPadKey:Oaralysis Arena3",
				["b"] = "CLICK BindPadKey:15",
				["["] = "CLICK BindPadKey:Vivify",
				["NUMPAD6"] = "CLICK BindPadKey:Transcendence: Transfer",
				[","] = "CLICK BindPadKey:Tiger Palm",
				["NUMPAD8"] = "CLICK BindPadKey:Interrupt Focus",
				["SHIFT-o"] = "CLICK BindPadKey:Fists of Fury",
				["."] = "CLICK BindPadKey:Tiger's Lust",
				["u"] = "CLICK BindPadKey:Blackout Kick",
				["m"] = "CLICK BindPadKey:Risiny Sun Kick",
				["SHIFT-9"] = "CLICK BindPadKey:Mighty Ox Kick",
				["SHIFT-j"] = "CLICK BindPadKey:25",
				["i"] = "CLICK BindPadKey:9",
				["SHIFT-y"] = "CLICK BindPadKey:Diffuse Magic",
				["SHIFT-NUMPAD6"] = "CLICK BindPadKey:Haymaker",
				["SHIFT-;"] = "CLICK BindPadKey:23",
				["SHIFT-5"] = "CLICK BindPadKey:Invoke Xuen, the White Tiger",
			},
			["CharacterSpecificTab3"] = {
				["numSlot"] = 49,
			},
		}, -- [5]
		["profileForTalentGroup"] = {
			5, -- [1]
			5, -- [2]
			2,
		},
	},
	["numSlot"] = 49,
	["GeneralKeyBindings"] = {
	},
}