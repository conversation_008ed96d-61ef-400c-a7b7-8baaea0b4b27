[{"name": "Anti-Magic Shell", "macro": "/cast Anti-Magic Shell"}, {"name": "Chains of Ice", "macro": "/cast Chains of Ice"}, {"name": "Chains of Ice Arena1", "macro": "/cast [@arena1]Chains of Ice"}, {"name": "Chains of Ice Arena2", "macro": "/cast [@arena2]Chains of Ice"}, {"name": "Chains of Ice Arena3", "macro": "/cast [@arena3]Chains of Ice"}, {"name": "<PERSON><PERSON>", "macro": "/cast Murderous Intent\n/cast Dark Command"}, {"name": "Defile | Death and Decay", "macro": "/cast [@player]Death and Decay"}, {"name": "Death Coil", "macro": "/cast Death Coil"}, {"name": "Death Coil Player", "macro": "/cast [@player]Death Coil"}, {"name": "Death Coil Pet", "macro": "/cast [@pet]Death Coil"}, {"name": "Death Grip", "macro": "/cast <PERSON> Grip"}, {"name": "Death Grip Arena1", "macro": "/cast [@arena1]Death Grip"}, {"name": "Death Grip Arena2", "macro": "/cast [@arena2]Death Grip"}, {"name": "Death Grip Arena3", "macro": "/cast [@arena3]Death Grip"}, {"name": "Death's Advance", "macro": "/cast Death's Advance\n/cast Death Charge"}, {"name": "Lichborne", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Cancelaura Lichborne", "macro": "/cancelaura <PERSON>"}, {"name": "Path of Frost", "macro": "/cast Path of Frost"}, {"name": "Raise <PERSON>", "macro": "/cast [@mouseover,exists,help][]<PERSON><PERSON>"}, {"name": "Strike", "macro": "/cast Obliterate\n/cast Rune Strike"}, {"name": "Icebound Fortitude", "macro": "/cast Icebound Fortitude"}, {"name": "Death Strike", "macro": "/cast Death Strike"}, {"name": "<PERSON><PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "Interrupt", "macro": "/cast <PERSON>"}, {"name": "Interrupt Arena1", "macro": "/cast [@arena1]Mind Freeze"}, {"name": "Interrupt Arena2", "macro": "/cast [@arena2]Mind Freeze"}, {"name": "Interrupt Arena3", "macro": "/cast [@arena3]Mind Freeze"}, {"name": "Blinding Sleet", "macro": "/cast <PERSON><PERSON>"}, {"name": "Control Undead", "macro": "/cast Control Undead"}, {"name": "Sacrificial Pact", "macro": "/cast Sacrificial Pact"}, {"name": "Death Pact", "macro": "/cast Death Pact"}, {"name": "Anti-Magic Zone", "macro": "/cast [@player]Anti-Magic Zone"}, {"name": "Asphyxiate", "macro": "/cast Asphyxiate"}, {"name": "Asphyxiate Arena1", "macro": "/cast [@arena1]Asphyxiate"}, {"name": "Asphyxiate Arena2", "macro": "/cast [@arena2]Asphyxiate"}, {"name": "Asphyxiate Arena3", "macro": "/cast [@arena3]Asphyxiate"}, {"name": "Wraith Walk", "macro": "/cast Wraith Walk"}, {"name": "Soul Reaper", "macro": "/cast <PERSON> Reaper"}, {"name": "Abomination Limb", "macro": "/cast Abomination Limb"}, {"name": "Remorseless Winter", "macro": "/cast Remorseless Winter"}, {"name": "Breath of Sin<PERSON>gos<PERSON>", "macro": "/cast Breath of Sindragos<PERSON>"}, {"name": "<PERSON>ll Streak", "macro": "/cast <PERSON><PERSON>"}, {"name": "Empower Rune Weapon", "macro": "/cast Empower Rune Weapon"}, {"name": "Frost Strike", "macro": "/cast <PERSON>"}, {"name": "Frostscythe", "macro": "/cast Frosts<PERSON><PERSON>"}, {"name": "Frostwyrm's Fury", "macro": "/cast <PERSON><PERSON><PERSON>'s Fury"}, {"name": "Glacial Advance", "macro": "/cast Glacial Advance"}, {"name": "Horn of Winter", "macro": "/cast Horn of Winter"}, {"name": "Howling <PERSON>", "macro": "/cast Howling <PERSON>"}, {"name": "<PERSON><PERSON> of Frost", "macro": "/cast <PERSON><PERSON> of Frost"}, {"name": "Reaper's Mark", "macro": "/cast <PERSON>'s Mark"}, {"name": "Dark Simulacrum", "macro": "/cast <PERSON>"}, {"name": "Dark Simulacrum Arena1", "macro": "/cast [@arena1]Dark Simulacrum"}, {"name": "Dark Simulacrum Arena2", "macro": "/cast [@arena2]Dark Simulacrum"}, {"name": "Dark Simulacrum Arena3", "macro": "/cast [@arena3]Dark Simulacrum"}, {"name": "Strangulate", "macro": "/cast Strangulate"}, {"name": "Strangulate Arena1", "macro": "/cast [@arena1]Strangulate"}, {"name": "Strangulate Arena2", "macro": "/cast [@arena2]Strangulate"}, {"name": "Strangulate Arena3", "macro": "/cast [@arena3]Strangulate"}, {"name": "Human Racial", "macro": "/cast Will to Survive"}, {"name": "Stoneform", "macro": "/cast Stoneform"}, {"name": "Shadowmeld", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Escape Artist", "macro": "/cast Escape Artist"}, {"name": "Gift of the Naaru", "macro": "/cast Gift of the Naaru"}, {"name": "Darkflight", "macro": "/cast Darkflight"}, {"name": "Blood Fury", "macro": "/cast Blood Fury"}, {"name": "Will of the Forsaken", "macro": "/cast Will of the Forsaken"}, {"name": "War Stomp", "macro": "/cast War Stomp"}, {"name": "Berserking", "macro": "/cast Berserking"}, {"name": "<PERSON><PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "Rocket Jump", "macro": "/cast Rocket Jump"}, {"name": "Rocket Barrage", "macro": "/cast Rocket Barrage"}, {"name": "Quaking Palm", "macro": "/cast Quaking Palm"}, {"name": "Spatial Rift", "macro": "/cast Spatial Rift"}, {"name": "Light's Judgment", "macro": "/cast <PERSON>'s Judgment"}, {"name": "Fireblood", "macro": "/cast Fireblood"}, {"name": "Arcane P<PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "<PERSON>", "macro": "/cast <PERSON>"}, {"name": "Ancestral Call", "macro": "/cast Ancestral Call"}, {"name": "Haymaker", "macro": "/cast Haymaker"}, {"name": "Regeneratin", "macro": "/cast [@player]Master's Call"}, {"name": "Bag of Tricks", "macro": "/cast Bag of Tricks"}, {"name": "Hyper Organic Light Originator", "macro": "/cast Hyper Organic Light Originator"}, {"name": "Azerite Surge", "macro": "/cast Azerite Surge"}]