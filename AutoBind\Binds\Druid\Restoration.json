[{"name": "Barkskin", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Bear Form", "macro": "/cast Bear <PERSON>"}, {"name": "Cat Form", "macro": "/cast Cat Form"}, {"name": "Dash", "macro": "/cast Tiger <PERSON>\n/cast Dash"}, {"name": "Entangling Roots", "macro": "/cast Entangling Roots"}, {"name": "Entangling Roots Arena1", "macro": "/cast [@arena1]Entangling Roots"}, {"name": "Entangling Roots Arena2", "macro": "/cast [@arena2]Entangling Roots"}, {"name": "Entangling Roots Arena3", "macro": "/cast [@arena3]Entangling Roots"}, {"name": "Ironfur | Ferocious Bite", "macro": "/cast [form:1]Ironfur;[form:2]Ferocious Bite"}, {"name": "<PERSON><PERSON>", "macro": "/cast Growl\n/cast Alpha Challenge"}, {"name": "Taunt <PERSON>s", "macro": "/stopcasting\n/cast [@arenapet1,harm]Growl\n/cast [@arenapet2,harm]Growl\n/cast [@arenapet3,harm]Growl\n/cast [@arenapet4,harm]Growl\n/cast [@arenapet5,harm]Growl\n/cast [@arenapet1,harm]Alpha Challenge\n/cast [@arenapet2,harm]Alpha Challenge\n/cast [@arenapet3,harm]Alpha Challenge\n/cast [@arenapet4,harm]Alpha Challenge\n/cast [@arenapet5,harm]Alpha Challenge"}, {"name": "Wrath", "macro": "/cast Wrath"}, {"name": "<PERSON>gle", "macro": "/cast Mangle"}, {"name": "Shred", "macro": "/cast Shred"}, {"name": "Mark of the Wild", "macro": "/cast <PERSON> of the Wild"}, {"name": "Moonfire", "macro": "/cast Moonfire"}, {"name": "Moonfire Arena1", "macro": "/cast [@arena1]Moonfire"}, {"name": "Moonfire Arena2", "macro": "/cast [@arena2]Moonfire"}, {"name": "Moonfire Arena3", "macro": "/cast [@arena3]Moonfire"}, {"name": "Prowl", "macro": "/stopattack\n/cast [nostealth]Prowl"}, {"name": "Revive", "macro": "/cast [nocombat]Revive;[combat]Rebirth"}, {"name": "<PERSON>row<PERSON>", "macro": "/cast [@target,help][@focus,help][]<PERSON><PERSON><PERSON>"}, {"name": "Strength of the Wild", "macro": "/cast Strength of the Wild"}, {"name": "Sunfire", "macro": "/cast Sunfire"}, {"name": "Sunfire Arena1", "macro": "/cast [@arena1]Sunfire"}, {"name": "Sunfire Arena2", "macro": "/cast [@arena2]Sunfire"}, {"name": "Sunfire Arena3", "macro": "/cast [@arena3]Sunfire"}, {"name": "Brutal Slash | Swipe", "macro": "/cast <PERSON><PERSON><PERSON>lash\n/cast Swipe"}, {"name": "<PERSON><PERSON><PERSON>", "macro": "/cast Thrash"}, {"name": "Travel Form", "macro": "/cast Travel Form"}, {"name": "Frenzied Regeneration", "macro": "/cast Frenzied Regeneration"}, {"name": "Starfire", "macro": "/cast Starfire"}, {"name": "<PERSON><PERSON>", "macro": "/cast Rake"}, {"name": "Rejuvenation", "macro": "/cast [@target,help][@focus,help][]Rejuvenation"}, {"name": "Dispel", "macro": "/cast [@target,help][@focus,help][]Nature's Cure"}, {"name": "Dispel Member1", "macro": "/cast [@raid1,exists][@party1,exists]Nature's Cure"}, {"name": "Dispel Member2", "macro": "/cast [@raid2,exists][@party2,exists]Nature's Cure"}, {"name": "Dispel Member3", "macro": "/cast [@raid3,exists][@party3,exists]Nature's Cure"}, {"name": "Dispel Member4", "macro": "/cast [@raid4,exists][@party4,exists]Nature's Cure"}, {"name": "Dispel Member5", "macro": "/cast [@player]Nature's Cure"}, {"name": "Starsurge", "macro": "/cast Starsurge"}, {"name": "<PERSON><PERSON>", "macro": "/cast Rip"}, {"name": "Wild Growth", "macro": "/cast [@target,help][@focus,help][]Wild Growth"}, {"name": "<PERSON><PERSON>", "macro": "/cast Mai<PERSON>"}, {"name": "Hibernate", "macro": "/cast Hibernate"}, {"name": "Hibernate Focus", "macro": "/cast [@focus]Hibernate"}, {"name": "Hibernate Arena1", "macro": "/cast [@arena1]Hibernate\n/cast [@arenapet1]Hibernate"}, {"name": "Hibernate Arena2", "macro": "/cast [@arena2]Hibernate\n/cast [@arenapet2]Hibernate"}, {"name": "Hibernate Arena3", "macro": "/cast [@arena3]Hibernate\n/cast [@arenapet3]Hibernate"}, {"name": "Interrupt", "macro": "/cast <PERSON>"}, {"name": "Interrupt Focus", "macro": "/cast [@focus]<PERSON> Bash"}, {"name": "Interrupt Arena1", "macro": "/cast [@arena1]<PERSON> Bash"}, {"name": "Interrupt Arena2", "macro": "/cast [@arena2]<PERSON> Bash"}, {"name": "Interrupt Arena3", "macro": "/cast [@arena3]<PERSON> Bash"}, {"name": "Mass Entanglement | Ursol's Vortex", "macro": "/cast Mass Entanglement\n/cast Ursol's Vortex"}, {"name": "Mass Entanglement | Ursol's Vortex Focus", "macro": "/stopcasting\n/cast [@focus]Mass Entanglement"}, {"name": "Mass Entanglement | Ursol's Vortex Unit1", "macro": "/stopcasting\n/cast [@arena1]Mass Entanglement"}, {"name": "Mass Entanglement | Ursol's Vortex Unit2", "macro": "/stopcasting\n/cast [@arena2]Mass Entanglement"}, {"name": "Mass Entanglement | Ursol's Vortex Unit3", "macro": "/stopcasting\n/cast [@arena3]Mass Entanglement"}, {"name": "Wild Charge", "macro": "/cast [stance:0,@mouseover,help][stance:0,@focus,help][stance:0,help][stance:1/2,harm][stance:3/4/6]Wild Charge"}, {"name": "<PERSON>the", "macro": "/stopcasting\n/cast Soothe"}, {"name": "Soothe Arena1", "macro": "/stopcasting\n/cast [@arena1]<PERSON>the"}, {"name": "Soothe Arena2", "macro": "/stopcasting\n/cast [@arena2]<PERSON>the"}, {"name": "Soothe Arena3", "macro": "/stopcasting\n/cast [@arena3]<PERSON>the"}, {"name": "Cyclone", "macro": "/cast Cyclone"}, {"name": "Cyclone Focus", "macro": "/cast [@focus]Cyclone"}, {"name": "Cyclone Arena1", "macro": "/cast [@arena1]Cyclone"}, {"name": "Cyclone Arena2", "macro": "/cast [@arena2]Cyclone"}, {"name": "Cyclone Arena3", "macro": "/cast [@arena3]Cyclone"}, {"name": "Renewal", "macro": "/cast <PERSON><PERSON>"}, {"name": "Stampeding Roar", "macro": "/cast Stampeding Roar"}, {"name": "Typhoon", "macro": "/cast Typhoon"}, {"name": "Incapacitating Roar | Mighty Bash", "macro": "/stopcasting\n/cast Incapacitating Roar\n/cast <PERSON>"}, {"name": "Incapacitating Roar | Mighty Bash Focus", "macro": "/stopcasting\n/cast Incapacitating Roar\n/cast [@focus]Mighty Bash"}, {"name": "Incapacitating Roar | Mighty Bash Arena1", "macro": "/stopcasting\n/cast [@arena1]Mighty Bash"}, {"name": "Incapacitating Roar | Mighty Bash Arena2", "macro": "/stopcasting\n/cast [@arena2]Mighty Bash"}, {"name": "Incapacitating Roar | Mighty Bash Arena3", "macro": "/stopcasting\n/cast [@arena3]Mighty Bash"}, {"name": "Innervate", "macro": "/stopcasting\n/cast [@mouseover,help][@focus,help][]Innervate"}, {"name": "Innervate Member1", "macro": "/cast [@raid1,exists][@party1,exists]Innervate"}, {"name": "Innervate Member2", "macro": "/cast [@raid2,exists][@party2,exists]Innervate"}, {"name": "Innervate Member3", "macro": "/cast [@raid3,exists][@party3,exists]Innervate"}, {"name": "Innervate Member4", "macro": "/cast [@raid4,exists][@party4,exists]Innervate"}, {"name": "Innervate Member5", "macro": "/cast [@player]Innervate"}, {"name": "Heart of the Wild", "macro": "/cast Heart of the Wild"}, {"name": "Nature's Vigil", "macro": "/cast Nature's Vigil"}, {"name": "Swiftmend", "macro": "/cast [@target,help][@focus,help][]Swiftmend"}, {"name": "Cenarion Ward", "macro": "/cast [@target,help][@focus,help][]Cenarion Ward"}, {"name": "Incarnation: Tree of Life | Convoke the Spirits", "macro": "/cast Incarnation: Tree of Life\n/cast [@target,help][@focus,help][]Convoke the Spirits"}, {"name": "Efflorescence", "macro": "/cast Efflorescence"}, {"name": "Flourish", "macro": "/cast Flourish"}, {"name": "Grove Guardians | Nourish", "macro": "/cast Grove Guardians\n/cast [@target,help][@focus,help][]Nourish"}, {"name": "Invigorate", "macro": "/cast Invigorate"}, {"name": "Ironbark", "macro": "/cast [@target,help][@focus,help][]Ironbark"}, {"name": "Lifebloom", "macro": "/cast [@target,help][@focus,help][]Lifebloom"}, {"name": "Nature's Swiftness", "macro": "/cast <PERSON>'s Swiftness"}, {"name": "Overgrowth", "macro": "/cast [@target,help][@focus,help][]Overgrowth"}, {"name": "Tranquility", "macro": "/cast Tranquility"}, {"name": "Thorns", "macro": "/cast [@target,help][@focus,help][]<PERSON>s"}, {"name": "Thorns Member1", "macro": "/cast [@raid1,exists][@party1,exists]<PERSON>s"}, {"name": "Thorns Member2", "macro": "/cast [@raid2,exists][@party2,exists]<PERSON>s"}, {"name": "Thorns Member3", "macro": "/cast [@raid3,exists][@party3,exists]<PERSON>s"}, {"name": "Thorns Member4", "macro": "/cast [@raid4,exists][@party4,exists]<PERSON>s"}, {"name": "Thorns Member5", "macro": "/cast [@player]<PERSON><PERSON>"}, {"name": "Shadowmeld", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Darkflight", "macro": "/cast Lifebloom"}, {"name": "War Stomp", "macro": "/cast War Stomp"}, {"name": "Berserking", "macro": "/cast Berserking"}, {"name": "<PERSON>", "macro": "/cast <PERSON>"}, {"name": "Haymaker", "macro": "/cast Haymaker"}, {"name": "Regeneratin", "macro": "/cast <PERSON><PERSON><PERSON><PERSON>"}]