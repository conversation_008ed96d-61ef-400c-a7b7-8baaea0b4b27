[{"name": "Azure Strike", "macro": "/cast Azure Strike"}, {"name": "Blessing of the Bronze", "macro": "/cast Blessing of the Bronze"}, {"name": "Deep Breath", "macro": "/cast Deep Breath"}, {"name": "Disintegrate", "macro": "/cast Disintegrate"}, {"name": "Emerald Blossom", "macro": "/cast [@target,help][@focus,help][]Emerald Blossom"}, {"name": "Fire Breath", "macro": "/cast Fire Breath"}, {"name": "Fury of the Aspects", "macro": "/cast <PERSON> of the Aspects"}, {"name": "Hover", "macro": "/cast Time Spiral\n/cast Hover"}, {"name": "Living Flame Heal", "macro": "/cast [@target,help][@focus,help][]Living Flame\n/cast [@target,help][@focus,help][]Chrono Flames"}, {"name": "Living Flame Damage", "macro": "/cast [nochanneling]Living Flame\n/cast [nochanneling]Chrono Flames"}, {"name": "Return", "macro": "/cast Mass Return"}, {"name": "Landslide", "macro": "/cast Landslide"}, {"name": "Obsidian Scales", "macro": "/cast Obsidian Scales"}, {"name": "Dispel", "macro": "/cast [@target,help][@focus,help][]Naturalize"}, {"name": "Dispel Member1", "macro": "/cast[@party1]Naturalize"}, {"name": "Dispel Member2", "macro": "/cast[@party2]Naturalize"}, {"name": "Dispel Member3", "macro": "/cast[@party3]Naturalize"}, {"name": "Dispel Member4", "macro": "/cast[@party4]Naturalize"}, {"name": "Dispel Member5", "macro": "/cast[@player]Naturalize"}, {"name": "<PERSON><PERSON><PERSON>", "macro": "/cast [@target,help][@focus,help][]Verd<PERSON>"}, {"name": "Verdant Embrace Member1", "macro": "/cast[@party1]V<PERSON><PERSON>"}, {"name": "Verdant Embrace Member2", "macro": "/cast[@party2]V<PERSON><PERSON>"}, {"name": "Verdant Embrace Member3", "macro": "/cast[@party3]V<PERSON><PERSON>"}, {"name": "Verdant Embrace Member4", "macro": "/cast[@party4]V<PERSON><PERSON>"}, {"name": "Verdant Embrace Member5", "macro": "/cast[@player]<PERSON><PERSON><PERSON>"}, {"name": "Interrupt", "macro": "/cast Quell"}, {"name": "Interrupt Arena1", "macro": "/cast [@arena1]Quell"}, {"name": "Interrupt Arena2", "macro": "/cast [@arena2]Quell"}, {"name": "Interrupt Arena3", "macro": "/cast [@arena3]Quell"}, {"name": "Cauterizing Flame", "macro": "/cast [@target,help][@focus,help][]Cauterizing Flame"}, {"name": "Cauterizing Flame Member1", "macro": "/cast [@party1]Cauterizing Flame"}, {"name": "Cauterizing Flame Member2", "macro": "/cast [@party2]Cauterizing Flame"}, {"name": "Cauterizing Flame Member3", "macro": "/cast [@party3]Cauterizing Flame"}, {"name": "Cauterizing Flame Member4", "macro": "/cast [@party4]Cauterizing Flame"}, {"name": "Cauterizing Flame Member5", "macro": "/cast [@player]Cauterizing Flame"}, {"name": "Tip the Scales", "macro": "/cast Tip the Scales"}, {"name": "Sleep Walk", "macro": "/cast Sleep Walk"}, {"name": "Sleep Walk Arena1", "macro": "/cast [@arena1]Sleep Walk"}, {"name": "Sleep Walk Arena2", "macro": "/cast [@arena2]Sleep Walk"}, {"name": "Sleep Walk Arena3", "macro": "/cast [@arena3]Sleep Walk"}, {"name": "Sleep Walk Arena4", "macro": "/cast [@arena4]Sleep Walk"}, {"name": "Sleep Walk Arena5", "macro": "/cast [@arena5]Sleep Walk"}, {"name": "Renewing Blaze", "macro": "/cast Renewing Blaze"}, {"name": "Unravel", "macro": "/cast Unravel"}, {"name": "Unravel Arena1", "macro": "/cast [@arena1]Unravel"}, {"name": "Unravel Arena2", "macro": "/cast [@arena2]Unravel"}, {"name": "Unravel Arena3", "macro": "/cast [@arena3]Unravel"}, {"name": "Unravel Arena4", "macro": "/cast [@arena4]Unravel"}, {"name": "Unravel Arena5", "macro": "/cast [@arena5]Unravel"}, {"name": "Oppressing Roar", "macro": "/cast Oppressing Roar"}, {"name": "Rescue", "macro": "/tar [@focus]\n/cast [@cursor]Rescue\n/targetlasttarget"}, {"name": "Rescue Member1", "macro": "/tar [@party1]\n/cast [@cursor]Rescue\n/targetlasttarget"}, {"name": "Rescue Member2", "macro": "/tar [@party2]\n/cast [@cursor]Rescue\n/targetlasttarget"}, {"name": "Rescue Member3", "macro": "/tar [@party3]\n/cast [@cursor]Rescue\n/targetlasttarget"}, {"name": "Rescue Member4", "macro": "/tar [@party4]\n/cast [@cursor]Rescue\n/targetlasttarget"}, {"name": "Source of Magic", "macro": "/cast [@player]Source of Magic"}, {"name": "Source of Magic Member1", "macro": "/cast [@party1]Source of Magic"}, {"name": "Source of Magic Member2", "macro": "/cast [@party2]Source of Magic"}, {"name": "Source of Magic Member3", "macro": "/cast [@party3]Source of Magic"}, {"name": "Source of Magic Member4", "macro": "/cast [@party4]Source of Magic"}, {"name": "Source of Magic Member5", "macro": "/cast [@player]Source of Magic"}, {"name": "Time Spiral | Spatial Paradox", "macro": "/cast Time Spiral\n/cast Spatial Paradox"}, {"name": "Spatial Paradox Member1", "macro": "/cast [@party1]Spatial Paradox"}, {"name": "Spatial Paradox Member2", "macro": "/cast [@party2]Spatial Paradox"}, {"name": "Spatial Paradox Member3", "macro": "/cast [@party3]Spatial Paradox"}, {"name": "Spatial Paradox Member4", "macro": "/cast [@party4]Spatial Paradox"}, {"name": "Spatial Paradox Member5", "macro": "/cast [@player]Spatial Paradox"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "macro": "/cast <PERSON><PERSON><PERSON><PERSON>"}, {"name": "Dream Breath", "macro": "/cast Dream Breath"}, {"name": "Dream Flight", "macro": "/cast Dream Flight"}, {"name": "Echo", "macro": "/cast [@target,help][@focus,help][]Echo"}, {"name": "Emerald Communion", "macro": "/cast Emerald Communion"}, {"name": "<PERSON><PERSON><PERSON>", "macro": "/cast [@target,help][@focus,help][]Engulf"}, {"name": "<PERSON><PERSON><PERSON>age", "macro": "/cast Engulf"}, {"name": "Reversion", "macro": "/cast [@target,help][@focus,help][]Reversion"}, {"name": "Rewind", "macro": "/cast Rewind"}, {"name": "Spiritbloom", "macro": "/cast [@target,help][@focus,help][]<PERSON><PERSON><PERSON>"}, {"name": "Stasis", "macro": "/cast [nochanneling]Stasis"}, {"name": "Temporal Anomaly", "macro": "/cast Temporal Anomaly"}, {"name": "Time Dilation", "macro": "/cast [@target,help][@focus,help][]Time Dilation"}, {"name": "Chrono Loop", "macro": "/cast Chrono Loop"}, {"name": "Chrono Loop Unit1", "macro": "/cast [@arena1]Chrono Loop"}, {"name": "Chrono Loop Unit2", "macro": "/cast [@arena2]Chrono Loop"}, {"name": "Chrono Loop Unit3", "macro": "/cast [@arena3]Chrono Loop"}, {"name": "Dream Projection", "macro": "/cast Dream Projection"}, {"name": "Nullifying <PERSON><PERSON><PERSON>", "macro": "/cast Nullifying <PERSON><PERSON><PERSON>"}, {"name": "Swoop Up", "macro": "/cast [@target,help][@focus,help][]Engulf"}, {"name": "Swoop Up Arena1", "macro": "/stopcasting"}, {"name": "Time Stop", "macro": "/cast [@target,help][@focus,help][]Time Stop"}, {"name": "Time Stop Member1", "macro": "/cast [@party1]Time Stop"}, {"name": "Time Stop Member2", "macro": "/cast [@party2]Time Stop"}, {"name": "Time Stop Member3", "macro": "/cast [@party3]Time Stop"}, {"name": "Time Stop Member4", "macro": "/cast [@party4]Time Stop"}, {"name": "Time Stop Member5", "macro": "/cast [@player]Time Stop"}, {"name": "Wing Buffet", "macro": "/cast Wing Buffet"}, {"name": "<PERSON><PERSON>pe", "macro": "/cast <PERSON><PERSON>"}]