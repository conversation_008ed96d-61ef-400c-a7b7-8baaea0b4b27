[{"name": "Shot", "macro": "/cast <PERSON><PERSON>"}, {"name": "Aspect of the Cheetah", "macro": "/cast Aspect of the Cheetah"}, {"name": "Aspect of the Turtle", "macro": "/cast Aspect of the Turtle"}, {"name": "PetAbilities", "macro": "/cast Primal Rage\n/cast Fortitude of the Bear\n/cast [@mouseover,help][@focus,help][]Master's Call"}, {"name": "Master's Call Member1", "macro": "/cast [@party1,exists]Master's Call"}, {"name": "Master's Call Member2", "macro": "/cast [@party2,exists]Master's Call"}, {"name": "Master's Call Member3", "macro": "/cast [@party3,exists]Master's Call"}, {"name": "Master's Call Member4", "macro": "/cast [@party4,exists]Master's Call"}, {"name": "Master's Call Member5", "macro": "/cast [@player]Master's Call"}, {"name": "Disengage", "macro": "/cast Disengage"}, {"name": "Exhilaration", "macro": "/cast Exhilaration"}, {"name": "Feign Death", "macro": "/cast Feign Death"}, {"name": "Flare", "macro": "/cast Flare"}, {"name": "<PERSON>'s <PERSON>", "macro": "/cast <PERSON>'s <PERSON>"}, {"name": "<PERSON>'s <PERSON>", "macro": "/cast [@arena1]Hunter's Mark"}, {"name": "<PERSON>'s <PERSON>", "macro": "/cast [@arena2]<PERSON>'s Mark"}, {"name": "<PERSON>'s <PERSON>", "macro": "/cast [@arena3]Hunter's Mark"}, {"name": "<PERSON><PERSON><PERSON> Shot", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Concussive Shot | Wing Clip", "macro": "/cast Concussive Shot\n/cast Wing Clip"}, {"name": "Kill Shot", "macro": "/cast Kill Shot"}, {"name": "Tar Trap", "macro": "/cast [@player]<PERSON><PERSON> Trap"}, {"name": "Tranquilizing Shot", "macro": "/cast Tranquilizing Shot"}, {"name": "Tranquilizing Shot Arena1", "macro": "/cast [@arena1]Tranquilizing Shot"}, {"name": "Tranquilizing Shot Arena2", "macro": "/cast [@arena2]Tranquilizing Shot"}, {"name": "Tranquilizing Shot Arena3", "macro": "/cast [@arena3]Tranquilizing Shot"}, {"name": "Survival of the Fittest", "macro": "/cast Survival of the Fittest"}, {"name": "Interrupt", "macro": "/cast Counter Shot"}, {"name": "Interrupt Arena1", "macro": "/cast [@arena1]Counter Shot"}, {"name": "Interrupt Arena2", "macro": "/cast [@arena2]Counter Shot"}, {"name": "Interrupt Arena3", "macro": "/cast [@arena3]Counter Shot"}, {"name": "Misdirection", "macro": "/cast [@focus,help,exists][@pet,exists][]Misdirection"}, {"name": "Intimidation", "macro": "/cast Intimidation"}, {"name": "Intimidation Arena1", "macro": "/cast [@arena1]Intimidation"}, {"name": "Intimidation Arena2", "macro": "/cast [@arena2]Intimidation"}, {"name": "Intimidation Arena3", "macro": "/cast [@arena3]Intimidation"}, {"name": "Explosive Shot", "macro": "/cast Explosive Shot"}, {"name": "Binding Shot", "macro": "/cast Binding Shot"}, {"name": "Scatter Shot | Bursting Shot", "macro": "/cast <PERSON><PERSON><PERSON> Shot\n/cast <PERSON><PERSON><PERSON>"}, {"name": "Scatter Shot Arena1", "macro": "/cast [@arena1]<PERSON><PERSON>er Shot"}, {"name": "<PERSON>atter Shot Arena2", "macro": "/cast [@arena2]<PERSON><PERSON>er Shot"}, {"name": "Scatter Shot Arena3", "macro": "/cast [@arena3]<PERSON><PERSON>er Shot"}, {"name": "Camouflage", "macro": "/cast <PERSON><PERSON><PERSON>lage"}, {"name": "Roar of Sacrifice", "macro": "/cast [@mouseover,help][@focus,help][]R<PERSON> of Sacrifice"}, {"name": "Roar of Sacrifice Member1", "macro": "/cast [@party1,exists]Roar of Sacrifice"}, {"name": "Roar of Sacrifice Member2", "macro": "/cast [@party2,exists]Roar of Sacrifice"}, {"name": "Roar of Sacrifice Member5", "macro": "/cast [@player,exists]Roar of Sacrifice"}, {"name": "Implosive Trap | Hi-Explosive Trap", "macro": "/cast [@player]Implosive Trap\n/cast [@player]Hi-Explosive Trap"}, {"name": "Aimed Shot | Wailing Arrow", "macro": "/cast Aimed Shot\n/cast Wailing <PERSON>"}, {"name": "Barrage", "macro": "/cast Barrage"}, {"name": "Black Arrow", "macro": "/cast <PERSON>"}, {"name": "Multi-Shot", "macro": "/cast Multi-Shot"}, {"name": "Rapid Fire", "macro": "/cast Rapid Fire"}, {"name": "Trueshot", "macro": "/cast Trueshot"}, {"name": "Chimaeral Sting", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Chimaeral Sting Arena1", "macro": "/cast [@arena1]Chimaeral Sting"}, {"name": "Chimaeral Sting Arena2", "macro": "/cast [@arena2]Chimaeral Sting"}, {"name": "Chimaeral Sting Arena3", "macro": "/cast [@arena3]Chimaeral Sting"}, {"name": "<PERSON><PERSON><PERSON> Shot", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Wild Kingdom", "macro": "/cast Wild Kingdom"}, {"name": "AutoTrap", "macro": "/cast [@cursor]Freezing Trap"}, {"name": "Human Racial", "macro": "/cast Will to Survive"}, {"name": "Stoneform", "macro": "/cast Stoneform"}, {"name": "Shadowmeld", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Escape Artist", "macro": "/cast Escape Artist"}, {"name": "Gift of the Naaru", "macro": "/cast Gift of the Naaru"}, {"name": "Darkflight", "macro": "/cast Darkflight"}, {"name": "Blood Fury", "macro": "/cast Blood Fury"}, {"name": "Will of the Forsaken", "macro": "/cast Will of the Forsaken"}, {"name": "War Stomp", "macro": "/cast War Stomp"}, {"name": "Berserking", "macro": "/cast Berserking"}, {"name": "<PERSON><PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "Rocket Jump", "macro": "/cast Rocket Jump"}, {"name": "Rocket Barrage", "macro": "/cast Rocket Barrage"}, {"name": "Quaking Palm", "macro": "/cast Quaking Palm"}, {"name": "Spatial Rift", "macro": "/cast Spatial Rift"}, {"name": "Light's Judgment", "macro": "/cast <PERSON>'s Judgment"}, {"name": "Fireblood", "macro": "/cast Fireblood"}, {"name": "Arcane P<PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "<PERSON>", "macro": "/cast <PERSON>"}, {"name": "Ancestral Call", "macro": "/cast Ancestral Call"}, {"name": "Haymaker", "macro": "/cast Haymaker"}, {"name": "Regeneratin", "macro": "/cast [@player]Master's Call"}, {"name": "Bag of Tricks", "macro": "/cast Bag of Tricks"}, {"name": "Hyper Organic Light Originator", "macro": "/cast Hyper Organic Light Originator"}, {"name": "Azerite Surge", "macro": "/cast Azerite Surge"}]