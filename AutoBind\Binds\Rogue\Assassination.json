[{"name": "Ambush", "macro": "/cast Ambush"}, {"name": "Cheap Shot", "macro": "/cast Cheap Shot"}, {"name": "Cheap Shot Arena1", "macro": "/cast [@arena1]Cheap Shot"}, {"name": "Cheap Shot Arena2", "macro": "/cast [@arena2]Cheap Shot"}, {"name": "Cheap Shot Arena3", "macro": "/cast [@arena3]Cheap Shot"}, {"name": "<PERSON> Vial", "macro": "/cast <PERSON>"}, {"name": "Crippling <PERSON>ison", "macro": "/cast Crippling <PERSON><PERSON>"}, {"name": "Distract", "macro": "/cast Distract"}, {"name": "Eviscerate", "macro": "/cast Eviscerate"}, {"name": "Feint", "macro": "/cast Feint"}, {"name": "Instant Poison", "macro": "/cast In<PERSON>"}, {"name": "Interrupt", "macro": "/cast Kick"}, {"name": "Interrupt Arena1", "macro": "/cast [@arena1]Kick"}, {"name": "Interrupt Arena2", "macro": "/cast [@arena2]Kick"}, {"name": "Interrupt Arena3", "macro": "/cast [@arena3]Kick"}, {"name": "<PERSON><PERSON> Shot", "macro": "/cast <PERSON><PERSON>"}, {"name": "Kidney Shot Arena1", "macro": "/cast [@arena1]<PERSON><PERSON> Shot"}, {"name": "Kidney Shot Arena2", "macro": "/cast [@arena2]<PERSON><PERSON> Shot"}, {"name": "Kidney Shot Arena3", "macro": "/cast [@arena3]<PERSON><PERSON> Shot"}, {"name": "<PERSON>p", "macro": "/cast Sap"}, {"name": "Sap Arena1", "macro": "/cast [@arena1]Sap"}, {"name": "Sap Arena2", "macro": "/cast [@arena2]Sap"}, {"name": "Sap Arena3", "macro": "/cast [@arena3]Sap"}, {"name": "Shroud of Concealment", "macro": "/cast Shroud of Concealment"}, {"name": "Sinister Strike", "macro": "/cast Sin<PERSON> Strike"}, {"name": "Slice and Dice", "macro": "/cast S<PERSON> and <PERSON><PERSON>"}, {"name": "Sprint", "macro": "/cast Sprint"}, {"name": "Stealth", "macro": "/cast [nostealth]Stealth"}, {"name": "Vanish", "macro": "/cancelqueuedspell\n/cast [nostealth]Vanish"}, {"name": "Wound Poison", "macro": "/cast Wound <PERSON>ison"}, {"name": "Shiv", "macro": "/cast Shiv"}, {"name": "Blind", "macro": "/cast Blind"}, {"name": "Blind Arena1", "macro": "/cast [@arena1]Blind"}, {"name": "Blind Arena2", "macro": "/cast [@arena2]Blind"}, {"name": "Blind Arena3", "macro": "/cast [@arena3]Blind"}, {"name": "Cloak of Shadows", "macro": "/cast <PERSON><PERSON><PERSON> of Shadows"}, {"name": "Evasion", "macro": "/cast Evasion"}, {"name": "Gouge", "macro": "/cast Gouge"}, {"name": "Gouge Arena1", "macro": "/cast [@arena1]Gouge"}, {"name": "Gouge Arena2", "macro": "/cast [@arena2]Gouge"}, {"name": "Gouge Arena3", "macro": "/cast [@arena3]Gouge"}, {"name": "Shadowstep", "macro": "/cast Shadowstep"}, {"name": "Shadowstep Arena1", "macro": "/cast [@arena1]Shadowstep"}, {"name": "Shadowstep Arena2", "macro": "/cast [@arena2]Shadowstep"}, {"name": "Shadowstep Arena3", "macro": "/cast [@arena3]Shadowstep"}, {"name": "Tricks of the Trade", "macro": "/cast [@focus,help,exists][]Tricks of the Trade"}, {"name": "Tricks of the Trade Member1", "macro": "/cast [@raid1,exists][@party1,exists]Tricks of the Trade"}, {"name": "Tricks of the Trade Member2", "macro": "/cast [@raid2,exists][@party2,exists]Tricks of the Trade"}, {"name": "Tricks of the Trade Member3", "macro": "/cast [@raid3,exists][@party3,exists]Tricks of the Trade"}, {"name": "Tricks of the Trade Member4", "macro": "/cast [@raid4,exists][@party4,exists]Tricks of the Trade"}, {"name": "Tricks of the Trade Member5", "macro": "/cast [@raid5,exists][@player,exists]Tricks of the Trade"}, {"name": "Atrophic Poison | Numbing Poison", "macro": "/cast Atrophic Poison\n/cast Numbing Poison"}, {"name": "Echoing Reprimand", "macro": "/cast Echoing Reprimand"}, {"name": "Thistle Tea", "macro": "/cast Thistle Tea"}, {"name": "Cold Blood", "macro": "/cast Cold Blood"}, {"name": "Envenom", "macro": "/cast Envenom"}, {"name": "Fan of Knives", "macro": "/cast Fan of Knives"}, {"name": "Garrote", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "macro": "/cast Mutilate"}, {"name": "Poisoned Knife", "macro": "/cast Poisoned Knife"}, {"name": "Rupture", "macro": "/cast Rupture"}, {"name": "Amplifying Poison", "macro": "/cast Amplifying <PERSON><PERSON>"}, {"name": "Crimson Tempest", "macro": "/cast <PERSON>"}, {"name": "<PERSON>ly <PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "Deathmark", "macro": "/cast Deathmark"}, {"name": "Kingsbane", "macro": "/cast Kingsbane"}, {"name": "Death from Above", "macro": "/cast Death from Above"}, {"name": "Dismantle", "macro": "/cast Dismantle"}, {"name": "Dismantle Arena1", "macro": "/cast [@arena1]Dismantle"}, {"name": "Dismantle Arena2", "macro": "/cast [@arena2]Dismantle"}, {"name": "Dismantle Arena3", "macro": "/cast [@arena3]Dismantle"}, {"name": "Smoke Bomb", "macro": "/cast Smoke Bomb"}, {"name": "Human Racial", "macro": "/cast Will to Survive"}, {"name": "Stoneform", "macro": "/cast Stoneform"}, {"name": "Shadowmeld", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Escape Artist", "macro": "/cast Escape Artist"}, {"name": "Gift of the Naaru", "macro": "/cast Gift of the Naaru"}, {"name": "Darkflight", "macro": "/cast Darkflight"}, {"name": "Blood Fury", "macro": "/cast Blood Fury"}, {"name": "Will of the Forsaken", "macro": "/cast Will of the Forsaken"}, {"name": "War Stomp", "macro": "/cast War Stomp"}, {"name": "Berserking", "macro": "/cast Berserking"}, {"name": "<PERSON><PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "Rocket Jump", "macro": "/cast Rocket Jump"}, {"name": "Rocket Barrage", "macro": "/cast Rocket Barrage"}, {"name": "Quaking Palm", "macro": "/cast Quaking Palm"}, {"name": "Spatial Rift", "macro": "/cast Spatial Rift"}, {"name": "Light's Judgment", "macro": "/cast <PERSON>'s Judgment"}, {"name": "Fireblood", "macro": "/cast Fireblood"}, {"name": "Arcane P<PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "<PERSON>", "macro": "/cast <PERSON>"}, {"name": "Ancestral Call", "macro": "/cast Ancestral Call"}, {"name": "Haymaker", "macro": "/cast Haymaker"}, {"name": "Regeneratin", "macro": "/cast <PERSON><PERSON><PERSON><PERSON>"}, {"name": "Bag of Tricks", "macro": "/cast Bag of Tricks"}, {"name": "Hyper Organic Light Originator", "macro": "/cast Hyper Organic Light Originator"}, {"name": "Azerite Surge", "macro": "/cast Azerite Surge"}]