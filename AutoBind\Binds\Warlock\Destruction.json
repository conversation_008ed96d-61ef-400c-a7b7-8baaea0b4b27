[{"name": "Corruption", "macro": "/cast Corruption"}, {"name": "Create Healthstone", "macro": "/cast Create Healthstone"}, {"name": "Create <PERSON><PERSON>", "macro": "/cast Create <PERSON>"}, {"name": "Curse of Weakness", "macro": "/cast Curse of Weakness"}, {"name": "Curse of Weakness Arena1", "macro": "/cast [@arena1]Curse of Weakness"}, {"name": "Curse of Weakness Arena2", "macro": "/cast [@arena2]Curse of Weakness"}, {"name": "Curse of Weakness Arena3", "macro": "/cast [@arena3]Curse of Weakness"}, {"name": "Drain Life", "macro": "/cast Drain <PERSON>"}, {"name": "Fear", "macro": "/cast Fear"}, {"name": "Fear Arena1", "macro": "/cast [@arena1]Fear"}, {"name": "Fear Arena2", "macro": "/cast [@arena2]Fear"}, {"name": "Fear Arena3", "macro": "/cast [@arena3]Fear"}, {"name": "Health Funnel", "macro": "/cast Health Funnel"}, {"name": "Shadow Bolt | Drain Soul", "macro": "/cast <PERSON>\n/cast <PERSON>ain <PERSON>"}, {"name": "Soulstone", "macro": "/cast [@mouseover,help,exists][]<PERSON><PERSON>"}, {"name": "Subjugate Demon", "macro": "/cast Subjugate Demon"}, {"name": "Pet", "macro": "/cast [mod:shift,mod:ctrl]<PERSON><PERSON><PERSON>;[mod:shift]<PERSON><PERSON><PERSON>;[mod:ctrl]<PERSON><PERSON><PERSON>;[mod:alt]<PERSON><PERSON><PERSON> Felhunter;[nomod]Summon Imp"}, {"name": "PetCommand1", "macro": "/cast Command Demon"}, {"name": "PetCommand Unit1", "macro": "/cast [@arena1]Command Demon"}, {"name": "PetCommand Unit2", "macro": "/cast [@arena2]Command Demon"}, {"name": "PetCommand Unit3", "macro": "/cast [@arena3]Command Demon"}, {"name": "PetInterrupt Arena1", "macro": "/cast [@arena1]Command Demon"}, {"name": "PetInterrupt Arena2", "macro": "/cast [@arena2]Command Demon"}, {"name": "PetInterrupt Arena3", "macro": "/cast [@arena3]Command Demon"}, {"name": "Unending Breath", "macro": "/cast Unending Breath"}, {"name": "Unending Resolve", "macro": "/cast Unending Resolve"}, {"name": "Fel Domination", "macro": "/cast Fel Domination"}, {"name": "Burning Rush", "macro": "/cast Burning Rush"}, {"name": "Curse of Tongues", "macro": "/cast Curse of Tongues"}, {"name": "Curse of Tongues Arena1", "macro": "/cast [@arena1]Curse of Tongues"}, {"name": "Curse of Tongues Arena2", "macro": "/cast [@arena2]Curse of Tongues"}, {"name": "Curse of Tongues Arena3", "macro": "/cast [@arena3]Curse of Tongues"}, {"name": "Curse of Exhaustion", "macro": "/cast Curse of Exhaustion"}, {"name": "Curse of Exhaustion Arena1", "macro": "/cast [@arena1]Curse of Exhaustion"}, {"name": "Curse of Exhaustion Arena2", "macro": "/cast [@arena2]Curse of Exhaustion"}, {"name": "Curse of Exhaustion Arena3", "macro": "/cast [@arena3]Curse of Exhaustion"}, {"name": "Demonic Circle", "macro": "/cast Demonic Circle"}, {"name": "Demonic Circle: Teleport", "macro": "/stopcasting\n/cast Demonic Circle: Teleport"}, {"name": "Howl of Terror | Mortal Coil", "macro": "/cast Howl of Terror\n/cast Mortal Coil"}, {"name": "Howl of Terror | Mortal Coil Arena1", "macro": "/cast [@arena1]Mortal Coil"}, {"name": "Howl of Terror | Mortal Coil Arena2", "macro": "/cast [@arena2]Mortal Coil"}, {"name": "Howl of Terror | Mortal Coil Arena3", "macro": "/cast [@arena3]Mortal Coil"}, {"name": "Amplify Curse", "macro": "/cast Amplify Curse"}, {"name": "Banish", "macro": "/cast Banish"}, {"name": "Banish Arena1", "macro": "/cast [@arena1]Banish"}, {"name": "Banish Arena2", "macro": "/cast [@arena2]Banish"}, {"name": "Banish Arena3", "macro": "/cast [@arena3]Banish"}, {"name": "Dark Pact", "macro": "/cast Dark Pact"}, {"name": "Shadowfury", "macro": "/cast Shadow<PERSON>ry"}, {"name": "Shadowflame", "macro": "/cast Shadowflame"}, {"name": "<PERSON><PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "Chaos Bolt | Ruination", "macro": "/cast <PERSON>\n/cast Ruination"}, {"name": "Cataclysm", "macro": "/cast [@cursor]Cataclysm"}, {"name": "Channel Demonfire", "macro": "/cast Channel Demonfire"}, {"name": "Conflagrate", "macro": "/cast Conflagrate"}, {"name": "Dimensional Rift", "macro": "/cast Dimensional Rift"}, {"name": "Grimoire of Sacrifice", "macro": "/cast G<PERSON><PERSON> of Sacrifice"}, {"name": "Havoc", "macro": "/cast Havoc"}, {"name": "Malevolence", "macro": "/cast Malevolence"}, {"name": "Rain of Fire", "macro": "/cast [@cursor]Rain of Fire"}, {"name": "<PERSON><PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "Soul Fire", "macro": "/cast Soul Fire"}, {"name": "Summon Infernal", "macro": "/cast [@cursor]<PERSON><PERSON>on Infernal"}, {"name": "<PERSON>er", "macro": "/cast Wither"}, {"name": "Immolate", "macro": "/cast Immolate"}, {"name": "Incinerate", "macro": "/cast Incinerate"}, {"name": "Bonds of Fel", "macro": "/cast <PERSON>s of Fel"}, {"name": "Call Observer", "macro": "/cast Call Observer"}, {"name": "Nether Ward", "macro": "/cast Nether <PERSON>"}, {"name": "Shadow Rift", "macro": "/cast <PERSON> Rift"}, {"name": "Soul Rip", "macro": "/cast <PERSON> Rip"}, {"name": "Human Racial", "macro": "/cast Will to Survive"}, {"name": "Stoneform", "macro": "/cast Stoneform"}, {"name": "Shadowmeld", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Escape Artist", "macro": "/cast Escape Artist"}, {"name": "Gift of the Naaru", "macro": "/cast Gift of the Naaru"}, {"name": "Darkflight", "macro": "/cast Darkflight"}, {"name": "Blood Fury", "macro": "/cast Blood Fury"}, {"name": "Will of the Forsaken", "macro": "/cast Will of the Forsaken"}, {"name": "War Stomp", "macro": "/cast War Stomp"}, {"name": "Berserking", "macro": "/cast Berserking"}, {"name": "<PERSON><PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "Rocket Jump", "macro": "/cast Rocket Jump"}, {"name": "Rocket Barrage", "macro": "/cast Rocket Barrage"}, {"name": "Quaking Palm", "macro": "/cast Quaking Palm"}, {"name": "Spatial Rift", "macro": "/cast Spatial Rift"}, {"name": "Light's Judgment", "macro": "/cast <PERSON>'s Judgment"}, {"name": "Fireblood", "macro": "/cast Fireblood"}, {"name": "Arcane P<PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "<PERSON>", "macro": "/cast <PERSON>"}, {"name": "Ancestral Call", "macro": "/cast Ancestral Call"}, {"name": "Haymaker", "macro": "/cast Haymaker"}, {"name": "Regeneratin", "macro": "/cast [@player]Master's Call"}, {"name": "Bag of Tricks", "macro": "/cast Bag of Tricks"}, {"name": "Hyper Organic Light Originator", "macro": "/cast Hyper Organic Light Originator"}, {"name": "Azerite Surge", "macro": "/cast Azerite Surge"}]