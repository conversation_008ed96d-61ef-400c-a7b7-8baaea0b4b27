[{"name": "Battle Shout", "macro": "/cast Battle Shout"}, {"name": "Berserker Shout | Berserker Rage", "macro": "/cast Be<PERSON>rk<PERSON>\n/cast Berserker <PERSON>"}, {"name": "Charge", "macro": "/cast Charge"}, {"name": "Charge Arena1", "macro": "/cast [@arena1]Charge"}, {"name": "Charge Arena2", "macro": "/cast [@arena2]Charge"}, {"name": "Charge Arena3", "macro": "/cast [@arena3]Charge"}, {"name": "Execute", "macro": "/cast Execute"}, {"name": "<PERSON><PERSON><PERSON>", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Heroic Throw", "macro": "/cast <PERSON><PERSON>ow"}, {"name": "Interrupt", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Interrupt Arena1", "macro": "/cast [@arena1]<PERSON><PERSON><PERSON>"}, {"name": "Interrupt Arena2", "macro": "/cast [@arena2]<PERSON><PERSON><PERSON>"}, {"name": "Interrupt Arena3", "macro": "/cast [@arena3]<PERSON><PERSON><PERSON>"}, {"name": "Shield Slam", "macro": "/cast Shield Slam"}, {"name": "Slam", "macro": "/cast Slam"}, {"name": "<PERSON><PERSON>", "macro": "/cast Taunt"}, {"name": "Taunt <PERSON>s", "macro": "/cast [@arenapet1,harm]Oppressor\n/cast [@arenapet2,harm]Oppressor\n/cast [@arenapet3,harm]Oppressor\n/cast [@arenapet1,harm]Taunt\n/cast [@arenapet2,harm]Taunt\n/cast [@arenapet3,harm]Taunt"}, {"name": "Taunt Unit1", "macro": "/cast [@arena1] Intimidating <PERSON><PERSON>"}, {"name": "Taunt Unit2", "macro": "/cast [@arena2] Intimidating <PERSON><PERSON>"}, {"name": "Taunt Unit3", "macro": "/cast [@arena3] Intimidating <PERSON><PERSON>"}, {"name": "Impending Victory | Victory Rush", "macro": "/cast Impending Victory\n/cast Victory Rush"}, {"name": "Whirlwind", "macro": "/cast Whirlwind"}, {"name": "Battle Stance", "macro": "/cast <PERSON>"}, {"name": "Defensive Stance", "macro": "/cast Defensive <PERSON>ce"}, {"name": "Intervene", "macro": "/cast Intervene"}, {"name": "Intervene Member1", "macro": "/cast [@raid1,exists][@party1,exists]Intervene"}, {"name": "Intervene Member2", "macro": "/cast [@raid2,exists][@party2,exists]Intervene"}, {"name": "Intervene Member3", "macro": "/cast [@raid3,exists][@party3,exists]Intervene"}, {"name": "Intervene Member4", "macro": "/cast [@raid4,exists][@party4,exists]Intervene"}, {"name": "<PERSON><PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "Storm Bolt", "macro": "/cast <PERSON>"}, {"name": "Storm Bolt Arena1", "macro": "/cast [@arena1]<PERSON> Bolt"}, {"name": "Storm Bolt Arena2", "macro": "/cast [@arena2]<PERSON> Bolt"}, {"name": "Storm Bolt Arena3", "macro": "/cast [@arena3]<PERSON> Bolt"}, {"name": "Intimidating Shout", "macro": "/cast Intimidating S<PERSON>"}, {"name": "Intimidating Shout Arena1", "macro": "/cast [@arena1]Intimidating <PERSON><PERSON>"}, {"name": "Intimidating Shout Arena2", "macro": "/cast [@arena2]Intimidating <PERSON><PERSON>"}, {"name": "Intimidating Shout Arena3", "macro": "/cast [@arena3]Intimidating <PERSON><PERSON>"}, {"name": "Thunder Clap", "macro": "/cast <PERSON>"}, {"name": "Spell Reflection", "macro": "/cast Spell Reflection"}, {"name": "Rallying Cry", "macro": "/cast Rallying Cry"}, {"name": "Shockwave", "macro": "/cast <PERSON><PERSON>"}, {"name": "Bitter Immunity", "macro": "/cast Bitter Immunity"}, {"name": "Wrecking Throw | Shattering Throw", "macro": "/cast Wrecking Throw\n/cast Shattering Throw"}, {"name": "Piercing Howl", "macro": "/cast Piercing Howl"}, {"name": "Thunderous Roar", "macro": "/cast Thunderous <PERSON>oar"}, {"name": "Avatar", "macro": "/cast <PERSON><PERSON>"}, {"name": "Champion's Spear", "macro": "/cast [@player]Champion's Spear"}, {"name": "Sweeping Strikes", "macro": "/cast Sweeping Strikes"}, {"name": "Cleave", "macro": "/cast Cleave"}, {"name": "Demolish", "macro": "/cast Demolish"}, {"name": "Die by the Sword", "macro": "/cast Die by the Sword"}, {"name": "Ignore Pain", "macro": "/cast Ignore Pain"}, {"name": "Mortal Strike", "macro": "/cast Mortal Strike"}, {"name": "Overpower", "macro": "/cast Overpower"}, {"name": "Bladestorm | Ravager", "macro": "/cast Bladestorm\n/cast [@player]<PERSON><PERSON><PERSON>"}, {"name": "Rend", "macro": "/cast Rend"}, {"name": "Rend Arena1", "macro": "/cast [@arena1]Rend"}, {"name": "Rend Arena2", "macro": "/cast [@arena2]Rend"}, {"name": "Rend Arena3", "macro": "/cast [@arena3]Rend"}, {"name": "Skullsplitter", "macro": "/cast <PERSON><PERSON><PERSON><PERSON>"}, {"name": "Warbreaker | Colossus Smash", "macro": "/cast Warbreaker\n/cast <PERSON><PERSON><PERSON>"}, {"name": "Disarm", "macro": "/cast Disarm"}, {"name": "Disarm Arena1", "macro": "/cast [@arena1]Disarm"}, {"name": "Disarm Arena2", "macro": "/cast [@arena2]Disarm"}, {"name": "Disarm Arena3", "macro": "/cast [@arena3]Disarm"}, {"name": "Duel", "macro": "/cast Duel"}, {"name": "Duel Arena1", "macro": "/cast [@arena1]Duel"}, {"name": "Duel Arena2", "macro": "/cast [@arena2]Duel"}, {"name": "Duel Arena3", "macro": "/cast [@arena3]Duel"}, {"name": "Sharpen <PERSON>", "macro": "/cast Sharpen <PERSON>"}, {"name": "War Banner", "macro": "/cast War Banner"}, {"name": "Human Racial", "macro": "/cast Will to Survive"}, {"name": "Stoneform", "macro": "/cast Stoneform"}, {"name": "Shadowmeld", "macro": "/cast <PERSON><PERSON><PERSON>"}, {"name": "Escape Artist", "macro": "/cast Escape Artist"}, {"name": "Gift of the Naaru", "macro": "/cast [@target,help][@focus,help][]Gift of the Naaru"}, {"name": "Darkflight", "macro": "/cast Darkflight"}, {"name": "Blood Fury", "macro": "/cast Blood Fury"}, {"name": "Will of the Forsaken", "macro": "/cast Will of the Forsaken"}, {"name": "War Stomp", "macro": "/cast War Stomp"}, {"name": "Berserking", "macro": "/cast Berserking"}, {"name": "<PERSON><PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "Rocket Jump", "macro": "/cast Rocket Jump"}, {"name": "Rocket Barrage", "macro": "/cast Rocket Barrage"}, {"name": "Quaking Palm", "macro": "/cast Quaking Palm"}, {"name": "Spatial Rift", "macro": "/cast Spatial Rift"}, {"name": "Light's Judgment", "macro": "/cast <PERSON>'s Judgment"}, {"name": "Fireblood", "macro": "/cast Fireblood"}, {"name": "Arcane P<PERSON>", "macro": "/cast <PERSON><PERSON>"}, {"name": "<PERSON>", "macro": "/cast <PERSON>"}, {"name": "Ancestral Call", "macro": "/cast Ancestral Call"}, {"name": "Haymaker", "macro": "/cast Haymaker"}, {"name": "Regeneratin", "macro": "/cast <PERSON><PERSON><PERSON><PERSON>"}, {"name": "Bag of Tricks", "macro": "/cast Bag of Tricks"}, {"name": "Hyper Organic Light Originator", "macro": "/cast Hyper Organic Light Originator"}, {"name": "Azerite Surge", "macro": "/cast Azerite Surge"}]