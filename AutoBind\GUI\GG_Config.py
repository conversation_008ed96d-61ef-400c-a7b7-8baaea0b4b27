import shutil
import os
import json
import sys

from LIBS.GGL import load_ggl_config, get_all_ggl_sections, section_stats, write_section_to_file
from LIBS.Keys import get_all_combinations
from LIBS.BPBinding import parse_key_events
from LIBS.Spec import build_spec_table
from LIBS.BPBinding import create_bindings
from LIBS.CustomLuaPrinter import MakuluLuaPrinter
from luaparser import ast

GGL_FILE_LOC = None
GGL_PARSED_CONFIG = None
GGL_SELECTED_SPEC = None

ORIGINAL_COPY = None

BP_FILE_LOC = None
BP_PARSED_CONFIG = None
BP_SELECTED_USER = None
BP_SELECTED_SLOT = None

BP_SELECTED_TAB = None
BP_SELECTED_KB = None

PROFILE_OVERRIDES = None

def ggl_file_load(file_path, ggl_spec_dropdown):
    global GGL_FILE_LOC, GGL_PARSED_CONFIG

    GGL_FILE_LOC = file_path
    GGL_PARSED_CONFIG = load_ggl_config(GGL_FILE_LOC)
    load_ggl_sections(ggl_spec_dropdown)


def load_ggl_sections(ggl_spec_dropdown):
    global GGL_PARSED_CONFIG

    spec_options = get_all_ggl_sections(GGL_PARSED_CONFIG)

    ggl_spec_dropdown['values'] = spec_options
    ggl_spec_dropdown['state'] = 'readonly'
    ggl_spec_dropdown.set("Choose a spec")


def ggl_section_picked(selected, status_label):
    global GGL_SELECTED_SPEC, GGL_PARSED_CONFIG

    if GGL_PARSED_CONFIG is None:
        return

    GGL_SELECTED_SPEC = selected

    bound_num, unbound_num, unbindable = section_stats(GGL_PARSED_CONFIG[selected])

    status_label.config(text=f"Currently {bound_num} bound out of {bound_num + unbound_num} binds. {unbindable} cant be bound at the moment.")


def backup_original_state(p = print):
    global GGL_FILE_LOC, GGL_PARSED_CONFIG, GGL_SELECTED_SPEC, ORIGINAL_COPY

    if GGL_FILE_LOC is None or GGL_PARSED_CONFIG is None or GGL_SELECTED_SPEC is None:
        return

    if ORIGINAL_COPY is not None:
        return

    backup_file = GGL_FILE_LOC + ".backup"

    while os.path.exists(backup_file):
        backup_file += ".backup"

    ORIGINAL_COPY = backup_file
    shutil.copyfile(GGL_FILE_LOC, backup_file)

    p(f"Creating backup file at {backup_file}")


def reserve_key_slot(reserve_table, bind_name, key, p = print):
    key_type = key.split("_")[0]
    if key_type in reserve_table:
        p(f"Key {key} already reserved for {reserve_table[key_type]}!")
        return False

    reserve_table[key_type] = bind_name
    return True


def map_ggl_cell(reserve_table, bind_name, cell, key_bind, p = print):

    macro = cell.get('macro')

    if macro is None:
        return reserve_key_slot(reserve_table, bind_name, key_bind, p)

    modifiers = []
    
    if '[mod:ctrl]' in macro:
        modifiers.append('^')

    if '[mod:alt]' in macro:
        modifiers.append('+')

    if '[mod:shift]' in macro:
        modifiers.append('!')

    keybind_modifiers = parse_key_events(key_bind)['modifiers']
    for modifier in modifiers:
        if modifier in keybind_modifiers:
            p(f"Key {bind_name} has modifier {modifier} so we can't use {key_bind} for it")
            return False

    if not reserve_key_slot(reserve_table, bind_name, key_bind, p):
        p(f"Error with bind: {bind_name}")
        return False
    
    for modifier in modifiers:
        if not reserve_key_slot(reserve_table, bind_name, f"{modifier}{key_bind}", p):
            p(f"Error with bind: {bind_name}")
            return False
        
    return True


def ggl_ready():
    global GGL_PARSED_CONFIG, GGL_SELECTED_SPEC

    return GGL_PARSED_CONFIG is not None and GGL_SELECTED_SPEC is not None


def no_log(s):
    return


def create_key_mapping(p = print):
    global GGL_FILE_LOC, GGL_PARSED_CONFIG, GGL_SELECTED_SPEC, ORIGINAL_COPY

    reserve_list = {}

    if GGL_FILE_LOC is None or GGL_PARSED_CONFIG is None or GGL_SELECTED_SPEC is None:
        return
    
    section = GGL_PARSED_CONFIG[GGL_SELECTED_SPEC]

    for bind_name, cell in section.items():
        key_bind = cell.get('bind')

        if key_bind is None:
            continue

        if not map_ggl_cell(reserve_list, bind_name, cell, key_bind, p):
            p(f"Error with bind {bind_name}")
            return

    print(f"All binds before reserved length is {len(reserve_list)}")
    global_binds = GGL_PARSED_CONFIG["General"]
    for bind_name, cell in global_binds.items():
        key_bind = cell.get('bind')

        if key_bind is None:
            continue

        if not map_ggl_cell(reserve_list, bind_name, cell, key_bind, p):
            p(f"Error with bind {bind_name}")
            return

    print(f"All binds reserved length is {len(reserve_list)}")

    for key, value in reserve_list.items():
        print(f"{key} - {value}")

    all_bindings = get_all_combinations()

    for bind_name, cell in section.items():
        if "START" in bind_name:
            continue

        key_bind = cell.get('bind')

        if key_bind is not None:
            continue

        if len(all_bindings) == 0:
            p("Ran out of binds")
            break

        for bindix in range(len(all_bindings)):
            if map_ggl_cell(reserve_list, bind_name, cell, all_bindings[bindix]['vk_name'], no_log):

                cell['bind'] = all_bindings[bindix]['vk_name']

                p(f"Binding {bind_name} to {all_bindings[bindix]['bp_name']}")
                all_bindings.pop(bindix)
                break

    print("All binds used")
    write_section_to_file(GGL_SELECTED_SPEC, section, GGL_FILE_LOC)


def write_file_update(p = print):
    global GGL_FILE_LOC, GGL_PARSED_CONFIG, GGL_SELECTED_SPEC, ORIGINAL_COPY

    if GGL_FILE_LOC is None or GGL_PARSED_CONFIG is None or GGL_SELECTED_SPEC is None:
        return

    backup_original_state(p)

    p("")
    p(f"Creating binds for {GGL_SELECTED_SPEC}")
    p(f"Writing changes to {GGL_FILE_LOC}")

    create_key_mapping(p)


def load_bp_config(file_path):
    with open(file_path, 'r') as file:
        bp_config = ast.parse(file.read())    

    return bp_config


def load_bp_users(bp_user_dropdown):
    global BP_PARSED_CONFIG

    found_users = []

    for node in ast.walk(BP_PARSED_CONFIG):
        if isinstance(node, ast.Field) and isinstance(node.key, ast.String) and 'PROFILE_' in node.key.s:
            found_users.append(node.key.s)

    bp_user_dropdown['values'] = found_users
    bp_user_dropdown['state'] = 'readonly'
    bp_user_dropdown.set("Choose a profile")

def open_bp_file(file_path, bp_user_dropdown):
    global BP_FILE_LOC, BP_PARSED_CONFIG

    BP_FILE_LOC = file_path
    BP_PARSED_CONFIG = load_bp_config(BP_FILE_LOC)
    load_bp_users(bp_user_dropdown)

def select_bp_section(selected):
    global BP_SELECTED_USER
    BP_SELECTED_USER = selected
    on_bp_selection()

def select_bp_slot(selected):
    global BP_SELECTED_SLOT
    BP_SELECTED_SLOT = selected
    on_bp_selection()

def on_bp_selection():
    global BP_SELECTED_USER, BP_SELECTED_SLOT, BP_PARSED_CONFIG, BP_SELECTED_TAB, BP_SELECTED_KB

    if BP_SELECTED_USER is None or BP_SELECTED_SLOT is None or BP_PARSED_CONFIG is None:
        return
    
    for node in ast.walk(BP_PARSED_CONFIG):
        if isinstance(node, ast.Field) and isinstance(node.key, ast.String) and node.key.s == BP_SELECTED_USER:
            my_table = node.value
            break

    for field in my_table.fields:
        if isinstance(field, ast.Field) and isinstance(field.key, ast.Number) and field.key.n == int(BP_SELECTED_SLOT):
            cell_one = field
            break

    character_tab = None
    keybinds = None

    for field in cell_one.value.fields:
        if isinstance(field, ast.Field) and isinstance(field.key, ast.String):
            if field.key.s == "CharacterSpecificTab1":
                character_tab = field

            if field.key.s == "AllKeyBindings":
                keybinds = field

    print("Character tab:", character_tab)
    print("Keybinds:", keybinds)

    BP_SELECTED_TAB = character_tab
    BP_SELECTED_KB = keybinds

    print("Selected tab count: ", len(character_tab.value.fields) - 1)
    print("Selected kb count: ", len(keybinds.value.fields))

def bp_ready():
    global BP_SELECTED_TAB, BP_SELECTED_KB

    return BP_SELECTED_TAB is not None and BP_SELECTED_KB is not None

def pf_override(path, p = print):
    global PROFILE_OVERRIDES

    try:
        with open(path, 'r') as f:
            PROFILE_OVERRIDES = json.loads(f.read())
            p(f"Loaded profile overrides")
    except:
        p(f"Error opening {path}")


def lets_do_this(p = print):
    global GGL_PARSED_CONFIG, GGL_SELECTED_SPEC, BP_PARSED_CONFIG, BP_SELECTED_TAB, BP_SELECTED_KB
    global PROFILE_OVERRIDES

    if not ggl_ready():
        p("GGL not ready")
        return
    
    if not bp_ready():
        p("BP not ready")
        return
    
    overriding = {}

    if PROFILE_OVERRIDES is not None and GGL_SELECTED_SPEC in PROFILE_OVERRIDES:
        overriding = PROFILE_OVERRIDES[GGL_SELECTED_SPEC]
    
    p("Lets do this")
    p(f"BP: {BP_SELECTED_USER} - {BP_SELECTED_SLOT}")
    p(f"GGL: {GGL_SELECTED_SPEC}")

    section = GGL_PARSED_CONFIG[GGL_SELECTED_SPEC]

    BP_SELECTED_TAB.value = build_spec_table(1, section, overriding)
    create_bindings(BP_SELECTED_KB, section, p)

    with open('./Bindpad.lua', 'w') as file:
        message = MakuluLuaPrinter(indent_size=1).visit(BP_PARSED_CONFIG)
        file.write(message)
