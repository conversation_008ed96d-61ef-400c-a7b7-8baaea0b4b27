import sys
import shelve
import tkinter as tk
import os
from tkinter import filedialog, ttk, scrolledtext
import GUI.GG_Config as GG_Config
from GUI.GG_Config import load_ggl_sections, ggl_file_load, ggl_section_picked, write_file_update

from LIBS.GGL import load_ggl_config, get_all_ggl_sections, section_stats


def resource_path(relative_path):
    """ Get absolute path to resource, works for dev and for PyInstaller """
    base_path = getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__)))
    return os.path.join(base_path, relative_path)

def add_log_message(message):
    log_area.configure(state='normal')  # Enable editing of the Text widget
    log_area.insert(tk.END, message + "\n")  # Add the log message at the end of the Text widget
    log_area.configure(state='disabled')  # Disable editing of the Text widget
    log_area.see(tk.END)  # Scroll to the bottom


def toggle_log_area():
    if log_area.winfo_viewable():
        log_area.pack_forget()  # Hide the log area
        toggle_button.config(text="Show Logs")
    else:
        log_area.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)  # Show the log area
        toggle_button.config(text="Hide Logs")


def update_text_content(entry, text):
    entry.delete(0, tk.END)
    entry.insert(0, text)


def update_labael_content(label, text):
    label.config(text=text)


def create_ggl_binds():
    write_file_update(p = add_log_message)


def lets_do_this():
    GG_Config.lets_do_this(p = add_log_message)

def select_ggl_file(entry):
    with shelve.open("last_dirs.db") as last_dirs:
        initial_dir = last_dirs.get("ggl_loc", "")
        file_path = filedialog.askopenfilename(initialdir=initial_dir)
        if file_path:
            last_dirs["ggl_loc"] = file_path
            ggl_file_load(file_path, ggl_spec_dropdown)

    update_text_content(entry, file_path)


def select_bindpad_file(entry):
    with shelve.open("last_dirs.db") as last_dirs:
        initial_dir = last_dirs.get("bp_loc", "")
        file_path = filedialog.askopenfilename(initialdir=initial_dir)
        if file_path:
            last_dirs["bp_loc"] = file_path
            GG_Config.open_bp_file(file_path, character_dropdown)

    update_text_content(entry, file_path)


def select_profile_overrides(entry):
    with shelve.open("last_dirs.db") as last_dirs:
        initial_dir = last_dirs.get("pf_or", "")
        file_path = filedialog.askopenfilename(initialdir='.')
        if file_path:
            last_dirs["pf_or"] = file_path
            GG_Config.pf_override(file_path, add_log_message)

    update_text_content(entry, file_path)

def update_progress(value):
    if value <= 100:
        progress['value'] = value

        # Example usage
        add_log_message(f"{value}% complete")

        if value != 100:
            root.after(50, update_progress, min(value + 5, 100))  # Increment value and schedule next update

def on_spec_selected(event):
    global GGL_PARSED_CONFIG

    selected_option = ggl_spec_dropdown.get()
    print("Selected:", selected_option)
    if selected_option is None or selected_option == "Choose a spec" or selected_option == "":
        return

    ggl_section_picked(selected_option, ggl_static_text)


def on_char_selected(event):
    selected_option = character_dropdown.get()
    print("Selected:", selected_option)
    GG_Config.select_bp_section(selected_option)


def on_slot_selected(event):
    selected_option = bp_spec_slot_dropdown.get()
    print("Selected:", selected_option)
    GG_Config.select_bp_slot(selected_option)


def post_load():
    print("post_load")
    with shelve.open("last_dirs.db") as last_dirs:
        initial_dir = last_dirs.get("ggl_loc", "")
        if initial_dir:
            try:
                ggl_file_load(initial_dir, ggl_spec_dropdown)
            except:
                pass
            update_text_content(ggl_config_entry, initial_dir)

        initial_dir = last_dirs.get("bp_loc", "")
        if initial_dir:
            try:
                GG_Config.open_bp_file(initial_dir, character_dropdown)
            except:
                pass
            update_text_content(bp_file_entry, initial_dir)


def start_action():
    lets_do_this()


class Tooltip:
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.enter)
        self.widget.bind("<Leave>", self.leave)

    def enter(self, event=None):
        x, y, cx, cy = self.widget.bbox("insert")
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() - 20
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_geometry("+%d+%d" % (x, y))
        self.tooltip.attributes("-alpha", 0.85)
        label = tk.Label(self.tooltip, text=self.text, background="yellow", relief='solid', borderwidth=1)
        label.pack()

    def leave(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None


# Create the main window
root = tk.Tk()
root.title("Makulu")
root.iconbitmap(resource_path('./MakuluBuilder.ico'))

# Add padding to the main window
root.config(padx=10, pady=10)

autobind_title = tk.Label(root, text="Makulu Auto Binding", font=("Arial", 16, "bold"))
autobind_title.pack(pady=(0, 20))

# Subheading for file selection
load_ggl_subheading = tk.Label(root, text="Load GGL Settings", font=("Arial", 12, "bold"))
load_ggl_subheading.pack(pady=(0, 5),)

# Subheading for file selection
select_ggl_header = tk.Label(root, text="Select GGL Config.ini", font=("Arial", 10))
select_ggl_header.pack(pady=(10, 5), anchor='w')

# Frame for file picker section 1
file_picker_frame = tk.Frame(root)
file_picker_frame.pack(pady=5)

ggl_config_entry = tk.Entry(file_picker_frame, width=40, state="disabled")
ggl_config_entry.pack(side=tk.LEFT, padx=(0, 10), anchor='w')

ggl_config_button = tk.Button(file_picker_frame, text="Select Config.ini", command=lambda: select_ggl_file(ggl_config_entry))
ggl_config_button.pack(side=tk.LEFT, anchor='w')


# Subheading for dropdown menu
spec_dropdown = tk.Label(root, text="Select a spec", font=("Arial", 12, "bold"), width=40)
spec_dropdown.pack(pady=(10, 5))

# Dropdown menu options
spec_options = []

# Dropdown menu
ggl_spec_dropdown = ttk.Combobox(root, values=spec_options, state="disabled")
ggl_spec_dropdown.pack(pady=5)
ggl_spec_dropdown.set("Choose an spec")
ggl_spec_dropdown.bind("<<ComboboxSelected>>", on_spec_selected)


ggl_static_text = tk.Label(root, text="", font=("Arial", 10))
ggl_static_text.pack(pady=(10, 5), anchor='w')

# Toggle button for the log area
ggl_bindings_create_b = tk.Button(root, text="Create missing binds", command=create_ggl_binds)
ggl_bindings_create_b.pack(pady=(5, 0))


ggl_bp_separator = ttk.Separator(root, orient='horizontal')
ggl_bp_separator.pack(fill='x', pady=5, padx=2)


# Subheading for file selection
bp_subtitle = tk.Label(root, text="Select Bindpad.lua", font=("Arial", 10))
bp_subtitle.pack(pady=(0, 5), anchor='w')

# Frame for file picker section 2
bp_select_frame = tk.Frame(root)
bp_select_frame.pack(pady=5)

bp_file_entry = tk.Entry(bp_select_frame, width=40, state="disabled")
bp_file_entry.pack(side=tk.LEFT, padx=(0, 5))

bp_select_button = tk.Button(bp_select_frame, text="Select Bindpad.lua", command=lambda: select_bindpad_file(bp_file_entry))
bp_select_button.pack(side=tk.LEFT)

# Subheading for dropdown menu
character_dropdown_subtitle = tk.Label(root, text="Select your character", font=("Arial", 12, "bold"))
character_dropdown_subtitle.pack(pady=(10, 5))

# Dropdown menu options
spec_options2 = []

# Dropdown menu
character_dropdown = ttk.Combobox(root, values=spec_options2, state="disabled", width=40, state="readonly")
character_dropdown.pack(pady=5)
character_dropdown.set("Select your Character")  # Placeholder text
character_dropdown.bind("<<ComboboxSelected>>", on_char_selected)

# Dropdown menu options
spec_options3 = ['1', '2', '3', '4']

# Dropdown menu
bp_spec_slot_dropdown = ttk.Combobox(root, values=spec_options3, width=40, state="readonly")
bp_spec_slot_dropdown.pack(pady=5)
bp_spec_slot_dropdown.set("Select your slot to load into")  # Placeholder text
bp_spec_slot_dropdown.bind("<<ComboboxSelected>>", on_slot_selected)

# Subheading for options
options_subheading = tk.Label(root, text="Options", font=("Arial", 12, "bold"))
options_subheading.pack(pady=(10, 5))

# Frame for file picker section 2
bp_select_frame = tk.Frame(root)
bp_select_frame.pack(pady=5)

profile_override_entry = tk.Entry(bp_select_frame, width=40, state="disabled")
profile_override_entry.pack(side=tk.LEFT, padx=(0, 5))

profile_override_button = tk.Button(bp_select_frame, text="Select a profile override", command=lambda: select_profile_overrides(profile_override_entry))
profile_override_button.pack(side=tk.LEFT)

# Frame for checkboxes
checkbox_frame = tk.Frame(root)
checkbox_frame.pack(pady=(0, 10), anchor='w')

# Checkboxes
randomise_macros_cb = tk.Checkbutton(checkbox_frame, text="Randomise macro names")
randomise_macros_cb.pack(side=tk.TOP, anchor='w')
randomise_macros_tt = Tooltip(randomise_macros_cb, "Randomise the names of the macros in the Bindpad file. This will make it harder to detect that the file has been tampered with.")

override_current_cb = tk.Checkbutton(checkbox_frame, text="Override current bindings")
override_current_cb.pack(side=tk.TOP, anchor='w')

shuffle_binds_cb = tk.Checkbutton(checkbox_frame, text="Shuffle bindings")
shuffle_binds_cb.pack(side=tk.TOP, anchor='w')

# Frame for file picker section 1
start_binding_frame = tk.Frame(root)
start_binding_frame.pack(pady=5)

# Button
start_binding_button = tk.Button(start_binding_frame, text="Start Binding!", command=start_action)
start_binding_button.pack(pady=(20, 10))

# Progress Bar
progress = ttk.Progressbar(start_binding_frame, orient=tk.HORIZONTAL, length=200, mode='determinate')


start_progress_serperator = ttk.Separator(root, orient='horizontal')
start_progress_serperator.pack(fill='x', pady=5, padx=2)


# Toggle button for the log area
toggle_button = tk.Button(root, text="Hide Logs", command=toggle_log_area)
toggle_button.pack(pady=(5, 0))

# Log area
log_area = scrolledtext.ScrolledText(root, wrap=tk.WORD, state='disabled', height=8)
log_area.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)

root.after(200, post_load)
# Start the GUI event loop
root.mainloop()
