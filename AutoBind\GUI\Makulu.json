{"Shaman - Elemental": {"Purge Unit1": {"macro": "/cast [@arena1] Flame Shock", "name": "Flame Arena 1"}, "Purge Unit2": {"macro": "/cast [@arena2] Flame Shock", "name": "Flame Arena 2"}, "Purge Unit3": {"macro": "/cast [@arena3] Flame Shock", "name": "Flame Arena 3"}, "Dispel Member1": {"macro": "/cast [@arena1] Hex", "name": "Hex Arena 1"}, "Dispel Member2": {"macro": "/cast [@arena2] Hex", "name": "Hex Arena 2"}, "Dispel Member3": {"macro": "/cast [@arena3] Hex", "name": "Hex Arena 3"}, "Fireblood": {"macro": "/cast [@focus] <PERSON>h Shield", "name": "Earth Shield Focus"}, "Regeneratin": {"macro": "/cast [@focus] Healing Surge", "name": "Healing Surge Focus"}, "Earthgrab Totem": {"macro": "/cast [@player] <PERSON><PERSON><PERSON>", "name": "Self Earthgrab Totem"}, "Rocket Jump": {"macro": "/focus party1", "name": "Focus Party 1"}, "Rocket Barrage": {"macro": "/focus party2", "name": "Focus Party 2"}, "Water Walking": {"macro": "/cast Healing Stream Totem", "name": "Healing Stream Totem"}, "Dispel": {"macro": "/cast [@mouseover,help][] Cleanse Spirit", "name": "Cleanse"}, "Earthquake": {"macro": "/cast [@cursor] Earthquake", "name": "Earthquake"}}, "Paladin - Holy": {"Target Member1": {"macro": "/focus [mod:ctrl]raidpet1; [mod:alt]party1; raid1", "name": "Focus 1"}, "Target Member2": {"macro": "/focus [mod:ctrl]raidpet2; [mod:alt]party2; raid2", "name": "Focus 2"}, "Target Member3": {"macro": "/focus [mod:ctrl]raidpet3; [mod:alt]party3; raid3", "name": "Focus 3"}, "Target Member4": {"macro": "/focus [mod:ctrl]raidpet4; [mod:alt]party4; raid4", "name": "Focus 4"}, "Target Member5": {"macro": "/focus [mod:ctrl]raidpet5; [mod:alt]party5; raid5", "name": "Focus 5"}, "Target Member6": {"macro": "/focus [mod:ctrl]raidpet6; raid6", "name": "Focus 6"}, "Target Member7": {"macro": "/focus [mod:ctrl]raidpet7; raid7", "name": "Focus 7"}, "Target Member8": {"macro": "/focus [mod:ctrl]raidpet8; raid8", "name": "Focus 8"}, "Target Member9": {"macro": "/focus [mod:ctrl]raidpet9; raid9", "name": "Focus 9"}, "Target Member10": {"macro": "/focus [mod:ctrl]raidpet10; raid10", "name": "Focus 10"}, "Target Member11": {"macro": "/focus [mod:ctrl]raidpet11; raid11", "name": "Focus 11"}, "Target Member12": {"macro": "/focus [mod:ctrl]raidpet12; raid12", "name": "Focus 12"}, "Target Member13": {"macro": "/focus [mod:ctrl]raidpet13; raid13", "name": "Focus 13"}, "Target Member14": {"macro": "/focus [mod:ctrl]raidpet14; raid14", "name": "Focus 14"}, "Target Member15": {"macro": "/focus [mod:ctrl]raidpet15; raid15", "name": "Focus 15"}, "Target Member16": {"macro": "/focus [mod:ctrl]raidpet16; raid16", "name": "Focus 16"}, "Target Member17": {"macro": "/focus [mod:ctrl]raidpet17; raid17", "name": "Focus 17"}, "Target Member18": {"macro": "/focus [mod:ctrl]raidpet18; raid18", "name": "Focus 18"}, "Target Member19": {"macro": "/focus [mod:ctrl]raidpet19; raid19", "name": "Focus 19"}, "Target Member20": {"macro": "/focus [mod:ctrl]raidpet20; raid20", "name": "Focus 20"}, "Target Member21": {"macro": "/focus [mod:ctrl]raidpet21; raid21", "name": "Focus 21"}, "Target Member22": {"macro": "/focus [mod:ctrl]raidpet22; raid22", "name": "Focus 22"}, "Target Member23": {"macro": "/focus [mod:ctrl]raidpet23; raid23", "name": "Focus 23"}, "Target Member24": {"macro": "/focus [mod:ctrl]raidpet24; raid24", "name": "Focus 24"}, "Target Member25": {"macro": "/focus [mod:ctrl]raidpet25; raid25", "name": "Focus 25"}, "Target Member26": {"macro": "/focus [mod:ctrl]raidpet26; raid26", "name": "Focus 26"}, "Target Member27": {"macro": "/focus [mod:ctrl]raidpet27; raid27", "name": "Focus 27"}, "Target Member28": {"macro": "/focus [mod:ctrl]raidpet28; raid28", "name": "Focus 28"}, "Target Member29": {"macro": "/focus [mod:ctrl]raidpet29; raid29", "name": "Focus 29"}, "Target Member30": {"macro": "/focus [mod:ctrl]raidpet30; raid30", "name": "Focus 30"}, "Target Member31": {"macro": "/focus [mod:ctrl]raidpet31; raid31", "name": "Focus 31"}, "Target Member32": {"macro": "/focus [mod:ctrl]raidpet32; raid32", "name": "Focus 32"}, "Target Member33": {"macro": "/focus [mod:ctrl]raidpet33; raid33", "name": "Focus 33"}, "Target Member34": {"macro": "/focus [mod:ctrl]raidpet34; raid34", "name": "Focus 34"}, "Target Member35": {"macro": "/focus [mod:ctrl]raidpet35; raid35", "name": "Focus 35"}, "Target Member36": {"macro": "/focus [mod:ctrl]raidpet36; raid36", "name": "Focus 36"}, "Target Member37": {"macro": "/focus [mod:ctrl]raidpet37; raid37", "name": "Focus 37"}, "Target Member38": {"macro": "/focus [mod:ctrl]raidpet38; raid38", "name": "Focus 38"}, "Target Member39": {"macro": "/focus [mod:ctrl]raidpet39; raid39", "name": "Focus 39"}, "Target Member40": {"macro": "/focus [mod:ctrl]raidpet40; raid40", "name": "Focus 40"}, "Dispel": {"macro": "/cast [@target,help][@focus,help][]Cleanse", "name": "Dispel"}, "Barrier of Faith": {"macro": "/cast [@target,help][@focus,help][]<PERSON><PERSON> of Faith", "name": "Barrier of Faith"}, "Holy Shock": {"macro": "/cast [@target,help][@focus,help][]Holy Shock", "name": "Holy Shock"}, "Word of Glory": {"macro": "/cast [@target,help][@focus,help][]Word of Glory", "name": "Word of Glory"}, "Light of the Martyr": {"macro": "/cast [@target,help][@focus,help][]Light of the Martyr", "name": "Light of the Martyr"}, "Holy Light": {"macro": "/cast [@target,help][@focus,help][]Holy Light", "name": "Holy Light"}, "Flash of Light": {"macro": "/cast [@target,help][@focus,help][]Flash of Light", "name": "Flash of Light"}, "Light's Hammer | Holy Prism": {"macro": "/cast [@player]Light's Hammer\n/cast [@target,help][@focus,help][]Holy Prism", "name": "Light's Hammer"}, "Blessing of Sacrifice": {"macro": "/cast [@target,help][@focus,help][]Blessing of <PERSON><PERSON>rifice", "name": "BoS"}, "Blessing of Protection": {"macro": "/cast [@target,help][@focus,help][]Blessing of Protection", "name": "BoP"}, "Blessing of Freedom": {"macro": "/cast [@target,help][@focus,help][]Blessing of Freedom", "name": "BoF"}, "Lay on Hands": {"macro": "/cast [@target,help][@focus,help][]Lay on Hands", "name": "LoH"}, "Beacon of Faith": {"macro": "/cast [@target,help][@focus,help][]Beacon of Faith", "name": "Beacon of Faith"}, "Beacon of Virtue | Beacon of Light": {"macro": "/cast [@target,help][@focus,help][]Beacon of Virtue\n/cast [@target,help][@focus,help][]Beacon of Light", "name": "Beacon of Light"}, "Turn Evil": {"macro": "/cast Holy Shock", "name": "HS Damage"}, "Stoneform": {"macro": "/cast Hand of Divinity", "name": "Hand of Div"}, "Fireblood": {"macro": "/cast Daybreak", "name": "Daybreak"}, "Blessing of Summer": {"macro": "/cast [@target,help][@focus,help][]Blessing of Summer", "name": "Blessing Summer"}}, "Druid - Restoration": {"Target Member1": {"macro": "/focus [mod:ctrl]raidpet1; [mod:alt]party1; raid1", "name": "Focus 1"}, "Target Member2": {"macro": "/focus [mod:ctrl]raidpet2; [mod:alt]party2; raid2", "name": "Focus 2"}, "Target Member3": {"macro": "/focus [mod:ctrl]raidpet3; [mod:alt]party3; raid3", "name": "Focus 3"}, "Target Member4": {"macro": "/focus [mod:ctrl]raidpet4; [mod:alt]party4; raid4", "name": "Focus 4"}, "Target Member5": {"macro": "/focus [mod:ctrl]raidpet5; [mod:alt]party5; raid5", "name": "Focus 5"}, "Target Member6": {"macro": "/focus [mod:ctrl]raidpet6; raid6", "name": "Focus 6"}, "Target Member7": {"macro": "/focus [mod:ctrl]raidpet7; raid7", "name": "Focus 7"}, "Target Member8": {"macro": "/focus [mod:ctrl]raidpet8; raid8", "name": "Focus 8"}, "Target Member9": {"macro": "/focus [mod:ctrl]raidpet9; raid9", "name": "Focus 9"}, "Target Member10": {"macro": "/focus [mod:ctrl]raidpet10; raid10", "name": "Focus 10"}, "Target Member11": {"macro": "/focus [mod:ctrl]raidpet11; raid11", "name": "Focus 11"}, "Target Member12": {"macro": "/focus [mod:ctrl]raidpet12; raid12", "name": "Focus 12"}, "Target Member13": {"macro": "/focus [mod:ctrl]raidpet13; raid13", "name": "Focus 13"}, "Target Member14": {"macro": "/focus [mod:ctrl]raidpet14; raid14", "name": "Focus 14"}, "Target Member15": {"macro": "/focus [mod:ctrl]raidpet15; raid15", "name": "Focus 15"}, "Target Member16": {"macro": "/focus [mod:ctrl]raidpet16; raid16", "name": "Focus 16"}, "Target Member17": {"macro": "/focus [mod:ctrl]raidpet17; raid17", "name": "Focus 17"}, "Target Member18": {"macro": "/focus [mod:ctrl]raidpet18; raid18", "name": "Focus 18"}, "Target Member19": {"macro": "/focus [mod:ctrl]raidpet19; raid19", "name": "Focus 19"}, "Target Member20": {"macro": "/focus [mod:ctrl]raidpet20; raid20", "name": "Focus 20"}, "Target Member21": {"macro": "/focus [mod:ctrl]raidpet21; raid21", "name": "Focus 21"}, "Target Member22": {"macro": "/focus [mod:ctrl]raidpet22; raid22", "name": "Focus 22"}, "Target Member23": {"macro": "/focus [mod:ctrl]raidpet23; raid23", "name": "Focus 23"}, "Target Member24": {"macro": "/focus [mod:ctrl]raidpet24; raid24", "name": "Focus 24"}, "Target Member25": {"macro": "/focus [mod:ctrl]raidpet25; raid25", "name": "Focus 25"}, "Target Member26": {"macro": "/focus [mod:ctrl]raidpet26; raid26", "name": "Focus 26"}, "Target Member27": {"macro": "/focus [mod:ctrl]raidpet27; raid27", "name": "Focus 27"}, "Target Member28": {"macro": "/focus [mod:ctrl]raidpet28; raid28", "name": "Focus 28"}, "Target Member29": {"macro": "/focus [mod:ctrl]raidpet29; raid29", "name": "Focus 29"}, "Target Member30": {"macro": "/focus [mod:ctrl]raidpet30; raid30", "name": "Focus 30"}, "Target Member31": {"macro": "/focus [mod:ctrl]raidpet31; raid31", "name": "Focus 31"}, "Target Member32": {"macro": "/focus [mod:ctrl]raidpet32; raid32", "name": "Focus 32"}, "Target Member33": {"macro": "/focus [mod:ctrl]raidpet33; raid33", "name": "Focus 33"}, "Target Member34": {"macro": "/focus [mod:ctrl]raidpet34; raid34", "name": "Focus 34"}, "Target Member35": {"macro": "/focus [mod:ctrl]raidpet35; raid35", "name": "Focus 35"}, "Target Member36": {"macro": "/focus [mod:ctrl]raidpet36; raid36", "name": "Focus 36"}, "Target Member37": {"macro": "/focus [mod:ctrl]raidpet37; raid37", "name": "Focus 37"}, "Target Member38": {"macro": "/focus [mod:ctrl]raidpet38; raid38", "name": "Focus 38"}, "Target Member39": {"macro": "/focus [mod:ctrl]raidpet39; raid39", "name": "Focus 39"}, "Target Member40": {"macro": "/focus [mod:ctrl]raidpet40; raid40", "name": "Focus 40"}, "Dispel": {"macro": "/cast [@target,help][@focus,help][]Nature's Cure", "name": "Dispel"}, "Dispel Member1": {"macro": "/cast [@player]Nature's Cure", "name": "Dispel1"}, "Dispel Member2": {"macro": "/cast [@party1]Nature's Cure", "name": "Dispel2"}, "Dispel Member3": {"macro": "/cast [@party2]Nature's Cure", "name": "Dispel3"}, "Darkflight": {"macro": "/cast [@player]Lifebloom", "name": "Lifebloom self"}, "Regeneratin": {"macro": "/cast [@target,help][@focus,help][]Grove Guardians", "name": "Grove Guardians"}, "Haymaker": {"macro": "/cast Tranquility", "name": "Tranquility"}, "Regrowth": {"macro": "/cast [@target,help][@focus,help][]<PERSON><PERSON><PERSON>", "name": "<PERSON>row<PERSON>"}, "Rejuvenation": {"macro": "/cast [@target,help][@focus,help][]Rejuvenation", "name": "Rejuvenation"}, "Swiftmend": {"macro": "/cast [@target,help][@focus,help][]Swiftmend", "name": "Swiftmend"}, "Wild Growth": {"macro": "/cast [@target,help][@focus,help][]Wild Growth", "name": "Wild Growth"}, "Innervate": {"macro": "/cast [@player]Innervate", "name": "Innervate Self"}, "Lifebloom": {"macro": "/cast [@target,help][@focus,help][]Lifebloom", "name": "Lifebloom"}, "Cenarion Ward": {"macro": "/cast [@target,help][@focus,help][]Cenarion Ward", "name": "Cenarion Ward"}, "Ironbark": {"macro": "/cast [@target,help][@focus,help][]Ironbark", "name": "Ironbark"}, "Overgrowth": {"macro": "/cast [@target,help][@focus,help][]Overgrowth", "name": "Overgrowth"}, "Thorns": {"macro": "/cast [@target,help][@focus,help][]<PERSON>s", "name": "Thorns"}, "Thorns Member1": {"macro": "/cast [@player]<PERSON><PERSON>", "name": "Thorns Self"}, "Thorns Member2": {"macro": "/cast [@party1]<PERSON>s", "name": "Thorns1"}, "Thorns Member3": {"macro": "/cast [@party2]<PERSON>s", "name": "Thorns2"}, "Mangle | Shred | Wrath": {"macro": "/cast [form:1]<PERSON>gle;[form:2]Shred;<PERSON>", "name": "MSW"}, "Ironfur | Ferocious Bite": {"macro": "/cast [form:1]Ironfur;[form:2]Ferocious Bite", "name": "FB"}, "Rake | Frenzied Regeneration | Starfire": {"macro": "/cast [form:2]Rake;[form:1]Frenzied Regeneration;Starfire", "name": "RakeSF"}, "Brutal Slash | Swipe": {"macro": "/cast Swipe", "name": "Swipe"}, "Maim": {"macro": "/cast Mai<PERSON>", "name": "<PERSON><PERSON>"}, "Wild Charge": {"macro": "/cast Wild Charge", "name": "Wild Charge"}, "Cyclone": {"macro": "/cast Cyclone", "name": "Cyclone"}, "Sunfire": {"macro": "/cast Sunfire", "name": "Sunfire"}}}