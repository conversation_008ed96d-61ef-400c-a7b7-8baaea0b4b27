import luaparser.astnodes as astNode
from luaparser import ast, utils

import sys
sys.path.append('../../AutoBundle')

from CustomLuaPrinter import MakuluLuaPrinter
from Spec import build_spec_table
from GGL import load_ggl_config, write_section_to_file
from Keys import get_all_combinations
from Helpers import table_to_lookup
from BPBinding import create_bindings

import json

def read_bind_stuff():
    file = "D:/WOW/World of Warcraft/_retail_/WTF/Account/**********#1/SavedVariables/Bindpad.lua"
    file = '../../Bindpad copy.lua'

    with open(file, 'r') as f:
        tree = ast.parse(f.read())

    my_name = "PROFILE_Tichondrius_Goblindznts"

    for node in ast.walk(tree):
        if isinstance(node, ast.Field) and isinstance(node.key, ast.String) and node.key.s == my_name:
            my_table = node.value
            break

    for field in my_table.fields:
        if isinstance(field, ast.Field) and isinstance(field.key, ast.Number) and field.key.n == 1:
            cell_one = field
            break

    character_tab = None
    keybinds = None
    for field in cell_one.value.fields:
        if isinstance(field, ast.Field) and isinstance(field.key, ast.String):
            if field.key.s == "CharacterSpecificTab1":
                character_tab = field

            if field.key.s == "AllKeyBindings":
                keybinds = field

    ggl_config = load_ggl_config('../../Config.ini')
    section = ggl_config['Monk - Windwalker']

    character_tab.value = build_spec_table(1, section)

    create_bindings(keybinds, section)

    with open('../Bindpad.lua', 'w') as file:
        message = MakuluLuaPrinter(indent_size=1).visit(tree)
        file.write(message)

def create_ggl_binding(all_binds, ggl_config, section, p = print):
    changes = {}

    for key, binds in ggl_config[section].items():
        if "START" in key:
            continue

        macro = binds.get('macro')
        notes = binds.get('notes')

        if binds['bind'] is not None or (macro is None and notes is not None and len(notes) > 0):
            continue

        if len(all_binds) == 0:
            p("Ran out of binds")
            break
        my_bind = all_binds.pop(0)
        binds['bind'] = my_bind['vk_name']

        changes[key] = binds

        p(f"Binding {key} to {my_bind['bp_name']}")

    # write_section_to_file('Monk - Windwalker', changes)

def load_ggl_binds():
    all_binds = get_all_combinations()
    ggl_config = load_ggl_config()

    changes = {}

    for key, binds in ggl_config['Monk - Windwalker'].items():
        if "START" in key:
            continue

        macro = binds.get('macro')
        notes = binds.get('notes')

        if binds['bind'] is not None or (macro is None and notes is not None and len(notes) > 0):
            continue

        if len(all_binds) == 0:
            print("Ran out of binds")
            break
        my_bind = all_binds.pop(0)
        binds['bind'] = my_bind['vk_name']

        changes[key] = binds

        print(f"Binding {key} to {my_bind['bp_name']}")

    write_section_to_file('Monk - Windwalker', changes)

load_ggl_binds()
read_bind_stuff()