from LIBS.Helpers import table_to_lookup, table_to_reverse_lookup
from LIBS.Keys import key2char, scancode_to_key
from luaparser import ast

import re
import random

def convert_to_char(key_event):
    if key_event['code_type'] == 'sc':
        return scancode_to_key(key_event['code_value'])
    
    value, is_numpad_key = key2char(key_event['code_value'])
    if value is None:
        print(f"Failed to convert {key_event['code_value']} to char")
        return value
    
    if is_numpad_key:
        return f"NUMPAD{value}"
    
    return value

def parse_modifiers(modifiers):
    result = ""

    if '!' in modifiers:
        result += "ALT-"

    if '^' in modifiers:
        result += "CTRL-"

    if '+' in modifiers:
        result += "SHIFT-"

    return result

def parse_key_events(event):
    if '_' not in event:
        event = f"{event}_0"

    # Regular expression to extract the necessary parts
    match = re.match(r'([!^+]*)(vk|sc)([0-9A-Fa-f]+)(?:_\d+)?', event)
    if match:
        modifiers, code_type, code_value = match.groups()
        return {
            'modifiers': modifiers,
            'code_type': code_type,
            'code_value': int(code_value, 16)
        }
    else:
        return None
    
def create_ast_field(key, value):
    return ast.Field(
        key=ast.String(key),
        value=ast.String(value),
        between_brackets=True
    )

def create_bindings(keybind_section, ggl_section_config, p = print):
    keybind_table = keybind_section.value
    key_lookup = table_to_lookup(keybind_table)
    value_lookup = table_to_reverse_lookup(keybind_table)

    items = list(ggl_section_config.items())
    random.shuffle(items)

    for key, value in items:
        bind = value.get('bind')
        bp_action = value.get('BP_ACTION')

        if bind is None or bp_action is None:
            p(f"Skipping {key}")
            continue

        key_event = parse_key_events(value['bind'])
        if key_event is None:

            p(f"Failed to convert bind {bind}")
            continue

        # if key_event['code_type'] == 'sc':
        #     continue

        # print(f"\n\n{key}: {value['bind']}")
        #print(key_event)

        char_res = convert_to_char(key_event)
        if char_res is None:
            p(f"Failed to convert key {key_event}")
            continue

        bp_bind = parse_modifiers(key_event['modifiers']) + char_res

        print(f"Binding {key} as: '{bp_action}'")

        value['BP_BIND'] = bp_bind

        if bp_action in value_lookup:
            value_lookup[bp_action].s = ""

        if bp_bind in key_lookup:
            # print(f"Found {bp_bind} in keybinds. Overwriting")
            key_lookup[bp_bind].s = bp_action
        else:
            new_field = create_ast_field(bp_bind, bp_action)
            keybind_table.fields.append(new_field)
