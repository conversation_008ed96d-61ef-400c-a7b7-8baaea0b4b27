from collections import OrderedDict

magic_split = '§§§'

def parse_comment(comment):
    if magic_split not in comment:
        usable_macro = comment.replace('§', '\n').strip()
        if not usable_macro.startswith('/'):
            return None, usable_macro
        return usable_macro, None

    split_on_notes = comment.split('§§§')
    if len(split_on_notes) > 1:
        first_section = split_on_notes[0].strip()
        if len(first_section) == 0:
            return None, split_on_notes[1].replace('§', '\n')
        
        if not first_section.startswith('/'):
            return None, split_on_notes[1].replace('§', '\n')

        usable_macro = split_on_notes[0].replace('§', '\n')
        notes = split_on_notes[1].replace('§', '\n')
        return usable_macro, notes

    notes = split_on_notes[0].replace('§', '\n')
    return None, notes

def load_ggl_config(file = '../Tests/Config.ini'):
    result = {}
    current_section = None

    with open(file, 'r', encoding='utf-16') as file:
        for line in file:
            line = line.strip()
            if line.startswith('['):
                current_section = line[1:-1]
                result[current_section] = {}
            elif line:
                split_res = line.split('=', 1)

                key = split_res[0]
                value = None
                macro = None
                notes = None

                if len(split_res) > 1 :
                    value = split_res[1].strip()
                    if len(value) == 0:
                        value = {
                            "bind": None,
                            "comment": None
                        }
                    elif value.startswith(';'):
                        macro, notes = parse_comment(value[1:])
                        value = {
                            "bind": None,
                            "comment": value[1:],
                            "macro": macro,
                            "notes": notes
                        }
                    else:
                        value_split = value.split(';')
                        comment = None
                        if len(value_split) > 1:
                            comment = value_split[1]

                        bind = value_split[0].strip()
                        if len(bind) == 0:
                            bind = None

                        if comment is not None:
                            macro, notes = parse_comment(comment)

                        value = {
                            "bind": value_split[0].strip(),
                            "comment": comment,
                            "macro": macro,
                            "notes": notes
                        }

                result[current_section][key.strip()] = value

    return result

ignore_list = [
    "Main",
    "SourceSettings",
    "General",
    "PhysInput",
    "ExportImport",
]

def get_all_ggl_sections(parsed = None):
    if parsed is None:
        parsed = load_ggl_config()

    return [key for key in parsed.keys() if key not in ignore_list and '_Settings' not in key]

def write_ggl_config_line(key, config):
    line = key + "="
    if config["bind"] is not None:
        line += config["bind"]
    
    if config["comment"] is not None:
        line += f";{config['comment']}"

    line += "\n"

    return line

def write_section_to_file(section, section_dict, file_loc):
    with open(file_loc, 'r', encoding='utf-16') as file:
         lines = file.readlines()

    in_section = False

    for i, line in enumerate(lines):
        if line.startswith('['):
            if in_section:
                break

            if section in line:
                in_section = True
                continue

        if not in_section:
            continue

        split_res = line.split('=', 1)
        key = split_res[0]

        if key in section_dict:
            value = section_dict[key]
            lines[i] = write_ggl_config_line(key, value)
    
    with open(file_loc, 'w', encoding='utf-16') as file:
        for line in lines:
            file.write(line)

def section_stats(keys):
    bound_num = 0
    unbound_num = 0
    unbindable_num = 0

    for _, value in keys.items():
        if value['bind'] is None:
            unbound_num += 1
        else:
            bound_num += 1

        macro = value.get('macro')
        notes = value.get('notes')

        if value['bind'] is None and (macro is None and notes is not None and len(notes) > 0):
            unbindable_num += 1

    return bound_num, unbound_num, unbindable_num


def stats(result):
    smallest_section = 999
    smallest_section_name = None

    stats = {}

    for header, profile in result.items():
        skipList = ["WOTLK", "Classic", "TBC", "_Settings", "PhysInput", "ExportImport", "SourceSettings", "Main", "General"]
        if any(str in header for str in skipList):
            continue

        if len(profile) < smallest_section:
            smallest_section = len(profile)
            smallest_section_name = header

        stats[header] = len(profile)

    print(smallest_section_name)
    print(smallest_section)

    import json
    sorted_stats = OrderedDict(sorted(stats.items(), key=lambda kv: kv[1]))
    print(json.dumps(sorted_stats, indent=4))

if __name__ == '__main__':
    res = load_ggl_config()
    stats(res)
