from luaparser import ast

def table_to_lookup(table):
    lookup = {}
    for field in table.fields:
        if isinstance(field, ast.Field) and isinstance(field.key, ast.String):
            lookup[field.key.s] = field.value
    return lookup

def table_to_reverse_lookup(table):
    lookup = {}
    for field in table.fields:
        if isinstance(field, ast.Field) and isinstance(field.key, ast.String):
            lookup[field.value.s] = field.value
    return lookup