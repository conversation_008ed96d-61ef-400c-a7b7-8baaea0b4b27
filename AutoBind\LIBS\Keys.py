from itertools import combinations
from ctypes import windll, wintypes, create_unicode_buffer
import random

def char2key(c):
    # https://msdn.microsoft.com/en-us/library/windows/desktop/ms646329(v=vs.85).aspx
    result = windll.User32.VkKeyScanW(ord(c))
    shift_state = (result & 0xFF00) >> 8
    vk_key = result & 0xFF

    return vk_key

def key2char(vk_key):
    if 0x70 <= vk_key <= 0x87:
        return f"F{vk_key - 0x6F}", False
    # Check if the key is a numpad key
    is_numpad_key = (vk_key >= 0x60 and vk_key <= 0x69) or (vk_key == 0x6E)  # 0x60-0x69 are VK_NUMPAD0 - VK_NUMPAD9, 0x6E is VK_DECIMAL

    # Get the current keyboard layout
    layout = windll.User32.GetKeyboardLayout(0)

    # Translate the virtual key code to a scan code
    # Use MapVirtualKeyExW with MAPVK_VK_TO_VSC (0) for non-numpad keys, and MAPVK_VK_TO_VSC_EX (3) for numpad keys
    map_type = 3 if is_numpad_key else 0
    scan_code = windll.User32.MapVirtualKeyExW(vk_key, map_type, layout)

    # Create a buffer to receive the translated character
    char_buff = create_unicode_buffer(2)

    # Prepare a keyboard state array (256 bytes), initialized to 0
    keyboard_state = (wintypes.BYTE * 256)()

    # Translate the scan code to a character
    if windll.User32.ToUnicodeEx(vk_key, scan_code, keyboard_state, char_buff, len(char_buff), 0, layout):
        return char_buff.value, is_numpad_key
    else:
        return None, False

def scancode_to_key(scan_code):
    # Get the current keyboard layout
    layout = windll.User32.GetKeyboardLayout(0)

    # Map the scan code to a virtual key code
    vk_key = windll.User32.MapVirtualKeyExW(scan_code, 1, layout)  # 1 is MAPVK_VSC_TO_VK

    # Now convert the virtual key code to a character, similar to your key2char function
    char_buff = create_unicode_buffer(2)
    keyboard_state = (wintypes.BYTE * 256)()

    if windll.User32.ToUnicodeEx(vk_key, scan_code, keyboard_state, char_buff, len(char_buff), 0, layout):
        return char_buff.value
    else:
        return None

scan_key_pool = []

scan_key_pool.extend(range(0x15, 0x1B))
scan_key_pool.extend(range(0x23, 0x28))

all_scan_codes = []

for i in scan_key_pool:
    char = scancode_to_key(i)

    if char is None:
        continue

    all_scan_codes.append({
        "VK": f"sc{(i):X}",
        "key": char
    })
    
def create_numbers():
    numbers = []
    for i in range(4, 10):
        numbers.append({
            "VK": f"sc{(2 + i):X}",
            "key": str(i),
        })
    return numbers

# Shuffle before we put number in. Having these in order makes sense
random.shuffle(all_scan_codes)
all_scan_codes.extend(create_numbers())

def create_numpads():
    numpads = []
    for i in range(10):
        numpads.append({
            "VK": f"vk{(0x60 + i):X}",
            "key": "NUMPAD" + str(i),
        })
    return numpads

def create_f_keys():
    numpads = []
    for i in range(10):
        numpads.append({
            # Start from F5
            "VK": f"vk{(0x74 + i):X}",
            "key": "F" + str(i + 5),
        })
    return numpads

def create_modifiers():
    all_options = [
        {
            "key": "ALT",
            "VK": "!",
            "BP_IDX": 1,
            "VK_IDX": 2
        },
        {
            "key": "CTRL",
            "VK": "^",
            "BP_IDX": 2,
            "VK_IDX": 1
        },
        {
            "key": "SHIFT",
            "VK": "+",
            "BP_IDX": 3,
            "VK_IDX": 3
        }
    ]

    all_combinations = [combo for r in range(1, len(all_options) + 1) for combo in combinations(all_options, r)]

    return all_combinations

def merge_key_modifier(key, modifiers):
    modifiers = modifiers or []

    modifiers_used = list(dict.fromkeys([modifier['key'] for modifier in modifiers]))

    modifier_list = list(dict.fromkeys([modifier['key'] for modifier in modifiers]))
    modifier_list.append(key['key'])

    name = "-".join(modifier_list)

    vk_sorted_modifiers = sorted(modifiers, key=lambda x: x['VK_IDX'], reverse=False)

    vk_list = [modifier['VK'] for modifier in vk_sorted_modifiers]
    vk_list.append(key["VK"])

    vk_name = "".join(vk_list) + "_134809609"

    return {
        "bp_name": name,
        "vk_name": vk_name,
        "modifiers": modifiers_used,
    }

def get_all_combinations(verbose=False):
    numpads = all_scan_codes
    numpads.extend(create_numpads())
    numpads.extend(create_f_keys())

    modifiers = create_modifiers()

    if verbose:
        print(f"Number of numpads: {len(numpads)}")

    potential_length = len(numpads) * (len(modifiers) + 1)

    if verbose:
        print(f"Potential length: {potential_length}")

    all_binds = [merge_key_modifier(numpad, None) for numpad in numpads]

    all_binds.extend([merge_key_modifier(numpad, modifier) for modifier in modifiers for numpad in numpads])

    if verbose:
        for option in all_binds:
            print(option)

    return all_binds

if __name__ == '__main__':
    get_all_combinations(True)
    print(len(scan_key_pool))

    for scan_code in all_scan_codes:
        print(scan_code)

