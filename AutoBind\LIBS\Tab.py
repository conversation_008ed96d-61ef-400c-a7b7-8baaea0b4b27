import luaparser.astnodes as astNode
import typo
import random
from random import randrange

escape_dict = {
    "\a": r"\a",
    "\b": r"\b",
    "\c": r"\c",
    "\f": r"\f",
    "\n": r"\n",
    "\r": r"\r",
    "\t": r"\t",
    "\v": r"\v",
    "'": r"\'",
    '"': r"\"",
    "\0": r"\0",
    "\1": r"\1",
    "\2": r"\2",
    "\3": r"\3",
    "\4": r"\4",
    "\5": r"\5",
    "\6": r"\6",
    "\7": r"\7",
    "\8": r"\8",
    "\9": r"\9",
}

def raw(text):
    """Returns a raw string representation of text"""
    new_string = ""
    for char in text:
        found = escape_dict.get(char)

        if found is not None:
            new_string += found
        else:
            new_string += char
    return new_string

def ligh_shuffle(l, factor=5):
    n = len(l)
    for _ in range(factor):
        a, b = randrange(n), randrange(n)
        l[b], l[a] = l[a], l[b]

def add_random_nones(list, count):
    for _ in range(count):
        list.insert(randrange(len(list)), (None, None))

def build_tab(idx, cells, overrides = {}):
    counter = { "i" : 0, 'blank': 1 }

    cell_items = list(cells.items())

    swap_factor = randrange(5, 20)

    ligh_shuffle(cell_items, swap_factor)

    add_random_nones(cell_items, randrange(15))

    all_cells = [build_cell(cell, name, counter, overrides) for name, cell in cell_items]
    all_cells = [cell for cell in all_cells if cell is not None]
    
    num_slots = astNode.Field(key=astNode.String(s="numSlot"), value=astNode.Number(n=175), between_brackets=True)

    all_cells.append(num_slots)

    return all_cells

    # table_entry = astNode.Field(key=astNode.String(s=f"CharacterSpecificTab{str(idx)}"), value=table, between_brackets=True)
    # return [table_entry]


def sanitize_macro_text(input_string):
    """
    This function takes a string and removes any lines that do not start with '/' or '#' or are not empty.
    
    Args:
    input_string (str): The input string containing multiple lines.
    
    Returns:
    str: The cleaned string.
    """
    # Split the input string into lines
    lines = input_string.split('\n')

    # Strip white space
    lines = [line.strip() for line in lines]

    # Filter lines that start with '/' or '#' or are empty
    cleaned_lines = [line for line in lines if line.startswith('/') or line.startswith('#') or line == '']

    # Join the cleaned lines back into a single string
    return '\n'.join(cleaned_lines)


def get_cell_macro(key, cell):
    macro_attr = cell.get("macro")
    if macro_attr is None:
        return f"/cast {key}"

    return raw(sanitize_macro_text(macro_attr))


def typo_in_name(name):
    to_typo = typo.StrErrer(name)
    
    while random.random() < ((len(name) / 1.5) / 100):

        print(f"Typoing {name}. Chance was {((len(name) / 1.5) / 100)}")
        to_typo.nearby_char()

    return to_typo.result


empty_chance = random.uniform(0.05, 0.3)

def build_cell(cell, name, counter, overrides):
    if name in overrides:
        old_name = name
        cell["macro"] = overrides[name]["macro"]
        name = overrides[name]["name"]
        print(f"Overriding {old_name} with {name}")

    if name is None:
        typo_name = f"{counter['blank']}"
        counter['blank'] += 1

        action = f"CLICK BindPadMacro:{typo_name}"

        code_params = {
            'type': astNode.String(s="CLICK"),
            'texture': astNode.Number(n=132089),
            'name': astNode.String(s=typo_name),
            'action': astNode.String(s=action),
            'macrotext': astNode.String(s=""),
        }
    else:
        if "START" in name or "Potion" in name:
            return None
        
        if random.random() < empty_chance:
            typo_name = f"{counter['blank']}"
            counter['blank'] += 1
        else:
            typo_name = typo_in_name(name)

        action = f"CLICK BindPadMacro:{typo_name}"

        code_params = {
            'type': astNode.String(s="CLICK"),
            'texture': astNode.Number(n=132089),
            'name': astNode.String(s=typo_name),
            'action': astNode.String(s=action),
            'macrotext': astNode.String(s=get_cell_macro(name, cell)),
        }

        cell['BP_ACTION'] = action

    new_table_fields = [astNode.Field(key=astNode.String(s=k), value=v, between_brackets=True)
                    for k, v in code_params.items()]
    table = astNode.Table(new_table_fields)

    counter['i'] += 1

    field = astNode.Field(key=astNode.Number(n=counter['i']), value=table,
            between_brackets=True, comments=[astNode.Comment(s="-- [" + str(counter['i']) + "]")])
    return field
