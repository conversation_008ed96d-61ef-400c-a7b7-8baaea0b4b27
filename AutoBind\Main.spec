# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['Main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('./GUI/*.py', 'GUI' ),
        ('./LIBS/*.py', 'LIBS' ),
        ('./MakuluBuilder.ico', '.' ),
    ],
    hiddenimports=[
        "shelve", 
        "tkinter", 
        "tkinter.filedialog",
        "tkinter.scrolledtext",
        "tkinter.ttk",
        "json",
        "ctypes",
        "ctypes.wintypes",
        "luaparser",
        "luaparser.ast",
        "typo",
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Makulu Auto Bind',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    icon='MakuluBuilder.ico',
    entitlements_file=None,
)
