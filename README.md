# Makulu Rotations

NOT TODO

## Bundling

TODO

## Development Guide to Git

We will try to develop in separate branches and then merge these into our main branch using Pull Requests to ensure we get good visibiliy of the changes going in

### Installing Git

Using it the CLI ends up being easier once you've got used to it so I recommend just getting used to it early. You can install the CLI here:

`https://git-scm.com/book/en/v2/Getting-Started-Installing-Git`

### Getting started

First we want to clone out repository onto our local machine to do this we navigate to the folder we want to work in inside the terminal for example:

```bash
cd Documents
mkdir MakuluRotations
cd MakuluRotations
```

After we've navigated to our directory in the terminal we can now clone the code to our machine to do this we simple run:

```bash
git clone https://github.com/JackThomson2/rotations
```

Easy right? We're hacking now 😎

### Developing our new AWESOME feature

#### Making sure our code is up to date with origin

First we want to make sure we're up to date with live. Jack's probably pushed some crappy code but we'll pull it down anyway. Lets get going!

- Make sure we are on `main` before we get going. Running `git status` will show us what branch we're currently working on.
- To checkout or switch to the main branch we will want to run `git checkout main`. Damn.. This git stuff is easy right?
- Now we need to pull down that crappy code from Jack... 🙄 We do with by running `git pull origin/main`
- Great, we've got the code but it hasn't changed anything wtf? So now we've hit our first weird git thing. The changes are not pulled into our local change yet
but they are there in remote, we now need to update our local to origin/main. We have two options here:
 - Reset to origin/main 😈: Simply run `git reset --hard origin/main` this is basically saying screw my local changes I want to just restart fresh. THIS WILL WIPE YOUR LOCAL CHANGES IF YOU HAVENT PUSHED THEM
 - Rebase to origin/main 😇: Run `git rebase origin/main` This will try to merge everything nicely and attempt to update and keep your changes. You MAY hit merge conflicts, this is the time you start crying.. I won't cover those now as they make me cry too much
- We did it! We've now got our local instance in sync with the remote server. Time to write this new feature

#### Creating our new branch to work on

When creating our new awesome feature we want to work in our own separate branch, this means you're not impacted by other changes and can work in isolation and get your new feature rolling!
This is super easy and we'll be going in new time, the hardest part is picking a name for it. Some common conventions you may want to follow is `feature/MY_NEW_FEATURE` or `fix/REMOVING_JACKS_CODE` etc.

- First step is checking out our new branch we simply just run `git checkout "feature/MY_NEW_FEATURE"`
- That's it.. Told you it was easy

#### Now lets code some shit

After adding some new parts to your feature you're going to want to commit these to your new branch. Git here is a little weird you need to add these to a commit then commit then push. Yeah weird right..
Lets get going anyway.

- So now you've written the changes you want to make with the files saved we're ready to go.
- First lets check git has seen them if you use `git status` it should show you files ready and files not yet commited. Time to add them
- There are two ways to do this the proper way or the way I do this.
  - You can add each file you want to commit with simple `git add FileIChanged.lua` and do this for each file or directory you want to add
  - Or just commit anything which has been created or changed with `git add .`
- Great! Our files are ready to be committed. If you do `git status` now you'll see the files have moved ready to be committed. Lets do this!
- Committing the changes is as easy as `git commit -m "Some change notes here!`. And baash we're almost done
- Now lets push this up to Github and its safe. We literally just run `git push` thats it?? Note you may see a weird error if you're pushing to a new branch.
Git will let you know to run a different one to create the new branch remotely as well. Just copy this and run it.

#### Ready to merge to main

Right.. We've made our change and now its time to get it into main and shipped to our users. Good news, no more CLI crap. 
Head to this link: https://github.com/JackThomson2/rotations/pulls and click the shiny button called create pull request. 
If everythings worked just select your branch in the `compare` drop down and then click create pull request. Put some notes and send it into our dev channel for someone to look at.

Once we're happy its ready to go we click merge pull request and we're done, that's it you've crushed it.

Happy Hacking!
