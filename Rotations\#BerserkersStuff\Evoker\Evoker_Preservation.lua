if not <PERSON><PERSON><PERSON>ValidCheck() then return true end
if not <PERSON><PERSON><PERSON>_magic_number == 2347956243324 then return true end


if GetSpecializationInfo(GetSpecialization()) ~= 1468 then return end

local frame = Create<PERSON>rame("Frame", "Ma<PERSON>luMessageFrame", UIParent, "BasicFrameTemplateWithInset")
frame:SetSize(300, 100)
frame:SetPoint("CENTER")

frame.title = frame:CreateFontString(nil, "OVERLAY")
frame.title:SetFontObject("GameFontHighlight")
frame.title:SetPoint("TOP", frame, "TOP", 0, -5)
frame.title:SetText("Makulu Alert")

frame.message = frame:CreateFontString(nil, "OVERLAY")
frame.message:SetFontObject("GameFontNormal")
frame.message:SetPoint("CENTER", frame, "CENTER", 0, 0)
frame.message:SetText("Thank you for using <PERSON><PERSON><PERSON>,\nthis is not the correct profile\nplease select the Ma<PERSON>lu Profile.")

frame:Show()