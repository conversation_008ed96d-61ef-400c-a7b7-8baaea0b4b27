if not Ma<PERSON>luValidCheck() then return true end
if not <PERSON><PERSON><PERSON>_magic_number == 2347956243324 then return true end


if GetSpecializationInfo(GetSpecialization()) ~= 270 then return end

local frame = Create<PERSON>rame("Frame", "MakuluMessageFrame", UIParent, "BasicFrameTemplateWithInset")
frame:SetSize(300, 100)
frame:SetPoint("CENTER")

frame.title = frame:CreateFontString(nil, "OVERLAY")
frame.title:SetFontObject("GameFontHighlight")
frame.title:SetPoint("TOP", frame, "TOP", 0, -5)
frame.title:SetText("Makulu Alert")

frame:Show()