local TMW											= TMW 
local CNDT											= TMW.CNDT
local Env											= CNDT.Env

local A												= Action
local GetToggle										= A.GetToggle
local InterruptIsValid								= A.InterruptIsValid

local UnitCooldown									= A.UnitCooldown
local Unit											= A.Unit 
local Player										= A.Player 
local Pet											= A.Pet
local LoC											= A.LossOfControl
local MultiUnits									= A.MultiUnits
local EnemyTeam										= A.EnemyTeam
local FriendlyTeam									= A.FriendlyTeam
local TeamCache										= A.TeamCache
local InstanceInfo									= A.InstanceInfo
local select, setmetatable							= select, setmetatable

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "v1.5.0 (24 April 2024)",
    -- Class settings
    [2] = {
        [1473] = {
            { -- GENERAL OPTIONS FIRST ROW
				{ -- AOE
                    E = "Checkbox",
                    DB = "AoE",
                    DBV = true,
                    L = {
                        enUS = "Use AoE",
                        ruRU = "Использовать AoE",
                        frFR = "Utiliser l'AoE",
                    },
                    TT = {
                        enUS = "Enable multiunits actions",
                        ruRU = "Включает действия для нескольких целей",
                        frFR = "Activer les actions multi-unités",
                    },
                    M = {},
                },
                { -- Hover
                    E = "Checkbox",
                    DB = "useHover",
                    DBV = true,
                    L = {
                        ANY = "Use Hover",
                    },
                    TT = {
                        ANY = "Automatically use Hover when moving and in combat.",
                    },
                    M = {},
                },
            },
            {
                { -- Automatic Interrupt
                    E = "Checkbox",
                    DB = "AutoInterrupt",
                    DBV = true,
                    L = {
                        ANY = "Switch Targets Interrupt",
                    },
                    TT = {
                        ANY = "Automatically switches targets to interrupt.",
                    },
                    M = {},
                },
            },
            { -- TRINKETS
                {
                    E = "Header",
                    L = {
                        ANY = " ====== DEFENSIVES ====== ",
                    },
                },
            },
            {
                { -- ObsidianScalesHP
                    E = "Slider",
                    MIN = 0,
                    MAX = 100,
                    DB = "ObsidianScalesHP",
                    DBV = 60,
                    ONOFF = false,
                    L = {
                        ANY = "Obsidian Scales HP",
                    },
                    TT = {
                        ANY = "Obsidian Scales HP (%)",
                    },
                    M = {},
                },
            },
            { -- LAYOUT SPACE
                {
                    E = "LayoutSpace",
                },
            },
            { -- TRINKETS
                {
                    E = "Header",
                    L = {
                        ANY = " ====== TRINKETS ====== ",
                    },
                },
            },
			{
				{ -- Trinket Type 1
                    E = "Dropdown",
                    OT = {
						{ text = "Damage", value = "Damage" },
						{ text = "Friendly", value = "Friendly" },
						{ text = "Self Defensive", value = "SelfDefensive" },
						{ text = "Mana Gain", value = "ManaGain" },
                    },
                    DB = "TrinketType1",
                    DBV = "Damage",
                    L = {
                        ANY = "First Trinket",
                    },
                    TT = {
                        ANY = "Pick what type of trinket you have in your first/upper trinket slot (only matters for trinkets with Use effects).",
                    },
                    M = {},
                },
				{ -- Trinket Type 2
                    E = "Dropdown",
                    OT = {
						{ text = "Damage", value = "Damage" },
						{ text = "Friendly", value = "Friendly" },
						{ text = "Self Defensive", value = "SelfDefensive" },
						{ text = "Mana Gain", value = "ManaGain" },
                    },
                    DB = "TrinketType2",
                    DBV = "Damage",
                    L = {
                        ANY = "Second Trinket",
                    },
                    TT = {
                        ANY = "Pick what type of trinket you have in your second/lower trinket slot (only matters for trinkets with Use effects).",
                    },
                    M = {},
                },
			},
			{
                { -- TrinketValue1
                    E = "Slider",
                    MIN = 0, 
                    MAX = 100,                            
                    DB = "TrinketValue1",
                    DBV = 40,
                    ONOFF = false,
                    L = {
                        ANY = "First Trinket Value",
                    },
                    TT = {
                        ANY = "HP/Mana (%) to use your first trinket, based on what you've chosen for your trinket type. Damage trinkets will be used on burst targets.", 
                    },
                    M = {},
                },
                { -- TrinketValue2
                    E = "Slider",
                    MIN = 0,
                    MAX = 100,
                    DB = "TrinketValue2",
                    DBV = 40,
                    ONOFF = false,
                    L = {
                        ANY = "Second Trinket Value",
                    },
                    TT = {
                        ANY = "HP/Mana (%) to use your second trinket, based on what you've chosen for your trinket type. Damage trinkets will be used on burst targets.",
                    },
                    M = {},
                },
			},
            { -- LAYOUT SPACE   
                {
                    E = "LayoutSpace",
                },
            },
            { -- LAYOUT SPACE   
                {
                    E = "LayoutSpace",
                },
            },
        },

	},
}