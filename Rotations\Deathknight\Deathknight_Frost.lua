-- TODO: 
-- Racial overhaul
-- Ranged fallback
-- 2h weapon check
-- runeforge enchant check
-- Remove new talent comments for TWW
-- Convert rest of zerk PVP logic (other things thatn kick/cc)


if not MakuluValidCheck() then return true end
if not Maku<PERSON>_magic_number == 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 251 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local Trinket          = MakuluFramework.Trinket
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local ActionUnit       = Action.Unit
local Player           = Action.Player
local MultiUnits       = Action.MultiUnits
local GetToggle        = Action.GetToggle
local BossMods         = Action.BossMods
local Unit             = Action.Unit

local _G, setmetatable = _G, setmetatable


local ActionID       = {
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    RocketJump = { ID = 69070 },
    RocketBarrage = { ID = 69041 },
    QuakingPalm = { ID = 107079 },
    SpatialRift = { ID = 256948 },
    LightsJudgment = { ID = 255647 },
    Fireblood = { ID = 265221 },
    ArcanePulse = { ID = 260364 },
    BullRush = { ID = 255654 },
    AncestralCall = { ID = 274738 },
    Haymaker = { ID = 287712 },
    Regeneratin = { ID = 291944 },
    BagOfTricks = { ID = 312411 }, 
    HyperOrganicLightOriginator = { ID = 312924 }, 

    -- Death Knight General
    AbominationLimb = { ID = 383269, MAKULU_INFO = { damageType = "magic" } },
    AntiMagicShell = { ID = 48707 },
    AntiMagicShellSW = { ID = 410358 },
    AntiMagicZone = { ID = 51052 },
    BlindingSleet = { ID = 207167 },
    ChainsofIce = { ID = 45524 },
    DarkCommand = { ID = 56222 },
    DeathandDecay = { ID = 43265 },
    DeathCoil = { ID = 47541 },
    DeathGrip = { ID = 49576, MAKULU_INFO = { damageType = "magic" } },
    DeathStrike = { ID = 49998, MAKULU_INFO = { damageType = "physical" } },
    DeathsAdvance = { ID = 48265 },
    IceboundFortitude = { ID = 48792 },
    Lichborne = { ID = 49039 },
    MindFreeze = { ID = 47528, MAKULU_INFO = { damageType = "magic" } },
    PathofFrost = { ID = 3714 },
    RaiseAlly = { ID = 61999 },
    RaiseDead = { ID = 46585 },
    DeathCoilPlayer	= { Type = "Spell", ID = 47541, Hidden = true, Texture = 98008 },
    SoulReaper = { ID = 343294, MAKULU_INFO = { damageType = "magic" } },
    
    -- Frost
    EmpowerRuneWeapon = { ID = 47568 },
    FrostStrike = { ID = 49143, MAKULU_INFO = { damageType = "magic" } },
    FrostwyrmsFury = { ID = 279302, MAKULU_INFO = { damageType = "magic" } },
    HowlingBlast = { ID = 49184, MAKULU_INFO = { damageType = "magic" } },
    Obliterate = { ID = 49020, MAKULU_INFO = { damageType = "physical" } },
    PillarofFrost = { ID = 51271 },
    RemorselessWinter = { ID = 196770, Texture = 136917, MAKULU_INFO = { damageType = "magic" } },
    GlacialAdvance = { ID = 194913, MAKULU_INFO = { damageType = "magic" } },
    HornofWinter = { ID = 57330 },
    ChillStreak = { ID = 305392, MAKULU_INFO = { damageType = "magic" } },
    ReapersMark = { ID = 439843, MAKULU_INFO = { damageType = "magic" } },
    BreathofSindragosa = { ID = 152279, MAKULU_INFO = { damageType = "magic" } },
    Frostscythe = { ID = 207230, MAKULU_INFO = { damageType = "magic" } },

    -- Talents
    CleavingStrikes = { ID = 316916 },
    ShatteringBlade = { ID = 207057, Hidden = true },
    GatheringStorm = { ID = 194912, Hidden = true },
    BitingCold = { ID = 377056, Hidden = true },
    ArcticAssault = { ID = 456230, Hidden = true },
    FrigidExecutioner = { ID = 377073, Hidden = true },
    RageOfTheFrozenChampion = { ID = 377076, Hidden = true },
    Icebreaker = { ID = 392950, Hidden = true },
    Avalanche = { ID = 207142, Hidden = true },
    UnleashedFrenzy = { ID = 376905, Hidden = true },
    SmotheringOffense = { ID = 435005, Hidden = true },
    DarkTalons = { ID = 435006, Hidden = true },
    Obliteration = { ID = 281238, Hidden = true },
    ShatteredFrost = { ID = 455993, Hidden = true },
    ColdHeart = { ID = 281208, Hidden = true },
    IcyTalons = { ID = 194878, Hidden = true },
    UnyieldingWill = { ID = 457574, Hidden = true },
    EnduringStrength = { ID = 377190, Hidden = true },
    TheLongWinter = { ID = 456240, Hidden = true },
    WitherAway = { ID = 441894, Hidden = true },
    FrozenDominion = { ID = 377226, Hidden = true },


    -- Borrowed/Other/Talents/Depreciated (CARRY OVER FROM UNHOLY - CLEANUP NEEDED)
    DeathCoilPet = { ID = 213690, Texture = 132179 },    
    Defile = { ID = 152280 },
    SacrificialPact = { ID = 327574 },
    ControlUndead = { ID = 111673 },
    Asphyxiate = { ID = 108194, MAKULU_INFO = { damageType = "physical" } },
    DeathPact = { ID = 48743 },
    WraithWalk = { ID = 212552 },
    ClawingShadows = { ID = 207311 },
    ScourgeStrike = { ID = 55090 },
    Outbreak = { ID = 77575 },
    DarkTransformation = { ID = 63560 }, 
    Epidemic = { ID = 207317 },
    Apocalypse = { ID = 275699 },
    VileContagion = { ID = 390279 },
    RaiseAbomination = { ID = 288853 },
    ArmyoftheDead = { ID = 42650 },
    SummonGargoyle = { ID = 49206 },
    UnholyAssault = { ID = 207289 },
    Leap = { ID = 47482 },
    Gnaw = { ID = 47481 },
    Huddle = { ID = 47484 },
    Reanimation = { ID = 210128 },
    Strangulate = { ID = 47476, MAKULU_INFO = { damageType = "magic" } },
    DarkSimulacrum = { ID = 77606 },
    UnholyEndurance = { ID = 389682, Hidden = true },
    ImprovedDeathCoil = { ID = 377580, Hidden = true },
    CoilofDevastation = { ID = 390270, Hidden = true },
    Festermight = { ID = 377590, Hidden = true },
    BurstingSores = { ID = 207264, Hidden = true },
    InfectedClaws = { ID = 207272, Hidden = true },
    ArmyoftheDamned = { ID = 276837, Hidden = true },
    RottenTouch = { ID = 390275, Hidden = true }, 
    SuddenDoom = { ID = 49530, Hidden = true },
    CommanderoftheDead = { ID = 390259, Hidden = true }, 
    Plaguebringer = { ID = 390175, Hidden = true }, 
    UnholyBlight = { ID = 115989, Hidden = true },   
    Superstrain = { ID = 390283, Hidden = true },
    EbonFever = { ID = 207269, Hidden = true },
    UnholyGround = { ID = 374265, Hidden = true },
    MagusoftheDead = { ID = 390196, Hidden = true },
    Morbidity = { ID = 377592, Hidden = true },
    DeathandDecayBuff = { ID = 188290, Hidden = true },
    FrostFever = { ID = 55095, Hidden = true },
    BloodPlague = { ID = 55078, Hidden = true },
    UnholyStrength = { ID = 53365, Hidden = true },
    FesteringWound = { ID = 194310, Hidden = true },
    RottenTouchDebuff = { ID = 390276, Hidden = true },
    SuddenDoomBuff = { ID = 81340, Hidden = true },
    CommanderoftheDeadBuff = { ID = 390260, Hidden = true },
    PlaguebringerBuff = { ID = 390178, Hidden = true },
    VirulentPlague = { ID = 191587, Hidden = true },
    DeathRot = { ID = 377540, Hidden = true },

    -- Hero Stuff
    RidersChampion = { ID = 444005, Hidden = true },
    ApocalypseNow = { ID = 444040, Hidden = true },
    AFeastofSouls = { ID = 444072, Hidden = true },
    ReaperofSouls = { ID = 440002, Hidden = true },



    -- Potions and Utility
    Healthstone = { Type = "Item", ID = 5512, Hidden = true },
    ElementalPotion1 = { Type = "Potion", ID = 191387, Texture = 176108, Hidden = true },
    ElementalPotion2 = { Type = "Potion", ID = 191388, Texture = 176108, Hidden = true },
    ElementalPotion3 = { Type = "Potion", ID = 191389, Texture = 176108, Hidden = true },
    ElementalUltimate1 = { Type = "Potion", ID = 191381, Texture = 176108, Hidden = true },
    ElementalUltimate2 = { Type = "Potion", ID = 191382, Texture = 176108, Hidden = true },
    ElementalUltimate3 = { Type = "Potion", ID = 191383, Texture = 176108, Hidden = true },
    TemperedPotion = { Type = "Potion", ID = 212265, Texture = 176108, Hidden = true },
    PotionofUnwaveringFocus = { Type = "Potion", ID = 212259, Texture = 176108, Hidden = true },
    FrontlinePotion = { Type = "Potion", ID = 212262, Texture = 176108, Hidden = true },
    AlgariManaPotion = { Type = "Potion", ID = 212241, Texture = 176108, Hidden = true },

    -- Auras
    ArenaPreparation = { ID = 32727, Hidden = true },

    -- Zerker (Queues for Scuzz)
    AsphyxiateFocus = { Type = "Spell", ID = 108194, Hidden = true, Texture = 260364 },
	DeathGripFocus = { Type = "Spell", ID = 49576, Hidden = true, Texture = 255654 },
}

local function createAction(attributes)
    return Action.Create({
        Type = attributes.Type or "Spell",
        ID = attributes.ID,
        Texture = attributes.Texture,
        FixedTexture = attributes.FixedTexture,
        Color = attributes.Color,
        Desc = attributes.Desc,
        MAKULU_INFO = attributes.MAKULU_INFO,
        Hidden = attributes.Hidden,
        QueueForbidden = attributes.QueueForbidden,
    })
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
	local result = {}
	for k, v in pairs(A) do
		result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
	end
	return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_DEATHKNIGHT_FROST] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local player = ConstUnit.player
local target = ConstUnit.target
local focus = ConstUnit.focus
local mouseover
local pet = ConstUnit.pet
local arena1 = ConstUnit.arena1
local arena2 = ConstUnit.arena2
local arena3 = ConstUnit.arena3
local party1 = ConstUnit.party1
local party2 = ConstUnit.party2

local gameState = {
    pooling = false,
    partyDanger = false,
    aliveEnemies = 0,
    healerAlive = false,
    rwBuffs = false,
    twoHandCheck = false,
    staticObliterateBuffs = false,
    breathRPCost = 0,
    staticRimeBuffs = false,
    breathRPThreshhold = 60,
    erwBreathRPTrigger = 70,
    erwBreathRuneTrigger = 3,
    oblitRunePooling = 4,
    breathRimeRPThreshhold = 60,
    stDnd = 1,
    playerRunes = 0,
    activeEnemies = 0,
    stPlanning = false,
    addsRemain = false,
    sendingCDs = false,
    useBreath = false,
    rimeBuffs = false,
    RPBuffs = false,
    cooldownCheck = false,
    trueBreathCooldown = 0,
    oblitPoolingTime = 0,
    breathPoolingTime = 0,
    poolingRunes = false,
    poolingRP = false,
    gaPriority = false,
    breathDying = false,
    fightRemains = 0,
    breathCooldown = 0,
    fwfBuffs = false,

}

local buffs = {
    empowerRuneWeapon = 47568,
    rime = 59052,
    pillarOfFrost = 51271,
    remorselesswinter = 196770,
    icyTalons = 194879,
    killingMachine = 51124,
    deathAndDecay = 188290,
    unleashedFrenzy = 376907,
    lichborne = 49039,
    antiMagicZone = 145629,
    antiMagicShell = 410358,
    darkSuccor = 178819,
    wraithWalk = 212552,
    deathsAdvance = 48265,
    mograineMight = 444047,
    exterminate = 441378,
    painfulDeath = 443564,
    aFeastOfSouls = 444072,
    breathOfSindragosa = 152279,
    unholyStrength = 53365,
    coldHeart = 281209,
    reaperOfSouls = 440002,
    gatheringStorm = 211805,

}

local debuffs = {
    frostFever = 55095,
    razorice = 51714,
    reapersMarkDebuff = 434765,
}

local talents = {
    cleavingstrikes = 316916,


}

local interrupts = {
    { spell = MindFreeze, isCC = false },
    { spell = Strangulate, isCC = false },
    { spell = DeathGrip, isCC = false },
    { spell = Asphyxiate, isCC = true },
    { spell = BlindingSleet, isCC = true, aoe = true },

}

local function num(val)
    if val then return 1 else return 0 end
end

Player:AddTier("Tier31", { 207261, 207262, 207263, 207264, 207266 })

local function AutoAntiMagicZonePvp()
    local checkDmgBuffsfromEnemy = {
        51271,   -- Pillar of Frost
        207289,  -- Unholy Assault
        323639,  -- The Hunt
        194223,  -- Celestial Alignment
        375087,  -- Dragonrage
        190319,  -- Combustion
        12472,   -- Icy Veins
        365350,  -- Arcane Surge
        31884,   -- Avenging Wrath
        2825,    -- Bloodlust
        114051,  -- Ascendance
        205180,  -- Summon Darkglare
        265187,  -- Summon Demonic Tyrant
		258925,  -- Fel Barrage
		357210,	 -- Deep Breath
    }

    for _, buffID in ipairs(checkDmgBuffsfromEnemy) do
        if target:HasBuff(buffID) then
            if imtarget() then
                if A.AntiMagicZone:IsReadyByPassCastGCD() then
                    return true
				end

                if A.AntiMagicShell:IsReadyByPassCastGCD() and A.AntiMagicZone:GetCooldown() > 5 and Unit(player):HealthPercent() <= 50 then
                    return true
                end
            end
            break
        end
    end

    return false
end

local runeMax = 6 -- The maximum number of runes

local function runeTimeTo(x)
    -- Validate input
    if not x or x <= 0 or x > runeMax then
        return 0
    end

    -- Get the current time
    local currentTime = GetTime()
    if not currentTime then
        return 0
    end

    -- Store the cooldown remaining for each rune
    local runeCooldowns = {}

    -- Fill the runeCooldowns table with the cooldown remaining for each rune
    for i = 1, runeMax do
        local start, duration, runeReady = GetRuneCooldown(i)
        if runeReady then
            runeCooldowns[i] = 0 -- Rune is ready
        elseif start and duration then
            runeCooldowns[i] = (start + duration) - currentTime -- Time remaining until the rune is ready
        else
            runeCooldowns[i] = 0 -- Default to 0 if we can't get rune info
        end
    end

    -- Sort the cooldowns in ascending order
    table.sort(runeCooldowns)

    -- Sum the cooldowns of the first `x` runes
    local timeRequired = 0
    for i = 1, x do
        if runeCooldowns[i] then
            timeRequired = math.max(timeRequired, runeCooldowns[i])
        end
    end

    return timeRequired or 0
end

local function shouldBurst()
    if A.BurstIsON("target") then
        --if A.Zone ~= "arena" then
        --    local activeEnemies = MultiUnits:GetActiveUnitPlates()
        --    for enemy in pairs(activeEnemies) do
        --        if ActionUnit(enemy):Health() > (A.FrostStrike:GetSpellDescription()[1] * 20) or target.isDummy or target.isBoss or target:IsPlayer() then
        --            return true
        --        end
        --    end
        --else
            return true
        --end
    end
    --return false
end

local cacheContext     = MakuluFramework.Cache

local constCell = cacheContext:getConstCacheCell()
local function enemiesInMelee()
    return constCell:GetOrSet("enemiesInMelee", function() 
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0

        for enemyGUID in pairs(activeEnemies) do -- Jack will fix our enemies check soon
            local enemy = MakUnit:new(enemyGUID) 
            if enemy.distance <= 10 and not enemy:IsTotem() and not enemy.isPet then  -- I haven't tested the new totem yet
                total = total + 1
            end 
        end  
        
        return total 
    end)
end


local function activeEnemies()
    return enemiesInMelee()
end

local function hasIncomingDamage()
    return incBigDmgIn() < 2000 or incModDmgIn() < 2000
end

local function defensiveActive()
    return player:BuffFrom(MakLists.Defensive) or UnitGetTotalAbsorbs("player") >= player.maxHealth * 0.15
end

local function shouldDefensive()
    local incomingDamage = hasIncomingDamage()

    return incomingDamage and not defensiveActive() 
end

local function partyDanger()
    local partyUnits = {party1, party2, player}

    for _, unit in ipairs(partyUnits) do
        if unit.exists and unit.hp > 0 and unit.hp < 50 and not unit:BuffFrom(MakLists.Defensive) then
            return true
        end
    end

    return false
end

local function enemiesAlive()
    local aliveEnemies = 0
    local healerAlive = false

    local arenaUnits = {arena1, arena2, arena3}

    for _, unit in ipairs(arenaUnits) do
        if unit.exists and unit.hp > 0 then
            aliveEnemies = aliveEnemies + 1
            if unit.isHealer then
                healerAlive = true
            end
        end
    end

    return aliveEnemies, healerAlive
end

local function updateGameState()
    -- Safety check for player object
    if not player or not player.exists then
        return
    end

    local function twoHandCheck()
        -- Get the item link for the main hand slot
        local itemLink = GetInventoryItemLink("player", INVSLOT_MAINHAND)

        -- Check if there's an item in the main hand slot
        if itemLink then
            -- Get item info using the item link
            local itemName, _, itemQuality, itemLevel, itemMinLevel, itemType, itemSubType, itemStackCount,
                  itemEquipLoc, itemTexture, sellPrice, classID, subclassID = GetItemInfo(itemLink)

            if classID and subclassID then
                -- List of 2H weapon subclass IDs (as of current WoW version)
                local twoHandedWeapons = {
                    [1] = true,  -- Two-Handed Axes
                    [5] = true,  -- Two-Handed Maces
                    [8] = true,  -- Two-Handed Swords
                    [10] = true, -- Polearms
                    [6] = true,  -- Staves
                }

                -- Check if the item is a weapon and is one of the 2H weapon subclass IDs
                if classID == LE_ITEM_CLASS_WEAPON and twoHandedWeapons[subclassID] then
                    return true
                end
            end
        end
        return false
    end

    --# Variables (Fixed) - Updated to match new APL
    -- variable,name=rw_buffs,value=talent.gathering_storm|talent.biting_cold
    gameState.rwBuffs = A.GatheringStorm:IsTalentLearned() or A.BitingCold:IsTalentLearned()
    gameState.twoHandCheck = twoHandCheck()
    gameState.staticObliterateBuffs = A.ArcticAssault:IsTalentLearned() or A.FrigidExecutioner:IsTalentLearned() or gameState.twoHandCheck
    -- variable,name=breath_rp_cost,value=dbc.power.9067.cost_per_tick%10
    gameState.breathRPCost = 17
    -- variable,name=static_rime_buffs,value=talent.rage_of_the_frozen_champion|talent.icebreaker|talent.bind_in_darkness
    gameState.staticRimeBuffs = A.RageOfTheFrozenChampion:IsTalentLearned() or A.Icebreaker:IsTalentLearned()
    -- APL Variable Options (these would normally be configurable)
    gameState.breathRPThreshhold = 60
    gameState.erwBreathRPTrigger = 70
    gameState.erwBreathRuneTrigger = 3
    gameState.oblitRunePooling = 4
    gameState.breathRimeRPThreshhold = 60
    gameState.stDnd = 1
    gameState.playerRunes = player.rune

    --# Variables (Live) - Updated to match new APL logic
    gameState.activeEnemies = activeEnemies()
    -- For PvE, we simplify the planning logic since we don't care about raid events
    gameState.stPlanning = activeEnemies() == 1
    gameState.addsRemain = activeEnemies() >= 2
    gameState.sendingCDs = gameState.stPlanning or gameState.addsRemain
    -- variable,name=use_breath,value=variable.st_planning|active_enemies>=2&(!raid_event.adds.exists|!raid_event.pull.exists&raid_event.adds.remains>15|(raid_event.pull.exists&!raid_event.adds.has_boss&raid_event.adds.remains>30))
    gameState.useBreath = gameState.stPlanning or activeEnemies() >= 2
    -- variable,name=rime_buffs,value=buff.rime.react&(variable.static_rime_buffs|talent.avalanche&!talent.arctic_assault&debuff.razorice.stack<5)
    local targetRazoriceStacks = (target and target.exists) and target:HasDeBuffCount(debuffs.razorice) or 0
    gameState.rimeBuffs = player:HasBuff(buffs.rime) and (gameState.staticRimeBuffs or (A.Avalanche:IsTalentLearned() and not A.ArcticAssault:IsTalentLearned() and targetRazoriceStacks < 5))
    -- variable,name=rp_buffs,value=talent.unleashed_frenzy&(buff.unleashed_frenzy.remains<gcd.max*3|buff.unleashed_frenzy.stack<3)|talent.icy_talons&(buff.icy_talons.remains<gcd.max*3|buff.icy_talons.stack<(3+(2*talent.smothering_offense)))
    gameState.RPBuffs = (A.UnleashedFrenzy:IsTalentLearned() and (player:BuffRemains(buffs.unleashedFrenzy) < Action.GetGCD() * 3 or player:HasBuffCount(buffs.unleashedFrenzy) < 3)) or (A.IcyTalons:IsTalentLearned() and (player:BuffRemains(buffs.icyTalons) < Action.GetGCD() * 3 or player:HasBuffCount(buffs.icyTalons) < (3 + (2 * num(A.SmotheringOffense:IsTalentLearned())))))
    -- Simplified cooldown check for PvE
    gameState.cooldownCheck = (not A.BreathofSindragosa:IsTalentLearned() or player:Buff(buffs.breathOfSindragosa)) and ((A.PillarofFrost:IsTalentLearned() and player:Buff(buffs.pillarOfFrost) and ((A.Obliteration:IsTalentLearned() and player:BuffRemains(buffs.pillarOfFrost) > 10000) or not A.Obliteration:IsTalentLearned())) or (not A.PillarofFrost:IsTalentLearned() and player:Buff(buffs.empowerRuneWeapon)) or (not A.PillarofFrost:IsTalentLearned() and not A.EmpowerRuneWeapon:IsTalentLearned()) or (activeEnemies() >= 2 and player:Buff(buffs.pillarOfFrost)))
    -- variable,name=true_breath_cooldown,op=setif,value=cooldown.breath_of_sindragosa.remains,value_else=cooldown.pillar_of_frost.remains,condition=cooldown.breath_of_sindragosa.remains>cooldown.pillar_of_frost.remains
    local breathCD = A.BreathofSindragosa:GetCooldown() or 0
    local pillarCD = A.PillarofFrost:GetCooldown() or 0
    if breathCD > pillarCD then
        gameState.trueBreathCooldown = breathCD
    else
        gameState.trueBreathCooldown = pillarCD
    end
    -- variable,name=oblit_pooling_time,op=setif,value=((cooldown.pillar_of_frost.remains+1)%gcd.max)%(rune+1)*6,value_else=5,condition=rune<variable.oblit_rune_pooling&cooldown.pillar_of_frost.remains<10
    if player and player.rune and player.rune < gameState.oblitRunePooling and A.PillarofFrost:GetCooldown() < 10000 then
        local gcd = Action.GetGCD()
        if gcd and gcd > 0 and (player.rune + 1) > 0 then
            gameState.oblitPoolingTime = ((A.PillarofFrost:GetCooldown() + 1000) / gcd) / (player.rune + 1) * 6000
        else
            gameState.oblitPoolingTime = 5000
        end
    else
        gameState.oblitPoolingTime = 5000
    end
    -- variable,name=breath_pooling_time,op=setif,value=((variable.true_breath_cooldown+1)%gcd.max)%((rune+1)*(runic_power+20))*100,value_else=0,condition=runic_power.deficit>10&variable.true_breath_cooldown<10
    if player and player.runicPowerDeficit and player.runicPowerDeficit > 10 and gameState.trueBreathCooldown < 10000 then
        local gcd = Action.GetGCD()
        local denominator = (player.rune + 1) * (player.runicPower + 20)
        if gcd and gcd > 0 and denominator > 0 then
            gameState.breathPoolingTime = ((gameState.trueBreathCooldown + 1000) / gcd) / denominator * 100
        else
            gameState.breathPoolingTime = 0
        end
    else
        gameState.breathPoolingTime = 0
    end
    -- variable,name=pooling_runes,value=rune<variable.oblit_rune_pooling&talent.obliteration&cooldown.pillar_of_frost.remains<variable.oblit_pooling_time
    gameState.poolingRunes = player and player.rune and player.rune < gameState.oblitRunePooling and A.Obliteration:IsTalentLearned() and A.PillarofFrost:GetCooldown() < gameState.oblitPoolingTime
    -- variable,name=pooling_runic_power,value=talent.breath_of_sindragosa&(variable.true_breath_cooldown<variable.breath_pooling_time|fight_remains<30&!cooldown.breath_of_sindragosa.remains)
    gameState.poolingRP = A.BreathofSindragosa:IsTalentLearned() and (gameState.trueBreathCooldown < gameState.breathPoolingTime or (gameState.fightRemains < 30000 and A.BreathofSindragosa:GetCooldown() == 0))
    -- variable,name=ga_priority,value=(!talent.shattered_frost&talent.shattering_blade&active_enemies>=4)|(!talent.shattered_frost&!talent.shattering_blade&active_enemies>=2)
    gameState.gaPriority = (not A.ShatteredFrost:IsTalentLearned() and A.ShatteringBlade:IsTalentLearned() and activeEnemies() >= 4) or (not A.ShatteredFrost:IsTalentLearned() and not A.ShatteringBlade:IsTalentLearned() and activeEnemies() >= 2)
    -- variable,name=breath_dying,value=runic_power<variable.breath_rp_cost*2*gcd.max&rune.time_to_2>runic_power%variable.breath_rp_cost
    if player and player.runicPower and gameState.breathRPCost and gameState.breathRPCost > 0 then
        local runeTime = runeTimeTo(2)
        local rpRatio = player.runicPower / gameState.breathRPCost
        gameState.breathDying = player.runicPower < gameState.breathRPCost * 2 * Action.GetGCD() and runeTime > rpRatio
    else
        gameState.breathDying = false
    end
    gameState.fightRemains = 999999 -- placeholder for fight_remains
    -- variable,name=fwf_buffs,value=(buff.pillar_of_frost.remains<gcd.max|(buff.unholy_strength.up&buff.unholy_strength.remains<gcd.max)|(talent.bonegrinder.rank=2&buff.bonegrinder_frost.up&buff.bonegrinder_frost.remains<gcd.max))&(active_enemies>1|debuff.razorice.stack=5|!death_knight.runeforge.razorice&(!talent.glacial_advance|!talent.avalanche|!talent.arctic_assault)|talent.shattering_blade)
    local targetRazoriceStacks2 = (target and target.exists) and target:HasDeBuffCount(debuffs.razorice) or 0
    gameState.fwfBuffs = (player:BuffRemains(buffs.pillarOfFrost) < Action.GetGCD() or (player:Buff(buffs.unholyStrength) and player:BuffRemains(buffs.unholyStrength) < Action.GetGCD())) and (activeEnemies() > 1 or targetRazoriceStacks2 == 5 or (not A.GlacialAdvance:IsTalentLearned() or not A.Avalanche:IsTalentLearned() or not A.ArcticAssault:IsTalentLearned()) or A.ShatteringBlade:IsTalentLearned())

end

WraithWalk:Callback(function(spell)
    local locData = C_LossOfControl.GetActiveLossOfControlData(1)
    if not locData then return end
    if locData.locType ~= "ROOT" then return end

    return spell:Cast(player)
end)

local deathsAdvanceList = {
    204666, -- Shattered Earth (Oakheart)
    199345, -- Down Draft (Dresaron)
}

DeathsAdvance:Callback(function(spell)
    if MultiUnits:GetByRangeCasting(nil, 1, nil, deathsAdvanceList) == 0 then return end

    return spell:Cast(player)
end)

Lichborne:Callback(function(spell)
    if not A.UnholyEndurance:IsTalentLearned() then return end
    if not shouldDefensive() then return end

    return spell:Cast(player)
end)

IceboundFortitude:Callback(function(spell)
    if not shouldDefensive() then return end

    return spell:Cast(player)
end)

AntiMagicShell:Callback(function(spell)
    if not shouldDefensive() then return end

    return spell:Cast(player)
end)

DeathPact:Callback(function(spell)
    if player.hp > A.GetToggle(2, "DeathPactSlider") then return end
    if not player.inCombat then return end

    return spell:Cast(player)
end)

DeathStrike:Callback(function(spell)
    if player.hp > A.GetToggle(2, "DeathStrikeSlider") then return end

    return spell:Cast(target)
end)

RaiseDead:Callback(function(spell)
    if player.combatTime <= 0 then return end
    if pet.exists and pet.hp > 0 then return end

    return spell:Cast(player)
end)

--###################################### AOE ######################################

-- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice+((hero_tree.deathbringer&debuff.reapers_mark_debuff.up)*5),if=buff.killing_machine.react&talent.cleaving_strikes&buff.death_and_decay.up
Obliterate:Callback("aoe", function(spell)
    if not player:Buff(buffs.killingMachine) then return end
    if not A.CleavingStrikes:IsTalentLearned() then return end
    if not player:Buff(buffs.deathAndDecay) then return end

    return spell:Cast(target)
end)

-- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=!variable.pooling_runic_power&debuff.razorice.stack=5&talent.shattering_blade&(talent.shattered_frost|active_enemies<4)
FrostStrike:Callback("aoe", function(spell)
    if gameState.poolingRP then return end
    if target:HasDeBuffCount(debuffs.razorice) ~= 5 then return end
    if not A.ShatteringBlade:IsTalentLearned() then return end
    if not (A.ShatteredFrost:IsTalentLearned() or activeEnemies() < 4) then return end

    return spell:Cast(target)
end)

-- howling_blast,if=buff.rime.react
HowlingBlast:Callback("aoe", function(spell)
    if not player:HasBuff(buffs.rime) then return end

    return spell:Cast(target)
end)

-- glacial_advance,target_if=max:(debuff.razorice.stack),if=!variable.pooling_runic_power&(variable.ga_priority|debuff.razorice.stack<5)
GlacialAdvance:Callback("aoe", function(spell)
    if gameState.poolingRP then return end
    if not (gameState.gaPriority or target:HasDeBuffCount(debuffs.razorice) < 5) then return end

    return spell:Cast(target)
end)

-- obliterate
Obliterate:Callback("aoe2", function(spell)
    return spell:Cast(target)
end)

-- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=!variable.pooling_runic_power
FrostStrike:Callback("aoe2", function(spell)
    if gameState.poolingRP then return end

    return spell:Cast(target)
end)

-- horn_of_winter,if=rune<2&runic_power.deficit>25&(!talent.breath_of_sindragosa|variable.true_breath_cooldown>cooldown.horn_of_winter.duration-15)
HornofWinter:Callback("aoe", function(spell)
    if player.rune >= 2 then return end
    if player.runicPowerDeficit <= 25 then return end
    if not (not A.BreathofSindragosa:IsTalentLearned() or gameState.trueBreathCooldown > (A.HornofWinter:GetCooldown() - 15000)) then return end

    return spell:Cast(player)
end)

-- arcane_torrent,if=runic_power.deficit>25
ArcaneTorrent:Callback("aoe", function(spell)
    if player.runicPowerDeficit <= 25 then return end

    return spell:Cast(player)
end)

-- abomination_limb,if=variable.sending_cds
AbominationLimb:Callback("aoe", function(spell)
    if not gameState.sendingCDs then return end
    return spell:Cast(player)
end)

local function aoe()
    -- Abomination Limb first for burst damage
    AbominationLimb("aoe")

    -- Priority abilities with buffs
    Obliterate("aoe")
    FrostStrike("aoe")
    HowlingBlast("aoe")

    -- Resource management and filler
    GlacialAdvance("aoe")
    Obliterate("aoe2")
    FrostStrike("aoe2")
    HornofWinter("aoe")
    ArcaneTorrent("aoe")
end

--##################################### BREATH ACTIVE ######################################

-- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice+((hero_tree.deathbringer&debuff.reapers_mark_debuff.up)*5),if=buff.killing_machine.react&buff.pillar_of_frost.up
Obliterate:Callback("breath", function(spell)
    if not player:Buff(buffs.killingMachine) then return end
    if not player:Buff(buffs.pillarOfFrost) then return end

    return spell:Cast(target)
end)

-- howling_blast,if=(variable.rime_buffs|!buff.killing_machine.react&buff.pillar_of_frost.up&talent.obliteration)&runic_power>(variable.breath_rime_rp_threshold-(talent.rage_of_the_frozen_champion*(dbc.effect.842306.base_value%10)))|!dot.frost_fever.ticking
HowlingBlast:Callback("breath", function(spell)
    if (gameState.rimeBuffs or (not player:Buff(buffs.killingMachine) and player:Buff(buffs.pillarOfFrost) and A.Obliteration:IsTalentLearned())) and player.runicPower > (gameState.breathRimeRPThreshhold - (num(A.RageOfTheFrozenChampion:IsTalentLearned()) * 10)) then
        return spell:Cast(target)
    end
    if not target:HasDeBuff(debuffs.frostFever) then
        return spell:Cast(target)
    end
end)

-- horn_of_winter,if=rune<2&runic_power.deficit>30&(!buff.empower_rune_weapon.up|runic_power<variable.breath_rp_cost*2*gcd.max)
HornofWinter:Callback("breath", function(spell)
    if player.rune >= 2 then return end
    if player.runicPowerDeficit <= 30 then return end
    if not (not player:Buff(buffs.empowerRuneWeapon) or player.runicPower < gameState.breathRPCost * 2 * Action.GetGCD()) then return end

    return spell:Cast(player)
end)

-- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice+((hero_tree.deathbringer&debuff.reapers_mark_debuff.up)*5),if=buff.killing_machine.react|runic_power.deficit>20
Obliterate:Callback("breath2", function(spell)
    if not (player:Buff(buffs.killingMachine) or player.runicPowerDeficit > 20) then return end

    return spell:Cast(target)
end)

-- soul_reaper,if=fight_remains>5&target.time_to_pct_35<5&target.time_to_pct_0>5&active_enemies<=1&rune>2
SoulReaper:Callback("breath", function(spell)
    if gameState.fightRemains <= 5000 then return end
    if target.hp > 35 then return end
    if target.hp <= 0 then return end
    if activeEnemies() > 1 then return end
    if player.rune <= 2 then return end

    return spell:Cast(target)
end)

-- howling_blast,if=variable.breath_dying
HowlingBlast:Callback("breath2", function(spell)
    if not gameState.breathDying then return end

    return spell:Cast(target)
end)

-- arcane_torrent,if=runic_power<60
ArcaneTorrent:Callback("breath", function(spell)
    if player.runicPower >= 60 then return end

    return spell:Cast(player)
end)

-- howling_blast,if=buff.rime.react
HowlingBlast:Callback("breath3", function(spell)
    if not player:Buff(buffs.rime) then return end

    return spell:Cast(target)
end)

--actions.breath+=/arcane_torrent,if=runic_power<60
ArcaneTorrent:Callback("breath", function(spell)
    if player.runicPower >= 60 then return end

    return spell:Cast(player)
end)


--actions.breath+=/howling_blast,if=buff.rime.react
HowlingBlast:Callback("breath2", function(spell)
    if not player:HasBuff(buffs.rime) then return end

    return spell:Cast(target)
end)

local function breath()
    Obliterate("breath")
    HowlingBlast("breath")
    HornofWinter("breath")
    Obliterate("breath2")
    SoulReaper("breath")
    HowlingBlast("breath2")
    ArcaneTorrent("breath")
    HowlingBlast("breath3")
end

--######################################## COLDHEART #######################################

--actions.cold_heart=chains_of_ice,if=fight_remains<gcd&(rune<2|!buff.killing_machine.up&(!variable.2h_check&buff.cold_heart.stack>=4|variable.2h_check&buff.cold_heart.stack>8)|buff.killing_machine.up&(!variable.2h_check&buff.cold_heart.stack>8|variable.2h_check&buff.cold_heart.stack>10))
ChainsofIce:Callback("coldHeart", function(spell)
    if gameState.fightRemains < MakGcd() and (player.rune < 2 or not player:buff(buffs.killingMachine and (not gameState.twoHandCheck and player:HasBuffCount(buffs.coldHeart) >= 4 or gameState.twoHandCheck and player:HasBuffCount(buffs.coldHeart) > 8))) or (player:buff(buffs.killingMachine) and (not gameState.twoHandCheck and player:HasBuffCount(buffs.coldHeart) > 8 or gameState.twoHandCheck and player:HasBuffCount(buffs.coldHeart) > 10)) then
        return spell:Cast(target)
    end

end)

--actions.cold_heart+=/chains_of_ice,if=!talent.obliteration&buff.pillar_of_frost.up&buff.cold_heart.stack>=10&(buff.pillar_of_frost.remains<gcd*(1+(talent.frostwyrms_fury&cooldown.frostwyrms_fury.ready))|buff.unholy_strength.up&buff.unholy_strength.remains<gcd)
ChainsofIce:Callback("coldHeart2", function(spell)
    if A.Obliteration:IsTalentLearned() then return end
    if not player:Buff(buffs.pillarOfFrost) then return end
    if player:HasBuffCount(buffs.coldHeart) < 10 then return end
    if player:BuffRemains(buffs.pillarOfFrost) < MakGcd() * (1 + num(A.FrostwyrmsFury:IsTalentLearned() and A.FrostwyrmsFury:GetCooldown() == 0)) or player:BuffRemains(buffs.unholyStrength) < MakGcd() or (player:Buff(buffs.unholyStrength and player:BuffRemains(buffs.unholyStrength < MakGcd()))) then return end

    return spell:Cast(target)
end)

--actions.cold_heart+=/chains_of_ice,if=!talent.obliteration&death_knight.runeforge.fallen_crusader&!buff.pillar_of_frost.up&cooldown.pillar_of_frost.remains_expected>15&(buff.cold_heart.stack>=10&buff.unholy_strength.up|buff.cold_heart.stack>=13)
--enchant check add
ChainsofIce:Callback("coldHeart3", function(spell)
    if not IsPlayerSpell(A.Obliteration.ID) and not player:Buff(buffs.pillarOfFrost) and PillarofFrost:Cooldown() > 15000 and (player:HasBuffCount(buffs.coldHeart) >= 10 and (player:Buff(buffs.unholyStrength) or player:HasBuffCount(buffs.coldHeart) >= 13)) then
        return spell:Cast(target)
    end
end)

--actions.cold_heart+=/chains_of_ice,if=!talent.obliteration&!death_knight.runeforge.fallen_crusader&buff.cold_heart.stack>=10&!buff.pillar_of_frost.up&cooldown.pillar_of_frost.remains_expected>20
ChainsofIce:Callback("coldHeart4", function(spell)
    if not IsPlayerSpell(A.Obliteration.ID) and player:HasBuffCount(buffs.coldHeart) >= 10 and not player:Buff(buffs.pillarOfFrost) and PillarofFrost:Cooldown() > 20000 then
        return spell:Cast(target)
    end
end)

--actions.cold_heart+=/chains_of_ice,if=talent.obliteration&!buff.pillar_of_frost.up&(buff.cold_heart.stack>=14&buff.unholy_strength.up|buff.cold_heart.stack>=19|cooldown.pillar_of_frost.remains_expected<3&buff.cold_heart.stack>=14)
ChainsofIce:Callback("coldHeart5", function(spell)
    if IsPlayerSpell(A.Obliteration.ID) and not player:Buff(buffs.pillarOfFrost) and (player:HasBuffCount(buffs.coldHeart) >= 14 and (player:Buff(buffs.unholyStrength) or player:HasBuffCount(buffs.coldHeart) >= 19 or PillarofFrost:Cooldown() < 3000) and player:HasBuffCount(buffs.coldHeart) >= 14) then
        return spell:Cast(target)
    end
end)

local function coldHeart()
    ChainsofIce("coldHeart")
    ChainsofIce("coldHeart2")
    ChainsofIce("coldHeart3")
    ChainsofIce("coldHeart4")
    ChainsofIce("coldHeart5")
end

--##################################### OBLITERATION ######################################

-- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice+((hero_tree.deathbringer&debuff.reapers_mark_debuff.up)*5),if=buff.killing_machine.react&(buff.exterminate.up|fight_remains<gcd*2)
Obliterate:Callback("obliteration", function(spell)
    if not player:Buff(buffs.killingMachine) then return end
    if not (player:Buff(buffs.exterminate) or gameState.fightRemains < Action.GetGCD() * 2) then return end

    return spell:Cast(target)
end)

-- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=debuff.razorice.stack=5&talent.shattering_blade&talent.a_feast_of_souls&buff.a_feast_of_souls.up
FrostStrike:Callback("obliteration", function(spell)
    if target:HasDeBuffCount(debuffs.razorice) ~= 5 then return end
    if not A.ShatteringBlade:IsTalentLearned() then return end
    if not A.AFeastofSouls:IsTalentLearned() then return end
    if not player:Buff(buffs.aFeastOfSouls) then return end

    return spell:Cast(target)
end)

-- soul_reaper,if=fight_remains>5&target.time_to_pct_35<5&target.time_to_pct_0>5&active_enemies<=1&rune>2&!buff.killing_machine.react
SoulReaper:Callback("obliteration", function(spell)
    if gameState.fightRemains <= 5000 then return end
    if target.hp > 35 then return end
    if target.hp <= 0 then return end
    if gameState.activeEnemies > 1 then return end
    if player.rune <= 2 then return end
    if player:Buff(buffs.killingMachine) then return end

    return spell:Cast(target)
end)

-- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=buff.killing_machine.react
Obliterate:Callback("obliteration2", function(spell)
    if not player:Buff(buffs.killingMachine) then return end

    return spell:Cast(target)
end)

-- glacial_advance,target_if=max:(debuff.razorice.stack),if=(variable.ga_priority|debuff.razorice.stack<5)&(!death_knight.runeforge.razorice&(debuff.razorice.stack<5|debuff.razorice.remains<gcd*3)|((variable.rp_buffs|rune<2)&active_enemies>1))
GlacialAdvance:Callback("obliteration", function(spell)
    if not (gameState.gaPriority or target:HasDeBuffCount(debuffs.razorice) < 5) then return end
    if not (target:HasDeBuffCount(debuffs.razorice) < 5 or target:DeBuffRemains(debuffs.razorice) < Action.GetGCD() * 3 or ((gameState.RPBuffs or player.rune < 2) and gameState.activeEnemies > 1)) then return end

    return spell:Cast(target)
end)

-- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=(rune<2|variable.rp_buffs|debuff.razorice.stack=5&talent.shattering_blade)&(!talent.glacial_advance|active_enemies=1|talent.shattered_frost)
FrostStrike:Callback("obliteration2", function(spell)
    if not (player.rune < 2 or gameState.RPBuffs or (target:HasDeBuffCount(debuffs.razorice) == 5 and A.ShatteringBlade:IsTalentLearned())) then return end
    if not (not A.GlacialAdvance:IsTalentLearned() or gameState.activeEnemies == 1 or A.ShatteredFrost:IsTalentLearned()) then return end

    return spell:Cast(target)
end)

-- howling_blast,if=buff.rime.react
HowlingBlast:Callback("obliteration", function(spell)
    if not player:Buff(buffs.rime) then return end

    return spell:Cast(target)
end)

-- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=!talent.glacial_advance|active_enemies=1|talent.shattered_frost
FrostStrike:Callback("obliteration3", function(spell)
    if not (not A.GlacialAdvance:IsTalentLearned() or gameState.activeEnemies == 1 or A.ShatteredFrost:IsTalentLearned()) then return end

    return spell:Cast(target)
end)

-- glacial_advance,target_if=max:(debuff.razorice.stack),if=variable.ga_priority
GlacialAdvance:Callback("obliteration2", function(spell)
    if not gameState.gaPriority then return end

    return spell:Cast(target)
end)

-- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice
FrostStrike:Callback("obliteration4", function(spell)
    return spell:Cast(target)
end)

-- horn_of_winter,if=rune<3
HornofWinter:Callback("obliteration", function(spell)
    if player.rune >= 3 then return end

    return spell:Cast(player)
end)

-- arcane_torrent,if=rune<1&runic_power<30
ArcaneTorrent:Callback("obliteration", function(spell)
    if player.rune >= 1 then return end
    if player.runicPower >= 30 then return end

    return spell:Cast(player)
end)

-- howling_blast,if=!buff.killing_machine.react
HowlingBlast:Callback("obliteration2", function(spell)
    if player:Buff(buffs.killingMachine) then return end

    return spell:Cast(target)
end)

local function obliteration()
    Obliterate("obliteration")
    FrostStrike("obliteration")
    SoulReaper("obliteration")
    Obliterate("obliteration2")
    GlacialAdvance("obliteration")
    FrostStrike("obliteration2")
    HowlingBlast("obliteration")
    FrostStrike("obliteration3")
    GlacialAdvance("obliteration2")
    FrostStrike("obliteration4")
    HornofWinter("obliteration")
    ArcaneTorrent("obliteration")
    HowlingBlast("obliteration2")
end

--##################################### RACIALS ######################################

-- blood_fury,use_off_gcd=1,if=variable.cooldown_check
BloodFury:Callback("racials", function(spell)
    if not gameState.cooldownCheck then return end

    return spell:Cast(player)
end)

-- berserking,use_off_gcd=1,if=variable.cooldown_check
Berserking:Callback("racials", function(spell)
    if not gameState.cooldownCheck then return end

    return spell:Cast(player)
end)

-- arcane_pulse,if=variable.cooldown_check
ArcanePulse:Callback("racials", function(spell)
    if not gameState.cooldownCheck then return end

    return spell:Cast(player)
end)

-- lights_judgment,if=variable.cooldown_check
LightsJudgment:Callback("racials", function(spell)
    if not gameState.cooldownCheck then return end

    return spell:Cast(target)
end)

-- ancestral_call,use_off_gcd=1,if=variable.cooldown_check
AncestralCall:Callback("racials", function(spell)
    if not gameState.cooldownCheck then return end

    return spell:Cast(player)
end)

-- fireblood,use_off_gcd=1,if=variable.cooldown_check
Fireblood:Callback("racials", function(spell)
    if not gameState.cooldownCheck then return end

    return spell:Cast(player)
end)

-- bag_of_tricks,if=talent.obliteration&!buff.pillar_of_frost.up&buff.unholy_strength.up
BagOfTricks:Callback("racials", function(spell)
    if not A.Obliteration:IsTalentLearned() then return end
    if player:Buff(buffs.pillarOfFrost) then return end
    if not player:Buff(buffs.unholyStrength) then return end

    return spell:Cast(target)
end)

-- bag_of_tricks,if=!talent.obliteration&buff.pillar_of_frost.up&(buff.unholy_strength.up&buff.unholy_strength.remains<gcd*3|buff.pillar_of_frost.remains<gcd*3)
BagOfTricks:Callback("racials2", function(spell)
    if A.Obliteration:IsTalentLearned() then return end
    if not player:Buff(buffs.pillarOfFrost) then return end
    if not ((player:Buff(buffs.unholyStrength) and player:BuffRemains(buffs.unholyStrength) < Action.GetGCD() * 3) or player:BuffRemains(buffs.pillarOfFrost) < Action.GetGCD() * 3) then return end

    return spell:Cast(target)
end)

local function racials()
    BloodFury("racials")
    Berserking("racials")
    ArcanePulse("racials")
    LightsJudgment("racials")
    AncestralCall("racials")
    Fireblood("racials")
    BagOfTricks("racials")
    BagOfTricks("racials2")
end

--##################################### COOLDOWNS ######################################

-- remorseless_winter,if=variable.rw_buffs&(cooldown.pillar_of_frost.remains>8|cooldown.pillar_of_frost.remains<2|buff.pillar_of_frost.up|!talent.pillar_of_frost|(buff.gathering_storm.stack=10&buff.remorseless_winter.remains<gcd.max))&fight_remains>10
-- Updated to use on CD but hold if Pillar of Frost is about to come off CD (within 8 seconds but more than 2 seconds)
-- REMOVED Arctic Assault restriction when Pillar of Frost is up to allow usage during burst
RemorselessWinter:Callback("cooldowns", function(spell)
    if not gameState.rwBuffs then return end
    if A.FrozenDominion:IsTalentLearned() then return end
    -- Removed the Arctic Assault + Pillar of Frost restriction that was blocking usage during burst

    local pillarCD = A.PillarofFrost:GetCooldown()
    local gcd = Action.GetGCD()

    -- Use RW if:
    -- 1. Pillar CD > 8 seconds (safe to use RW)
    -- 2. Pillar CD < 2 seconds (about to be available, can combo)
    -- 3. Pillar is already up (ALWAYS use during burst window)
    -- 4. Don't have Pillar talent
    -- 5. Gathering Storm at 10 stacks and RW about to expire
    local condition1 = pillarCD > 8000  -- Safe to use, Pillar won't be ready soon
    local condition2 = pillarCD < 2000  -- Pillar almost ready, can combo
    local condition3 = player:Buff(buffs.pillarOfFrost)  -- Already in burst window - ALWAYS USE
    local condition4 = not A.PillarofFrost:IsTalentLearned()  -- No Pillar talent
    local condition5 = A.GatheringStorm:IsTalentLearned() and player:HasBuffCount(buffs.gatheringStorm) == 10 and player:BuffRemains(buffs.remorselesswinter) < gcd

    if not (condition1 or condition2 or condition3 or condition4 or condition5) then return end
    if gameState.fightRemains <= 10000 then return end

    return spell:Cast(player)
end)

-- chill_streak,if=variable.sending_cds&(!talent.arctic_assault|!buff.pillar_of_frost.up)
ChillStreak:Callback("cooldowns", function(spell)
    if not gameState.sendingCDs then return end
    if A.ArcticAssault:IsTalentLearned() and player:Buff(buffs.pillarOfFrost) then return end

    return spell:Cast(target)
end)


-- empower_rune_weapon,if=talent.obliteration&!talent.breath_of_sindragosa&buff.pillar_of_frost.up|fight_remains<20
EmpowerRuneWeapon:Callback("cooldowns", function(spell)
    if A.EmpowerRuneWeapon:GetCooldown() > 0 then return end
    if not (A.Obliteration:IsTalentLearned() and not A.BreathofSindragosa:IsTalentLearned() and player:Buff(buffs.pillarOfFrost)) then
        if gameState.fightRemains >= 20000 then return end
    end

    return spell:Cast(player)
end)

-- empower_rune_weapon,if=buff.breath_of_sindragosa.up&(runic_power<40|runic_power<variable.erw_breath_rp_trigger&rune<variable.erw_breath_rune_trigger)
EmpowerRuneWeapon:Callback("cooldowns2", function(spell)
    if A.EmpowerRuneWeapon:GetCooldown() > 0 then return end
    if not player:Buff(buffs.breathOfSindragosa) then return end
    if not (player.runicPower < 40 or (player.runicPower < gameState.erwBreathRPTrigger and player.rune < gameState.erwBreathRuneTrigger)) then return end

    return spell:Cast(player)
end)

-- empower_rune_weapon,if=!talent.breath_of_sindragosa&!talent.obliteration&!buff.empower_rune_weapon.up&rune<5&(cooldown.pillar_of_frost.remains_expected<7|buff.pillar_of_frost.up|!talent.pillar_of_frost)
EmpowerRuneWeapon:Callback("cooldowns3", function(spell)
    if A.EmpowerRuneWeapon:GetCooldown() > 0 then return end
    if not (not A.BreathofSindragosa:IsTalentLearned() and not A.Obliteration:IsTalentLearned() and not player:Buff(buffs.empowerRuneWeapon) and player.rune < 5) then return end
    if not (A.PillarofFrost:GetCooldown() < 7000 or player:Buff(buffs.pillarOfFrost) or not A.PillarofFrost:IsTalentLearned()) then return end

    return spell:Cast(player)
end)

-- pillar_of_frost,if=talent.obliteration&!talent.breath_of_sindragosa&(!hero_tree.deathbringer|(rune>=2|(rune>=1&cooldown.empower_rune_weapon.ready)))&variable.sending_cds|fight_remains<20
PillarofFrost:Callback("cooldowns", function(spell)
    if A.Obliteration:IsTalentLearned() and not A.BreathofSindragosa:IsTalentLearned() then
        -- Check deathbringer hero tree conditions (simplified for PvE)
        if not (player.rune >= 2 or (player.rune >= 1 and A.EmpowerRuneWeapon:GetCooldown() == 0)) then return end
        if not gameState.sendingCDs then return end
    elseif gameState.fightRemains >= 20000 then
        return
    end

    return spell:Cast(player)
end)

-- pillar_of_frost,if=talent.breath_of_sindragosa&variable.sending_cds&(cooldown.breath_of_sindragosa.remains>10|!variable.use_breath)&buff.unleashed_frenzy.up&(!hero_tree.deathbringer|rune>1)
PillarofFrost:Callback("cooldowns2", function(spell)
    if not A.BreathofSindragosa:IsTalentLearned() then return end
    if not gameState.sendingCDs then return end
    if not (A.BreathofSindragosa:GetCooldown() > 10000 or not gameState.useBreath) then return end
    if not player:Buff(buffs.unleashedFrenzy) then return end
    -- Simplified deathbringer check
    if player.rune <= 1 then return end

    return spell:Cast(player)
end)

-- pillar_of_frost,if=!talent.obliteration&!talent.breath_of_sindragosa&variable.sending_cds
PillarofFrost:Callback("cooldowns3", function(spell)
    if A.Obliteration:IsTalentLearned() then return end
    if A.BreathofSindragosa:IsTalentLearned() then return end
    if not gameState.sendingCDs then return end

    return spell:Cast(player)
end)

-- reapers_mark,target_if=first:debuff.reapers_mark_debuff.down,if=buff.pillar_of_frost.up|cooldown.pillar_of_frost.remains>5|fight_remains<20
ReapersMark:Callback("cooldowns", function(spell)
    if target:HasDeBuff(debuffs.reapersMarkDebuff) then return end
    if not (player:Buff(buffs.pillarOfFrost) or A.PillarofFrost:GetCooldown() > 5000 or gameState.fightRemains < 20000) then return end

    return spell:Cast(target)
end)

-- breath_of_sindragosa,use_off_gcd=1,if=!buff.breath_of_sindragosa.up&runic_power>=variable.breath_rp_threshold&(rune<2|runic_power>80)&(cooldown.pillar_of_frost.remains<gcd.max&variable.use_breath|fight_remains<30)|(time<10&rune<=1)me<10&rune<1)
BreathofSindragosa:Callback("cooldowns", function(spell)
    if player:Buff(buffs.breathOfSindragosa) then return end
    if not (player.runicPower >= gameState.breathRPThreshhold and (player.rune < 2 or player.runicPower > 80)) then
        if not (player.combatTime < 10000 and player.rune <= 1) then return end
    end
    if not (A.PillarofFrost:GetCooldown() < Action.GetGCD() and gameState.useBreath) then
        if gameState.fightRemains >= 30000 then return end
    end

    return spell:Cast(player)
end)

-- frostwyrms_fury,if=hero_tree.rider_of_the_apocalypse&talent.apocalypse_now&(!talent.breath_of_sindragosa&buff.pillar_of_frost.up|buff.breath_of_sindragosa.up)|fight_remains<20
-- Removed variable.sending_cds requirement for Apocalypse Now builds
FrostwyrmsFury:Callback("cooldowns", function(spell)
    if A.RidersChampion:IsTalentLearned() and A.ApocalypseNow:IsTalentLearned() then
        if (not A.BreathofSindragosa:IsTalentLearned() and player:Buff(buffs.pillarOfFrost)) or player:Buff(buffs.breathOfSindragosa) then
            return spell:Cast(target)
        end
    end
    if gameState.fightRemains < 20000 then
        return spell:Cast(target)
    end
end)

-- frostwyrms_fury,if=!talent.apocalypse_now&active_enemies=1&buff.pillar_of_frost.up&(!talent.obliteration|!talent.pillar_of_frost)
-- Simplified conditions to ensure FWF is used during Pillar of Frost window
FrostwyrmsFury:Callback("cooldowns2", function(spell)
    if A.ApocalypseNow:IsTalentLearned() then return end
    if activeEnemies() ~= 1 then return end
    -- Ensure Pillar of Frost is active for FWF usage
    if not player:Buff(buffs.pillarOfFrost) then return end
    -- Simplified Obliteration check
    if A.Obliteration:IsTalentLearned() and A.PillarofFrost:IsTalentLearned() then return end

    return spell:Cast(target)
end)

-- frostwyrms_fury,if=!talent.apocalypse_now&active_enemies>=2&buff.pillar_of_frost.up
-- Simplified to ensure FWF is used during Pillar of Frost window for AoE
FrostwyrmsFury:Callback("cooldowns3", function(spell)
    if A.ApocalypseNow:IsTalentLearned() then return end
    if activeEnemies() < 2 then return end
    -- Require Pillar of Frost to be active for AoE FWF usage
    if not player:Buff(buffs.pillarOfFrost) then return end

    return spell:Cast(target)
end)

-- frostwyrms_fury,if=!talent.apocalypse_now&talent.obliteration&buff.pillar_of_frost.up
-- Simplified to ensure FWF is used during Pillar of Frost window for Obliteration
FrostwyrmsFury:Callback("cooldowns4", function(spell)
    if A.ApocalypseNow:IsTalentLearned() then return end
    if not A.Obliteration:IsTalentLearned() then return end
    -- Require Pillar of Frost to be active for Obliteration FWF usage
    if not player:Buff(buffs.pillarOfFrost) then return end

    return spell:Cast(target)
end)

--actions.cooldowns+=/raise_dead
RaiseDead:Callback("cooldowns", function(spell)
    if pet.exists and pet.hp > 0 then return end
    if player.combatTime <= 0 then return end

    return spell:Cast(player)
end)

--cooldowns->add_action( "soul_reaper,if=fight_remains>5&target.time_to_pct_35<5&target.time_to_pct_0>5&active_enemies<=1&(talent.obliteration&(buff.pillar_of_frost.up&!buff.killing_machine.react&rune>2|!buff.pillar_of_frost.up|buff.killing_machine.react<2&!buff.exterminate.up&!buff.exterminate_painful_death.up&buff.pillar_of_frost.remains<gcd)|talent.breath_of_sindragosa&(buff.breath_of_sindragosa.up&runic_power>50|!buff.breath_of_sindragosa.up)|!talent.breath_of_sindragosa&!talent.obliteration)" );
SoulReaper:Callback("cooldowns", function(spell)
    if gameState.activeEnemies <= 1 and target.hp <= 45 and (IsPlayerSpell(A.Obliteration.ID) or (player:Buff(buffs.pillarOfFrost and not player:Buff(buffs.killingMachine) and (player.rune > 2 or not player:Buff(buffs.pillarOfFrost) or player:HasBuffCount(buffs.killingMachine) < 2) and not player:Buff(buffs.exterminate) and not player:Buff(buffs.painfulDeath) and player:BuffRemains(buffs.pillarOfFrost) < MakGcd()) or IsPlayerSpell(A.BreathofSindragosa.ID) and (player:Buff(buffs.breathOfSindragosa) and player.runicPower > 50 or not player:Buff(buffs.breathOfSindragosa)) or not IsPlayerSpell(A.BreathofSindragosa.ID) and not IsPlayerSpell(A.Obliteration.ID))) then
        return spell:Cast(target)
    end
end)

--cooldowns->add_action( "frostscythe,if=!buff.killing_machine.react&!buff.pillar_of_frost.up" );
Frostscythe:Callback("cooldowns", function(spell)
    if player:Buff(buffs.killingMachine) then return end
    if player:Buff(buffs.pillarOfFrost) then return end

    return spell:Cast(target)
end)

--cooldowns->add_action( "any_dnd,if=!buff.death_and_decay.up&variable.adds_remain&(buff.pillar_of_frost.up&buff.killing_machine.react&(talent.enduring_strength|buff.pillar_of_frost.remains>5)|!buff.pillar_of_frost.up&(cooldown.death_and_decay.charges=2|cooldown.pillar_of_frost.remains>cooldown.death_and_decay.duration|!talent.the_long_winter&cooldown.pillar_of_frost.remains<gcd.max*2)|fight_remains<15)&(active_enemies>5|talent.cleaving_strikes&active_enemies>=2)" );
DeathandDecay:Callback("cooldowns", function(spell)
    if DeathandDecay:Used() > 0 and DeathandDecay:Used() < 3000 then return end
    if gameState.activeEnemies == 0 then return end
    if player:Buff(buffs.deathAndDecay) then return end
    if not player:Buff(buffs.deathAndDecay) 
    and (gameState.activeEnemies > 1 and DeathandDecay:Fraction() == 2) 
    or (player:Buff(buffs.pillarOfFrost) 
        and player:Buff(buffs.killingMachine) 
        and ((IsPlayerSpell(A.EnduringStrength.ID) 
        or player:BuffRemains(buffs.pillarOfFrost) > 5000) 
        or (not player:Buff(buffs.pillarOfFrost) 
            and (DeathandDecay:Fraction() == 2 
            or PillarofFrost:Cooldown() > DeathandDecay:Cooldown() 
            or (not IsPlayerSpell(A.TheLongWinter.ID) 
                and PillarofFrost:Cooldown() < MakGcd() * 2) 
                and ((gameState.activeEnemies > 5 
                or (IsPlayerSpell(A.CleavingStrikes.ID) and gameState.activeEnemies >= 2))))))) 
    and gameState.activeEnemies ~= 1 then
         return spell:Cast(player)
 end
end)

-- soul_reaper,if=talent.reaper_of_souls&buff.reaper_of_souls.up&buff.killing_machine.react<2
SoulReaper:Callback("cooldowns", function(spell)
    if not A.ReaperofSouls:IsTalentLearned() then return end
    if not player:Buff(buffs.reaperOfSouls) then return end
    if player:HasBuffCount(buffs.killingMachine) >= 2 then return end

    return spell:Cast(target)
end)

-- frostscythe,if=!buff.killing_machine.react&!buff.pillar_of_frost.up
Frostscythe:Callback("cooldowns", function(spell)
    if player:Buff(buffs.killingMachine) then return end
    if player:Buff(buffs.pillarOfFrost) then return end

    return spell:Cast(target)
end)

-- any_dnd,if=hero_tree.deathbringer&!buff.death_and_decay.up&variable.st_planning&cooldown.reapers_mark.remains<gcd.max*2&talent.unholy_ground&variable.st_dnd
DeathandDecay:Callback("cooldowns_st_deathbringer", function(spell)
    if not A.RidersChampion:IsTalentLearned() then return end -- Using RidersChampion as proxy for deathbringer hero tree
    if player:Buff(buffs.deathAndDecay) then return end
    if not gameState.stPlanning then return end
    if A.ReapersMark:GetCooldown() >= (Action.GetGCD() * 2) then return end
    if not A.UnholyGround:IsTalentLearned() then return end
    if gameState.stDnd ~= 1 then return end

    return spell:Cast(player)
end)

-- any_dnd,if=!buff.death_and_decay.up&(raid_event.adds.remains>5|!raid_event.adds.exists&active_enemies>1)&(buff.pillar_of_frost.up&buff.killing_machine.react&(talent.enduring_strength|buff.pillar_of_frost.remains>5))&(active_enemies>5|talent.cleaving_strikes&active_enemies>=2)
DeathandDecay:Callback("cooldowns_aoe1", function(spell)
    if player:Buff(buffs.deathAndDecay) then return end
    if not (activeEnemies() > 1) then return end
    if not (player:Buff(buffs.pillarOfFrost) and player:Buff(buffs.killingMachine) and (A.EnduringStrength:IsTalentLearned() or player:BuffRemains(buffs.pillarOfFrost) > 5000)) then return end
    if not (activeEnemies() > 5 or (A.CleavingStrikes:IsTalentLearned() and activeEnemies() >= 2)) then return end

    return spell:Cast(player)
end)

-- any_dnd,if=!buff.death_and_decay.up&(raid_event.adds.remains>5|!raid_event.adds.exists&active_enemies>1)&(!buff.pillar_of_frost.up&(cooldown.death_and_decay.charges=2&cooldown.pillar_of_frost.remains))&(active_enemies>5|talent.cleaving_strikes&active_enemies>=2)
DeathandDecay:Callback("cooldowns_aoe2", function(spell)
    if player:Buff(buffs.deathAndDecay) then return end
    if not (activeEnemies() > 1) then return end
    if player:Buff(buffs.pillarOfFrost) then return end
    if A.PillarofFrost:GetCooldown() == 0 then return end
    if not (activeEnemies() > 5 or (A.CleavingStrikes:IsTalentLearned() and activeEnemies() >= 2)) then return end

    return spell:Cast(player)
end)

-- any_dnd,if=!buff.death_and_decay.up&(raid_event.adds.remains>5|!raid_event.adds.exists&active_enemies>1)&(!buff.pillar_of_frost.up&(cooldown.death_and_decay.charges=1&cooldown.pillar_of_frost.remains>(cooldown.death_and_decay.duration-(cooldown.death_and_decay.duration*(cooldown.death_and_decay.charges_fractional%%1)))))&(active_enemies>5|talent.cleaving_strikes&active_enemies>=2)
DeathandDecay:Callback("cooldowns_aoe3", function(spell)
    if player:Buff(buffs.deathAndDecay) then return end
    if not (activeEnemies() > 1) then return end
    if player:Buff(buffs.pillarOfFrost) then return end
    if A.PillarofFrost:GetCooldown() < 10000 then return end -- Simplified condition
    if not (activeEnemies() > 5 or (A.CleavingStrikes:IsTalentLearned() and activeEnemies() >= 2)) then return end

    return spell:Cast(player)
end)

-- any_dnd,if=!buff.death_and_decay.up&(raid_event.adds.remains>5|!raid_event.adds.exists&active_enemies>1)&(!buff.pillar_of_frost.up&(!talent.the_long_winter&cooldown.pillar_of_frost.remains<gcd.max*2)|fight_remains<15)&(active_enemies>5|talent.cleaving_strikes&active_enemies>=2)
DeathandDecay:Callback("cooldowns_aoe4", function(spell)
    if player:Buff(buffs.deathAndDecay) then return end
    if not (activeEnemies() > 1) then return end
    if player:Buff(buffs.pillarOfFrost) then return end

    local condition1 = not A.TheLongWinter:IsTalentLearned() and A.PillarofFrost:GetCooldown() < (Action.GetGCD() * 2)
    local condition2 = gameState.fightRemains < 15000
    if not (condition1 or condition2) then return end

    if not (activeEnemies() > 5 or (A.CleavingStrikes:IsTalentLearned() and activeEnemies() >= 2)) then return end

    return spell:Cast(player)
end)

-- abomination_limb,if=variable.sending_cds
AbominationLimb:Callback("cooldowns", function(spell)
    if not gameState.sendingCDs then return end
    return spell:Cast(player)
end)

local function cooldowns()
    -- Major cooldowns first (highest priority)
    PillarofFrost("cooldowns")
    PillarofFrost("cooldowns2")
    PillarofFrost("cooldowns3")
    BreathofSindragosa("cooldowns")
    EmpowerRuneWeapon("cooldowns")
    EmpowerRuneWeapon("cooldowns2")
    EmpowerRuneWeapon("cooldowns3")

    -- Damage cooldowns (including Abomination Limb)
    AbominationLimb("cooldowns")
    FrostwyrmsFury("cooldowns")
    FrostwyrmsFury("cooldowns2")
    FrostwyrmsFury("cooldowns3")
    FrostwyrmsFury("cooldowns4")
    RemorselessWinter("cooldowns")
    ChillStreak("cooldowns")
    ReapersMark("cooldowns")

    -- Utility and setup abilities
    RaiseDead("cooldowns")
    DeathandDecay("cooldowns_st_deathbringer")
    DeathandDecay("cooldowns_aoe1")
    DeathandDecay("cooldowns_aoe2")
    DeathandDecay("cooldowns_aoe3")
    DeathandDecay("cooldowns_aoe4")

    -- Lower priority abilities
    SoulReaper("cooldowns")
    Frostscythe("cooldowns")
end

--####################################### HIGH PRIORITY ACTIONS ######################################





-- antimagic_shell,if=runic_power.deficit>40&death_knight.first_ams_cast<time&(!talent.breath_of_sindragosa|talent.breath_of_sindragosa&variable.true_breath_cooldown>cooldown.antimagic_shell.duration)
AntiMagicShell:Callback("highPrioActions", function(spell)
    if player.runicPowerDeficit <= 40 then return end
    if not (not A.BreathofSindragosa:IsTalentLearned() or (A.BreathofSindragosa:IsTalentLearned() and gameState.trueBreathCooldown > A.AntiMagicShell:GetCooldown())) then return end

    return spell:Cast(player)
end)

-- howling_blast,if=!dot.frost_fever.ticking&active_enemies>=2&(!talent.breath_of_sindragosa|!buff.breath_of_sindragosa.up)&(!talent.obliteration|talent.wither_away|talent.obliteration&(!cooldown.pillar_of_frost.ready|buff.pillar_of_frost.up&!buff.killing_machine.react))
HowlingBlast:Callback("highPrioActions", function(spell)
    if target:HasDeBuff(debuffs.frostFever) then return end
    if activeEnemies() < 2 then return end
    if A.BreathofSindragosa:IsTalentLearned() and player:Buff(buffs.breathOfSindragosa) then return end
    if A.Obliteration:IsTalentLearned() and not A.WitherAway:IsTalentLearned() then
        if A.PillarofFrost:GetCooldown() == 0 then return end
        if not (player:Buff(buffs.pillarOfFrost) and not player:Buff(buffs.killingMachine)) then return end
    end

    return spell:Cast(target)
end)

local function highPrioActions()
    AntiMagicShell("highPrioActions")
    HowlingBlast("highPrioActions")
end

--####################################### OBLITERATION ACTIVE ROTATION #######################################

-- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice+((hero_tree.deathbringer&debuff.reapers_mark_debuff.up)*5),if=buff.killing_machine.react&(buff.exterminate.up|fight_remains<gcd*2)
Obliterate:Callback("obliteration", function(spell)
    if not player:Buff(buffs.killingMachine) then return end
    if not (player:Buff(buffs.exterminate) or gameState.fightRemains < (Action.GetGCD() * 2)) then return end

    return spell:Cast(target)
end)

-- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=debuff.razorice.stack=5&talent.shattering_blade&talent.a_feast_of_souls&buff.a_feast_of_souls.up
FrostStrike:Callback("obliteration", function(spell)
    if target:HasDeBuffCount(debuffs.razorice) ~= 5 then return end
    if not A.ShatteringBlade:IsTalentLearned() then return end
    if not A.AFeastofSouls:IsTalentLearned() then return end
    if not player:Buff(buffs.aFeastOfSouls) then return end

    return spell:Cast(target)
end)

-- soul_reaper,if=fight_remains>5&target.time_to_pct_35<5&target.time_to_pct_0>5&active_enemies<=1&rune>2&!buff.killing_machine.react
SoulReaper:Callback("obliteration", function(spell)
    if gameState.fightRemains <= 5000 then return end
    if target.hp > 35 then return end
    if target.hp <= 0 then return end
    if activeEnemies() > 1 then return end
    if player.rune <= 2 then return end
    if player:Buff(buffs.killingMachine) then return end

    return spell:Cast(target)
end)

-- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=buff.killing_machine.react
Obliterate:Callback("obliteration2", function(spell)
    if not player:Buff(buffs.killingMachine) then return end

    return spell:Cast(target)
end)

-- glacial_advance,target_if=max:(debuff.razorice.stack),if=(variable.ga_priority|debuff.razorice.stack<5)&(!death_knight.runeforge.razorice&(debuff.razorice.stack<5|debuff.razorice.remains<gcd*3)|((variable.rp_buffs|rune<2)&active_enemies>1))
GlacialAdvance:Callback("obliteration", function(spell)
    if not (gameState.gaPriority or target:HasDeBuffCount(debuffs.razorice) < 5) then return end
    local condition1 = target:HasDeBuffCount(debuffs.razorice) < 5 or target:DeBuffRemains(debuffs.razorice) < (Action.GetGCD() * 3)
    local condition2 = (gameState.RPBuffs or player.rune < 2) and activeEnemies() > 1
    if not (condition1 or condition2) then return end

    return spell:Cast(target)
end)

-- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=(rune<2|variable.rp_buffs|debuff.razorice.stack=5&talent.shattering_blade)&(!talent.glacial_advance|active_enemies=1|talent.shattered_frost)
FrostStrike:Callback("obliteration2", function(spell)
    if not (player.rune < 2 or gameState.RPBuffs or (target:HasDeBuffCount(debuffs.razorice) == 5 and A.ShatteringBlade:IsTalentLearned())) then return end
    if not (not A.GlacialAdvance:IsTalentLearned() or activeEnemies() == 1 or A.ShatteredFrost:IsTalentLearned()) then return end

    return spell:Cast(target)
end)

-- howling_blast,if=buff.rime.react
HowlingBlast:Callback("obliteration", function(spell)
    if not player:Buff(buffs.rime) then return end

    return spell:Cast(target)
end)

-- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=!talent.glacial_advance|active_enemies=1|talent.shattered_frost
FrostStrike:Callback("obliteration3", function(spell)
    if not (not A.GlacialAdvance:IsTalentLearned() or activeEnemies() == 1 or A.ShatteredFrost:IsTalentLearned()) then return end

    return spell:Cast(target)
end)

-- glacial_advance,target_if=max:(debuff.razorice.stack),if=variable.ga_priority
GlacialAdvance:Callback("obliteration2", function(spell)
    if not gameState.gaPriority then return end

    return spell:Cast(target)
end)

-- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice
FrostStrike:Callback("obliteration4", function(spell)
    return spell:Cast(target)
end)

-- horn_of_winter,if=rune<3
HornofWinter:Callback("obliteration", function(spell)
    if player.rune >= 3 then return end

    return spell:Cast(player)
end)

-- arcane_torrent,if=rune<1&runic_power<30
ArcaneTorrent:Callback("obliteration", function(spell)
    if player.rune >= 1 then return end
    if player.runicPower >= 30 then return end

    return spell:Cast(player)
end)

-- howling_blast,if=!buff.killing_machine.react
HowlingBlast:Callback("obliteration2", function(spell)
    if player:Buff(buffs.killingMachine) then return end

    return spell:Cast(target)
end)

local function obliteration()
    Obliterate("obliteration")
    FrostStrike("obliteration")
    SoulReaper("obliteration")
    Obliterate("obliteration2")
    GlacialAdvance("obliteration")
    FrostStrike("obliteration2")
    HowlingBlast("obliteration")
    FrostStrike("obliteration3")
    GlacialAdvance("obliteration2")
    FrostStrike("obliteration4")
    HornofWinter("obliteration")
    ArcaneTorrent("obliteration")
    HowlingBlast("obliteration2")
end

--####################################### SINGLE TARGET #########################################

-- frost_strike,if=talent.a_feast_of_souls&debuff.razorice.stack=5&talent.shattering_blade&buff.a_feast_of_souls.up
FrostStrike:Callback("singleTarget", function(spell)
    if not A.AFeastofSouls:IsTalentLearned() then return end
    if target:HasDeBuffCount(debuffs.razorice) ~= 5 then return end
    if not A.ShatteringBlade:IsTalentLearned() then return end
    if not player:Buff(buffs.aFeastOfSouls) then return end

    return spell:Cast(target)
end)

-- obliterate,if=buff.killing_machine.react=2|buff.exterminate.up
Obliterate:Callback("singleTarget", function(spell)
    if not (player:HasBuffCount(buffs.killingMachine) == 2 or player:Buff(buffs.exterminate)) then return end

    return spell:Cast(target)
end)

-- horn_of_winter,if=(!talent.breath_of_sindragosa|variable.true_breath_cooldown>cooldown.horn_of_winter.duration-15)&cooldown.pillar_of_frost.remains<variable.oblit_pooling_time
HornofWinter:Callback("singleTarget", function(spell)
    if not (not A.BreathofSindragosa:IsTalentLearned() or gameState.trueBreathCooldown > (A.HornofWinter:GetCooldown() - 15000)) then return end
    if A.PillarofFrost:GetCooldown() >= gameState.oblitPoolingTime then return end

    return spell:Cast(player)
end)

-- frost_strike,if=debuff.razorice.stack=5&talent.shattering_blade
FrostStrike:Callback("singleTarget2", function(spell)
    if target:HasDeBuffCount(debuffs.razorice) ~= 5 then return end
    if not A.ShatteringBlade:IsTalentLearned() then return end

    return spell:Cast(target)
end)

-- soul_reaper,if=fight_remains>5&target.time_to_pct_35<5&target.time_to_pct_0>5&!buff.killing_machine.react
SoulReaper:Callback("singleTarget", function(spell)
    if gameState.fightRemains <= 5000 then return end
    if target.hp > 35 then return end
    if target.hp <= 0 then return end
    if player:Buff(buffs.killingMachine) then return end

    return spell:Cast(target)
end)

-- obliterate,if=buff.killing_machine.react&rune>3
Obliterate:Callback("singleTarget2", function(spell)
    if not player:Buff(buffs.killingMachine) then return end
    if player.rune <= 3 then return end

    return spell:Cast(target)
end)

-- obliterate,if=variable.pooling_runic_power&runic_power.deficit>=20
Obliterate:Callback("singleTarget3", function(spell)
    if not gameState.poolingRP then return end
    if player.runicPowerDeficit < 20 then return end

    return spell:Cast(target)
end)

-- howling_blast,if=buff.rime.react
HowlingBlast:Callback("singleTarget", function(spell)
    if not player:Buff(buffs.rime) then return end

    return spell:Cast(target)
end)

-- frost_strike,if=!variable.pooling_runic_power&runic_power.deficit<=30
FrostStrike:Callback("singleTarget3", function(spell)
    if gameState.poolingRP then return end
    if player.runicPowerDeficit > 30 then return end

    return spell:Cast(target)
end)

-- obliterate,if=!variable.pooling_runes
Obliterate:Callback("singleTarget4", function(spell)
    if gameState.poolingRunes then return end

    return spell:Cast(target)
end)

-- horn_of_winter,if=rune<2&runic_power.deficit>25&(!talent.breath_of_sindragosa|variable.true_breath_cooldown>cooldown.horn_of_winter.duration-15)
HornofWinter:Callback("singleTarget2", function(spell)
    if player.rune >= 2 then return end
    if player.runicPowerDeficit <= 25 then return end
    if not (not A.BreathofSindragosa:IsTalentLearned() or gameState.trueBreathCooldown > (A.HornofWinter:GetCooldown() - 15000)) then return end

    return spell:Cast(player)
end)

-- arcane_torrent,if=!talent.breath_of_sindragosa&runic_power.deficit>20
ArcaneTorrent:Callback("singleTarget", function(spell)
    if A.BreathofSindragosa:IsTalentLearned() then return end
    if player.runicPowerDeficit <= 20 then return end

    return spell:Cast(player)
end)

-- frost_strike,if=!variable.pooling_runic_power
FrostStrike:Callback("singleTarget4", function(spell)
    if gameState.poolingRP then return end

    return spell:Cast(target)
end)

-- abomination_limb,if=variable.sending_cds
AbominationLimb:Callback("singleTarget", function(spell)
    if not gameState.sendingCDs then return end
    return spell:Cast(player)
end)

local function singleTarget()
    -- Abomination Limb first for burst damage
    AbominationLimb("singleTarget")

    -- High priority abilities with buffs/procs
    FrostStrike("singleTarget")
    Obliterate("singleTarget")
    FrostStrike("singleTarget2")
    SoulReaper("singleTarget")
    Obliterate("singleTarget2")

    -- Resource pooling and management
    Obliterate("singleTarget3")
    HowlingBlast("singleTarget")
    FrostStrike("singleTarget3")
    Obliterate("singleTarget4")

    -- Resource generation and filler
    HornofWinter("singleTarget")
    HornofWinter("singleTarget2")
    ArcaneTorrent("singleTarget")
    FrostStrike("singleTarget4")
end


--##################################### ARENA FALLBACK #####################################


ChainsofIce:Callback("arenaFallback", function(spell)
    if not A.ChainsofIce:IsTalentLearned() then return end
    if Unit("target"):GetRange() < 10 then return end
    --if not target:IsPlayer() then return end
    if target:HasDeBuff(debuffs.chainsOfIce, true) then return end

    return spell:Cast(target)
end)

HowlingBlast:Callback("arenaFallback", function(spell)
    if not player:Buff(buffs.rime) or target:HasDeBuff(debuffs.frostFever, true) then return end
    if Unit("target"):GetRange() < 10 then return end

    return spell:Cast(target)
end)

DeathGrip:Callback("arenaFallback", function(spell)
    if target:HasDeBuff(MakLists.dontGrip) then return end
    if target:HasDeBuff(MakLists.zerkRoot) then return end
    if target.hp > GetToggle(2, "TargetHealthSlider") then return end
    if Unit("target"):GetRange() < 15 then return end
    return spell:Cast(target)
end)

RemorselessWinter:Callback("arenaFallback", function(spell)
    if A.FrozenDominion:IsTalentLearned() then return end
    if Unit("target"):GetRange() < 5 then return end
    if Unit("target"):GetRange() > 9 then return end
    --if not target:IsPlayer() then return end

    return spell:Cast(target)
end)

local function arenaFallback()
    DeathGrip("arenaFallback")
    HowlingBlast("arenaFallback")
    ChainsofIce("arenaFallback")
    RemorselessWinter("arenaFallback")
end

--##################################### ARENA DEFENSIVES #####################################

DeathStrike:Callback("arenaDefensive", function(spell)
    if Unit(player):GetLastTimeDMGX(4) <= GetToggle(2, "DeathStrikeSliderSpecial") then return end

    return spell:Cast(target)
end)

SacrificialPact:Callback("arenaDefensive", function(spell)
    if not pet.exists then return end
    if player.HasBuff(buffs.lichborne) then return end
    if player.hp > GetToggle(2, "SacrificialPactSlider") then return end

    return spell:Cast(player)
end)

DeathPact:Callback("arenaDefensive", function(spell)
    if player.hp > GetToggle(2, "DeathPactSlider") then return end

    return spell:Cast(player)
end)

IceboundFortitude:Callback("arenaDefensive", function(spell)
    if player.hp > GetToggle(2, "IceboundFortitudeSlider") then return end

    return spell:Cast(player)
end)


AntiMagicShell:Callback("arenaDefensiveDebuff", function(spell)
    if player:HasBuff(buffs.antiMagicZone) then return end
    if not A.UnyieldingWill:IsTalentLearned() then return end
    if not player:HasDeBuff(MakLists.AMSDispell) then return end

    return spell:Cast(player)
end)

AntiMagicShellSW:Callback("arenaDefensiveDebuff", function(spell)
    if player:HasBuff(buffs.antiMagicZone) then return end
    if not A.UnyieldingWill:IsTalentLearned() then return end
    if not player:HasDeBuff(MakLists.AMSDispell) then return end

    return spell:Cast(player)
end)

AntiMagicShell:Callback("arenaDefensiveOther", function(spell)
    if player:HasBuff(buffs.antiMagicZone) then return end
    if not AutoAntiMagicZonePvp() or not player:HasDeBuff({122470, 6789, 389794, 385627, 196770}) or player.hp > GetToggle(2, "AntiMagicShellSlider") then return end
    if not player:HasDeBuff(MakLists.AMSDispell) then return end

    return spell:Cast(player)
end)

AntiMagicShellSW:Callback("arenaDefensiveOther", function(spell)
    if player:HasBuff(buffs.antiMagicZone) then return end
    if not AutoAntiMagicZonePvp() or not player:HasDeBuff({122470, 6789, 389794, 385627, 196770}) or player.hp > GetToggle(2, "AntiMagicShellSlider") then return end
    if not player:HasDeBuff(MakLists.AMSDispell) then return end

    return spell:Cast(player)
end)

AntiMagicShell:Callback("arenaDefensiveLitFuse", function(spell)
    if player:HasBuff(buffs.antiMagicZone) then return end
    if not target:HasBuff(450716) then return end

    return spell:Cast(player)
end)

AntiMagicShellSW:Callback("arenaDefensiveLitFuse", function(spell)
    if player:HasBuff(buffs.antiMagicZone) then return end
    if not target:HasBuff(450716) then return end

    return spell:Cast(player)
end)

AntiMagicZone:Callback("arenaDefensive", function(spell)
    if player:HasBuff(buffs.antiMagicShell) then return end
    if not AutoAntiMagicZonePvp() or player.hp > GetToggle(2, "AntiMagicZoneSlider") then return end

    return spell:Cast(player)
end)

Lichborne:Callback("arenaDefensive", function(spell)
    if player.hp > GetToggle(2, "LichborneSlider") then return end

    return spell:Cast(player)
end)

DeathCoilPlayer:Callback("arenaDefensive", function(spell)
    --if player:HasBuff(buffs.deathPact) then return end
    if not player:HasBuff(buffs.lichborne) then return end
    if player.hp > 90 then return end

    return spell:Cast(player)
end)


DeathStrike:Callback("arenaDefensive", function(spell)
    if player:HasBuff(buffs.lichborne) then return end
    if not player:HasBuff(buffs.darkSuccor) then return end
    if player.hp > 85 then return end

    return spell:Cast(target)
end)

DeathStrike:Callback("arenaDefensive2", function(spell)
    if player:HasBuff(buffs.lichborne) then return end
    if player.hp > GetToggle(2, "DeathStrikeSlider") then return end
    if player.hp > 85 then return end

    return spell:Cast(target)
end)

DeathsAdvance:Callback("arenaDefensive", function(spell)
    if player:HasBuff(buffs.wraithWalk) then return end
    if player:HasBuff(buffs.deathsAdvance) then return end
    if not target.canAttack then return end
    if MindFreeze:InRange(target) then return end

    return spell:Cast(player)
end)

local function arenaDefensives()
    DeathStrike("arenaDefensive")
    SacrificialPact("arenaDefensive")
    DeathPact("arenaDefensive")
    IceboundFortitude("arenaDefensive")
    AntiMagicShell("arenaDefensiveDebuff")
    AntiMagicShellSW("arenaDefensiveDebuff")
    AntiMagicShell("arenaDefensiveOther")
    AntiMagicShellSW("arenaDefensiveOther")
    AntiMagicShell("arenaDefensiveLitFuse")
    AntiMagicShellSW("arenaDefensiveLitFuse")
    AntiMagicZone("arenaDefensive")
    Lichborne("arenaDefensive")
    DeathCoilPlayer("arenaDefensive")
    DeathStrike("arenaDefensive")
    DeathStrike("arenaDefensive2")
    DeathsAdvance("arenaDefensive")
end

--[[ Need to redo racials
ArcaneTorrent:Callback(function(spell)
    _, _, raceId = UnitRace("player")
    if raceId ~= 10 then return end

    if enemiesInMelee() < 1 then return end
    if player.runicPower >= 20 then return end
    if player.rune >= 2 then return end

    return spell:Cast(player)
end)

BloodFury:Callback(function(spell)
    if not FesteringStrike:InRange(target) then return end
    if gameState.gargTime > 0 and gameState.gargTime <= 18 then
        return spell:Cast(player)
    end

    if gameState.fightRemains < 18 and target:IsBoss() and IsInRaid() then
        return spell:Cast(player)
    end

    local burstCheck = gameState.armyGhoulTime > 0 and gameState.armyGhoulTime <= 18 or gameState.apocGhoulTime > 0 and gameState.apocGhoulTime <= 18 or activeEnemies() >= 2 and player:Buff(buffs.deathAndDecay)

    if not burstCheck then return end
    if A.SummonGargoyle:IsTalentLearned() and SummonGargoyle:Cooldown() <= 60 then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not FesteringStrike:InRange(target) then return end
    if gameState.gargTime > 0 and gameState.gargTime <= 15 then
        return spell:Cast(player)
    end

    if gameState.fightRemains < 15 and target:IsBoss() and IsInRaid() then
        return spell:Cast(player)
    end

    local burstCheck = gameState.armyGhoulTime > 0 and gameState.armyGhoulTime <= 15 or gameState.apocGhoulTime > 0 and gameState.apocGhoulTime <= 15 or activeEnemies() >= 2 and player:Buff(buffs.deathAndDecay)

    if not burstCheck then return end
    if A.SummonGargoyle:IsTalentLearned() and SummonGargoyle:Cooldown() <= 60 then return end

    return spell:Cast(player)
end)

LightsJudgment:Callback(function(spell)
    if not player:Buff(buffs.unholyStrength) then return end
    if A.Festermight:IsTalentLearned() and not player:Buff(buffs.festermight) then return end

    return spell:Cast(player)
end)

AncestralCall:Callback(function(spell)
    if not FesteringStrike:InRange(target) then return end
    if gameState.gargTime > 0 and gameState.gargTime <= 18 then
        return spell:Cast(player)
    end

    if gameState.fightRemains < 18 and target:IsBoss() and IsInRaid() then
        return spell:Cast(player)
    end

    local burstCheck = gameState.armyGhoulTime > 0 and gameState.armyGhoulTime <= 18 or gameState.apocGhoulTime > 0 and gameState.apocGhoulTime <= 18 or activeEnemies() >= 2 and player:Buff(buffs.deathAndDecay)

    if not burstCheck then return end
    if A.SummonGargoyle:IsTalentLearned() and SummonGargoyle:Cooldown() <= 60 then return end

    return spell:Cast(player)
end)

ArcanePulse:Callback(function(spell)
    if activeEnemies() >= 2 or (player.rune <= 1 and player.runicPowerDeficit >= 60) then
        return spell:Cast(player)
    end
end)

Fireblood:Callback(function(spell)
    if not FesteringStrike:InRange(target) then return end
    if gameState.gargTime > 0 and gameState.gargTime <= 11 then
        return spell:Cast(player)
    end

    if gameState.fightRemains < 11 and target:IsBoss() and IsInRaid() then
        return spell:Cast(player)
    end

    local burstCheck = gameState.armyGhoulTime > 0 and gameState.armyGhoulTime <= 11 or gameState.apocGhoulTime > 0 and gameState.apocGhoulTime <= 11 or activeEnemies() >= 2 and player:Buff(buffs.deathAndDecay)

    if not burstCheck then return end
    if A.SummonGargoyle:IsTalentLearned() and SummonGargoyle:Cooldown() <= 60 then return end

    return spell:Cast(player)
end)

BagOfTricks:Callback(function(spell)
    if activeEnemies() > 1 then return end 
    if not player:Buff(buffs.unholyStrength) then return end
    
    return spell:Cast(player)
end)

local function racials()
    ArcaneTorrent()
    BloodFury()
    Berserking()
    LightsJudgment()
    AncestralCall()
    ArcanePulse()
    Fireblood()
    BagOfTricks()
end]]

A[3] = function(icon)
	FrameworkStart(icon)

    -- Safety check for player object
    if not player or not player.exists then
        return FrameworkEnd()
    end

    updateGameState()

    Lichborne()
    WraithWalk()
    DeathsAdvance()
    IceboundFortitude()
    AntiMagicShell()
    DeathPact()

    makInterrupt(interrupts)

    if Action.Zone == "arena" then
        arenaDefensives()
    end

    if target and target.exists and target.canAttack and FrostStrike:InRange(target) then
        DeathStrike()

        -- Always check for cooldowns first, regardless of burst toggle
        cooldowns()
        highPrioActions()

        if shouldBurst() then
            if Trinket(1, "Damage") then Trinket1() end
            if Trinket(2, "Damage") then Trinket2() end
            racials()

            --[[ NEED TO UPDATE POTION LOGIC
            local damagePotion = Action.GetToggle(2, "damagePotion")
            local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
            local potionExhausted = Action.GetToggle(2, "potionExhausted")
            local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")
            local damagePotionObject = Action.DetermineUsableObject("player", nil, nil, true, nil, A.TemperedPotion, A.PotionofUnwaveringFocus)



            if damagePotionObject and damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
                local summonGargoyle = not A.SummonGargoyle:IsTalentLearned() or SummonGargoyle:Cooldown() > 60000
                local shouldPot = pet:Buff(buffs.darkTransformation) and pet:BuffRemains(buffs.darkTransformation) < 30 or gameState.armyGhoulTime > 0 and gameState.armyGhoulTime <= 30 or gameState.apocGhoulTime > 0 and gameState.apocGhoulTime <= 30
                if summonGargoyle and shouldPot then
                    return damagePotionObject:Show(icon)
                end
            end]]
        end
        
        --actions+=/call_action_list,name=cold_heart,if=talent.cold_heart&(!buff.killing_machine.up|talent.breath_of_sindragosa)&((debuff.razorice.stack=5|!death_knight.runeforge.razorice&!talent.glacial_advance&!talent.avalanche&!talent.arctic_assault)|fight_remains<=gcd)
        if A.ColdHeart:IsTalentLearned() and (not player:Buff(buffs.killingMachine) or A.BreathofSindragosa:IsTalentLearned()) and ((target:HasDeBuffCount(debuffs.razorice, true) == 5 or not A.GlacialAdvance:IsTalentLearned() and not A.Avalanche:IsTalentLearned() and not A.ArcticAssault:IsTalentLearned()) or gameState.fightRemains <= Action.GetGCD()) then
            coldHeart()
        end

        --actions+=/run_action_list,name=breath,if=buff.breath_of_sindragosa.up
        if player:Buff(buffs.breathOfSindragosa) then
            breath()
        end

        --actions+=/run_action_list,name=obliteration,if=talent.obliteration&buff.pillar_of_frost.up&!buff.breath_of_sindragosa.up
        if A.Obliteration:IsTalentLearned() and player:Buff(buffs.pillarOfFrost) and not player:Buff(buffs.breathOfSindragosa) then
            obliteration()
        end

        local useAoE = activeEnemies() >= 2 and A.GetToggle(2, "AoE") and not A.IsInPvP 
        if useAoE then
            aoe()
        end
        
        if not useAoE then
            singleTarget()
        end
    end

    if target.exists and target.canAttack and DeathGrip:InRange(target) then
        arenaFallback()
    end

    if A.GetToggle(2, "makDebug") then

        --Let's print our new set of gamestates
        MakPrint(1, "ST Planning", gameState.stPlanning)
        MakPrint(2, "RP Deficit", player.runicPowerDeficit)
        MakPrint(3, "Spend CDs", gameState.sendingCDs)
        MakPrint(4, "Rime Buffs", gameState.rimeBuffs)
        MakPrint(5, "RP Buffs", gameState.RPBuffs)
        MakPrint(6, "Cooldown Check", gameState.cooldownCheck)
        MakPrint(7, "True Breath Cooldown", gameState.trueBreathCooldown)
        MakPrint(8, "Oblit Pooling Time", gameState.oblitPoolingTime)
        MakPrint(9, "Breath Pooling Time", gameState.breathPoolingTime)
        MakPrint(10, "Pooling Runes", gameState.poolingRunes)
        MakPrint(11, "Pooling RP", gameState.poolingRP)
        MakPrint(12, "GA Priority", gameState.gaPriority)
        MakPrint(13, "Breath Dying", gameState.breathDying)
        MakPrint(14, "True Breath Cooldown", gameState.trueBreathCooldown)

    end

	return FrameworkEnd()
end

Asphyxiate:Callback("arena45", function(spell, enemy)
    if not GetToggle(2, "AsphyxiateDropdown") == "4" then return end
    local ccRemains = 0
    if enemy.cc then
        ccRemains = enemy:CCRemains()
    end
    if ccRemains > 1500 then return end
    if not enemy:IsTarget() then return end
    if enemy.hp >= 45 then return end
    if enemy.stunDR < 0.5 then return end
    return spell:Cast(enemy)
end)

Strangulate:Callback("arenastun", function(spell, enemy)
    if not GetToggle(2, "StrangulateDropdown") == "4" then return end
    if not enemy:IsTarget() then return end
    if enemy.hp >= 35 then return end
    if enemy.silenceDr < 0.5 then return end
    return spell:Cast(enemy)
end)

Asphyxiate:Callback("arenacc", function(spell, enemy)
    if not GetToggle(2, "AsphyxiateDropdown") == "1" or GetToggle(2, "AsphyxiateDropdown") == "3" then return end
    if not arena1.hp <= 45 or arena2.hp <= 45 or arena3.hp <= 45 then return end
    if not enemy.isHealer then return end
    if enemy.stunDr < 0.50 then return end
    return spell:Cast(enemy)
end)

Strangulate:Callback("arenacc", function(spell, enemy)
    if not GetToggle(2, "StrangulateDropdown") == "1" or GetToggle(2, "StrangulateDropdown") == "3" then return end
    if not arena1.hp <= 35 or arena2.hp <= 35 or arena3.hp <= 35 then return end
    if not enemy.isHealer then return end
    if enemy.silenceDr < 0.50 then return end
    return spell:Cast(enemy)
end)

MindFreeze:Callback("arenakick", function(spell, enemy)
    if not enemy.pvpKick then return end
    return spell:Cast(enemy)
end)

Strangulate:Callback("arenakick", function(spell, enemy)
    if not GetToggle(2, "AsphyxiateDropdown") == "1" or GetToggle(2, "AsphyxiateDropdown") == "2" then return end
    if not enemy.pvpKick then return end
    if enemy.silenceDr < 0.50 then return end
    
    return spell:Cast(enemy)
end)

DeathGrip:Callback("arenakick", function(spell, enemy)
    if not GetToggle(2, "DeathGripDropdown") == "1" or GetToggle(2, "DeathGripDropdown") == "2" then return end
    if enemy:HasDeBuff(MakLists.dontGrip) then return end
    if enemy:HasDeBuff(MakLists.zerkRoot) then return end
    if not enemy.pvpKick then return end
    return spell:Cast(enemy)
end)

local function enemyRotation(enemy)
    if Action.Zone ~= "arena" then return end
	if Player:IsMounted() or Player:IsStealthed() then return false end
    
    -- Target Stuns
    Asphyxiate("arenastun", enemy)
    Strangulate("arenastun", enemy)

    -- CC
    Asphyxiate("arenacc", enemy)
    Strangulate("arenacc", enemy)

    -- Kicks
    MindFreeze("arenakick", enemy)
    Strangulate("arenakick", enemy)
end

local partyRotation = function(friendly)
    if Action.Zone ~= "arena" then return end
    if not friendly.exists then return end

end

A[6] = function(icon)
	RegisterIcon(icon)
    if targetForInterrupt(interrupts) then return TabTarget() end
	enemyRotation(arena1)
	partyRotation(party1)

	return FrameworkEnd()
end

A[7] = function(icon)
	RegisterIcon(icon)
	enemyRotation(arena2)
	partyRotation(party2)

	return FrameworkEnd()
end

A[8] = function(icon)
	RegisterIcon(icon)
	enemyRotation(arena3)
	partyRotation(player)

	return FrameworkEnd()
end

