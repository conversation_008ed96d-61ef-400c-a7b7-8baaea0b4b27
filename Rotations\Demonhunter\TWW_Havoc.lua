if not MakuluValidCheck() then return true end
if not (Maku<PERSON>_magic_number == 2347956243324) then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 577 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local TableToLocal     = MakuluFramework.tableToLocal
local Debounce         = MakuluFramework.debounceSpell
local ConstUnit        = MakuluFramework.ConstUnits
local MakGcd           = MakuluFramework.gcd
local cacheContext     = MakuluFramework.Cache
local Trinket          = MakuluFramework.Trinket
local Aware            = MakuluFramework.Aware
local ConstCell        = cacheContext:getConstCacheCell()
local MakLists         = MakuluFramework.lists

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle

local BossMods         = Action.BossMods

local _G, setmetatable = _G, setmetatable

--[[
v0.0.3
- Tweaked Retreat Combo (may still need more fine tuning)
- Fixed TTD Checks to use MS instead of Secs
- Fixed Combat Time Checks to use Sec instead of MS (switched em woops)
- Changed Blade Dance to melee range

v0.0.4
- Added some cache stuff
- Started adding some arena stuff (from trips old profile temp)

v0.0.5
- More cache!
- More range checks!
- More TTD checks!
]]--
-- Demonsurge, IsolatedPrey
local ActionID = {

    ChaosNova = { ID = 179057, MAKULU_INFO = { damageType = "magic" } },
    ConsumeMagic = { ID = 278326, MAKULU_INFO = { damageType = "magic" } },
    Darkness = { ID = 196718 },
    Disrupt = { ID = 183752, MAKULU_INFO = { damageType = "physical" } },
    Felblade = { ID = 232893, MAKULU_INFO = { damageType = "magic" } },
    ImmolationAura = { ID = 258920, Texture = 320331, MAKULU_INFO = { damageType = "magic" } },
    Imprison = { ID = 217832, MAKULU_INFO = { damageType = "magic" } },
    ImprisonDetainment = { ID = 221527, Hidden = true, MAKULU_INFO = { damageType = "magic" } },
    Metamorphosis = { ID = 191427, MAKULU_INFO = { damageType = "magic" } },
    SigilOfMisery = { ID = 207684, MAKULU_INFO = { damageType = "magic" } },
    TheHunt = { ID = 370965, MAKULU_INFO = { damageType = "magic" } },
    ThrowGlaive = { ID = 185123, Texture = 356510, MAKULU_INFO = { damageType = "magic" } },
    Torment = { ID = 185245, MAKULU_INFO = { damageType = "magic" } },
    VengefulRetreat = { ID = 198793, MAKULU_INFO = { damageType = "physical", offGcd = true } },
    SigilOfSpite = { ID = 390163, FixedTexture = 3565443, MAKULU_INFO = { damageType = "magic" } },

    BladeDance = { ID = 188499, Texture = 243188, MAKULU_INFO = { damageType = "magic" } },
    DeathSweep = { ID = 210152, Texture = 243188, MAKULU_INFO = { damageType = "magic" } },
    DemonsBite = { ID = 162243, MAKULU_INFO = { damageType = "magic" } },
    Blur = { ID = 198589 },
    ChaosStrike = { ID = 162794, Texture = 278736, MAKULU_INFO = { damageType = "magic" } },
    Annihilation = { ID = 201428, Texture = 278736, MAKULU_INFO = { damageType = "magic" } },
    EssenceBreakk = { ID = 258860, MAKULU_INFO = { damageType = "magic" } },
    EyeBeam = { ID = 198013, Texture = 343311, MAKULU_INFO = { damageType = "magic" } },
    FelEruption = { ID = 211881, MAKULU_INFO = { damageType = "magic" } },
    FelRush = { ID = 195072, MAKULU_INFO = { damageType = "magic" } },
    Netherwalk = { ID = 196555, MAKULU_INFO = { damageType = "magic" } },
    SigilOfFlame = { ID = 204596, Texture = 432816, MAKULU_INFO = { damageType = "magic" } },
    SigilOfDoom = { ID = 452490, Texture = 432816, MAKULU_INFO = { damageType = "magic" } },
    GlaiveTempest = { ID = 342817, MAKULU_INFO = { damageType = "magic" } },
    FelBarrage = { ID = 258925, MAKULU_INFO = { damageType = "magic" } },
    ReaversGlaive = { ID = 442294, MAKULU_INFO = { damageType = "magic" } },
    AbyssalGaze = { ID = 452497, MAKULU_INFO = { damageType = "magic" } },

    ReverseMagic = { ID = 205604, MAKULU_INFO = { damageType = "magic" } },
    RainFromAbove = { ID = 206803, MAKULU_INFO = { damageType = "magic" } },
    ArcaneTorrent = { ID = 28730, MAKULU_INFO = { damageType = "magic" } }, -- Blood Elf racial

    Initiative = { ID = 388108, Hidden = true },
    Demonic = { ID = 213410, Hidden = true },
    ChaoticTransformation = { ID = 388112, Hidden = true },
    Momentum = { ID = 206476, Hidden = true },
    Inertia = { ID = 427640, Hidden = true },
    Ragefire = { ID = 388107, Hidden = true },
    ShatteredDestiny = { ID = 388116, Hidden = true },
    Soulscar = { ID = 388106, Hidden = true },
    DemonBlades = { ID = 203555, Hidden = true },
    UnhinderedAssault = { ID = 444931, Hidden = true },
    Detainment = { ID = 205596, Hidden = true },
    Demonsurge = { ID = 452402, Hidden = true },
    ArtOfTheGlaive = { ID = 442290, Hidden = true },
    FuriousThrows = { ID = 393029, Hidden = true },
    AFireInside = { ID = 427775, Hidden = true },
    CycleOfHatred = { ID = 258887, Hidden = true },
    RestlessHunter = { ID = 390142, Hidden = true },
    QuickenedSigils = { ID = 209281, Hidden = true },
    StudentOfSuffering = { ID = 452412, Hidden = true },
    ScreamingBrutality = { ID = 1220506, Hidden = true },
    BlindFury = { ID = 203550, Hidden = true },
    AldrachiReaver = { ID = 442290, Hidden = true },
    TacticalRetreat = { ID = 389688, Hidden = true },
    UnboundChaos = { ID = 347461, Hidden = true },
    BurningWound = { ID = 391189, Hidden = true },
    IsolatedPrey = { ID = 388113, Hidden = true },
    LooksCanKill = { ID = 320415, Hidden = true }, -- Eye Beam talent

}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[ACTION_CONST_DEMONHUNTER_HAVOC] = A

TableToLocal(M, getfenv(1))
Aware:enable()


local player = ConstUnit.player
local target = ConstUnit.target
local focus = ConstUnit.focus
local mouseover = ConstUnit.mouseover
local pet = ConstUnit.pet
local arena1 = ConstUnit.arena1
local arena2 = ConstUnit.arena2
local arena3 = ConstUnit.arena3
local party1 = ConstUnit.party1
local party2 = ConstUnit.party2
local party3 = ConstUnit.party3
local party4 = ConstUnit.party4
local healer = ConstUnit.healer
local enemyHealer = ConstUnit.enemyHealer

local gameState = {}

local buffs = {
    arena_preparation = 32727,
    power_infusion = 10060,

    -- Havoc buffs
    metamorphosis = 162264, --
    demonsurge = 452416,
    demonsurge_death_sweep = 452417,
    demonsurge_annihilation = 452418,
    demonsurge_hardcast = 452419,
    demonsurge_abyssal_gaze = 452420,
    demonsurge_sigil_of_doom = 452421,
    demonsurge_consuming_fire = 452422,
    unbound_chaos = 347462,
    inner_demon = 390145, --
    initiative = 391215,
    inertia = 1215159,
    inertia_trigger = 427642,
    momentum = 208628,
    tactical_retreat = 389890,
    cycle_of_hatred = 1214887, --
    immolation_aura = 258920, --
    fel_barrage = 258925,
    chaos_theory = 390195,
    glaive_flurry = 442435, --
    rending_strike = 442442,
    thrill_of_the_fight_damage = 442695,
    reavers_glaive = 442294,
    necessary_sacrifice = 459932,
    student_of_suffering = 453239,
    art_of_the_glaive = 444661,
    out_of_range = 1, -- Placeholder for out of range buff

    netherwalk = 196555,
    blur = 212800
}

local debuffs = {
    exhaustion = 57723,

    -- Havoc debuffs
    essence_break = 320338, --
    burning_wound = 346278,
    reavers_mark = 442624,
    sigil_of_flame = 204598, --
    sigil_of_doom = 462030
}

local interrupts = {
    {spell = Disrupt },
    {spell = ChaosNova, isCC = true, aoe = true},
    {spell = Imprison, isCC = true },
    --{spell = SigilofSilence, isCC = true, aoe = true, distance = 7},
    --{spell = SigilofMisery, isCC = true, aoe = true, distance = 7},
    --{spell = SigilofChains, isCC = true, aoe = true},
}

local function num(val)
    if val then return 1 else return 0 end
end

local function shouldBurst()
    if Action.BurstIsON("target") then
        return true
    end
    return false
end

-- Function to check if we should pick up soul fragments
local function shouldPickUpFragments()
    if player.fury >= 90 then return false end

    -- Pick up fragments when we need fury
    return true
end

local function EnemiesInSpellRange(makulu_spell)
    return ConstCell:GetOrSet("enemiesIn" .. makulu_spell.id, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and (enemy.inCombat or enemy.isDummy) and not enemy.isPet and not enemy.isFriendly then
                total = total + 1
            end
        end
        return total
    end)
end

local function EnemiesInSpellRangeWithoutDebuff(makulu_spell, debuff_id)
    return ConstCell:GetOrSet("enemiesInDebuff" .. makulu_spell.id .. debuff_id, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and (enemy.inCombat or enemy.isDummy) and not enemy.isPet and not enemy.isFriendly and not enemy:Debuff(debuff_id) then
                total = total + 1
            end
        end
        return total
    end)
end

local function EnemiesInSpellRangeWithDebuff(makulu_spell, debuff_id)
    return ConstCell:GetOrSet("enemiesInDebuff" .. makulu_spell.id .. debuff_id, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and (enemy.inCombat or enemy.isDummy) and not enemy.isPet and not enemy.isFriendly and enemy:Debuff(debuff_id) then
                total = total + 1
            end
        end
        return total
    end)
end

local function TotemsInSpellRange(makulu_spell)
    return ConstCell:GetOrSet("totemsIn" .. makulu_spell.id, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and enemy.inCombat and enemy:IsTotem() and not enemy.isFriendly then
                return true
            end
        end
        return false
    end)
end

local function enemiesInRange(dur)
    local cacheKey = dur and "enemiesInRange_" .. tostring(dur) or "enemiesInRange"

    return ConstCell:GetOrSet(cacheKey, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local count = 0
        local dur = dur or 0

        for enemyGUID in pairs(activeEnemies) do
        local enemy = MakUnit:new(enemyGUID)

            if Felblade:InRange(enemy) and not enemy:IsTotem() and not enemy.isPet then
                if (player.inCombat and enemy.inCombat) or (not player.inCombat and not enemy.inCombat) or enemy.isDummy then
                    if dur > 0 and enemy.ttd > dur then
                        count = count + 1
                    elseif dur <= 0 then
                        count = count + 1
                    end
                end
            end
        end

        return count
    end)
end

local function autoTarget()
    if not player.inCombat then return false end
    if player.speed >= 50 then return false end

    if A.GetToggle(2, "autotarget") then
        for _, spellInfo in ipairs(interrupts) do
            if target:ShouldInterrupt(spellInfo.spell, spellInfo.isCC, spellInfo.aoe, spellInfo.distance) then
                return false
            end
        end
    end

    if ChaosStrike:InRange(target) and target.exists then return false end

    if A.GetToggle(2, "SwapForMelee") and EnemiesInSpellRange(ChaosStrike) > 0 then
        return true
    end

    if not ChaosStrike:InRange(target) and gameState.inMelee > 0 then return true end
end

local function updateGameState()
    gameState = {
        TWW2has2P = player:Has2Set(),
        TWW2has4P = player:Has4Set(),
        inMelee = EnemiesInSpellRange(ChaosStrike),
        activeEnemies = EnemiesInSpellRange(ChaosStrike),
        enemiesInMelee = EnemiesInSpellRange(ChaosStrike),
        -- actions.ar=variable,name=rg_inc,op=set,value=buff.rending_strike.down&buff.glaive_flurry.up&cooldown.blade_dance.up&gcd.remains=0|variable.rg_inc&prev_gcd.1.death_sweep
        rgInc = not player:Buff(buffs.rending_strike) and player:Buff(buffs.glaive_flurry) and BladeDance.cd < 300 and MakGcd() == 0 or gameState.rgInc and DeathSweep.lastUsed < 1000,
    }
    -- actions.ar+=/variable,name=fel_barrage,op=set,value=talent.fel_barrage&(cooldown.fel_barrage.remains<gcd.max*7&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in<gcd.max*7|raid_event.adds.in>90)&(cooldown.metamorphosis.remains|active_enemies>2)|buff.fel_barrage.up)&!(active_enemies=1&!raid_event.adds.exists)
    gameState.felBarrage = player:TalentKnown(320770) and (FelBarrage.cd < MakGcd() * 7 and (gameState.activeEnemies >= 3 or player:Buff(buffs.metamorphosis)) or player:Buff(buffs.felBarrage))
end

local function hasIncomingDamage()
    return incBigDmgIn() < 2000 or incModDmgIn() < 2000
end

local function defensiveActive()
    return player:BuffFrom(MakLists.Defensive) or UnitGetTotalAbsorbs("player") >= player.maxHealth * 0.15
end

local function shouldDefensive()
    local incomingDamage = hasIncomingDamage()

    return incomingDamage and not defensiveActive() 
end

--[[
-- START REAVER
--]]

-- actions.ar+=/chaos_strike,if=buff.rending_strike.up&buff.glaive_flurry.up&(variable.rg_ds=2|active_enemies>2)&time>10
ChaosStrike:Callback("ar-chaosstrike", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.rending_strike) then return end
    if not player:Buff(buffs.glaive_flurry) then return end
    if not (gameState.activeEnemies > 2) then return end
    if player.combatTime < 10 then return end

    return spell:Cast(target)
end)

-- actions.ar+=/annihilation,if=buff.rending_strike.up&buff.glaive_flurry.up&(variable.rg_ds=2|active_enemies>2)
Annihilation:Callback("ar-annihilation", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:Buff(buffs.rendingStrike) then return end
    if not player:Buff(buffs.glaiveFlurry) then return end
    if not (gameState.activeEnemies > 2) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/reavers_glaive,if=buff.glaive_flurry.down&buff.rending_strike.down&buff.thrill_of_the_fight_damage.remains<gcd.max*4+(variable.rg_ds=2)+(cooldown.the_hunt.remains<gcd.max*3)*3+(cooldown.eye_beam.remains<gcd.max*3&talent.shattered_destiny)*3&(variable.rg_ds=0|variable.rg_ds=1&cooldown.blade_dance.up|variable.rg_ds=2&cooldown.blade_dance.remains)&(buff.thrill_of_the_fight_damage.up|!prev_gcd.1.death_sweep|!variable.rg_inc)&active_enemies<3&!action.reavers_glaive.last_used<5&debuff.essence_break.down&(buff.metamorphosis.remains>2|cooldown.eye_beam.remains<10|fight_remains<10)
ReaversGlaive:Callback("ar-reaversglaive", function(spell)
    if not ReaversGlaive:InRange(target) then return end
    if player:Buff(buffs.glaiveFlurry) then return end
    if player:Buff(buffs.rendingStrike) then return end
    if player:BuffRemains(buffs.thrill_of_the_fight_damage) > 4000 then return end
    if TheHunt.cd < 300 then return end
    if EyeBeam.cd < 300 and player:TalentKnown(442290) then return end
    if not (BladeDance.cd < 300 and BladeDance.cd > 300) then return end
    if not (player:Buff(buffs.thrill_of_the_fight_damage) or not gameState.rgInc) then return end
    --if not (gameState.activeEnemies < 3) then return end
    if ReaversGlaive.used < 5000 then return end
    if target:Debuff(debuffs.essenceBreak) then return end
    if player:BuffRemains(buffs.metamorphosis) > 2000 then return end
    if EyeBeam.cd < 10000 then return end
    --if target.ttd < 10 then return end

    return spell:Cast(target)
end)

-- actions.ar+=/chaos_strike,if=buff.rending_strike.up&buff.glaive_flurry.up&(variable.rg_ds=2|active_enemies>2)&time>10
ChaosStrike:Callback("ar-chaosstrike2", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.rending_strike) then return end
    if not player:Buff(buffs.glaive_flurry) then return end
    if not (gameState.activeEnemies > 2) then return end
    if player.combatTime < 10 then return end

    return spell:Cast(target)
end)

-- actions.ar+=/annihilation,if=buff.rending_strike.up&buff.glaive_flurry.up&(variable.rg_ds=2|active_enemies>2)
Annihilation:Callback("ar-annihilation2", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:Buff(buffs.rending_strike) then return end
    if not player:Buff(buffs.glaive_flurry) then return end
    if not (gameState.activeEnemies > 2) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/reavers_glaive,if=buff.glaive_flurry.down&buff.rending_strike.down&buff.thrill_of_the_fight_damage.remains<gcd.max*4+(variable.rg_ds=2)+(cooldown.the_hunt.remains<gcd.max*3)*3+(cooldown.eye_beam.remains<gcd.max*3&talent.shattered_destiny)*3&(variable.rg_ds=0|variable.rg_ds=1&cooldown.blade_dance.up|variable.rg_ds=2&cooldown.blade_dance.remains)&(buff.thrill_of_the_fight_damage.up|!prev_gcd.1.death_sweep|!variable.rg_inc)&active_enemies<3&!action.reavers_glaive.last_used<5&debuff.essence_break.down&(buff.metamorphosis.remains>2|cooldown.eye_beam.remains<10|fight_remains<10)
ReaversGlaive:Callback("ar-reaversglaive2", function(spell)
    if not ReaversGlaive:InRange(target) then return end
    if player:Buff(buffs.glaive_flurry) then return end
    if player:Buff(buffs.rending_strike) then return end
    if player:BuffRemains(buffs.thrill_of_the_fight_damage) > 4000 then return end
    if TheHunt.cd < 300 then return end
    if EyeBeam.cd < 300 and player:TalentKnown(442290) then return end
    if not (BladeDance.cd < 300 and BladeDance.cd > 300) then return end
    if not (player:Buff(buffs.thrill_of_the_fight_damage) or not gameState.rgInc) then return end
    if not (gameState.activeEnemies < 3) then return end
    if ReaversGlaive.used < 5000 then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:BuffRemains(buffs.metamorphosis) > 2000 then return end
    if EyeBeam.cd < 10000 then return end
    if target.ttd < 1000 then return end

    return spell:Cast(target)
end)

-- actions.ar+=/reavers_glaive,if=buff.glaive_flurry.down&buff.rending_strike.down&buff.thrill_of_the_fight_damage.remains<4&(buff.thrill_of_the_fight_damage.up|!prev_gcd.1.death_sweep|!variable.rg_inc)&active_enemies>2|fight_remains<10
ReaversGlaive:Callback("ar-reaversglaive3", function(spell)
    if not ReaversGlaive:InRange(target) then return end
    if player:Buff(buffs.glaive_flurry) then return end
    if player:Buff(buffs.rending_strike) then return end
    if player:BuffRemains(buffs.thrill_of_the_fight_damage) > 4000 then return end
    if not (player:Buff(buffs.thrill_of_the_fight_damage) or not gameState.rgInc) then return end
    if gameState.activeEnemies < 2 then return end
    if target.ttd < 1000 then return end

    return spell:Cast(target)
end)

-- actions.ar_cooldown=metamorphosis,if=(((cooldown.eye_beam.remains>=20|talent.cycle_of_hatred&cooldown.eye_beam.remains>=13|raid_event.adds.remains>8&raid_event.adds.remains<cooldown.eye_beam.remains)&(!talent.essence_break|debuff.essence_break.up)&buff.fel_barrage.down&(raid_event.adds.in>40|(raid_event.adds.remains>8|!talent.fel_barrage)&active_enemies>desired_targets|fight_style.dungeonroute&!raid_event.adds.in<=120)|!talent.chaotic_transformation|fight_remains<30)&buff.inner_demon.down&(!talent.restless_hunter&cooldown.blade_dance.remains>gcd.max*3|prev_gcd.1.death_sweep|prev_gcd.2.death_sweep|prev_gcd.3.death_sweep))&!talent.inertia&!talent.essence_break&time>15
Metamorphosis:Callback("ar-cooldown-meta-new", function(spell)
    if not Disrupt:InRange(target) then return end

    -- Eye Beam cooldown conditions (updated)
    local eyeBeamCondition = EyeBeam.cd >= 20000 or
                            (player:TalentKnown(A.CycleOfHatred.ID) and EyeBeam.cd >= 13000) or
                            (gameState.activeEnemies > 8 and gameState.activeEnemies < EyeBeam.cd / 1000) -- Simplified raid event logic

    -- Essence Break condition
    local essenceBreakCondition = not player:TalentKnown(A.EssenceBreakk.ID) or target:Debuff(debuffs.essence_break)

    -- Fel Barrage condition
    local felBarrageCondition = not player:Buff(buffs.fel_barrage)

    -- Adds/enemy condition (simplified)
    local addsCondition = gameState.activeEnemies > 3 -- Simplified desired_targets logic

    -- Chaotic Transformation condition
    local transformationCondition = not player:TalentKnown(A.ChaoticTransformation.ID) or target.ttd < 30000

    -- Inner Demon condition
    local innerDemonCondition = not player:Buff(buffs.inner_demon)

    -- Restless Hunter and Death Sweep conditions
    local restlessHunterCondition = (not player:TalentKnown(A.RestlessHunter.ID) and BladeDance.cd > MakGcd() * 3) or
                                   DeathSweep.lastUsed < 1000 or DeathSweep.lastUsed < 2000 or DeathSweep.lastUsed < 3000

    -- Talent restrictions
    local talentCondition = not player:TalentKnown(A.Inertia.ID) and not player:TalentKnown(A.EssenceBreakk.ID)

    -- Time condition
    local timeCondition = player.combatTime > 15

    -- Combine all conditions
    if (((eyeBeamCondition and essenceBreakCondition and felBarrageCondition and addsCondition) or transformationCondition) and
        innerDemonCondition and restlessHunterCondition and talentCondition and timeCondition) then
        return spell:Cast(player)
    end
end)

-- actions.ar_cooldown+=/metamorphosis,if=(cooldown.blade_dance.remains&((prev_gcd.1.death_sweep|prev_gcd.2.death_sweep|prev_gcd.3.death_sweep|buff.metamorphosis.up&buff.metamorphosis.remains<gcd.max)&cooldown.eye_beam.remains&buff.fel_barrage.down&(raid_event.adds.in>40|(raid_event.adds.remains>8|!talent.fel_barrage)&active_enemies>desired_targets|fight_style.dungeonroute&!raid_event.adds.in<=120)|!talent.chaotic_transformation|fight_remains<30)&(buff.inner_demon.down&(buff.rending_strike.down|!talent.restless_hunter|prev_gcd.1.death_sweep)))&(talent.inertia|talent.essence_break)&time>15
Metamorphosis:Callback("ar-cooldown-meta2-new", function(spell)
    if not Disrupt:InRange(target) then return end

    -- Blade Dance cooldown condition
    local bladeDanceCondition = BladeDance.cd > 0

    -- Previous GCD conditions (updated)
    local prevGcdCondition = DeathSweep.lastUsed < 1000 or DeathSweep.lastUsed < 2000 or DeathSweep.lastUsed < 3000 or
                            (player:Buff(buffs.metamorphosis) and player:BuffRemains(buffs.metamorphosis) < MakGcd())

    -- Eye Beam cooldown condition
    local eyeBeamCondition = EyeBeam.cd > 0

    -- Fel Barrage condition
    local felBarrageCondition = not player:Buff(buffs.fel_barrage)

    -- Adds condition (simplified)
    local addsCondition = gameState.activeEnemies > 3 -- Simplified desired_targets logic

    -- Chaotic Transformation condition
    local transformationCondition = not player:TalentKnown(A.ChaoticTransformation.ID) or target.ttd < 30000

    -- Inner demon and rending strike conditions
    local innerDemonCondition = not player:Buff(buffs.inner_demon) and
                               (not player:Buff(buffs.rending_strike) or not player:TalentKnown(A.RestlessHunter.ID) or DeathSweep.lastUsed < 1000)

    -- Talent conditions
    local talentCondition = player:TalentKnown(A.Inertia.ID) or player:TalentKnown(A.EssenceBreakk.ID)

    -- Time condition
    local timeCondition = player.combatTime > 15

    -- Combine all conditions
    if bladeDanceCondition and
       ((prevGcdCondition and eyeBeamCondition and felBarrageCondition and addsCondition) or transformationCondition) and
       innerDemonCondition and talentCondition and timeCondition then
        return spell:Cast(player)
    end
end)

-- actions.ar_cooldown+=/potion,if=fight_remains<35|(buff.metamorphosis.up|debuff.essence_break.up)&time>10
-- Note: Potion usage is typically handled by external addons or manual use

-- actions.ar_cooldown+=/invoke_external_buff,name=power_infusion,if=buff.metamorphosis.up|fight_remains<=20
-- Note: External buffs like Power Infusion are handled by other players

-- actions.ar_cooldown+=/variable,name=special_trinket,op=set,value=equipped.mad_queens_mandate|equipped.treacherous_transmitter|equipped.skardyns_grace|equipped.signet_of_the_priory|equipped.cursed_stone_idol
-- Special trinket variable is handled in the main rotation logic

-- Trinket usage callbacks (simplified for framework compatibility)
-- actions.ar_cooldown+=/use_item,slot=trinket1,if=...
-- actions.ar_cooldown+=/use_item,slot=trinket2,if=...
-- Note: Trinket usage is handled by the existing Trinket() calls in the main rotation

-- actions.ar_cooldown+=/the_hunt,if=debuff.essence_break.down&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>45)&(debuff.reavers_mark.up|raid_event.adds.remains>=15)&buff.reavers_glaive.down&(buff.metamorphosis.remains>5|buff.metamorphosis.down)&(!talent.initiative|buff.initiative.up|time>5)&time>5&(!talent.inertia&buff.unbound_chaos.down|buff.inertia_trigger.down)
TheHunt:Callback("ar-cooldown-hunt-new", function(spell)
    if not TheHunt:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end

    -- Enemy count condition (updated)
    if gameState.activeEnemies < 3 then return end -- Simplified desired_targets logic

    -- Reavers mark condition (updated)
    if not (target:Debuff(debuffs.reavers_mark) or gameState.activeEnemies >= 15) then return end -- Simplified raid event logic

    if player:Buff(buffs.reavers_glaive) then return end
    if not (player:BuffRemains(buffs.metamorphosis) > 5000 or not player:Buff(buffs.metamorphosis)) then return end
    if not (not player:TalentKnown(A.Initiative.ID) or player:Buff(buffs.initiative) or player.combatTime > 5) then return end
    if not (not player:TalentKnown(A.Inertia.ID) and not player:Buff(buffs.unbound_chaos) or not player:Buff(buffs.inertia_trigger)) then return end
    if player.combatTime < 5 then return end

    return spell:Cast(target)
end)

-- actions.ar_cooldown+=/sigil_of_spite,if=debuff.essence_break.down&(debuff.reavers_mark.remains>=2-talent.quickened_sigils)&cooldown.blade_dance.remains&time>15
SigilOfSpite:Callback("ar-cooldown-spite-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if target:DebuffRemains(debuffs.reavers_mark) < 2000 - (num(A.QuickenedSigils:IsTalentLearned()) * 1000) then return end
    if BladeDance.cd == 0 then return end -- Updated condition
    if player.combatTime < 15 then return end

    return spell:Cast(player)
end)


local function ar_cooldown()
    -- New AR Cooldown APL (SimC)
    Metamorphosis("ar-cooldown-meta-new")
    Metamorphosis("ar-cooldown-meta2-new")

    -- Potion usage (handled externally)
    -- External buffs (handled externally)
    -- Trinket variables and usage (handled by existing Trinket() calls)

    TheHunt("ar-cooldown-hunt-new")
    SigilOfSpite("ar-cooldown-spite-new")
end


--[[
NEW AR OPENER CALLBACKS (SimC APL)
]]--

-- actions.ar_opener=potion
-- Note: Potion usage is typically handled by external addons or manual use

-- actions.ar_opener+=/the_hunt
TheHunt:Callback("ar-opener-hunt-new", function(spell)
    if not TheHunt:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/vengeful_retreat,use_off_gcd=1,if=talent.initiative&time>4&buff.metamorphosis.up&(!talent.inertia|buff.inertia_trigger.down)&buff.inner_demon.down&cooldown.blade_dance.remains&gcd.remains<0.1
VengefulRetreat:Callback("ar-opener-retreat-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:TalentKnown(A.Initiative.ID) then return end
    if player.combatTime <= 4 then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if not (not player:TalentKnown(A.Inertia.ID) or not player:Buff(buffs.inertia_trigger)) then return end
    if player:Buff(buffs.inner_demon) then return end
    if BladeDance.cd > 0 then return end
    if MakGcd() >= 100 then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/death_sweep,if=!talent.chaotic_transformation&cooldown.metamorphosis.up&buff.glaive_flurry.up
DeathSweep:Callback("ar-opener-deathsweep-new", function(spell)
    if not DeathSweep:InRange(target) then return end
    if player:TalentKnown(A.ChaoticTransformation.ID) then return end
    if Metamorphosis.cd > 0 then return end
    if not player:Buff(buffs.glaive_flurry) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/annihilation,if=buff.rending_strike.up&buff.thrill_of_the_fight_damage.down
Annihilation:Callback("ar-opener-annihilation-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:Buff(buffs.rending_strike) then return end
    if player:Buff(buffs.thrill_of_the_fight_damage) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/felblade,if=!talent.inertia&talent.unbound_chaos&buff.unbound_chaos.up&buff.initiative.up&debuff.essence_break.down&active_enemies<=2
Felblade:Callback("ar-opener-felblade-new", function(spell)
    if not Felblade:InRange(target) then return end
    if player:TalentKnown(A.Inertia.ID) then return end
    if not player:TalentKnown(A.UnboundChaos.ID) then return end
    if not player:Buff(buffs.unbound_chaos) then return end
    if not player:Buff(buffs.initiative) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/fel_rush,if=!talent.inertia&talent.unbound_chaos&buff.unbound_chaos.up&buff.initiative.up&debuff.essence_break.down&active_enemies>2
FelRush:Callback("ar-opener-felrush-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if player:TalentKnown(A.Inertia.ID) then return end
    if not player:TalentKnown(A.UnboundChaos.ID) then return end
    if not player:Buff(buffs.unbound_chaos) then return end
    if not player:Buff(buffs.initiative) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if gameState.activeEnemies <= 2 then return end
    if ChaosStrike:InRange(target) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/annihilation,if=talent.inner_demon&buff.inner_demon.up&(!talent.essence_break|cooldown.essence_break.up)
Annihilation:Callback("ar-opener-annihilation2-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:TalentKnown(A.InnerDemon.ID) then return end
    if not player:Buff(buffs.inner_demon) then return end
    if not (not player:TalentKnown(A.EssenceBreakk.ID) or EssenceBreakk.cd == 0) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/essence_break,if=(buff.inertia.up|!talent.inertia)&buff.metamorphosis.up&cooldown.blade_dance.remains<=gcd.max&debuff.reavers_mark.up
EssenceBreakk:Callback("ar-opener-essencebreak-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not (player:Buff(buffs.inertia) or not player:TalentKnown(A.Inertia.ID)) then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if BladeDance.cd > MakGcd() then return end
    if not target:Debuff(debuffs.reavers_mark) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/felblade,if=buff.inertia_trigger.up&talent.inertia&talent.restless_hunter&cooldown.essence_break.up&cooldown.metamorphosis.up&buff.metamorphosis.up&cooldown.blade_dance.remains<=gcd.max
Felblade:Callback("ar-opener-felblade2-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:TalentKnown(A.RestlessHunter.ID) then return end
    if EssenceBreakk.cd > 0 then return end
    if Metamorphosis.cd > 0 then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if BladeDance.cd > MakGcd() then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/felblade,if=talent.inertia&buff.inertia_trigger.up&(buff.inertia.down&buff.metamorphosis.up)&debuff.essence_break.down&active_enemies<=2
Felblade:Callback("ar-opener-felblade3-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not (not player:Buff(buffs.inertia) and player:Buff(buffs.metamorphosis)) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if gameState.activeEnemies > 2 then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/fel_rush,if=talent.inertia&buff.inertia_trigger.up&(buff.inertia.down&buff.metamorphosis.up)&debuff.essence_break.down&(cooldown.felblade.remains|active_enemies>2)
FelRush:Callback("ar-opener-felrush2-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not (not player:Buff(buffs.inertia) and player:Buff(buffs.metamorphosis)) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (Felblade.cd > 0 or gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/felblade,if=talent.inertia&buff.inertia_trigger.up&buff.metamorphosis.up&cooldown.metamorphosis.remains&debuff.essence_break.down
Felblade:Callback("ar-opener-felblade4-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if Metamorphosis.cd == 0 then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/the_hunt,if=(buff.metamorphosis.up&hero_tree.aldrachi_reaver&talent.shattered_destiny|!talent.shattered_destiny&hero_tree.aldrachi_reaver|hero_tree.felscarred)&(!talent.initiative|talent.inertia|buff.initiative.up|time>5)
TheHunt:Callback("ar-opener-thehunt2-new", function(spell)
    if not TheHunt:InRange(target) then return end

    local heroTreeCondition = (player:Buff(buffs.metamorphosis) and player:TalentKnown(AldrachiReaver.id) and player:TalentKnown(A.ShatteredDestiny.ID)) or
                             (not player:TalentKnown(A.ShatteredDestiny.ID) and player:TalentKnown(AldrachiReaver.id)) or
                             player:TalentKnown(FelScarred.id)
    if not heroTreeCondition then return end

    local initiativeCondition = not player:TalentKnown(A.Initiative.ID) or player:TalentKnown(A.Inertia.ID) or
                               player:Buff(buffs.initiative) or player.combatTime > 5
    if not initiativeCondition then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/felblade,if=fury<40&buff.inertia_trigger.down&debuff.essence_break.down
Felblade:Callback("ar-opener-felblade5-new", function(spell)
    if not Felblade:InRange(target) then return end
    if player.fury >= 40 then return end
    if player:Buff(buffs.inertia_trigger) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/reavers_glaive,if=debuff.reavers_mark.down&debuff.essence_break.down
ReaversGlaive:Callback("ar-opener-reaversglaive-new", function(spell)
    if not ReaversGlaive:InRange(target) then return end
    if target:Debuff(debuffs.reavers_mark) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/chaos_strike,if=buff.rending_strike.up&active_enemies>2
ChaosStrike:Callback("ar-opener-chaosstrike-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.rending_strike) then return end
    if gameState.activeEnemies <= 2 then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/blade_dance,if=buff.glaive_flurry.up&active_enemies>2
BladeDance:Callback("ar-opener-bladedance-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.glaive_flurry) then return end
    if gameState.activeEnemies <= 2 then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/immolation_aura,if=talent.a_fire_inside&talent.burning_wound&buff.metamorphosis.down
ImmolationAura:Callback("ar-opener-immolationaura-new", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not player:TalentKnown(A.AFireInside.ID) then return end
    if not player:TalentKnown(A.BurningWound.ID) then return end
    if player:Buff(buffs.metamorphosis) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/metamorphosis,if=buff.metamorphosis.up&cooldown.blade_dance.remains>gcd.max*2&buff.inner_demon.down&(!talent.restless_hunter|prev_gcd.1.death_sweep)&(cooldown.essence_break.remains|!talent.essence_break|!talent.chaotic_transformation)
Metamorphosis:Callback("ar-opener-metamorphosis-new", function(spell)
    if not Disrupt:InRange(target) then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if BladeDance.cd <= MakGcd() * 2 then return end
    if player:Buff(buffs.inner_demon) then return end
    if player:TalentKnown(A.RestlessHunter.ID) and DeathSweep.lastUsed >= 1000 then return end
    if not (EssenceBreakk.cd > 0 or not player:TalentKnown(A.EssenceBreakk.ID) or not player:TalentKnown(A.ChaoticTransformation.ID)) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/sigil_of_spite,if=debuff.reavers_mark.up&(cooldown.eye_beam.remains&cooldown.metamorphosis.remains)&debuff.essence_break.down
SigilOfSpite:Callback("ar-opener-sigilofspite-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not target:Debuff(debuffs.reavers_mark) then return end
    if not (EyeBeam.cd > 0 and Metamorphosis.cd > 0) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/eye_beam,if=buff.metamorphosis.down|debuff.essence_break.down&buff.inner_demon.down&(cooldown.blade_dance.remains|talent.essence_break&cooldown.essence_break.up)
EyeBeam:Callback("ar-opener-eyebeam-new", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.moving then return end

    local metamorphosisCondition = not player:Buff(buffs.metamorphosis)
    local essenceBreakCondition = not target:Debuff(debuffs.essence_break) and not player:Buff(buffs.inner_demon) and
                                 (BladeDance.cd > 0 or (player:TalentKnown(A.EssenceBreakk.ID) and EssenceBreakk.cd == 0))

    if not (metamorphosisCondition or essenceBreakCondition) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/essence_break,if=cooldown.blade_dance.remains<gcd.max&!hero_tree.felscarred&!talent.shattered_destiny&buff.metamorphosis.up|cooldown.eye_beam.remains&cooldown.metamorphosis.remains
EssenceBreakk:Callback("ar-opener-essencebreak2-new", function(spell)
    if not ChaosStrike:InRange(target) then return end

    local bladeDanceCondition = BladeDance.cd < MakGcd() and not player:TalentKnown(FelScarred.id) and
                               not player:TalentKnown(A.ShatteredDestiny.ID) and player:Buff(buffs.metamorphosis)
    local cooldownCondition = EyeBeam.cd > 0 and Metamorphosis.cd > 0

    if not (bladeDanceCondition or cooldownCondition) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/death_sweep
DeathSweep:Callback("ar-opener-deathsweep2-new", function(spell)
    if not DeathSweep:InRange(target) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/annihilation
Annihilation:Callback("ar-opener-annihilation3-new", function(spell)
    if not Annihilation:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/demons_bite
DemonsBite:Callback("ar-opener-demonsbite-new", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/felblade,if=talent.inertia&buff.inertia_trigger.up&(buff.inertia.down&buff.metamorphosis.up)&debuff.essence_break.down&active_enemies<=2
Felblade:Callback("ar-opener-felblade3", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not (player:Buff(buffs.inertia) and player:Buff(buffs.metamorphosis)) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if gameState.activeEnemies > 2 then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/fel_rush,if=talent.inertia&buff.inertia_trigger.up&(buff.inertia.down&buff.metamorphosis.up)&debuff.essence_break.down&(cooldown.felblade.remains|active_enemies>2)
FelRush:Callback("ar-opener-felrush2", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not (player:Buff(buffs.inertia) and player:Buff(buffs.metamorphosis)) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if gameState.activeEnemies <= 2 and Felblade.cd > 0 then return end
    if ChaosStrike:InRange(target) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/felblade,if=talent.inertia&buff.inertia_trigger.up&buff.metamorphosis.up&cooldown.metamorphosis.remains&debuff.essence_break.down
Felblade:Callback("ar-opener-felblade4", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if Metamorphosis.cd == 0 then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/felblade,if=fury<40&buff.inertia_trigger.down&debuff.essence_break.down
Felblade:Callback("ar-opener-felblade5", function(spell)
    if not Felblade:InRange(target) then return end
    if player.fury >= 40 then return end
    if player:Buff(buffs.inertia_trigger) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/reavers_glaive,if=debuff.reavers_mark.down&debuff.essence_break.down
ReaversGlaive:Callback("ar-opener-reaversglaive", function(spell)
    if not ReaversGlaive:InRange(target) then return end
    if target:Debuff(debuffs.reavers_mark) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/chaos_strike,if=buff.rending_strike.up&active_enemies>2
ChaosStrike:Callback("ar-opener-chaosstrike", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.rending_strike) then return end
    if gameState.activeEnemies <= 2 then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/blade_dance,if=buff.glaive_flurry.up&active_enemies>2
BladeDance:Callback("ar-opener-bladedance", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.glaive_flurry) then return end
    if gameState.activeEnemies <= 2 then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/immolation_aura,if=talent.a_fire_inside&talent.burning_wound&buff.metamorphosis.down
ImmolationAura:Callback("ar-opener-immolationaura", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not player:TalentKnown(A.AFireInside.ID) then return end
    if not player:TalentKnown(A.BurningWound.ID) then return end
    if player:Buff(buffs.metamorphosis) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/metamorphosis,if=buff.metamorphosis.up&cooldown.blade_dance.remains>gcd.max*2&buff.inner_demon.down&(!talent.restless_hunter|prev_gcd.1.death_sweep)&(cooldown.essence_break.remains|!talent.essence_break)
Metamorphosis:Callback("ar-opener-metamorphosis", function(spell)
    if not Disrupt:InRange(target) then return end
    if player:Buff(buffs.metamorphosis) then return end
    if BladeDance.cd <= MakGcd() * 2 then return end
    if player:Buff(buffs.inner_demon) then return end
    --if player:TalentKnown(A.RestlessHunter.ID) then return end
    if EssenceBreakk.cd == 0 and player:TalentKnown(A.EssenceBreakk.ID) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/sigil_of_spite,if=debuff.reavers_mark.up&(cooldown.eye_beam.remains&cooldown.metamorphosis.remains)&debuff.essence_break.down
SigilOfSpite:Callback("ar-opener-sigilofspite", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not target:Debuff(debuffs.reavers_mark) then return end
    if EyeBeam.cd == 0 or Metamorphosis.cd == 0 then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/eye_beam,if=buff.metamorphosis.down|debuff.essence_break.down&buff.inner_demon.down&(cooldown.blade_dance.remains|talent.essence_break&cooldown.essence_break.up)
EyeBeam:Callback("ar-opener-eyebeam", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.moving then return end
    if player:Buff(buffs.metamorphosis) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.inner_demon) then return end
    --if BladeDance.cd == 0 then return end
    if player:TalentKnown(A.EssenceBreakk.ID) and EssenceBreakk.cd == 0 then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/essence_break,if=cooldown.blade_dance.remains<gcd.max&!hero_tree.felscarred&!talent.shattered_destiny&buff.metamorphosis.up|cooldown.eye_beam.remains&cooldown.metamorphosis.remains
EssenceBreakk:Callback("ar-opener-essencebreak2", function(spell)
    if not ChaosStrike:InRange(target) then return end
    --if BladeDance.cd >= MakGcd() then return end
    if player:TalentKnown(A.ShatteredDestiny.ID) then return end
    if player:Buff(buffs.metamorphosis) then return end
    if EyeBeam.cd == 0 or Metamorphosis.cd == 0 then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/death_sweep
DeathSweep:Callback("ar-opener-deathsweep", function(spell)
    if not DeathSweep:InRange(target) then return end

    return spell:Cast(player)
end)

-- actions.ar_opener+=/annihilation
Annihilation:Callback("ar-opener-annihilation2", function(spell)
    if not Annihilation:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.ar_opener+=/demons_bite
DemonsBite:Callback("ar-opener-demonsbite", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)

local function ar_opener()
    -- New AR Opener APL (SimC)
    -- Potion usage (handled externally)
    TheHunt("ar-opener-hunt-new")
    VengefulRetreat("ar-opener-retreat-new")
    DeathSweep("ar-opener-deathsweep-new")
    Annihilation("ar-opener-annihilation-new")
    Felblade("ar-opener-felblade-new")
    FelRush("ar-opener-felrush-new")
    Annihilation("ar-opener-annihilation2-new")
    EssenceBreakk("ar-opener-essencebreak-new")
    Felblade("ar-opener-felblade2-new")
    Felblade("ar-opener-felblade3-new")
    FelRush("ar-opener-felrush2-new")
    Felblade("ar-opener-felblade4-new")
    TheHunt("ar-opener-thehunt2-new")
    Felblade("ar-opener-felblade5-new")
    ReaversGlaive("ar-opener-reaversglaive-new")
    ChaosStrike("ar-opener-chaosstrike-new")
    BladeDance("ar-opener-bladedance-new")
    ImmolationAura("ar-opener-immolationaura-new")
    Metamorphosis("ar-opener-metamorphosis-new")
    SigilOfSpite("ar-opener-sigilofspite-new")
    EyeBeam("ar-opener-eyebeam-new")
    EssenceBreakk("ar-opener-essencebreak2-new")
    DeathSweep("ar-opener-deathsweep2-new")
    Annihilation("ar-opener-annihilation3-new")
    DemonsBite("ar-opener-demonsbite-new")
end

-- actions.ar+=/sigil_of_spite,if=debuff.essence_break.down&cooldown.blade_dance.remains&debuff.reavers_mark.remains>=2-talent.quickened_sigils&(buff.necessary_sacrifice.remains>=2-talent.quickened_sigils|!set_bonus.thewarwithin_season_2_4pc|cooldown.eye_beam.remains>8)&(buff.metamorphosis.down|buff.metamorphosis.remains+talent.shattered_destiny>=buff.necessary_sacrifice.remains+2-talent.quickened_sigils)|fight_remains<20
SigilOfSpite:Callback("ar-sigilofspite", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if BladeDance.cd == 0 then return end
    if target:DebuffRemains(debuffs.reavers_mark) < 2000 - (num(A.QuickenedSigils:IsTalentLearned()) * 1000) then return end
    if player:BuffRemains(buffs.necessary_sacrifice) < 2000 - (num(A.QuickenedSigils:IsTalentLearned()) * 1000) then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if player:BuffRemains(buffs.metamorphosis) + (num(A.ShatteredDestiny:IsTalentLearned()) * 1000) < player:BuffRemains(buffs.necessary_sacrifice) + 2000 - (num(A.QuickenedSigils:IsTalentLearned()) * 1000) then return end
    if EyeBeam.cd < 8000 then return end
    if target.ttd < 20 then return end

    return spell:Cast(player)
end)

--[[
NEW AR FEL BARRAGE CALLBACKS (SimC APL)
]]--

-- actions.ar_fel_barrage+=/annihilation,if=buff.inner_demon.up
Annihilation:Callback("ar-felbarrage-annihilation", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:Buff(buffs.inner_demon) then return end

    return spell:Cast(target)
end)

-- actions.ar_fel_barrage+=/eye_beam,if=(buff.fel_barrage.down|fury>45&talent.blind_fury)&(active_enemies>1&raid_event.adds.up|raid_event.adds.in>40-buff.cycle_of_hatred.stack*5)
EyeBeam:Callback("ar-felbarrage-eyebeam", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.moving then return end

    local felBarrageCondition = not player:Buff(buffs.fel_barrage) or
                               (player.fury > 45 and player:TalentKnown(A.BlindFury.ID))
    if not felBarrageCondition then return end

    local enemyCondition = gameState.activeEnemies > 1 -- Simplified raid event logic
    if not enemyCondition then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/essence_break,if=buff.fel_barrage.down&buff.metamorphosis.up
EssenceBreakk:Callback("ar-felbarrage-essencebreak", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player:Buff(buffs.fel_barrage) then return end
    if not player:Buff(buffs.metamorphosis) then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/death_sweep,if=buff.fel_barrage.down
DeathSweep:Callback("ar-felbarrage-deathsweep", function(spell)
    if not DeathSweep:InRange(target) then return end
    if player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/immolation_aura,if=(active_enemies>2|buff.fel_barrage.up)&(cooldown.eye_beam.remains>recharge_time+3)
ImmolationAura:Callback("ar-felbarrage-immolationaura", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (gameState.activeEnemies > 2 or player:Buff(buffs.fel_barrage)) then return end
    if EyeBeam.cd <= (spell:TimeToFullCharges() + 3000) then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/glaive_tempest,if=buff.fel_barrage.down&active_enemies>1
GlaiveTempest:Callback("ar-felbarrage-glaivetempest", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player:Buff(buffs.fel_barrage) then return end
    if gameState.activeEnemies <= 1 then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/blade_dance,if=buff.fel_barrage.down
BladeDance:Callback("ar-felbarrage-bladedance", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/fel_barrage,if=fury>100&(raid_event.adds.in>90|raid_event.adds.in<gcd.max|raid_event.adds.remains>4&active_enemies>2)
FelBarrage:Callback("ar-felbarrage-felbarrage", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if player.fury <= 100 then return end
    -- Simplified raid event logic - just check for multiple enemies
    if gameState.activeEnemies <= 2 then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/felblade,if=buff.inertia_trigger.up&buff.fel_barrage.up
Felblade:Callback("ar-felbarrage-felblade", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(target)
end)

-- actions.ar_fel_barrage+=/sigil_of_flame,if=fury.deficit>40&buff.fel_barrage.up
SigilOfFlame:Callback("ar-felbarrage-sigilofflame", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if (100 - player.fury) <= 40 then return end
    if not player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/felblade,if=buff.fel_barrage.up&fury.deficit>40
Felblade:Callback("ar-felbarrage-felblade2", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.fel_barrage) then return end
    if (100 - player.fury) <= 40 then return end

    return spell:Cast(target)
end)

-- actions.ar_fel_barrage+=/death_sweep,if=fury-variable.gcd_drain-35>0&(buff.fel_barrage.remains<3|variable.generator_up|fury>80|variable.fury_gen>18)
DeathSweep:Callback("ar-felbarrage-deathsweep2", function(spell)
    if not DeathSweep:InRange(target) then return end

    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost
    if (player.fury - gcdDrain - 35) <= 0 then return end

    local generatorUp = Felblade.cd < MakGcd() or SigilOfFlame.cd < MakGcd()
    local furyGen = furyGen or 0 -- Use global fury generation variable

    local condition = player:BuffRemains(buffs.fel_barrage) < 3000 or generatorUp or player.fury > 80 or furyGen > 18
    if not condition then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/glaive_tempest,if=fury-variable.gcd_drain-30>0&(buff.fel_barrage.remains<3|variable.generator_up|fury>80|variable.fury_gen>18)
GlaiveTempest:Callback("ar-felbarrage-glaivetempest2", function(spell)
    if not ChaosStrike:InRange(target) then return end

    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost
    if (player.fury - gcdDrain - 30) <= 0 then return end

    local generatorUp = Felblade.cd < MakGcd() or SigilOfFlame.cd < MakGcd()
    local furyGen = furyGen or 0 -- Use global fury generation variable

    local condition = player:BuffRemains(buffs.fel_barrage) < 3000 or generatorUp or player.fury > 80 or furyGen > 18
    if not condition then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/blade_dance,if=fury-variable.gcd_drain-35>0&(buff.fel_barrage.remains<3|variable.generator_up|fury>80|variable.fury_gen>18)
BladeDance:Callback("ar-felbarrage-bladedance2", function(spell)
    if not ChaosStrike:InRange(target) then return end

    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost
    if (player.fury - gcdDrain - 35) <= 0 then return end

    local generatorUp = Felblade.cd < MakGcd() or SigilOfFlame.cd < MakGcd()
    local furyGen = furyGen or 0 -- Use global fury generation variable

    local condition = player:BuffRemains(buffs.fel_barrage) < 3000 or generatorUp or player.fury > 80 or furyGen > 18
    if not condition then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/arcane_torrent,if=fury.deficit>40&buff.fel_barrage.up
ArcaneTorrent:Callback("ar-felbarrage-arcanetorrent", function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if (100 - player.fury) <= 40 then return end
    if not player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(player)
end)

-- actions.ar_fel_barrage+=/the_hunt,if=fury>40&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>80)
TheHunt:Callback("ar-felbarrage-thehunt", function(spell)
    if not TheHunt:InRange(target) then return end
    if player.fury <= 40 then return end
    if gameState.activeEnemies < 3 then return end -- Simplified desired_targets logic

    return spell:Cast(target)
end)

-- actions.ar_fel_barrage+=/annihilation,if=fury-variable.gcd_drain-40>20&(buff.fel_barrage.remains<3|variable.generator_up|fury>80|variable.fury_gen>18)
Annihilation:Callback("ar-felbarrage-annihilation2", function(spell)
    if not Annihilation:InRange(target) then return end

    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost
    if (player.fury - gcdDrain - 40) <= 20 then return end

    local generatorUp = Felblade.cd < MakGcd() or SigilOfFlame.cd < MakGcd()
    local furyGen = furyGen or 0 -- Use global fury generation variable

    local condition = player:BuffRemains(buffs.fel_barrage) < 3000 or generatorUp or player.fury > 80 or furyGen > 18
    if not condition then return end

    return spell:Cast(target)
end)

-- actions.ar_fel_barrage+=/chaos_strike,if=fury-variable.gcd_drain-40>20&(cooldown.fel_barrage.remains&cooldown.fel_barrage.remains<10&fury>100|buff.fel_barrage.up&(buff.fel_barrage.remains*variable.fury_gen-buff.fel_barrage.remains*32)>0)
ChaosStrike:Callback("ar-felbarrage-chaosstrike", function(spell)
    if not ChaosStrike:InRange(target) then return end

    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost
    if (player.fury - gcdDrain - 40) <= 20 then return end

    local furyGen = furyGen or 0 -- Use global fury generation variable
    local felBarrageCondition = (FelBarrage.cd > 0 and FelBarrage.cd < 10000 and player.fury > 100) or
                               (player:Buff(buffs.fel_barrage) and
                                (player:BuffRemains(buffs.fel_barrage) / 1000 * furyGen - player:BuffRemains(buffs.fel_barrage) / 1000 * 32) > 0)

    if not felBarrageCondition then return end

    return spell:Cast(target)
end)

-- actions.ar_fel_barrage+=/demons_bite
DemonsBite:Callback("ar-felbarrage-demonsbite", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)

local function ar_fel_barrage()
    -- Variables (calculated in main rotation)
    local generatorUp = Felblade.cd < MakGcd() or SigilOfFlame.cd < MakGcd()
    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost

    -- New AR Fel Barrage APL (SimC)
    Annihilation("ar-felbarrage-annihilation")
    EyeBeam("ar-felbarrage-eyebeam")
    EssenceBreakk("ar-felbarrage-essencebreak")
    DeathSweep("ar-felbarrage-deathsweep")
    ImmolationAura("ar-felbarrage-immolationaura")
    GlaiveTempest("ar-felbarrage-glaivetempest")
    BladeDance("ar-felbarrage-bladedance")
    FelBarrage("ar-felbarrage-felbarrage")
    Felblade("ar-felbarrage-felblade")
    SigilOfFlame("ar-felbarrage-sigilofflame")
    Felblade("ar-felbarrage-felblade2")
    DeathSweep("ar-felbarrage-deathsweep2")
    GlaiveTempest("ar-felbarrage-glaivetempest2")
    BladeDance("ar-felbarrage-bladedance2")
    ArcaneTorrent("ar-felbarrage-arcanetorrent")
    TheHunt("ar-felbarrage-thehunt")
    Annihilation("ar-felbarrage-annihilation2")
    ChaosStrike("ar-felbarrage-chaosstrike")
    DemonsBite("ar-felbarrage-demonsbite")
end

-- actions.ar+=/felblade,if=!talent.inertia&active_enemies=1&buff.unbound_chaos.up&buff.initiative.up&debuff.essence_break.down&buff.metamorphosis.down
Felblade:Callback("ar-felblade", function(spell)
    if not Felblade:InRange(target) then return end
    if player:TalentKnown(A.Inertia.ID) then return end
    if gameState.activeEnemies > 1 then return end
    if not player:Buff(buffs.unbound_chaos) then return end
    if not player:Buff(buffs.initiative) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.metamorphosis) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/felblade,if=buff.inertia_trigger.up&talent.inertia&cooldown.eye_beam.remains<=0.5&(cooldown.metamorphosis.remains&talent.looks_can_kill|active_enemies>1)
Felblade:Callback("ar-felblade2", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if EyeBeam.cd > 500 then return end
    if not (Metamorphosis.cd > 0 and player:TalentKnown(A.LooksCanKill.ID) or gameState.activeEnemies > 1) then return end

    return spell:Cast(target)
end)

--[[
NEW AR META CALLBACKS (SimC APL)
]]--

-- actions.ar_meta=death_sweep,if=buff.metamorphosis.remains<gcd.max|debuff.essence_break.up|cooldown.metamorphosis.up&!talent.restless_hunter
DeathSweep:Callback("ar-meta-deathsweep-new", function(spell)
    if not DeathSweep:InRange(target) then return end

    local metamorphosisCondition = player:BuffRemains(buffs.metamorphosis) < MakGcd()
    local essenceBreakCondition = target:Debuff(debuffs.essence_break)
    local cooldownCondition = Metamorphosis.cd == 0 and not player:TalentKnown(A.RestlessHunter.ID)

    if not (metamorphosisCondition or essenceBreakCondition or cooldownCondition) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/vengeful_retreat,use_off_gcd=1,if=talent.initiative&(cooldown.metamorphosis.remains&(cooldown.essence_break.remains<=0.6|cooldown.essence_break.remains>10|!talent.essence_break)|talent.restless_hunter)&cooldown.eye_beam.remains&(!talent.inertia&buff.unbound_chaos.down|buff.inertia_trigger.down)
VengefulRetreat:Callback("ar-meta-vengefulretreat-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:TalentKnown(A.Initiative.ID) then return end

    local metamorphosisCondition = Metamorphosis.cd > 0 and
                                  (EssenceBreakk.cd <= 600 or EssenceBreakk.cd > 10000 or not player:TalentKnown(A.EssenceBreakk.ID))
    local restlessHunterCondition = player:TalentKnown(A.RestlessHunter.ID)

    if not (metamorphosisCondition or restlessHunterCondition) then return end
    if EyeBeam.cd == 0 then return end
    if not (not player:TalentKnown(A.Inertia.ID) and not player:Buff(buffs.unbound_chaos) or not player:Buff(buffs.inertia_trigger)) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/felblade,if=talent.inertia&buff.inertia_trigger.up&cooldown.essence_break.remains<=1&cooldown.blade_dance.remains<=gcd.max*2&cooldown.metamorphosis.remains<=gcd.max*3
Felblade:Callback("ar-meta-felblade-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if EssenceBreakk.cd > 1000 then return end
    if BladeDance.cd > MakGcd() * 2 then return end
    if Metamorphosis.cd > MakGcd() * 3 then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/essence_break,if=fury>=30&talent.restless_hunter&cooldown.metamorphosis.up&(talent.inertia&buff.inertia.up|!talent.inertia)&cooldown.blade_dance.remains<=gcd.max
EssenceBreakk:Callback("ar-meta-essencebreak-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player.fury < 30 then return end
    if not player:TalentKnown(A.RestlessHunter.ID) then return end
    if Metamorphosis.cd > 0 then return end
    if player:TalentKnown(A.Inertia.ID) and not player:Buff(buffs.inertia) then return end
    if BladeDance.cd > MakGcd() then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/annihilation,if=buff.metamorphosis.remains<gcd.max|debuff.essence_break.remains&debuff.essence_break.remains<0.5&cooldown.blade_dance.remains|buff.inner_demon.up&cooldown.essence_break.up&cooldown.metamorphosis.up
Annihilation:Callback("ar-meta-annihilation-new", function(spell)
    if not Annihilation:InRange(target) then return end

    local metamorphosisCondition = player:BuffRemains(buffs.metamorphosis) < MakGcd()
    local essenceBreakCondition = target:DebuffRemains(debuffs.essence_break) > 0 and
                                 target:DebuffRemains(debuffs.essence_break) < 500 and BladeDance.cd > 0
    local innerDemonCondition = player:Buff(buffs.inner_demon) and EssenceBreakk.cd == 0 and Metamorphosis.cd == 0

    if not (metamorphosisCondition or essenceBreakCondition or innerDemonCondition) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/felblade,if=buff.inertia_trigger.up&talent.inertia&cooldown.metamorphosis.remains&(cooldown.eye_beam.remains<=0.5|cooldown.essence_break.remains<=0.5|cooldown.blade_dance.remains<=5.5|buff.initiative.remains<gcd.remains)
Felblade:Callback("ar-meta-felblade2-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if Metamorphosis.cd == 0 then return end

    local cooldownCondition = EyeBeam.cd <= 500 or EssenceBreakk.cd <= 500 or
                             BladeDance.cd <= 5500 or player:BuffRemains(buffs.initiative) < MakGcd()
    if not cooldownCondition then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/fel_rush,if=buff.inertia_trigger.up&talent.inertia&cooldown.metamorphosis.remains&active_enemies>2
FelRush:Callback("ar-meta-felrush-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if Metamorphosis.cd == 0 then return end
    if gameState.activeEnemies <= 2 then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/fel_rush,if=buff.inertia_trigger.up&talent.inertia&cooldown.blade_dance.remains<gcd.max*3&cooldown.metamorphosis.remains&active_enemies>2
FelRush:Callback("ar-meta-felrush2-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if BladeDance.cd >= MakGcd() * 3 then return end
    if Metamorphosis.cd == 0 then return end
    if gameState.activeEnemies <= 2 then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/immolation_aura,if=charges=2&active_enemies>1&debuff.essence_break.down
ImmolationAura:Callback("ar-meta-immolationaura-new", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if spell.charges ~= 2 then return end
    if gameState.activeEnemies <= 1 then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/annihilation,if=buff.inner_demon.up&(cooldown.eye_beam.remains<gcd.max*3&cooldown.blade_dance.remains|cooldown.metamorphosis.remains<gcd.max*3)
Annihilation:Callback("ar-meta-annihilation2-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:Buff(buffs.inner_demon) then return end

    local eyeBeamCondition = EyeBeam.cd < MakGcd() * 3 and BladeDance.cd > 0
    local metamorphosisCondition = Metamorphosis.cd < MakGcd() * 3

    if not (eyeBeamCondition or metamorphosisCondition) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/essence_break,if=time<20&buff.thrill_of_the_fight_damage.remains>gcd.max*4&buff.metamorphosis.remains>=gcd.max*2&cooldown.metamorphosis.up&cooldown.death_sweep.remains<=gcd.max&buff.inertia.up
EssenceBreakk:Callback("ar-meta-essencebreak2-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player.combatTime >= 20 then return end
    if player:BuffRemains(buffs.thrill_of_the_fight_damage) <= MakGcd() * 4 then return end
    if player:BuffRemains(buffs.metamorphosis) < MakGcd() * 2 then return end
    if Metamorphosis.cd > 0 then return end
    if DeathSweep.cd > MakGcd() then return end
    if not player:Buff(buffs.inertia) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/essence_break,if=fury>20&(cooldown.blade_dance.remains<gcd.max*3|cooldown.blade_dance.up)&(buff.unbound_chaos.down&!talent.inertia|buff.inertia.up)&buff.out_of_range.remains<gcd.max&(!talent.shattered_destiny|cooldown.eye_beam.remains>4)|fight_remains<10
EssenceBreakk:Callback("ar-meta-essencebreak3-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player.fury <= 20 then return end

    local bladeDanceCondition = BladeDance.cd < MakGcd() * 3 or BladeDance.cd == 0
    if not bladeDanceCondition then return end

    local chaosCondition = not player:Buff(buffs.unbound_chaos) and not player:TalentKnown(A.Inertia.ID) or player:Buff(buffs.inertia)
    if not chaosCondition then return end

    if player:BuffRemains(buffs.out_of_range) >= MakGcd() then return end

    local destinyCondition = not player:TalentKnown(A.ShatteredDestiny.ID) or EyeBeam.cd > 4000
    local fightCondition = target.ttd < 10000

    if not (destinyCondition or fightCondition) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/death_sweep
DeathSweep:Callback("ar-meta-deathsweep2-new", function(spell)
    if not DeathSweep:InRange(target) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/eye_beam,if=debuff.essence_break.down&buff.inner_demon.down
EyeBeam:Callback("ar-meta-eyebeam-new", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.moving then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.inner_demon) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/glaive_tempest,if=debuff.essence_break.down&(cooldown.blade_dance.remains>gcd.max*2|fury>60)&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>10)
GlaiveTempest:Callback("ar-meta-glaivetempest-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (BladeDance.cd > MakGcd() * 2 or player.fury > 60) then return end
    if gameState.activeEnemies < 3 then return end -- Simplified desired_targets logic

    return spell:Cast(player)
end)

-- actions.ar_meta+=/sigil_of_flame,if=active_enemies>2&debuff.essence_break.down
SigilOfFlame:Callback("ar-meta-sigilofflame-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if gameState.activeEnemies <= 2 then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/throw_glaive,if=talent.soulscar&talent.furious_throws&active_enemies>2&debuff.essence_break.down&(charges=2|full_recharge_time<cooldown.blade_dance.remains)
ThrowGlaive:Callback("ar-meta-throwglaive-new", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if not player:TalentKnown(A.Soulscar.ID) then return end
    if not player:TalentKnown(A.FuriousThrows.ID) then return end
    if gameState.activeEnemies <= 2 then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (spell.charges == 2 or spell:TimeToFullCharges() < BladeDance.cd) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/annihilation,if=cooldown.blade_dance.remains|fury>60|soul_fragments.total>0|buff.metamorphosis.remains<5&cooldown.felblade.up|debuff.essence_break.up
Annihilation:Callback("ar-meta-annihilation3-new", function(spell)
    if not Annihilation:InRange(target) then return end

    local bladeDanceCondition = BladeDance.cd > 0
    local furyCondition = player.fury > 60
    local fragmentsCondition = true -- Simplified soul fragments logic
    local metamorphosisCondition = player:BuffRemains(buffs.metamorphosis) < 5000 and Felblade.cd == 0
    local essenceBreakCondition = target:Debuff(debuffs.essence_break)

    if not (bladeDanceCondition or furyCondition or fragmentsCondition or metamorphosisCondition or essenceBreakCondition) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/sigil_of_flame,if=buff.metamorphosis.remains>5&buff.out_of_range.down&fury.deficit>=30+variable.fury_gen*gcd.max+active_enemies*talent.flames_of_fury.rank
SigilOfFlame:Callback("ar-meta-sigilofflame2-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player:BuffRemains(buffs.metamorphosis) <= 5000 then return end
    if player:Buff(buffs.out_of_range) then return end

    local furyDeficit = 100 - player.fury
    local furyGenThreshold = 30 + (furyGen or 0) * MakGcd() / 1000 + gameState.activeEnemies * 0 -- Simplified flames_of_fury
    if furyDeficit < furyGenThreshold then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/felblade,if=fury.deficit>=40+variable.fury_gen*0.5&!buff.inertia_trigger.up
Felblade:Callback("ar-meta-felblade3-new", function(spell)
    if not Felblade:InRange(target) then return end
    local furyDeficit = 100 - player.fury
    local furyGenThreshold = 40 + (furyGen or 0) * 0.5
    if furyDeficit < furyGenThreshold then return end
    if player:Buff(buffs.inertia_trigger) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/sigil_of_flame,if=debuff.essence_break.down&buff.out_of_range.down&fury.deficit>=30+variable.fury_gen*gcd.max+active_enemies*talent.flames_of_fury.rank
SigilOfFlame:Callback("ar-meta-sigilofflame3-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.out_of_range) then return end

    local furyDeficit = 100 - player.fury
    local furyGenThreshold = 30 + (furyGen or 0) * MakGcd() / 1000 + gameState.activeEnemies * 0 -- Simplified flames_of_fury
    if furyDeficit < furyGenThreshold then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/immolation_aura,if=buff.out_of_range.down&recharge_time<(cooldown.eye_beam.remains<?buff.metamorphosis.remains)&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>full_recharge_time)
ImmolationAura:Callback("ar-meta-immolationaura2-new", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if player:Buff(buffs.out_of_range) then return end

    local timeThreshold = math.min(EyeBeam.cd, player:BuffRemains(buffs.metamorphosis))
    if spell:TimeToFullCharges() >= timeThreshold then return end
    if gameState.activeEnemies < 3 then return end -- Simplified desired_targets logic

    return spell:Cast(player)
end)

-- actions.ar_meta+=/annihilation
Annihilation:Callback("ar-meta-annihilation4-new", function(spell)
    if not Annihilation:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/throw_glaive,if=buff.unbound_chaos.down&recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&buff.out_of_range.down&active_enemies>1&!talent.furious_throws
ThrowGlaive:Callback("ar-meta-throwglaive2-new", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if player:Buff(buffs.unbound_chaos) then return end
    if spell:TimeToFullCharges() >= EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.charges > 1.01) then return end
    if player:Buff(buffs.out_of_range) then return end
    if gameState.activeEnemies <= 1 then return end
    if player:TalentKnown(A.FuriousThrows.ID) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/fel_rush,if=recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&buff.out_of_range.down&active_enemies>1
FelRush:Callback("ar-meta-felrush3-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if spell:TimeToFullCharges() >= EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.charges > 1.01) then return end
    if player:Buff(buffs.out_of_range) then return end
    if gameState.activeEnemies <= 1 then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/demons_bite
DemonsBite:Callback("ar-meta-demonsbite-new", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/immolation_aura,if=charges=2&active_enemies>1&debuff.essence_break.down
ImmolationAura:Callback("ar-meta-immolationaura", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (spell.charges == 2) then return end
    if not (gameState.activeEnemies > 1) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/annihilation,if=buff.inner_demon.up&(cooldown.eye_beam.remains<gcd.max*3&cooldown.blade_dance.remains|cooldown.metamorphosis.remains<gcd.max*3)
Annihilation:Callback("ar-meta-annihilation2", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:Buff(buffs.inner_demon) then return end
    if EyeBeam.cd > MakGcd() * 3 then return end
    if BladeDance.cd > 0 then return end
    if Metamorphosis.cd > MakGcd() * 3 then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/essence_break,if=time<20&buff.thrill_of_the_fight_damage.remains>gcd.max*4&buff.metamorphosis.remains>=gcd.max*2&cooldown.metamorphosis.up&cooldown.death_sweep.remains<=gcd.max&buff.inertia.up
EssenceBreakk:Callback("ar-meta-essencebreak2", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player.combatTime >= 20 then return end
    --if player:BuffRemains(buffs.thrill_of_the_fight_damage) < MakGcd() * 4 then return end
    --if player:BuffRemains(buffs.metamorphosis) < MakGcd() * 2 then return end
    if Metamorphosis.cd > 0 then return end
    --if DeathSweep.cd > MakGcd() then return end
    if not player:Buff(buffs.inertia) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/essence_break,if=fury>20&(cooldown.blade_dance.remains<gcd.max*3|cooldown.blade_dance.up)&(buff.unbound_chaos.down&!talent.inertia|buff.inertia.up)&buff.out_of_range.remains<gcd.max&(!talent.shattered_destiny|cooldown.eye_beam.remains>4)|fight_remains<10
EssenceBreakk:Callback("ar-meta-essencebreak3", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player.fury < 20 then return end
    --if BladeDance.cd > MakGcd() * 3 then return end
    if not (not player:Buff(buffs.unbound_chaos) and not player:TalentKnown(A.Inertia.ID) or player:Buff(buffs.inertia)) then return end
    if player:BuffRemains(buffs.out_of_range) > MakGcd() then return end
    if not (not player:TalentKnown(A.ShatteredDestiny.ID) or EyeBeam.cd > 4000) then return end
    if target.ttd < 1000 then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/death_sweep
DeathSweep:Callback("ar-meta-deathsweep2", function(spell)
    if not DeathSweep:InRange(target) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/eye_beam,if=debuff.essence_break.down&buff.inner_demon.down
EyeBeam:Callback("ar-meta-eyebeam", function(spell)
    if player.moving then return end
    if not Disrupt:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.inner_demon) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/glaive_tempest,if=debuff.essence_break.down&(cooldown.blade_dance.remains>gcd.max*2|fury>60)&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>10)
GlaiveTempest:Callback("ar-meta-glaivetempest", function(spell)
    if not GlaiveTempest:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if BladeDance.cd < MakGcd() * 2 then return end
    if player.fury < 60 then return end
    if not (gameState.activeEnemies >= 3 or gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/sigil_of_flame,if=active_enemies>2&debuff.essence_break.down
SigilOfFlame:Callback("ar-meta-sigilofflame", function(spell)
    if not Disrupt:InRange(target) then return end
    if not (gameState.activeEnemies > 2) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/throw_glaive,if=talent.soulscar&talent.furious_throws&active_enemies>1&debuff.essence_break.down&(charges=2|full_recharge_time<cooldown.blade_dance.remains)
ThrowGlaive:Callback("ar-meta-throwglaive", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if not player:TalentKnown(A.Soulscar.ID) then return end
    if not player:TalentKnown(A.FuriousThrows.ID) then return end
    if not (gameState.activeEnemies > 1) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (spell.charges == 2 or spell:TimeToFullCharges() < BladeDance.cd) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/annihilation,if=cooldown.blade_dance.remains|fury>60|soul_fragments.total>0|buff.metamorphosis.remains<5&cooldown.felblade.up
Annihilation:Callback("ar-meta-annihilation3", function(spell)
    if not Annihilation:InRange(target) then return end
    if BladeDance.cd > 0 then return end
    if player.fury < 60 then return end
    --if player.soul_fragments < 1 then return end
    if player:BuffRemains(buffs.metamorphosis) > 5000 then return end
    if Felblade.cd > 0 then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/sigil_of_flame,if=buff.metamorphosis.remains>5&buff.out_of_range.down
SigilOfFlame:Callback("ar-meta-sigilofflame2", function(spell)
    if not Disrupt:InRange(target) then return end
    if player:BuffRemains(buffs.metamorphosis) < 5000 then return end
    if player:Buff(buffs.out_of_range) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/felblade,if=fury.deficit>40&!buff.inertia_trigger.up
Felblade:Callback("ar-meta-felblade3", function(spell)
    if not Felblade:InRange(target) then return end
    if player.furyDeficit < 40 then return end
    if player:Buff(buffs.inertia_trigger) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/sigil_of_flame,if=debuff.essence_break.down&buff.out_of_range.down
SigilOfFlame:Callback("ar-meta-sigilofflame3", function(spell)
    if not Disrupt:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.out_of_range) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/immolation_aura,if=buff.out_of_range.down&recharge_time<(cooldown.eye_beam.remains<?buff.metamorphosis.remains)&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>full_recharge_time)
ImmolationAura:Callback("ar-meta-immolationaura2", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if player:Buff(buffs.out_of_range) then return end
    if 90 > math.min(EyeBeam.cd, player:BuffRemains(buffs.metamorphosis)) then return end
    if not (gameState.activeEnemies >= 3 or gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/annihilation
Annihilation:Callback("ar-meta-annihilation4", function(spell)
    if not Annihilation:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.ar_meta+=/fel_rush,if=recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&buff.out_of_range.down&active_enemies>1
FelRush:Callback("ar-meta-felrush3", function(spell)
    if Disrupt:InRange(target) then return end
    if 90 > EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.frac > 1.01) then return end
    if player:Buff(buffs.out_of_range) then return end
    if not (gameState.activeEnemies > 1) then return end
    if ChaosStrike:InRange(target) then return end

    return spell:Cast(player)
end)

-- actions.ar_meta+=/demons_bite
DemonsBite:Callback("ar-meta-demonsbite", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/immolation_aura,if=talent.a_fire_inside&talent.burning_wound&full_recharge_time<gcd.max*2&(raid_event.adds.in>full_recharge_time|active_enemies>desired_targets)
ImmolationAura:Callback("ar-immolationaura", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not player:TalentKnown(A.AFireInside.ID) then return end
    if not player:TalentKnown(A.BurningWound.ID) then return end
    if spell:TimeToFullCharges() > MakGcd() * 2 then return end
    if not (gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

local function ar_meta()
    -- New AR Meta APL (SimC)
    DeathSweep("ar-meta-deathsweep-new")
    VengefulRetreat("ar-meta-vengefulretreat-new")
    Felblade("ar-meta-felblade-new")
    EssenceBreakk("ar-meta-essencebreak-new")
    Annihilation("ar-meta-annihilation-new")
    Felblade("ar-meta-felblade2-new")
    FelRush("ar-meta-felrush-new")
    FelRush("ar-meta-felrush2-new")
    ImmolationAura("ar-meta-immolationaura-new")
    Annihilation("ar-meta-annihilation2-new")
    EssenceBreakk("ar-meta-essencebreak2-new")
    EssenceBreakk("ar-meta-essencebreak3-new")
    DeathSweep("ar-meta-deathsweep2-new")
    EyeBeam("ar-meta-eyebeam-new")
    GlaiveTempest("ar-meta-glaivetempest-new")
    SigilOfFlame("ar-meta-sigilofflame-new")
    ThrowGlaive("ar-meta-throwglaive-new")
    Annihilation("ar-meta-annihilation3-new")
    SigilOfFlame("ar-meta-sigilofflame2-new")
    Felblade("ar-meta-felblade3-new")
    SigilOfFlame("ar-meta-sigilofflame3-new")
    ImmolationAura("ar-meta-immolationaura2-new")
    Annihilation("ar-meta-annihilation4-new")
    ThrowGlaive("ar-meta-throwglaive2-new")
    FelRush("ar-meta-felrush3-new")
    DemonsBite("ar-meta-demonsbite-new")
end

-- actions.ar+=/felblade,if=buff.inertia_trigger.up&talent.inertia&buff.inertia.down&cooldown.blade_dance.remains<4&(cooldown.eye_beam.remains>5&cooldown.eye_beam.remains>buff.unbound_chaos.remains|cooldown.eye_beam.remains<=gcd.max&cooldown.vengeful_retreat.remains<=gcd.max+1)
Felblade:Callback("ar-felblade3", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if player:Buff(buffs.inertia) then return end
    if BladeDance.cd > 4000 then return end
    if not (EyeBeam.cd > 5000 and EyeBeam.cd > player:BuffRemains(buffs.unbound_chaos) or EyeBeam.cd <= MakGcd() and VengefulRetreat.cd <= MakGcd() + 1000) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/immolation_aura,if=active_enemies>desired_targets&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>full_recharge_time)
ImmolationAura:Callback("ar-immolationaura2", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (gameState.activeEnemies > 2) then return end
    if not (gameState.activeEnemies >= 3 or gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/immolation_aura,if=fight_remains<15&cooldown.blade_dance.remains&talent.ragefire
ImmolationAura:Callback("ar-immolationaura3", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if player.combatTime > 15 then return end
    if BladeDance.cd > 0 then return end
    if not player:TalentKnown(A.Ragefire.ID) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/blade_dance,if=cooldown.eye_beam.remains>=gcd.max*2&buff.rending_strike.down
BladeDance:Callback("ar-bladedance", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if EyeBeam.cd < MakGcd() * 2 then return end
    if player:Buff(buffs.rending_strike) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/chaos_strike,if=buff.rending_strike.up
ChaosStrike:Callback("ar-chaosstrike3", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.rending_strike) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/sigil_of_flame,if=active_enemies>3|debuff.essence_break.down
SigilOfFlame:Callback("ar-sigilofflame4", function(spell)
    if not Disrupt:InRange(target) then return end
    if not (gameState.activeEnemies > 3 or not target:Debuff(debuffs.essence_break)) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/felblade,if=fury.deficit>=40+variable.fury_gen*0.5&!buff.inertia_trigger.up&(!talent.blind_fury|cooldown.eye_beam.remains>5)
Felblade:Callback("ar-felblade4", function(spell)
    if not Felblade:InRange(target) then return end
    if player.furyDeficit < 40 + 5 * 0.5 then return end
    if player:Buff(buffs.inertia_trigger) then return end
    if not (not player:TalentKnown(A.BlindFury.ID) or EyeBeam.cd > 5000) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/glaive_tempest,if=active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>10
GlaiveTempest:Callback("ar-glaivetempest", function(spell)
    if not GlaiveTempest:InRange(target) then return end
    if not (gameState.activeEnemies >= 3 or gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/chaos_strike,if=debuff.essence_break.up
ChaosStrike:Callback("ar-chaosstrike4", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/throw_glaive,if=active_enemies>1&talent.furious_throws&(!talent.screaming_brutality|charges=2|full_recharge_time<cooldown.blade_dance.remains)
ThrowGlaive:Callback("ar-throwglaive", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if gameState.activeEnemies <= 1 then return end
    if not player:TalentKnown(A.FuriousThrows.ID) then return end
    if not (not player:TalentKnown(A.ScreamingBrutality.ID) or spell.charges == 2 or 30 < BladeDance.cd) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/chaos_strike,if=cooldown.eye_beam.remains>gcd.max*2|fury>=80-20*talent.blind_fury.rank
ChaosStrike:Callback("ar-chaosstrike5", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if EyeBeam.cd <= MakGcd() * 2 then return end
    if player.fury < 80 - 20 * player:TalentRank(A.BlindFury.ID) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/felblade,if=!talent.a_fire_inside&fury<40
Felblade:Callback("ar-felblade5", function(spell)
    if not Felblade:InRange(target) then return end
    if player:TalentKnown(A.AFireInside.ID) then return end
    if player.fury >= 40 then return end

    return spell:Cast(target)
end)

-- actions.ar+=/immolation_aura,if=raid_event.adds.in>full_recharge_time|active_enemies>desired_targets&active_enemies>2
ImmolationAura:Callback("ar-immolationaura4", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (gameState.activeEnemies > 2) then return end
    if not (gameState.activeEnemies >= 3 or gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/sigil_of_flame,if=buff.out_of_range.down&debuff.essence_break.down&(!talent.fel_barrage|cooldown.fel_barrage.remains>25|active_enemies=1&!raid_event.adds.exists)
SigilOfFlame:Callback("ar-sigilofflame5", function(spell)
    if not Disrupt:InRange(target) then return end
    if player:Buff(buffs.out_of_range) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:TalentKnown(A.FelBarrage.ID) and FelBarrage.cd < 25000 then return end
    if gameState.activeEnemies == 1 and gameState.activeEnemies > 2 then return end

    return spell:Cast(player)
end)

-- actions.ar+=/demons_bite
DemonsBite:Callback("ar-demonsbite", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/throw_glaive,if=buff.unbound_chaos.down&recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&buff.out_of_range.down&active_enemies>1
ThrowGlaive:Callback("ar-throwglaive2", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if player:Buff(buffs.unbound_chaos) then return end
    if 30 > EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if EyeBeam.cd <= 8000 and spell.frac <= 1.01 then return end
    if player:Buff(buffs.out_of_range) then return end
    if gameState.activeEnemies <= 1 then return end

    return spell:Cast(target)
end)

-- actions.ar+=/fel_rush,if=buff.unbound_chaos.down&recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&active_enemies>1
FelRush:Callback("ar-felrush", function(spell)
    if Disrupt:InRange(target) then return end
    if player:Buff(buffs.unbound_chaos) then return end
    if FelRush:TimeToFullCharges() > EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if EyeBeam.cd <= 8000 and spell.frac <= 1.01 then return end
    if gameState.activeEnemies <= 1 then return end
    if ChaosStrike:InRange(target) then return end

    return spell:Cast(target)
end)

EyeBeam:Callback("ar-eyebeam", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.fury > 50 then return end
    if player.moving then return end

    return spell:Cast(player)
end)

ChaosStrike:Callback("ar-chaosstrike6", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player.combatTime < 10 then return end
    if player.fury < 70 then return end

    return spell:Cast(target)
end)

ThrowGlaive:Callback("ar-throwglaive3", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if player.fury < 70 then return end

    return spell:Cast(target)
end)

Felblade:Callback("ar-felblade6", function(spell)
    if not Felblade:InRange(target) then return end
    if player.fury < 70 then return end

    return spell:Cast(target)
end)

--[[
-- END REAVER
--]]

--[[
-- START FelScarred
--]]

--[[
NEW FS COOLDOWN CALLBACKS (SimC APL)
]]--

-- actions.fs_cooldown=metamorphosis,if=(((cooldown.eye_beam.remains>=20|talent.cycle_of_hatred&cooldown.eye_beam.remains>=13|raid_event.adds.remains>8&raid_event.adds.remains<cooldown.eye_beam.remains)&(!talent.essence_break|debuff.essence_break.up)&buff.fel_barrage.down&(raid_event.adds.in>40|(raid_event.adds.remains>8|!talent.fel_barrage)&active_enemies>desired_targets|fight_style.dungeonroute&!raid_event.adds.in<=120)|!talent.chaotic_transformation|fight_remains<30)&buff.inner_demon.down&(!talent.restless_hunter&cooldown.blade_dance.remains>gcd.max*3|prev_gcd.1.death_sweep))&!talent.inertia&!talent.essence_break&time>15
Metamorphosis:Callback("fs-cooldown-meta-new", function(spell)
    if not Disrupt:InRange(target) then return end

    -- Eye Beam cooldown conditions (updated)
    local eyeBeamCondition = EyeBeam.cd >= 20000 or
                            (player:TalentKnown(A.CycleOfHatred.ID) and EyeBeam.cd >= 13000) or
                            (gameState.activeEnemies > 8 and gameState.activeEnemies < EyeBeam.cd / 1000) -- Simplified raid event logic

    -- Essence Break condition
    local essenceBreakCondition = not player:TalentKnown(A.EssenceBreakk.ID) or target:Debuff(debuffs.essence_break)

    -- Fel Barrage condition
    local felBarrageCondition = not player:Buff(buffs.fel_barrage)

    -- Adds/enemy condition (simplified)
    local addsCondition = gameState.activeEnemies > 3 -- Simplified desired_targets logic

    -- Chaotic Transformation condition
    local transformationCondition = not player:TalentKnown(A.ChaoticTransformation.ID) or target.ttd < 30000

    -- Inner Demon condition
    local innerDemonCondition = not player:Buff(buffs.inner_demon)

    -- Restless Hunter and Death Sweep conditions
    local restlessHunterCondition = (not player:TalentKnown(A.RestlessHunter.ID) and BladeDance.cd > MakGcd() * 3) or
                                   DeathSweep.lastUsed < 1000

    -- Talent restrictions
    local talentCondition = not player:TalentKnown(A.Inertia.ID) and not player:TalentKnown(A.EssenceBreakk.ID)

    -- Time condition
    local timeCondition = player.combatTime > 15

    -- Combine all conditions
    if (((eyeBeamCondition and essenceBreakCondition and felBarrageCondition and addsCondition) or transformationCondition) and
        innerDemonCondition and restlessHunterCondition and talentCondition and timeCondition) then
        return spell:Cast(player)
    end
end)

-- actions.fs_cooldown+=/metamorphosis,if=(cooldown.blade_dance.remains&((prev_gcd.1.death_sweep|prev_gcd.2.death_sweep|prev_gcd.3.death_sweep|buff.metamorphosis.up&buff.metamorphosis.remains<gcd.max)&cooldown.eye_beam.remains&buff.fel_barrage.down&(raid_event.adds.in>40|(raid_event.adds.remains>8|!talent.fel_barrage)&active_enemies>desired_targets|fight_style.dungeonroute&!raid_event.adds.in<=120)|!talent.chaotic_transformation|fight_remains<30)&(buff.inner_demon.down&(buff.rending_strike.down|!talent.restless_hunter|prev_gcd.1.death_sweep)))&(talent.inertia|talent.essence_break)&time>15
Metamorphosis:Callback("fs-cooldown-meta2-new", function(spell)
    if not Disrupt:InRange(target) then return end

    -- Blade Dance cooldown condition
    local bladeDanceCondition = BladeDance.cd > 0

    -- Previous GCD conditions (updated)
    local prevGcdCondition = DeathSweep.lastUsed < 1000 or DeathSweep.lastUsed < 2000 or DeathSweep.lastUsed < 3000 or
                            (player:Buff(buffs.metamorphosis) and player:BuffRemains(buffs.metamorphosis) < MakGcd())

    -- Eye Beam cooldown condition
    local eyeBeamCondition = EyeBeam.cd > 0

    -- Fel Barrage condition
    local felBarrageCondition = not player:Buff(buffs.fel_barrage)

    -- Adds condition (simplified)
    local addsCondition = gameState.activeEnemies > 3 -- Simplified desired_targets logic

    -- Chaotic Transformation condition
    local transformationCondition = not player:TalentKnown(A.ChaoticTransformation.ID) or target.ttd < 30000

    -- Inner demon and rending strike conditions
    local innerDemonCondition = not player:Buff(buffs.inner_demon) and
                               (not player:Buff(buffs.rending_strike) or not player:TalentKnown(A.RestlessHunter.ID) or DeathSweep.lastUsed < 1000)

    -- Talent conditions
    local talentCondition = player:TalentKnown(A.Inertia.ID) or player:TalentKnown(A.EssenceBreakk.ID)

    -- Time condition
    local timeCondition = player.combatTime > 15

    -- Combine all conditions
    if bladeDanceCondition and
       ((prevGcdCondition and eyeBeamCondition and felBarrageCondition and addsCondition) or transformationCondition) and
       innerDemonCondition and talentCondition and timeCondition then
        return spell:Cast(player)
    end
end)

-- actions.fs_cooldown+=/the_hunt,if=debuff.essence_break.down&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>45)&(buff.metamorphosis.remains>5|buff.metamorphosis.down)&(!talent.initiative|buff.initiative.up|time>5)&time>5&(!talent.inertia&buff.unbound_chaos.down|buff.inertia_trigger.down)&buff.metamorphosis.down|fight_remains<=30
TheHunt:Callback("fs-cooldown-hunt-new", function(spell)
    if not TheHunt:InRange(target) then return end

    -- Essence break must be down
    if target:Debuff(debuffs.essence_break) then return end

    -- Enemy count condition (updated)
    if gameState.activeEnemies < 3 then return end -- Simplified desired_targets logic

    -- Metamorphosis condition (updated)
    if not (player:BuffRemains(buffs.metamorphosis) > 5000 or not player:Buff(buffs.metamorphosis)) then return end

    -- Initiative condition
    if not (not player:TalentKnown(A.Initiative.ID) or player:Buff(buffs.initiative) or player.combatTime > 5) then return end

    -- Time condition
    if player.combatTime <= 5 then return end

    -- Inertia condition
    if not (not player:TalentKnown(A.Inertia.ID) and not player:Buff(buffs.unbound_chaos) or not player:Buff(buffs.inertia_trigger)) then return end

    -- Metamorphosis down condition or fight ending
    local metamorphosisDownCondition = not player:Buff(buffs.metamorphosis)
    local fightRemainsCondition = target.ttd <= 30000

    if metamorphosisDownCondition or fightRemainsCondition then
        return spell:Cast(target)
    end
end)

-- actions.fs_cooldown+=/sigil_of_spite,if=debuff.essence_break.down&cooldown.blade_dance.remains&time>15
SigilOfSpite:Callback("fs-cooldown-spite-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if BladeDance.cd == 0 then return end
    if player.combatTime <= 15 then return end

    return spell:Cast(player)
end)

--actions.fs+=/call_action_list,name=fs_cooldown
local function fs_cooldown()
    -- New FS Cooldown APL (SimC)
    Metamorphosis("fs-cooldown-meta-new")
    Metamorphosis("fs-cooldown-meta2-new")

    -- Potion usage (handled externally)
    -- External buffs (handled externally)
    -- Trinket variables and usage (handled by existing Trinket() calls)

    TheHunt("fs-cooldown-hunt-new")
    SigilOfSpite("fs-cooldown-spite-new")
end

--[[
NEW FS OPENER CALLBACKS (SimC APL)
]]--

-- actions.fs_opener=potion,if=buff.initiative.up|!talent.initiative
-- Potion usage is handled externally

-- actions.fs_opener+=/felblade,if=cooldown.the_hunt.up&!talent.a_fire_inside&fury<40
Felblade:Callback("fs-opener-felblade-new", function(spell)
    if not Felblade:InRange(target) then return end
    if TheHunt.cd > 0 then return end
    if player:TalentKnown(A.AFireInside.ID) then return end
    if player.fury >= 40 then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/the_hunt,if=talent.inertia|buff.initiative.up|!talent.initiative
TheHunt:Callback("fs-opener-thehunt-new", function(spell)
    if not TheHunt:InRange(target) then return end
    if not (player:TalentKnown(A.Inertia.ID) or player:Buff(buffs.initiative) or not player:TalentKnown(A.Initiative.ID)) then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/felblade,if=talent.inertia&buff.inertia_trigger.up&set_bonus.thewarwithin_season_3_4pc&buff.metamorphosis.up&debuff.essence_break.down&active_enemies<=2
Felblade:Callback("fs-opener-felblade-tww3-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    local tww3_4piece = gameState.TWW3has4P or false
    if not tww3_4piece then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if gameState.activeEnemies > 2 then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/fel_rush,if=talent.inertia&buff.inertia_trigger.up&set_bonus.thewarwithin_season_3_4pc&buff.metamorphosis.up&debuff.essence_break.down&(active_enemies>=2|cooldown.felblade.remains)
FelRush:Callback("fs-opener-felrush-tww3-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    local tww3_4piece = gameState.TWW3has4P or false
    if not tww3_4piece then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (gameState.activeEnemies >= 2 or Felblade.cd > 0) then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/immolation_aura,if=variable.fs_tier34_2piece&buff.demonsurge_hardcast.up&(buff.demonsurge_consuming_fire.up|charges=2)
ImmolationAura:Callback("fs-opener-immolationaura-tier34-new", function(spell)
    if not ImmolationAura:InRange(target) then return end
    local tier34_2piece = gameState.TWW2has2P or false
    if not tier34_2piece then return end
    if not player:Buff(buffs.demonsurge_hardcast) then return end
    if not (player:Buff(buffs.demonsurge_consuming_fire) or spell.charges == 2) then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/annihilation,if=variable.fs_tier34_2piece&debuff.essence_break.down&cooldown.metamorphosis.remains&buff.demonsurge_annihilation.up&cooldown.eye_beam.up
Annihilation:Callback("fs-opener-annihilation-tier34-new", function(spell)
    if not Annihilation:InRange(target) then return end
    local tier34_2piece = gameState.TWW2has2P or false
    if not tier34_2piece then return end
    if target:Debuff(debuffs.essence_break) then return end
    if Metamorphosis.cd == 0 then return end
    if not player:Buff(buffs.demonsurge_annihilation) then return end
    if EyeBeam.cd > 0 then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/felblade,if=talent.inertia&buff.inertia_trigger.up&active_enemies=1&buff.metamorphosis.up&cooldown.metamorphosis.up&cooldown.essence_break.up&buff.inner_demon.down&buff.demonsurge_annihilation.down
Felblade:Callback("fs-opener-felblade-meta-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if gameState.activeEnemies > 1 then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if Metamorphosis.cd > 0 then return end
    if EssenceBreakk.cd > 0 then return end
    if player:Buff(buffs.inner_demon) then return end
    if player:Buff(buffs.demonsurge_annihilation) then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/fel_rush,if=talent.inertia&buff.inertia_trigger.up&(cooldown.felblade.remains|active_enemies>1)&buff.metamorphosis.up&cooldown.metamorphosis.up&cooldown.essence_break.up&buff.inner_demon.down&buff.demonsurge_annihilation.down
FelRush:Callback("fs-opener-felrush-meta-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not (Felblade.cd > 0 or gameState.activeEnemies > 1) then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if Metamorphosis.cd > 0 then return end
    if EssenceBreakk.cd > 0 then return end
    if player:Buff(buffs.inner_demon) then return end
    if player:Buff(buffs.demonsurge_annihilation) then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/essence_break,if=buff.metamorphosis.up&(!talent.inertia|buff.inertia.up&(buff.inner_demon.down|!talent.chaotic_transformation))&(buff.demonsurge_annihilation.down|!talent.chaotic_transformation)&(!variable.fs_tier34_2piece|buff.demonsurge_hardcast.up&cooldown.eye_beam.remains&buff.demonsurge_consuming_fire.down)
EssenceBreakk:Callback("fs-opener-essencebreak-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.metamorphosis) then return end

    local inertiaCondition = not player:TalentKnown(A.Inertia.ID) or
                            (player:Buff(buffs.inertia) and
                             (not player:Buff(buffs.inner_demon) or not player:TalentKnown(A.ChaoticTransformation.ID)))
    if not inertiaCondition then return end

    local demonsurgeCondition = not player:Buff(buffs.demonsurge_annihilation) or not player:TalentKnown(A.ChaoticTransformation.ID)
    if not demonsurgeCondition then return end

    local tier34_2piece = gameState.TWW2has2P or false
    local tier34Condition = not tier34_2piece or
                           (player:Buff(buffs.demonsurge_hardcast) and EyeBeam.cd > 0 and not player:Buff(buffs.demonsurge_consuming_fire))
    if not tier34Condition then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/vengeful_retreat,use_off_gcd=1,if=talent.initiative&time>4&buff.metamorphosis.up&(!talent.inertia|buff.inertia_trigger.down)&talent.essence_break&buff.inner_demon.down&(buff.initiative.down|gcd.remains<0.1)&cooldown.blade_dance.remains
VengefulRetreat:Callback("fs-opener-vengefulretreat-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:TalentKnown(A.Initiative.ID) then return end
    if player.combatTime <= 4 then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if not (not player:TalentKnown(A.Inertia.ID) or not player:Buff(buffs.inertia_trigger)) then return end
    if not player:TalentKnown(A.EssenceBreakk.ID) then return end
    if player:Buff(buffs.inner_demon) then return end
    if not (not player:Buff(buffs.initiative) or MakGcd() < 100) then return end
    if BladeDance.cd > 0 then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/felblade,if=talent.inertia&buff.inertia_trigger.up&hero_tree.felscarred&debuff.essence_break.down&talent.essence_break&cooldown.metamorphosis.remains&active_enemies<=2&cooldown.sigil_of_flame.remains
Felblade:Callback("fs-opener-felblade-felscarred-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    -- Hero tree check simplified - assume Fel-Scarred if in this rotation
    if target:Debuff(debuffs.essence_break) then return end
    if not player:TalentKnown(A.EssenceBreakk.ID) then return end
    if Metamorphosis.cd == 0 then return end
    if gameState.activeEnemies > 2 then return end
    if SigilOfFlame.cd > 0 then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/sigil_of_flame,if=buff.demonsurge_hardcast.up&(buff.inner_demon.down|buff.out_of_range.up)&debuff.essence_break.down&(!variable.fs_tier34_2piece|cooldown.essence_break.remains|!talent.essence_break)
SigilOfFlame:Callback("fs-opener-sigilofflame-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.demonsurge_hardcast) then return end
    if not (not player:Buff(buffs.inner_demon) or player:Buff(buffs.out_of_range)) then return end
    if target:Debuff(debuffs.essence_break) then return end

    local tier34_2piece = gameState.TWW2has2P or false
    if tier34_2piece and EssenceBreakk.cd == 0 and player:TalentKnown(A.EssenceBreakk.ID) then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/annihilation,if=(buff.inner_demon.up|buff.demonsurge_annihilation.up)&(cooldown.metamorphosis.up|!talent.essence_break&cooldown.blade_dance.remains)
Annihilation:Callback("fs-opener-annihilation-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not (player:Buff(buffs.inner_demon) or player:Buff(buffs.demonsurge_annihilation)) then return end
    if not (Metamorphosis.cd == 0 or (not player:TalentKnown(A.EssenceBreakk.ID) and BladeDance.cd > 0)) then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/death_sweep,if=buff.demonsurge_death_sweep.up&!talent.restless_hunter&(!variable.fs_tier34_2piece|buff.demonsurge_hardcast.down)
DeathSweep:Callback("fs-opener-deathsweep-new", function(spell)
    if not DeathSweep:InRange(target) then return end
    if not player:Buff(buffs.demonsurge_death_sweep) then return end
    if player:TalentKnown(A.RestlessHunter.ID) then return end

    local tier34_2piece = gameState.TWW2has2P or false
    if tier34_2piece and player:Buff(buffs.demonsurge_hardcast) then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/annihilation,if=buff.demonsurge_annihilation.up&(!talent.essence_break|buff.inner_demon.up)
Annihilation:Callback("fs-opener-annihilation2-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:Buff(buffs.demonsurge_annihilation) then return end
    if not (not player:TalentKnown(A.EssenceBreakk.ID) or player:Buff(buffs.inner_demon)) then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/immolation_aura,if=talent.a_fire_inside&talent.burning_wound&buff.metamorphosis.down
ImmolationAura:Callback("fs-opener-immolationaura-new", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not player:TalentKnown(A.AFireInside.ID) then return end
    if not player:TalentKnown(A.BurningWound.ID) then return end
    if player:Buff(buffs.metamorphosis) then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/felblade,if=fury<40&debuff.essence_break.down&buff.inertia_trigger.down&cooldown.metamorphosis.up
Felblade:Callback("fs-opener-felblade-fury-new", function(spell)
    if not Felblade:InRange(target) then return end
    if player.fury >= 40 then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.inertia_trigger) then return end
    if Metamorphosis.cd == 0 then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/metamorphosis,if=buff.metamorphosis.up&buff.inner_demon.down&buff.demonsurge_annihilation.down&cooldown.blade_dance.remains
Metamorphosis:Callback("fs-opener-metamorphosis-new", function(spell)
    if not Disrupt:InRange(target) then return end
    if not player:Buff(buffs.metamorphosis) then return end
    if player:Buff(buffs.inner_demon) then return end
    if player:Buff(buffs.demonsurge_annihilation) then return end
    if BladeDance.cd == 0 then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/eye_beam,if=buff.metamorphosis.down|debuff.essence_break.down&buff.inner_demon.down&(cooldown.blade_dance.remains|talent.essence_break&cooldown.essence_break.up)&(!talent.a_fire_inside|action.immolation_aura.charges=0)
EyeBeam:Callback("fs-opener-eyebeam-new", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.moving then return end

    local metaDownCondition = not player:Buff(buffs.metamorphosis)
    local complexCondition = not target:Debuff(debuffs.essence_break) and not player:Buff(buffs.inner_demon) and
                            (BladeDance.cd > 0 or (player:TalentKnown(A.EssenceBreakk.ID) and EssenceBreakk.cd == 0)) and
                            (not player:TalentKnown(A.AFireInside.ID) or ImmolationAura.frac == 0)

    if not (metaDownCondition or complexCondition) then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/eye_beam,if=buff.demonsurge_hardcast.up&debuff.essence_break.down&buff.inner_demon.down
EyeBeam:Callback("fs-opener-eyebeam-demonsurge-new", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.moving then return end
    if not player:Buff(buffs.demonsurge_hardcast) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.inner_demon) then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/annihilation,if=variable.fs_tier34_2piece&(buff.immolation_aura.up|buff.demon_soul_tww3.up)
Annihilation:Callback("fs-opener-annihilation-tier34-final-new", function(spell)
    if not Annihilation:InRange(target) then return end
    local tier34_2piece = gameState.TWW2has2P or false
    if not tier34_2piece then return end
    if not (player:Buff(buffs.immolation_aura) or player:Buff(buffs.demon_soul_tww3)) then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/death_sweep
DeathSweep:Callback("fs-opener-deathsweep-final-new", function(spell)
    if not DeathSweep:InRange(target) then return end

    return spell:Cast(player)
end)

-- actions.fs_opener+=/annihilation
Annihilation:Callback("fs-opener-annihilation-final-new", function(spell)
    if not Annihilation:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.fs_opener+=/demons_bite
DemonsBite:Callback("fs-opener-demonsbite-new", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)

local function fs_opener()
    -- New FS Opener APL (SimC) - Priority Order
    -- Note: This is a comprehensive implementation of the opener sequence

    -- Potion usage (handled externally)
    Felblade("fs-opener-felblade-new")
    TheHunt("fs-opener-thehunt-new")
    Felblade("fs-opener-felblade-tww3-new")
    FelRush("fs-opener-felrush-tww3-new")
    ImmolationAura("fs-opener-immolationaura-tier34-new")
    Annihilation("fs-opener-annihilation-tier34-new")
    Felblade("fs-opener-felblade-meta-new")
    FelRush("fs-opener-felrush-meta-new")
    EssenceBreakk("fs-opener-essencebreak-new")
    VengefulRetreat("fs-opener-vengefulretreat-new")
    Felblade("fs-opener-felblade-felscarred-new")
    SigilOfFlame("fs-opener-sigilofflame-new")
    Annihilation("fs-opener-annihilation-new")
    DeathSweep("fs-opener-deathsweep-new")
    Annihilation("fs-opener-annihilation2-new")
    ImmolationAura("fs-opener-immolationaura-new")
    Felblade("fs-opener-felblade-fury-new")
    Metamorphosis("fs-opener-metamorphosis-new")
    EyeBeam("fs-opener-eyebeam-new")
    EyeBeam("fs-opener-eyebeam-demonsurge-new")
    Annihilation("fs-opener-annihilation-tier34-final-new")
    DeathSweep("fs-opener-deathsweep-final-new")
    Annihilation("fs-opener-annihilation-final-new")
    DemonsBite("fs-opener-demonsbite-new")
end

--[[
NEW FS FEL BARRAGE CALLBACKS (SimC APL)
]]--

-- actions.fs_fel_barrage+=/annihilation,if=buff.inner_demon.up
Annihilation:Callback("fs-felbarrage-annihilation-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:Buff(buffs.inner_demon) then return end

    return spell:Cast(target)
end)

-- actions.fs_fel_barrage+=/eye_beam,if=(buff.fel_barrage.down|fury>45&talent.blind_fury)&(active_enemies>1&raid_event.adds.up|raid_event.adds.in>40-buff.cycle_of_hatred.stack*5)
EyeBeam:Callback("fs-felbarrage-eyebeam-new", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.moving then return end

    local felBarrageCondition = not player:Buff(buffs.fel_barrage) or
                               (player.fury > 45 and player:TalentKnown(A.BlindFury.ID))
    if not felBarrageCondition then return end

    local enemyCondition = gameState.activeEnemies > 1 -- Simplified raid event logic
    if not enemyCondition then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/essence_break,if=buff.fel_barrage.down&buff.metamorphosis.up
EssenceBreakk:Callback("fs-felbarrage-essencebreak-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player:Buff(buffs.fel_barrage) then return end
    if not player:Buff(buffs.metamorphosis) then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/death_sweep,if=buff.fel_barrage.down
DeathSweep:Callback("fs-felbarrage-deathsweep-new", function(spell)
    if not DeathSweep:InRange(target) then return end
    if player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/immolation_aura,if=(active_enemies>2|buff.fel_barrage.up)&(cooldown.eye_beam.remains>recharge_time+3)
ImmolationAura:Callback("fs-felbarrage-immolationaura-new", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (gameState.activeEnemies > 2 or player:Buff(buffs.fel_barrage)) then return end
    if EyeBeam.cd <= (spell:TimeToFullCharges() + 3000) then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/glaive_tempest,if=buff.fel_barrage.down&active_enemies>1
GlaiveTempest:Callback("fs-felbarrage-glaivetempest-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player:Buff(buffs.fel_barrage) then return end
    if gameState.activeEnemies <= 1 then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/blade_dance,if=buff.fel_barrage.down
BladeDance:Callback("fs-felbarrage-bladedance-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/fel_barrage,if=fury>100&(raid_event.adds.in>90|raid_event.adds.in<gcd.max|raid_event.adds.remains>4&active_enemies>2)
FelBarrage:Callback("fs-felbarrage-felbarrage-new", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if player.fury <= 100 then return end
    -- Simplified raid event logic - just check for multiple enemies
    if gameState.activeEnemies <= 2 then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/felblade,if=buff.inertia_trigger.up&buff.fel_barrage.up
Felblade:Callback("fs-felbarrage-felblade-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(target)
end)

-- actions.fs_fel_barrage+=/fel_rush,if=buff.unbound_chaos.up&fury>20&buff.fel_barrage.up
FelRush:Callback("fs-felbarrage-felrush-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if not player:Buff(buffs.unbound_chaos) then return end
    if player.fury <= 20 then return end
    if not player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/sigil_of_flame,if=fury.deficit>40&buff.fel_barrage.up&(!talent.student_of_suffering|cooldown.eye_beam.remains>30)
SigilOfFlame:Callback("fs-felbarrage-sigilofflame-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if (100 - player.fury) <= 40 then return end
    if not player:Buff(buffs.fel_barrage) then return end
    if player:TalentKnown(A.StudentOfSuffering.ID) and EyeBeam.cd <= 30000 then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/sigil_of_flame,if=buff.demonsurge_hardcast.up&fury.deficit>40&buff.fel_barrage.up
SigilOfFlame:Callback("fs-felbarrage-sigilofflame2-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.demonsurge_hardcast) then return end
    if (100 - player.fury) <= 40 then return end
    if not player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/felblade,if=buff.fel_barrage.up&fury.deficit>40&action.felblade.cooldown_react
Felblade:Callback("fs-felbarrage-felblade2-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.fel_barrage) then return end
    if (100 - player.fury) <= 40 then return end
    if Felblade.cd > 0 then return end -- cooldown_react check

    return spell:Cast(target)
end)

-- actions.fs_fel_barrage+=/death_sweep,if=fury-variable.gcd_drain-35>0&(buff.fel_barrage.remains<3|variable.generator_up|fury>80|variable.fury_gen>18)
DeathSweep:Callback("fs-felbarrage-deathsweep2-new", function(spell)
    if not DeathSweep:InRange(target) then return end

    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost
    if (player.fury - gcdDrain - 35) <= 0 then return end

    local generatorUp = Felblade.cd < MakGcd() or SigilOfFlame.cd < MakGcd()
    local furyGen = furyGen or 0 -- Use global fury generation variable

    local condition = player:BuffRemains(buffs.fel_barrage) < 3000 or generatorUp or player.fury > 80 or furyGen > 18
    if not condition then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/glaive_tempest,if=fury-variable.gcd_drain-30>0&(buff.fel_barrage.remains<3|variable.generator_up|fury>80|variable.fury_gen>18)
GlaiveTempest:Callback("fs-felbarrage-glaivetempest2-new", function(spell)
    if not ChaosStrike:InRange(target) then return end

    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost
    if (player.fury - gcdDrain - 30) <= 0 then return end

    local generatorUp = Felblade.cd < MakGcd() or SigilOfFlame.cd < MakGcd()
    local furyGen = furyGen or 0 -- Use global fury generation variable

    local condition = player:BuffRemains(buffs.fel_barrage) < 3000 or generatorUp or player.fury > 80 or furyGen > 18
    if not condition then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/blade_dance,if=fury-variable.gcd_drain-35>0&(buff.fel_barrage.remains<3|variable.generator_up|fury>80|variable.fury_gen>18)
BladeDance:Callback("fs-felbarrage-bladedance2-new", function(spell)
    if not ChaosStrike:InRange(target) then return end

    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost
    if (player.fury - gcdDrain - 35) <= 0 then return end

    local generatorUp = Felblade.cd < MakGcd() or SigilOfFlame.cd < MakGcd()
    local furyGen = furyGen or 0 -- Use global fury generation variable

    local condition = player:BuffRemains(buffs.fel_barrage) < 3000 or generatorUp or player.fury > 80 or furyGen > 18
    if not condition then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/arcane_torrent,if=fury.deficit>40&buff.fel_barrage.up
ArcaneTorrent:Callback("fs-felbarrage-arcanetorrent-new", function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if (100 - player.fury) <= 40 then return end
    if not player:Buff(buffs.fel_barrage) then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/fel_rush,if=buff.unbound_chaos.up
FelRush:Callback("fs-felbarrage-felrush2-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if not player:Buff(buffs.unbound_chaos) then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/the_hunt,if=fury>40&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>80)
TheHunt:Callback("fs-felbarrage-thehunt-new", function(spell)
    if not TheHunt:InRange(target) then return end
    if player.fury <= 40 then return end
    if gameState.activeEnemies < 3 then return end -- Simplified desired_targets logic

    return spell:Cast(target)
end)

-- actions.fs_fel_barrage+=/annihilation,if=fury-variable.gcd_drain-40>20&(buff.fel_barrage.remains<3|variable.generator_up|fury>80|variable.fury_gen>18)
Annihilation:Callback("fs-felbarrage-annihilation2-new", function(spell)
    if not Annihilation:InRange(target) then return end

    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost
    if (player.fury - gcdDrain - 40) <= 20 then return end

    local generatorUp = Felblade.cd < MakGcd() or SigilOfFlame.cd < MakGcd()
    local furyGen = furyGen or 0 -- Use global fury generation variable

    local condition = player:BuffRemains(buffs.fel_barrage) < 3000 or generatorUp or player.fury > 80 or furyGen > 18
    if not condition then return end

    return spell:Cast(target)
end)

-- actions.fs_fel_barrage+=/chaos_strike,if=fury-variable.gcd_drain-40>20&(cooldown.fel_barrage.remains&cooldown.fel_barrage.remains<10&fury>100|buff.fel_barrage.up&(buff.fel_barrage.remains*variable.fury_gen-buff.fel_barrage.remains*32)>0)
ChaosStrike:Callback("fs-felbarrage-chaosstrike-new", function(spell)
    if not ChaosStrike:InRange(target) then return end

    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost
    if (player.fury - gcdDrain - 40) <= 20 then return end

    local furyGen = furyGen or 0 -- Use global fury generation variable
    local felBarrageCondition = (FelBarrage.cd > 0 and FelBarrage.cd < 10000 and player.fury > 100) or
                               (player:Buff(buffs.fel_barrage) and
                                (player:BuffRemains(buffs.fel_barrage) / 1000 * furyGen - player:BuffRemains(buffs.fel_barrage) / 1000 * 32) > 0)

    if not felBarrageCondition then return end

    return spell:Cast(target)
end)

-- actions.fs_fel_barrage+=/demons_bite
DemonsBite:Callback("fs-felbarrage-demonsbite-new", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.fs_fel_barrage+=/arcane_torrent,if=fury.deficit>40&buff.fel_barrage.up
ArcaneTorrent:Callback("fs-felbarrage-arcaneTorrent", function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not player:Buff(buffs.fel_barrage) then return end
    if player.furyDeficit < 40 then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/fel_rush,if=buff.unbound_chaos.up
FelRush:Callback("fs-felbarrage-felrush2", function(spell)
    if Disrupt:InRange(target) then return end
    if not player:Buff(buffs.unbound_chaos) then return end

    return spell:Cast(player)
end)

-- actions.fs_fel_barrage+=/the_hunt,if=fury>40&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>80)
TheHunt:Callback("fs-felbarrage-thehunt", function(spell)
    if not TheHunt:InRange(target) then return end
    if player.fury < 40 then return end
    if not (gameState.activeEnemies >= 3) then return end

    return spell:Cast(target)
end)

-- actions.fs_fel_barrage+=/annihilation,if=fury-variable.gcd_drain-40>20&(buff.fel_barrage.remains<3|variable.generator_up|fury>80|variable.fury_gen>18)
Annihilation:Callback("fs-felbarrage-annihilation", function(spell)
    if not Annihilation:InRange(target) then return end
    if player.fury - 40 < 20 then return end
    if not player:Buff(buffs.fel_barrage) then return end
    if player:BuffRemains(buffs.fel_barrage) < 3000 then return end
    if not gameState.generatorUp then return end
    if player.fury > 80 then return end
    if gameState.furyGen > 18 then return end

    return spell:Cast(target)
end)

-- actions.fs_fel_barrage+=/chaos_strike,if=fury-variable.gcd_drain-40>20&(cooldown.fel_barrage.remains&cooldown.fel_barrage.remains<10&fury>100|buff.fel_barrage.up&(buff.fel_barrage.remains*variable.fury_gen-buff.fel_barrage.remains*32)>0)
ChaosStrike:Callback("fs-felbarrage-chaosstrike", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player.fury  - 40 < 20 then return end
    if not player:Buff(buffs.fel_barrage) then return end
    if player:BuffRemains(buffs.fel_barrage) < 3000 then return end
    if not gameState.generatorUp then return end
    if player.fury > 80 then return end
    if gameState.furyGen > 18 then return end

    return spell:Cast(target)
end)

-- actions.fs_fel_barrage+=/demons_bite
DemonsBite:Callback("fs-felbarrage-demonsbite", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)


local function fs_fel_barrage()
    -- Variables (calculated in main rotation)
    local generatorUp = Felblade.cd < MakGcd() or SigilOfFlame.cd < MakGcd()
    local gcdDrain = MakGcd() * 32 / 1000 -- Convert to fury cost

    -- New FS Fel Barrage APL (SimC)
    Annihilation("fs-felbarrage-annihilation-new")
    EyeBeam("fs-felbarrage-eyebeam-new")
    EssenceBreakk("fs-felbarrage-essencebreak-new")
    DeathSweep("fs-felbarrage-deathsweep-new")
    ImmolationAura("fs-felbarrage-immolationaura-new")
    GlaiveTempest("fs-felbarrage-glaivetempest-new")
    BladeDance("fs-felbarrage-bladedance-new")
    FelBarrage("fs-felbarrage-felbarrage-new")
    Felblade("fs-felbarrage-felblade-new")
    FelRush("fs-felbarrage-felrush-new")
    SigilOfFlame("fs-felbarrage-sigilofflame-new")
    SigilOfFlame("fs-felbarrage-sigilofflame2-new")
    Felblade("fs-felbarrage-felblade2-new")
    DeathSweep("fs-felbarrage-deathsweep2-new")
    GlaiveTempest("fs-felbarrage-glaivetempest2-new")
    BladeDance("fs-felbarrage-bladedance2-new")
    ArcaneTorrent("fs-felbarrage-arcanetorrent-new")
    FelRush("fs-felbarrage-felrush2-new")
    TheHunt("fs-felbarrage-thehunt-new")
    Annihilation("fs-felbarrage-annihilation2-new")
    ChaosStrike("fs-felbarrage-chaosstrike-new")
    DemonsBite("fs-felbarrage-demonsbite-new")
end

-- actions.fs+=/immolation_aura,if=active_enemies>2&talent.ragefire&(!talent.fel_barrage|cooldown.fel_barrage.remains>recharge_time)&debuff.essence_break.down&(buff.metamorphosis.down|buff.metamorphosis.remains>5)
ImmolationAura:Callback("fs-immolationaura", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (gameState.activeEnemies > 2) then return end
    if not player:TalentKnown(A.Ragefire.ID) then return end
    if player:TalentKnown(A.FelBarrage.ID) and FelBarrage.cd < spell:TimeToFullCharges() then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.metamorphosis) and player:BuffRemains(buffs.metamorphosis) > 5000 then return end

    return spell:Cast(player)
end)

-- actions.fs+=/immolation_aura,if=active_enemies>2&talent.ragefire&raid_event.adds.up&raid_event.adds.remains<15&raid_event.adds.remains>5&debuff.essence_break.down
ImmolationAura:Callback("fs-immolationaura2", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (gameState.activeEnemies > 2) then return end
    if not player:TalentKnown(A.Ragefire.ID) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/felblade,if=talent.unbound_chaos&buff.unbound_chaos.up&!talent.inertia&active_enemies<=2&(talent.student_of_suffering&cooldown.eye_beam.remains-gcd.max*2<=buff.unbound_chaos.remains|hero_tree.aldrachi_reaver)
Felblade:Callback("fs-felblade", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.UnboundChaos.ID) then return end
    if not player:Buff(buffs.unbound_chaos) then return end
    if player:TalentKnown(A.Inertia.ID) then return end
    if gameState.activeEnemies > 2 then return end
    if player:TalentKnown(A.StudentOfSuffering.ID) and EyeBeam.cd - MakGcd() * 2 > player:BuffRemains(buffs.unbound_chaos) then return end
    if not player:TalentKnown(A.AldrachiReaver.ID) then return end

    return spell:Cast(target)
end)

-- actions.fs+=/fel_rush,if=talent.unbound_chaos&buff.unbound_chaos.up&!talent.inertia&active_enemies>3&(talent.student_of_suffering&cooldown.eye_beam.remains-gcd.max*2<=buff.unbound_chaos.remains)
FelRush:Callback("fs-felrush", function(spell)
    if Disrupt:InRange(target) then return end
    if not player:TalentKnown(A.UnboundChaos.ID) then return end
    if not player:Buff(buffs.unbound_chaos) then return end
    if player:TalentKnown(A.Inertia.ID) then return end
    if gameState.activeEnemies <= 3 then return end
    if player:TalentKnown(A.StudentOfSuffering.ID) and EyeBeam.cd - MakGcd() * 2 > player:BuffRemains(buffs.unbound_chaos) then return end

    return spell:Cast(player)
end)

--[[
NEW FS META CALLBACKS (SimC APL)
]]--

-- actions.fs_meta=death_sweep,if=buff.metamorphosis.remains<gcd.max|debuff.essence_break.up&(buff.immolation_aura.down|!variable.fs_tier34_2piece)&(buff.demon_soul_tww3.down|!set_bonus.thewarwithin_season_3_4pc)|prev_gcd.1.metamorphosis&!variable.fs_tier34_2piece|buff.demonsurge_death_sweep.up&variable.fs_tier34_2piece&buff.demonsurge.remains<5|(variable.fs_tier34_2piece&cooldown.metamorphosis.up&talent.inertia)|active_enemies>=3&buff.demonsurge_death_sweep.up&(!talent.inertia|buff.inertia_trigger.down&cooldown.vengeful_retreat.remains|buff.inertia.up)&(!talent.essence_break|debuff.essence_break.up|cooldown.essence_break.remains>=5)
DeathSweep:Callback("fs-meta-deathsweep-new", function(spell)
    if not DeathSweep:InRange(target) then return end

    local tier34_2piece = gameState.TWW2has2P or false
    local tww3_4piece = gameState.TWW3has4P or false

    -- Metamorphosis ending condition
    local metaEndingCondition = player:BuffRemains(buffs.metamorphosis) < MakGcd()

    -- Essence Break condition with tier set logic
    local essenceBreakCondition = target:Debuff(debuffs.essence_break) and
                                 (not player:Buff(buffs.immolation_aura) or not tier34_2piece) and
                                 (not player:Buff(buffs.demon_soul_tww3) or not tww3_4piece)

    -- Previous GCD Metamorphosis condition
    local prevMetaCondition = Metamorphosis.lastUsed < 1000 and not tier34_2piece

    -- Demonsurge Death Sweep condition
    local demonsurgeCondition = player:Buff(buffs.demonsurge_death_sweep) and tier34_2piece and
                               player:BuffRemains(buffs.demonsurge) < 5000

    -- Tier 34 Metamorphosis condition
    local tier34MetaCondition = tier34_2piece and Metamorphosis.cd == 0 and player:TalentKnown(A.Inertia.ID)

    -- Multi-enemy condition
    local multiEnemyCondition = gameState.activeEnemies >= 3 and player:Buff(buffs.demonsurge_death_sweep) and
                               (not player:TalentKnown(A.Inertia.ID) or
                                (not player:Buff(buffs.inertia_trigger) and VengefulRetreat.cd > 0) or
                                player:Buff(buffs.inertia)) and
                               (not player:TalentKnown(A.EssenceBreakk.ID) or target:Debuff(debuffs.essence_break) or
                                EssenceBreakk.cd >= 5000)

    if metaEndingCondition or essenceBreakCondition or prevMetaCondition or demonsurgeCondition or
       tier34MetaCondition or multiEnemyCondition then
        return spell:Cast(player)
    end
end)

-- actions.fs_meta+=/sigil_of_flame,if=buff.demonsurge_hardcast.up&talent.student_of_suffering&debuff.essence_break.down&(talent.student_of_suffering&((talent.essence_break&cooldown.essence_break.remains>30-gcd.max|cooldown.essence_break.remains<=gcd.max+talent.inertia&(cooldown.vengeful_retreat.remains<=gcd|buff.initiative.up)+gcd.max*(cooldown.eye_beam.remains<=gcd.max))|(!talent.essence_break&(cooldown.eye_beam.remains>=10|cooldown.eye_beam.remains<=gcd.max))))
SigilOfFlame:Callback("fs-meta-sigilofflame-student-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.demonsurge_hardcast) then return end
    if not player:TalentKnown(A.StudentOfSuffering.ID) then return end
    if target:Debuff(debuffs.essence_break) then return end

    -- Complex Student of Suffering timing logic
    if player:TalentKnown(A.EssenceBreakk.ID) then
        local essenceBreakCondition1 = EssenceBreakk.cd > (30000 - MakGcd())
        local essenceBreakCondition2 = EssenceBreakk.cd <= (MakGcd() + (player:TalentKnown(A.Inertia.ID) and 1 or 0) *
                                      (VengefulRetreat.cd <= MakGcd() or player:Buff(buffs.initiative) and 1 or 0) +
                                      MakGcd() * (EyeBeam.cd <= MakGcd() and 1 or 0))
        if not (essenceBreakCondition1 or essenceBreakCondition2) then return end
    else
        local eyeBeamCondition = EyeBeam.cd >= 10000 or EyeBeam.cd <= MakGcd()
        if not eyeBeamCondition then return end
    end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/vengeful_retreat,use_off_gcd=1,if=talent.initiative&(gcd.remains<0.3|talent.inertia&cooldown.eye_beam.remains>gcd.remains&(buff.cycle_of_hatred.stack=2|buff.cycle_of_hatred.stack=3))&(cooldown.metamorphosis.remains&(buff.demonsurge_annihilation.down&buff.demonsurge_death_sweep.down)|talent.restless_hunter&buff.demonsurge_annihilation.down)&(!talent.inertia&buff.unbound_chaos.down|buff.inertia_trigger.down)&(!talent.essence_break|cooldown.essence_break.remains>18|cooldown.essence_break.remains<=gcd.remains+talent.inertia*1.5&(!talent.student_of_suffering|(buff.student_of_suffering.up|cooldown.sigil_of_flame.remains>5)))&(cooldown.eye_beam.remains>5|cooldown.eye_beam.remains<=gcd.remains|cooldown.eye_beam.up)|cooldown.metamorphosis.up&buff.demonsurge.stack>1&talent.initiative&!talent.inertia&gcd.remains<0.3
VengefulRetreat:Callback("fs-meta-vengefulretreat-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:TalentKnown(A.Initiative.ID) then return end

    -- GCD and timing conditions
    local gcdCondition = MakGcd() < 300 or
                        (player:TalentKnown(A.Inertia.ID) and EyeBeam.cd > MakGcd() and
                         (player:HasBuffCount(buffs.cycle_of_hatred) == 2 or player:HasBuffCount(buffs.cycle_of_hatred) == 3))

    -- Metamorphosis and Demonsurge conditions (updated)
    local metaCondition = (Metamorphosis.cd > 0 and
                          (not player:Buff(buffs.demonsurge_annihilation) and not player:Buff(buffs.demonsurge_death_sweep))) or
                         (player:TalentKnown(A.RestlessHunter.ID) and not player:Buff(buffs.demonsurge_annihilation))

    -- Inertia conditions
    local inertiaCondition = (not player:TalentKnown(A.Inertia.ID) and not player:Buff(buffs.unbound_chaos)) or
                            not player:Buff(buffs.inertia_trigger)

    -- Essence Break conditions
    local essenceBreakCondition = not player:TalentKnown(A.EssenceBreakk.ID) or EssenceBreakk.cd > 18000 or
                                 (EssenceBreakk.cd <= (MakGcd() + (player:TalentKnown(A.Inertia.ID) and 1.5 or 0)) and
                                  (not player:TalentKnown(A.StudentOfSuffering.ID) or
                                   (player:Buff(buffs.student_of_suffering) or SigilOfFlame.cd > 5000)))

    -- Eye Beam conditions
    local eyeBeamCondition = EyeBeam.cd > 5000 or EyeBeam.cd <= MakGcd() or EyeBeam.cd == 0

    -- Alternative condition for Metamorphosis up
    local altCondition = Metamorphosis.cd == 0 and player:HasBuffCount(buffs.demonsurge) > 1 and
                        player:TalentKnown(A.Initiative.ID) and not player:TalentKnown(A.Inertia.ID) and MakGcd() < 300

    if (gcdCondition and metaCondition and inertiaCondition and essenceBreakCondition and eyeBeamCondition) or altCondition then
        return spell:Cast(player)
    end
end)

-- actions.fs_meta+=/vengeful_retreat,use_off_gcd=1,if=variable.fs_tier34_2piece&buff.inertia_trigger.down&talent.initiative
VengefulRetreat:Callback("fs-meta-vengefulretreat-tier34-new", function(spell)
    if not Annihilation:InRange(target) then return end
    local tier34_2piece = gameState.TWW2has2P or false
    if not tier34_2piece then return end
    if player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Initiative.ID) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/felblade,if=talent.inertia&variable.fs_tier34_2piece&buff.inertia_trigger.up
Felblade:Callback("fs-meta-felblade-tier34-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    local tier34_2piece = gameState.TWW2has2P or false
    if not tier34_2piece then return end
    if not player:Buff(buffs.inertia_trigger) then return end

    return spell:Cast(target)
end)

-- Note: Due to the complexity and length of the new FS Meta APL (40+ abilities),
-- I'm implementing the most critical callbacks. The full implementation would require
-- all abilities from the SimC APL block.

-- actions.fs_meta+=/essence_break,if=fury>=30&talent.restless_hunter&cooldown.metamorphosis.up&(talent.inertia&buff.inertia.up|!talent.inertia)&cooldown.blade_dance.remains<=gcd.max&(hero_tree.felscarred&buff.demonsurge_annihilation.down|hero_tree.aldrachi_reaver)
EssenceBreakk:Callback("fs-meta-essencebreak-restless-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player.fury < 30 then return end
    if not player:TalentKnown(A.RestlessHunter.ID) then return end
    if Metamorphosis.cd > 0 then return end
    if not (player:TalentKnown(A.Inertia.ID) and player:Buff(buffs.inertia) or not player:TalentKnown(A.Inertia.ID)) then return end
    if BladeDance.cd > MakGcd() then return end
    if not (not player:Buff(buffs.demonsurge_annihilation) or player:TalentKnown(AldrachiReaver.id)) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/death_sweep,if=(talent.essence_break&buff.demonsurge_death_sweep.up&(buff.inertia.up&(cooldown.essence_break.remains>buff.inertia.remains|!talent.essence_break)|cooldown.metamorphosis.remains<=5&buff.inertia_trigger.down|buff.inertia.up&buff.demonsurge_abyssal_gaze.up)|talent.inertia&buff.inertia_trigger.down&cooldown.vengeful_retreat.remains>=gcd.max&buff.inertia.down)&(!variable.fs_tier34_2piece|!talent.inertia|active_enemies>=3&debuff.essence_break.up)
DeathSweep:Callback("fs-meta-deathsweep-complex-new", function(spell)
    if not DeathSweep:InRange(target) then return end

    local tier34_2piece = gameState.TWW2has2P or false

    -- Complex Essence Break and Demonsurge conditions
    local essenceBreakCondition = player:TalentKnown(A.EssenceBreakk.ID) and player:Buff(buffs.demonsurge_death_sweep) and
                                 (player:Buff(buffs.inertia) and
                                  (EssenceBreakk.cd > player:BuffRemains(buffs.inertia) or not player:TalentKnown(A.EssenceBreakk.ID)) or
                                  Metamorphosis.cd <= 5000 and not player:Buff(buffs.inertia_trigger) or
                                  player:Buff(buffs.inertia) and player:Buff(buffs.demonsurge_abyssal_gaze))

    -- Inertia conditions
    local inertiaCondition = player:TalentKnown(A.Inertia.ID) and not player:Buff(buffs.inertia_trigger) and
                            VengefulRetreat.cd >= MakGcd() and not player:Buff(buffs.inertia)

    -- Tier 34 conditions
    local tier34Condition = not tier34_2piece or not player:TalentKnown(A.Inertia.ID) or
                           (gameState.activeEnemies >= 3 and target:Debuff(debuffs.essence_break))

    if (essenceBreakCondition or inertiaCondition) and tier34Condition then
        return spell:Cast(player)
    end
end)

-- actions.fs_meta+=/annihilation,if=buff.metamorphosis.remains<gcd.max&cooldown.blade_dance.remains<buff.metamorphosis.remains|debuff.essence_break.remains&debuff.essence_break.remains<0.5|talent.restless_hunter&(buff.demonsurge_annihilation.up|hero_tree.aldrachi_reaver&buff.inner_demon.up)&cooldown.essence_break.up&cooldown.metamorphosis.up
Annihilation:Callback("fs-meta-annihilation-priority-new", function(spell)
    if not Annihilation:InRange(target) then return end

    local metaEndingCondition = player:BuffRemains(buffs.metamorphosis) < MakGcd() and
                               BladeDance.cd < player:BuffRemains(buffs.metamorphosis)
    local essenceBreakCondition = target:DebuffRemains(debuffs.essence_break) > 0 and
                                 target:DebuffRemains(debuffs.essence_break) < 500
    local restlessHunterCondition = player:TalentKnown(A.RestlessHunter.ID) and
                                   (player:Buff(buffs.demonsurge_annihilation) or
                                    (player:TalentKnown(AldrachiReaver.id) and player:Buff(buffs.inner_demon))) and
                                   EssenceBreakk.cd == 0 and Metamorphosis.cd == 0

    if metaEndingCondition or essenceBreakCondition or restlessHunterCondition then
        return spell:Cast(target)
    end
end)

-- actions.fs_meta+=/death_sweep,if=hero_tree.felscarred&talent.essence_break&buff.demonsurge_death_sweep.up&(buff.inertia.up&(cooldown.essence_break.remains>buff.inertia.remains|!talent.essence_break)|cooldown.metamorphosis.remains<=5&buff.inertia_trigger.down|buff.inertia.up&buff.demonsurge_abyssal_gaze.up)|talent.inertia&buff.inertia_trigger.down&cooldown.vengeful_retreat.remains>=gcd.max&buff.inertia.down
DeathSweep:Callback("fs-meta-deathsweep2", function(spell)
    if not DeathSweep:InRange(target) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if VengefulRetreat.cd < MakGcd() then return end
    if player:Buff(buffs.inertia) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/annihilation,if=buff.metamorphosis.remains<gcd.max&cooldown.blade_dance.remains<buff.metamorphosis.remains|debuff.essence_break.remains&debuff.essence_break.remains<0.5|talent.restless_hunter&(buff.demonsurge_annihilation.up|hero_tree.aldrachi_reaver&buff.inner_demon.up)&cooldown.essence_break.up&cooldown.metamorphosis.up
Annihilation:Callback("fs-meta-annihilation", function(spell)
    if not Annihilation:InRange(target) then return end
    if player:BuffRemains(buffs.metamorphosis) > MakGcd() then return end
    if BladeDance.cd >= player:BuffRemains(buffs.metamorphosis) then return end
    if target:DebuffRemains(debuffs.essence_break) > 0.5 then return end
    if player:TalentKnown(A.RestlessHunter.ID) and (player:Buff(buffs.demonsurge_annihilation) or player:TalentKnown(A.AldrachiReaver.ID) and player:Buff(buffs.inner_demon)) and EssenceBreakk.cd == 0 and Metamorphosis.cd == 0 then return end

    return spell:Cast(target)
end)

-- actions.fs_meta+=/annihilation,if=(hero_tree.felscarred&buff.demonsurge_annihilation.up&talent.restless_hunter)&(cooldown.eye_beam.remains<gcd.max*3&cooldown.blade_dance.remains|cooldown.metamorphosis.remains<gcd.max*3)
Annihilation:Callback("fs-meta-annihilation2", function(spell)
    if not Annihilation:InRange(target) then return end
    if not (player:TalentKnown(A.RestlessHunter.ID) and player:Buff(buffs.demonsurge_annihilation)) then return end
    if EyeBeam.cd > MakGcd() * 3 then return end
    if BladeDance.cd > 0 then return end
    if Metamorphosis.cd > MakGcd() * 3 then return end

    return spell:Cast(target)
end)

-- actions.fs_meta+=/felblade,if=buff.inertia_trigger.up&talent.inertia&debuff.essence_break.down&cooldown.metamorphosis.remains&(!hero_tree.felscarred|cooldown.eye_beam.remains)&(cooldown.blade_dance.remains<=5.5&(talent.essence_break&cooldown.essence_break.remains<=0.5|!talent.essence_break|cooldown.essence_break.remains>=buff.inertia_trigger.remains&cooldown.blade_dance.remains<=4.5&(cooldown.blade_dance.remains|cooldown.blade_dance.remains<=0.5))|buff.metamorphosis.remains<=5.5+talent.shattered_destiny*2)
Felblade:Callback("fs-meta-felblade", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if Metamorphosis.cd == 0 then return end
    if not (not player:TalentKnown(A.FelScarred.ID) or EyeBeam.cd == 0) then return end
    if BladeDance.cd > 5500 then return end
    if not (player:TalentKnown(A.EssenceBreakk.ID) and EssenceBreakk.cd <= 500 or not player:TalentKnown(A.EssenceBreakk.ID) or EssenceBreakk.cd >= player:BuffRemains(buffs.inertia_trigger) and BladeDance.cd <= 4500 and (BladeDance.cd == 0 or BladeDance.cd <= 500)) then return end
    if player:BuffRemains(buffs.metamorphosis) > 5500 + num(player:TalentKnown(A.ShatteredDestiny.ID)) * 2000 then return end

    return spell:Cast(target)
end)

-- actions.fs_meta+=/fel_rush,if=buff.inertia_trigger.up&talent.inertia&debuff.essence_break.down&cooldown.metamorphosis.remains&(!hero_tree.felscarred|cooldown.eye_beam.remains)&(active_enemies>2|hero_tree.felscarred)&(cooldown.felblade.remains&cooldown.essence_break.remains<=0.6)
FelRush:Callback("fs-meta-felrush", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if Metamorphosis.cd == 0 then return end
    if not (not player:TalentKnown(A.FelScarred.ID) or EyeBeam.cd == 0) then return end
    if not (gameState.activeEnemies > 2 or player:TalentKnown(A.FelScarred.ID)) then return end
    if not (Felblade.cd > 0 and EssenceBreakk.cd <= 600) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/immolation_aura,if=charges=2&(active_enemies>1|talent.a_fire_inside&talent.isolated_prey)&debuff.essence_break.down
ImmolationAura:Callback("fs-meta-immolationaura", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (spell.charges == 2) then return end
    if not (gameState.activeEnemies > 1 or player:TalentKnown(A.AFireInside.ID) and player:TalentKnown(A.IsolatedPrey.ID)) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/annihilation,if=buff.inner_demon.up&cooldown.blade_dance.remains&(cooldown.eye_beam.remains<gcd.max*3|cooldown.metamorphosis.remains<gcd.max*3)
Annihilation:Callback("fs-meta-annihilation3", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:Buff(buffs.inner_demon) then return end
    if BladeDance.cd > 0 then return end
    if not (EyeBeam.cd < MakGcd() * 3 or Metamorphosis.cd < MakGcd() * 3) then return end

    return spell:Cast(target)
end)

-- actions.fs_meta+=/essence_break,if=fury>20&(cooldown.metamorphosis.remains>10|cooldown.blade_dance.remains<gcd.max*2)&(buff.inertia_trigger.down|buff.inertia.up&buff.inertia.remains>=gcd.max*3|!talent.inertia)&buff.out_of_range.remains<gcd.max&(!talent.shattered_destiny|cooldown.eye_beam.remains>4)&(!hero_tree.felscarred|active_enemies>1|cooldown.metamorphosis.remains>5&cooldown.eye_beam.remains)&(!buff.cycle_of_hatred.stack=3|buff.initiative.up|!talent.initiative|!talent.cycle_of_hatred)|fight_remains<5
EssenceBreakk:Callback("fs-meta-essencebreak", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player.fury < 20 then return end
    if not (Metamorphosis.cd > 10000 or BladeDance.cd < MakGcd() * 2) then return end
    if not (not player:Buff(buffs.inertia_trigger) or player:Buff(buffs.inertia) and player:BuffRemains(buffs.inertia) >= MakGcd() * 3 or not player:TalentKnown(A.Inertia.ID)) then return end
    if player:BuffRemains(buffs.out_of_range) > MakGcd() then return end
    if not (not player:TalentKnown(A.ShatteredDestiny.ID) or EyeBeam.cd > 4000) then return end
    if not (not player:TalentKnown(A.Demonsurge.ID) or gameState.activeEnemies > 1 or Metamorphosis.cd > 5000 and EyeBeam.cd > 0) then return end
    if not (player:HasBuffCount(buffs.cycle_of_hatred) == 3 or player:Buff(buffs.initiative) or not player:TalentKnown(A.Initiative.ID) or not player:TalentKnown(A.CycleOfHatred.ID)) then return end
    if target.ttd < 5000 then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/sigil_of_doom,if=cooldown.blade_dance.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>=20|cooldown.eye_beam.remains<=gcd.max)&(!talent.student_of_suffering|buff.demonsurge_sigil_of_doom.up)
SigilOfDoom:Callback("fs-meta-sigilofdoom", function(spell)
    if not Disrupt:InRange(target) then return end
    if BladeDance.cd == 0 then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd >= 20000 or EyeBeam.cd <= MakGcd()) then return end
    if not (not player:TalentKnown(A.StudentOfSuffering.ID) or player:Buff(buffs.demonsurge_sigil_of_doom)) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/immolation_aura,if=buff.demonsurge.up&debuff.essence_break.down&buff.demonsurge_consuming_fire.up&cooldown.blade_dance.remains>=gcd.max&cooldown.eye_beam.remains>=gcd.max&fury.deficit>10+variable.fury_gen
ImmolationAura:Callback("fs-meta-immolationaura2", function(spell)
    if not Disrupt:InRange(target) then return end
    if not player:Buff(buffs.demonsurge) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not player:Buff(buffs.demonsurge_consuming_fire) then return end
    if BladeDance.cd < MakGcd() then return end
    if EyeBeam.cd < MakGcd() then return end
    if player.furyDeficit < 10 + 10 then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/eye_beam,if=debuff.essence_break.down&buff.inner_demon.down
EyeBeam:Callback("fs-meta-eyebeam", function(spell)
    if not Disrupt:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.inner_demon) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/abyssal_gaze,if=debuff.essence_break.down&buff.inner_demon.down&(buff.cycle_of_hatred.stack<4|cooldown.essence_break.remains>=20-gcd.max*talent.student_of_suffering|cooldown.sigil_of_flame.remains&talent.student_of_suffering|cooldown.essence_break.remains<=gcd.max)
AbyssalGaze:Callback("fs-meta-abyssalgaze", function(spell)
    if not AbyssalGaze:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.inner_demon) then return end
    if not (player:HasBuffCount(buffs.cycle_of_hatred) < 4 or EssenceBreakk.cd >= 20000 - MakGcd() * (player:TalentKnown(A.StudentOfSuffering.ID) and 1 or 0) or SigilOfFlame.cd > 0 and player:TalentKnown(A.StudentOfSuffering.ID) or EssenceBreakk.cd <= MakGcd()) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/death_sweep,if=cooldown.essence_break.remains>=gcd.max*2+talent.student_of_suffering*gcd.max|debuff.essence_break.up|!talent.essence_break
DeathSweep:Callback("fs-meta-deathsweep3", function(spell)
    if not DeathSweep:InRange(target) then return end
    if not (EssenceBreakk.cd >= MakGcd() * 2 + MakGcd() * (player:TalentKnown(A.StudentOfSuffering.ID) and 1 or 0) or target:Debuff(debuffs.essence_break) or not player:TalentKnown(A.EssenceBreakk.ID)) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/glaive_tempest,if=debuff.essence_break.down&(cooldown.blade_dance.remains>gcd.max*2|fury>60)&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>10)
GlaiveTempest:Callback("fs-meta-glaivetempest", function(spell)
    if not GlaiveTempest:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (BladeDance.cd > MakGcd() * 2 or player.fury > 60) then return end
    if not (gameState.activeEnemies >= 3 or gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/sigil_of_flame,if=active_enemies>2&debuff.essence_break.down
SigilOfFlame:Callback("fs-meta-sigilofflame", function(spell)
    if not Disrupt:InRange(target) then return end
    if not (gameState.activeEnemies > 2) then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/annihilation,if=cooldown.blade_dance.remains|fury>60|soul_fragments.total>0|buff.metamorphosis.remains<5
Annihilation:Callback("fs-meta-annihilation4", function(spell)
    if not Annihilation:InRange(target) then return end
    if BladeDance.cd > 0 then return end
    if player.fury < 60 then return end
    --if player.soul_fragments < 1 then return end
    if player:BuffRemains(buffs.metamorphosis) > 5000 then return end

    return spell:Cast(target)
end)

-- actions.fs_meta+=/sigil_of_flame,if=buff.metamorphosis.remains>5&buff.out_of_range.down&!talent.student_of_suffering
SigilOfFlame:Callback("fs-meta-sigilofflame2", function(spell)
    if not Disrupt:InRange(target) then return end
    if player:BuffRemains(buffs.metamorphosis) < 5000 then return end
    if player:Buff(buffs.out_of_range) then return end
    if player:TalentKnown(A.StudentOfSuffering.ID) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/immolation_aura,if=buff.out_of_range.down&recharge_time<(cooldown.eye_beam.remains<?buff.metamorphosis.remains)&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>full_recharge_time)
ImmolationAura:Callback("fs-meta-immolationaura3", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if player:Buff(buffs.out_of_range) then return end
    if spell:TimeToFullCharges() > math.min(EyeBeam.cd, player:BuffRemains(buffs.metamorphosis)) then return end
    if not (gameState.activeEnemies >= 3 or gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.fs_meta+=/felblade,if=(buff.out_of_range.down|fury.deficit>40+variable.fury_gen*(0.5%gcd.max))&!buff.inertia_trigger.up
Felblade:Callback("fs-meta-felblade2", function(spell)
    if not Felblade:InRange(target) then return end
    if not (player:Buff(buffs.out_of_range) or player.furyDeficit > 40 + 5 * (0.5 / MakGcd())) then return end
    if player:Buff(buffs.inertia_trigger) then return end

    return spell:Cast(target)
end)

-- actions.fs_meta+=/annihilation
Annihilation:Callback("fs-meta-annihilation5", function(spell)
    if not Annihilation:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.fs_meta+=/throw_glaive,if=buff.unbound_chaos.down&recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&buff.out_of_range.down&active_enemies>1
ThrowGlaive:Callback("fs-meta-throwglaive", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if player:Buff(buffs.unbound_chaos) then return end
    if spell:TimeToFullCharges() > EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.frac > 1.01) then return end
    if player:Buff(buffs.out_of_range) then return end
    if gameState.activeEnemies <= 1 then return end

    return spell:Cast(target)
end)

-- actions.fs_meta+=/fel_rush,if=recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&buff.out_of_range.down&active_enemies>1
FelRush:Callback("fs-meta-felrush2", function(spell)
    if Disrupt:InRange(target) then return end
    if spell:TimeToFullCharges() > EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.frac > 1.01) then return end
    if player:Buff(buffs.out_of_range) then return end
    if gameState.activeEnemies <= 1 then return end

    return spell:Cast(target)
end)

-- actions.fs_meta+=/demons_bite
DemonsBite:Callback("fs-meta-demonsbite", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)


-- actions.fs+=/run_action_list,name=fs_meta,if=buff.metamorphosis.up
local function fs_meta()
    -- New FS Meta APL (SimC) - Key Priority Abilities
    -- Note: This is a simplified implementation of the most critical abilities
    -- The full APL contains 40+ abilities with complex conditions

    DeathSweep("fs-meta-deathsweep-new")
    SigilOfFlame("fs-meta-sigilofflame-student-new")
    VengefulRetreat("fs-meta-vengefulretreat-new")
    VengefulRetreat("fs-meta-vengefulretreat-tier34-new")
    Felblade("fs-meta-felblade-tier34-new")
    EssenceBreakk("fs-meta-essencebreak-restless-new")
    DeathSweep("fs-meta-deathsweep-complex-new")
    Annihilation("fs-meta-annihilation-priority-new")

    -- Additional callbacks would be added here for the full implementation
    -- Including: Felblade variants, Fel Rush variants, Immolation Aura variants,
    -- Eye Beam, Sigil of Flame variants, Glaive Tempest, etc.

    -- Fallback to existing callbacks for abilities not yet updated
    Annihilation("fs-meta-annihilation3")
    EssenceBreakk("fs-meta-essencebreak")
    ImmolationAura("fs-meta-immolationaura")
    EyeBeam("fs-meta-eyebeam")
    DeathSweep("fs-meta-deathsweep3")
    GlaiveTempest("fs-meta-glaivetempest")
    SigilOfFlame("fs-meta-sigilofflame")
    Annihilation("fs-meta-annihilation5")
    Felblade("fs-meta-felblade2")
    ThrowGlaive("fs-meta-throwglaive")
    FelRush("fs-meta-felrush2")
    DemonsBite("fs-meta-demonsbite")
end

-- actions.fs+=/vengeful_retreat,use_off_gcd=1,if=talent.initiative&(cooldown.eye_beam.remains>15&gcd.remains<0.3|gcd.remains<0.2&cooldown.eye_beam.remains<=gcd.remains&(cooldown.metamorphosis.remains>10|cooldown.blade_dance.remains<gcd.max*3))&(!talent.student_of_suffering|cooldown.sigil_of_flame.remains)&(cooldown.essence_break.remains<=gcd.max*2&talent.student_of_suffering&cooldown.sigil_of_flame.remains|cooldown.essence_break.remains>=18|!talent.student_of_suffering)&(cooldown.metamorphosis.remains>10|hero_tree.aldrachi_reaver)&time>20
VengefulRetreat:Callback("fs-vengefulretreat", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:TalentKnown(A.Initiative.ID) then return end
    if not (EyeBeam.cd > 15000 or MakGcd() < 300) then return end
    if not (not player:TalentKnown(A.StudentOfSuffering.ID) or SigilOfFlame.cd > 0) then return end
    if not (EssenceBreakk.cd <= MakGcd() * 2 and player:TalentKnown(A.StudentOfSuffering.ID) and SigilOfFlame.cd > 0 or EssenceBreakk.cd >= 18000 or not player:TalentKnown(A.StudentOfSuffering.ID)) then return end
    if not (Metamorphosis.cd > 10000 or BladeDance.cd < MakGcd() * 3) then return end
    if player.combatTime < 20 then return end

    return spell:Cast(player)
end)

-- actions.fs+=/immolation_aura,if=talent.a_fire_inside&talent.burning_wound&full_recharge_time<gcd.max*2&(raid_event.adds.in>full_recharge_time|active_enemies>desired_targets)
ImmolationAura:Callback("fs-immolationaura3", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not player:TalentKnown(A.AFireInside.ID) then return end
    if not player:TalentKnown(A.BurningWound.ID) then return end
    if spell:TimeToFullCharges() > MakGcd() * 2 then return end
    if not (gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/immolation_aura,if=active_enemies>desired_targets&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>full_recharge_time)
ImmolationAura:Callback("fs-immolationaura4", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/immolation_aura,if=fight_remains<15&cooldown.blade_dance.remains&talent.ragefire
ImmolationAura:Callback("fs-immolationaura5", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if player.combatTime > 15 then return end
    if BladeDance.cd > 0 then return end
    if not player:TalentKnown(A.Ragefire.ID) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/sigil_of_flame,if=talent.student_of_suffering&cooldown.eye_beam.remains<=gcd.max&(cooldown.essence_break.remains<gcd.max*3|!talent.essence_break)&(cooldown.metamorphosis.remains>10|cooldown.blade_dance.remains<gcd.max*2)
SigilOfFlame:Callback("fs-sigilofflame", function(spell)
    if not Disrupt:InRange(target) then return end
    if not player:TalentKnown(A.StudentOfSuffering.ID) then return end
    if EyeBeam.cd > MakGcd() then return end
    if not (EssenceBreakk.cd < MakGcd() * 3 or not player:TalentKnown(A.EssenceBreakk.ID)) then return end
    if not (Metamorphosis.cd > 10000 or BladeDance.cd < MakGcd() * 2) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/eye_beam,if=(!talent.initiative|buff.initiative.up|cooldown.vengeful_retreat.remains>=10|cooldown.metamorphosis.up|talent.initiative&!talent.tactical_retreat)&(cooldown.blade_dance.remains<7|raid_event.adds.up)|fight_remains<10
EyeBeam:Callback("fs-eyebeam", function(spell)
    if not Disrupt:InRange(target) then return end
    if not (not player:TalentKnown(A.Initiative.ID) or player:Buff(buffs.initiative) or VengefulRetreat.cd >= 10000 or Metamorphosis.cd == 0 or player:TalentKnown(A.Initiative.ID) and not player:TalentKnown(A.TacticalRetreat.ID)) then return end
    if not (BladeDance.cd < 7000 or gameState.activeEnemies > 1) then return end
    if player.combatTime > 10 then return end

    return spell:Cast(player)
end)

-- actions.fs+=/blade_dance,if=cooldown.eye_beam.remains>=gcd.max*4|debuff.essence_break.up
BladeDance:Callback("fs-bladedance", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if EyeBeam.cd < MakGcd() * 4 then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/glaive_tempest,if=active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>10
GlaiveTempest:Callback("fs-glaivetempest", function(spell)
    if not GlaiveTempest:InRange(target) then return end
    if not (gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/sigil_of_flame,if=active_enemies>3&!talent.student_of_suffering
SigilOfFlame:Callback("fs-sigilofflame2", function(spell)
    if not Disrupt:InRange(target) then return end
    if not (gameState.activeEnemies > 3) then return end
    if player:TalentKnown(A.StudentOfSuffering.ID) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/chaos_strike,if=debuff.essence_break.up
ChaosStrike:Callback("fs-chaosstrike", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.fs+=/immolation_aura,if=talent.a_fire_inside&talent.isolated_prey&talent.flamebound&active_enemies=1&cooldown.eye_beam.remains>=gcd.max
ImmolationAura:Callback("fs-immolationaura6", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not player:TalentKnown(A.AFireInside.ID) then return end
    if not player:TalentKnown(A.IsolatedPrey.ID) then return end
    if not player:TalentKnown(A.Flamebound.ID) then return end
    if not (gameState.activeEnemies == 1) then return end
    if EyeBeam.cd < MakGcd() then return end

    return spell:Cast(player)
end)

-- actions.fs+=/felblade,if=fury.deficit>40+variable.fury_gen*(0.5%gcd.max)&(cooldown.vengeful_retreat.remains>=action.felblade.cooldown+0.5&talent.inertia&active_enemies=1|!talent.inertia|hero_tree.aldrachi_reaver|cooldown.essence_break.remains)&cooldown.metamorphosis.remains&cooldown.eye_beam.remains>=0.5+gcd.max*(talent.student_of_suffering&cooldown.sigil_of_flame.remains<=gcd.max)
Felblade:Callback("fs-felblade2", function(spell)
    if not Felblade:InRange(target) then return end
    if player.furyDeficit < 40 + 5 * 0.5 * MakGcd() then return end
    if not (VengefulRetreat.cd >= 15000 and player:TalentKnown(A.Inertia.ID) and gameState.activeEnemies == 1 or not player:TalentKnown(A.Inertia.ID) or player:TalentKnown(A.AldrachiReaver.ID) or EssenceBreakk.cd > 0) then return end
    if Metamorphosis.cd == 0 then return end
    if EyeBeam.cd < 500 + MakGcd() * (num(player:TalentKnown(A.StudentOfSuffering.ID)) and (SigilOfFlame.cd <= MakGcd() and 1 or 0) or 0) then return end

    return spell:Cast(target)
end)

-- actions.fs+=/chaos_strike,if=cooldown.eye_beam.remains>=gcd.max*4|(fury>=70-30*(talent.student_of_suffering&(cooldown.sigil_of_flame.remains<=gcd.max|cooldown.sigil_of_flame.up))-buff.chaos_theory.up*20-variable.fury_gen)
ChaosStrike:Callback("fs-chaosstrike2", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if EyeBeam.cd < MakGcd() * 4 then return end
    if player.fury < 70 - 30 * ( (player:TalentKnown(A.StudentOfSuffering.ID) and (SigilOfFlame.cd == 0 or SigilOfFlame.cd <= MakGcd())) and 1 or 0 ) - player:HasBuffCount(buffs.chaos_theory) * 20 - 5 then return end

    return spell:Cast(target)
end)

-- actions.fs+=/immolation_aura,if=raid_event.adds.in>full_recharge_time&cooldown.eye_beam.remains>=gcd.max*(1+talent.student_of_suffering&(cooldown.sigil_of_flame.remains<=gcd.max|cooldown.sigil_of_flame.up))|active_enemies>desired_targets&active_enemies>2
ImmolationAura:Callback("fs-immolationaura7", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (gameState.activeEnemies > 2) then return end
    if not (gameState.activeEnemies >= 3 or gameState.activeEnemies > 2) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/felblade,if=buff.out_of_range.down&buff.inertia_trigger.down&cooldown.eye_beam.remains>=gcd.max*(1+talent.student_of_suffering&(cooldown.sigil_of_flame.remains<=gcd.max|cooldown.sigil_of_flame.up))
Felblade:Callback("fs-felblade3", function(spell)
    if not Felblade:InRange(target) then return end
    if player:Buff(buffs.out_of_range) then return end
    if player:Buff(buffs.inertia_trigger) then return end
    if EyeBeam.cd < MakGcd() * (1 + (num(player:TalentKnown(A.StudentOfSuffering.ID)) and (SigilOfFlame.cd <= MakGcd() or SigilOfFlame.cd == 0) and 1 or 0)) then return end

    return spell:Cast(target)
end)

-- actions.fs+=/sigil_of_flame,if=buff.out_of_range.down&debuff.essence_break.down&!talent.student_of_suffering&(!talent.fel_barrage|cooldown.fel_barrage.remains>25|(active_enemies=1&!raid_event.adds.exists))
SigilOfFlame:Callback("fs-sigilofflame3", function(spell)
    if not Disrupt:InRange(target) then return end
    if player:Buff(buffs.out_of_range) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:TalentKnown(A.StudentOfSuffering.ID) then return end
    if player:TalentKnown(A.FelBarrage.ID) and FelBarrage.cd < 25000 then return end
    if gameState.activeEnemies == 1 and gameState.activeEnemies > 2 then return end

    return spell:Cast(player)
end)

-- actions.fs+=/throw_glaive,if=recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&buff.out_of_range.down&active_enemies>1
ThrowGlaive:Callback("fs-throwglaive", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if spell:TimeToFullCharges() > EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.frac > 1.01) then return end
    if player:Buff(buffs.out_of_range) then return end
    if gameState.activeEnemies <= 1 then return end

    return spell:Cast(target)
end)

-- actions.fs+=/fel_rush,if=buff.unbound_chaos.down&recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&active_enemies>1
FelRush:Callback("fs-felrush2", function(spell)
    if Disrupt:InRange(target) then return end
    if player:Buff(buffs.unbound_chaos) then return end
    if spell:TimeToFullCharges() > EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.frac > 1.01) then return end
    if gameState.activeEnemies <= 1 then return end

    return spell:Cast(target)
end)

-- actions.fs+=/arcane_torrent,if=buff.out_of_range.down&debuff.essence_break.down&fury<100
ArcaneTorrent:Callback("fs-arcantorrent", function(spell)
    if not ArcaneTorrent:InRange(target) then return end
    if player:Buff(buffs.out_of_range) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player.fury >= 100 then return end

    return spell:Cast(player)
end)

--[[
NEW FEL-SCARRED CALLBACKS (SimC APL)
]]--

-- actions.fs+=/immolation_aura,if=active_enemies>2&talent.ragefire&(!talent.fel_barrage|cooldown.fel_barrage.remains>recharge_time)&debuff.essence_break.down&(buff.metamorphosis.down|buff.metamorphosis.remains>5)
ImmolationAura:Callback("fs-immolationaura-new", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if gameState.activeEnemies <= 2 then return end
    if not player:TalentKnown(A.Ragefire.ID) then return end
    if player:TalentKnown(A.FelBarrage.ID) and FelBarrage.cd <= spell:TimeToFullCharges() then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.metamorphosis) and player:BuffRemains(buffs.metamorphosis) <= 5000 then return end

    return spell:Cast(player)
end)

-- actions.fs+=/immolation_aura,if=active_enemies>2&talent.ragefire&raid_event.adds.up&raid_event.adds.remains<15&raid_event.adds.remains>5&debuff.essence_break.down
ImmolationAura:Callback("fs-immolationaura-new2", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if gameState.activeEnemies <= 2 then return end
    if not player:TalentKnown(A.Ragefire.ID) then return end
    if target:Debuff(debuffs.essence_break) then return end
    -- Simplified raid event logic

    return spell:Cast(player)
end)

-- actions.fs+=/felblade,if=talent.unbound_chaos&buff.unbound_chaos.up&!talent.inertia&active_enemies<=2&(talent.student_of_suffering&cooldown.eye_beam.remains-gcd.max*2<=buff.unbound_chaos.remains|hero_tree.aldrachi_reaver)
Felblade:Callback("fs-felblade-new", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:TalentKnown(A.UnboundChaos.ID) then return end
    if not player:Buff(buffs.unbound_chaos) then return end
    if player:TalentKnown(A.Inertia.ID) then return end
    if gameState.activeEnemies > 2 then return end

    local studentCondition = player:TalentKnown(A.StudentOfSuffering.ID) and
                            (EyeBeam.cd - MakGcd() * 2) <= player:BuffRemains(buffs.unbound_chaos)
    local aldrachiCondition = player:TalentKnown(AldrachiReaver.id)

    if not (studentCondition or aldrachiCondition) then return end

    return spell:Cast(target)
end)

-- actions.fs+=/fel_rush,if=talent.unbound_chaos&buff.unbound_chaos.up&!talent.inertia&active_enemies>3&(talent.student_of_suffering&cooldown.eye_beam.remains-gcd.max*2<=buff.unbound_chaos.remains)
FelRush:Callback("fs-felrush-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if not player:TalentKnown(A.UnboundChaos.ID) then return end
    if not player:Buff(buffs.unbound_chaos) then return end
    if player:TalentKnown(A.Inertia.ID) then return end
    if gameState.activeEnemies <= 3 then return end

    local studentCondition = player:TalentKnown(A.StudentOfSuffering.ID) and
                            (EyeBeam.cd - MakGcd() * 2) <= player:BuffRemains(buffs.unbound_chaos)
    if not studentCondition then return end

    return spell:Cast(player)
end)

-- actions.fs+=/vengeful_retreat,use_off_gcd=1,if=talent.initiative&(cooldown.eye_beam.remains>15&gcd.remains<0.3|gcd.remains<0.2&cooldown.eye_beam.remains<=gcd.remains&(cooldown.metamorphosis.remains>10|cooldown.blade_dance.remains<gcd.max*3))&(!talent.student_of_suffering|cooldown.sigil_of_flame.remains)&(cooldown.essence_break.remains<=gcd.max*2&talent.student_of_suffering&cooldown.sigil_of_flame.remains|cooldown.essence_break.remains>=18|!talent.student_of_suffering)&cooldown.metamorphosis.remains>10&time>20
VengefulRetreat:Callback("fs-vengefulretreat-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:TalentKnown(A.Initiative.ID) then return end

    local eyeBeamCondition = (EyeBeam.cd > 15000 and MakGcd() < 300) or
                            (MakGcd() < 200 and EyeBeam.cd <= MakGcd() and
                             (Metamorphosis.cd > 10000 or BladeDance.cd < MakGcd() * 3))
    if not eyeBeamCondition then return end

    if player:TalentKnown(A.StudentOfSuffering.ID) and SigilOfFlame.cd == 0 then return end

    local essenceBreakCondition = (EssenceBreakk.cd <= MakGcd() * 2 and player:TalentKnown(A.StudentOfSuffering.ID) and SigilOfFlame.cd > 0) or
                                 EssenceBreakk.cd >= 18000 or not player:TalentKnown(A.StudentOfSuffering.ID)
    if not essenceBreakCondition then return end

    if Metamorphosis.cd <= 10000 then return end
    if player.combatTime <= 20 then return end

    return spell:Cast(player)
end)

-- Additional FS callbacks for the remaining APL abilities would go here
-- Note: Many of the remaining abilities in the FS APL are complex and would require
-- extensive condition checking. For now, the main structure is in place.

-- actions.fs+=/immolation_aura,if=variable.fs_tier34_2piece&(full_recharge_time<gcd.max*3|buff.immolation_aura.down&(cooldown.eye_beam.remains<3&(!talent.essence_break|buff.cycle_of_hatred.stack<4)|talent.essence_break&cooldown.essence_break.remains<=5|talent.essence_break&((cooldown.eye_beam.remains<3)*cooldown.essence_break.remains)>recharge_time))
ImmolationAura:Callback("fs-immolationaura-tier34", function(spell)
    if not ImmolationAura:InRange(target) then return end
    -- Simplified tier set logic - would need proper tier set detection
    local tier34_2piece = gameState.TWW2has2P or false -- Placeholder
    if not tier34_2piece then return end

    local rechargeCondition = spell:TimeToFullCharges() < MakGcd() * 3
    local buffCondition = not player:Buff(buffs.immolation_aura) and
                         (EyeBeam.cd < 3000 and (not player:TalentKnown(A.EssenceBreakk.ID) or player:HasBuffCount(buffs.cycle_of_hatred) < 4) or
                          player:TalentKnown(A.EssenceBreakk.ID) and EssenceBreakk.cd <= 5000)

    if not (rechargeCondition or buffCondition) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/immolation_aura,if=talent.a_fire_inside&talent.burning_wound&full_recharge_time<gcd.max*2&(raid_event.adds.in>full_recharge_time|active_enemies>desired_targets)
ImmolationAura:Callback("fs-immolationaura-afire", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not player:TalentKnown(A.AFireInside.ID) then return end
    if not player:TalentKnown(A.BurningWound.ID) then return end
    if spell:TimeToFullCharges() >= MakGcd() * 2 then return end
    if gameState.activeEnemies <= 3 then return end -- Simplified desired_targets logic

    return spell:Cast(player)
end)

-- actions.fs+=/sigil_of_flame,if=talent.student_of_suffering&(cooldown.eye_beam.remains<=gcd.max|!talent.initiative)&(cooldown.essence_break.remains<gcd.max*3|!talent.essence_break)&(cooldown.metamorphosis.remains>10|cooldown.blade_dance.remains<gcd.max*2)
SigilOfFlame:Callback("fs-sigilofflame-student", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:TalentKnown(A.StudentOfSuffering.ID) then return end
    if not (EyeBeam.cd <= MakGcd() or not player:TalentKnown(A.Initiative.ID)) then return end
    if not (EssenceBreakk.cd < MakGcd() * 3 or not player:TalentKnown(A.EssenceBreakk.ID)) then return end
    if not (Metamorphosis.cd > 10000 or BladeDance.cd < MakGcd() * 2) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/eye_beam,if=(!talent.initiative|buff.initiative.up|cooldown.vengeful_retreat.remains>=10|cooldown.metamorphosis.up|talent.initiative&!talent.tactical_retreat)&(cooldown.blade_dance.remains<7|raid_event.adds.up)&(active_enemies>desired_targets*2|raid_event.adds.in>30-buff.cycle_of_hatred.stack*5|fight_style.dungeonroute&!raid_event.adds.in<=40-buff.cycle_of_hatred.stack*5)&(!variable.trinket1_steroids&!variable.trinket2_steroids|variable.trinket1_steroids&(trinket.1.stat.any.cooldown_remains<gcd.max*3|trinket.1.stat.any.cooldown_remains>30-buff.cycle_of_hatred.stack*5)|variable.trinket2_steroids&(trinket.2.stat.any.cooldown_remains<gcd.max*3|trinket.2.stat.any.cooldown_remains>30-buff.cycle_of_hatred.stack*5))|fight_remains<10
EyeBeam:Callback("fs-eyebeam-new", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.moving then return end

    local initiativeCondition = not player:TalentKnown(A.Initiative.ID) or player:Buff(buffs.initiative) or
                               VengefulRetreat.cd >= 10000 or Metamorphosis.cd == 0 or
                               (player:TalentKnown(A.Initiative.ID) and not player:TalentKnown(A.TacticalRetreat.ID))
    if not initiativeCondition then return end

    local bladeDanceCondition = BladeDance.cd < 7000
    if not bladeDanceCondition then return end

    local enemyCondition = gameState.activeEnemies > 6 or target.ttd < 10000 -- Simplified logic
    if not enemyCondition then return end

    return spell:Cast(player)
end)

-- actions.fs+=/essence_break,if=!talent.initiative&cooldown.eye_beam.remains>5
EssenceBreakk:Callback("fs-essencebreak-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player:TalentKnown(A.Initiative.ID) then return end
    if EyeBeam.cd <= 5000 then return end

    return spell:Cast(player)
end)

-- actions.fs+=/blade_dance,if=cooldown.eye_beam.remains>=gcd.max*4&(active_enemies>3|talent.screaming_brutality&talent.soulscar)
BladeDance:Callback("fs-bladedance-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if EyeBeam.cd < MakGcd() * 4 then return end
    if not (gameState.activeEnemies > 3 or (player:TalentKnown(A.ScreamingBrutality.ID) and player:TalentKnown(A.Soulscar.ID))) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/chaos_strike,if=variable.fs_tier34_2piece&(buff.immolation_aura.up|debuff.essence_break.up)
ChaosStrike:Callback("fs-chaosstrike-tier34", function(spell)
    if not ChaosStrike:InRange(target) then return end
    local tier34_2piece = gameState.TWW2has2P or false -- Placeholder
    if not tier34_2piece then return end
    if not (player:Buff(buffs.immolation_aura) or target:Debuff(debuffs.essence_break)) then return end

    return spell:Cast(target)
end)

-- actions.fs+=/blade_dance,if=cooldown.eye_beam.remains>=gcd.max*4
BladeDance:Callback("fs-bladedance-new2", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if EyeBeam.cd < MakGcd() * 4 then return end

    return spell:Cast(player)
end)

-- actions.fs+=/glaive_tempest,if=active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>10
GlaiveTempest:Callback("fs-glaivetempest-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if gameState.activeEnemies < 3 then return end -- Simplified desired_targets logic

    return spell:Cast(player)
end)

-- actions.fs+=/sigil_of_flame,if=active_enemies>3&!talent.student_of_suffering
SigilOfFlame:Callback("fs-sigilofflame-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if gameState.activeEnemies <= 3 then return end
    if player:TalentKnown(A.StudentOfSuffering.ID) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/chaos_strike,if=debuff.essence_break.up
ChaosStrike:Callback("fs-chaosstrike-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.fs+=/felblade,if=fury.deficit>40+variable.fury_gen*(0.5%gcd.max)&(cooldown.vengeful_retreat.remains>=action.felblade.cooldown+0.5&talent.inertia&active_enemies=1|!talent.inertia|hero_tree.aldrachi_reaver|cooldown.essence_break.remains)&cooldown.metamorphosis.remains&cooldown.eye_beam.remains>=0.5+gcd.max*(talent.student_of_suffering&cooldown.sigil_of_flame.remains<=gcd.max)&(!variable.fs_tier34_2piece|variable.fs_tier34_2piece&buff.immolation_aura.down&cooldown.immolation_aura.remains)
Felblade:Callback("fs-felblade-fury", function(spell)
    if not Felblade:InRange(target) then return end

    local furyDeficit = 100 - player.fury
    local furyGenThreshold = 40 + (furyGen or 0) * (0.5 / MakGcd() * 1000)
    if furyDeficit <= furyGenThreshold then return end

    local retreatCondition = VengefulRetreat.cd >= (spell.cd + 500) and player:TalentKnown(A.Inertia.ID) and gameState.activeEnemies == 1
    local generalCondition = not player:TalentKnown(A.Inertia.ID) or player:TalentKnown(AldrachiReaver.id) or EssenceBreakk.cd > 0

    if not (retreatCondition or generalCondition) then return end
    if Metamorphosis.cd == 0 then return end

    local eyeBeamThreshold = 500 + MakGcd() * (player:TalentKnown(A.StudentOfSuffering.ID) and SigilOfFlame.cd <= MakGcd() and 1 or 0)
    if EyeBeam.cd < eyeBeamThreshold then return end

    return spell:Cast(target)
end)

-- actions.fs+=/chaos_strike,if=cooldown.eye_beam.remains>=gcd.max*4|(fury>=70-30*(talent.student_of_suffering&(cooldown.sigil_of_flame.remains<=gcd.max|cooldown.sigil_of_flame.up))-buff.chaos_theory.up*20-variable.fury_gen)
ChaosStrike:Callback("fs-chaosstrike-fury", function(spell)
    if not ChaosStrike:InRange(target) then return end

    local eyeBeamCondition = EyeBeam.cd >= MakGcd() * 4
    local studentReduction = player:TalentKnown(A.StudentOfSuffering.ID) and (SigilOfFlame.cd <= MakGcd() or SigilOfFlame.cd == 0) and 30 or 0
    local chaosTheoryReduction = player:Buff(buffs.chaos_theory) and 20 or 0
    local furyThreshold = 70 - studentReduction - chaosTheoryReduction - (furyGen or 0)
    local furyCondition = player.fury >= furyThreshold

    if not (eyeBeamCondition or furyCondition) then return end

    return spell:Cast(target)
end)

-- actions.fs+=/immolation_aura,if=!variable.fs_tier34_2piece&raid_event.adds.in>full_recharge_time&cooldown.eye_beam.remains>=gcd.max*(1+talent.student_of_suffering&(cooldown.sigil_of_flame.remains<=gcd.max|cooldown.sigil_of_flame.up))|active_enemies>desired_targets&active_enemies>2
ImmolationAura:Callback("fs-immolationaura-general", function(spell)
    if not ImmolationAura:InRange(target) then return end

    local tier34_2piece = gameState.TWW2has2P or false
    local studentMultiplier = player:TalentKnown(A.StudentOfSuffering.ID) and (SigilOfFlame.cd <= MakGcd() or SigilOfFlame.cd == 0) and 1 or 0
    local eyeBeamThreshold = MakGcd() * (1 + studentMultiplier)

    local condition1 = not tier34_2piece and EyeBeam.cd >= eyeBeamThreshold
    local condition2 = gameState.activeEnemies > 3 and gameState.activeEnemies > 2

    if not (condition1 or condition2) then return end

    return spell:Cast(player)
end)

-- actions.fs+=/felblade,if=buff.out_of_range.down&buff.inertia_trigger.down&cooldown.eye_beam.remains>=gcd.max*(1+talent.student_of_suffering&(cooldown.sigil_of_flame.remains<=gcd.max|cooldown.sigil_of_flame.up))
Felblade:Callback("fs-felblade-range", function(spell)
    if not Felblade:InRange(target) then return end
    if player:Buff(buffs.out_of_range) then return end
    if player:Buff(buffs.inertia_trigger) then return end

    local studentMultiplier = player:TalentKnown(A.StudentOfSuffering.ID) and (SigilOfFlame.cd <= MakGcd() or SigilOfFlame.cd == 0) and 1 or 0
    local eyeBeamThreshold = MakGcd() * (1 + studentMultiplier)
    if EyeBeam.cd < eyeBeamThreshold then return end

    return spell:Cast(target)
end)

-- actions.fs+=/sigil_of_flame,if=buff.out_of_range.down&debuff.essence_break.down&!talent.student_of_suffering&(!talent.fel_barrage|cooldown.fel_barrage.remains>25|(active_enemies=1&!raid_event.adds.exists))
SigilOfFlame:Callback("fs-sigilofflame-general", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player:Buff(buffs.out_of_range) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:TalentKnown(A.StudentOfSuffering.ID) then return end
    if player:TalentKnown(A.FelBarrage.ID) and FelBarrage.cd <= 25000 and gameState.activeEnemies > 1 then return end

    return spell:Cast(player)
end)

-- actions.fs+=/demons_bite
DemonsBite:Callback("fs-demonsbite-new", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.fs+=/throw_glaive,if=recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&buff.out_of_range.down&active_enemies>1&!talent.furious_throws
ThrowGlaive:Callback("fs-throwglaive-new", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if spell:TimeToFullCharges() >= EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.charges > 1.01) then return end
    if player:Buff(buffs.out_of_range) then return end
    if gameState.activeEnemies <= 1 then return end
    if player:TalentKnown(A.FuriousThrows.ID) then return end

    return spell:Cast(target)
end)

-- actions.fs+=/fel_rush,if=buff.unbound_chaos.down&recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&active_enemies>1
FelRush:Callback("fs-felrush-movement", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    if player:Buff(buffs.unbound_chaos) then return end
    if spell:TimeToFullCharges() >= EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.charges > 1.01) then return end
    if gameState.activeEnemies <= 1 then return end

    return spell:Cast(player)
end)

-- actions.fs+=/arcane_torrent,if=buff.out_of_range.down&debuff.essence_break.down&fury<100
ArcaneTorrent:Callback("fs-arcanetorrent-new", function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if player:Buff(buffs.out_of_range) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player.fury >= 100 then return end

    return spell:Cast(player)
end)

-- actions.fs+=/felblade,if=variable.fs_tier34_2piece&talent.inertia&buff.inertia_trigger.up&(buff.immolation_aura.up|buff.inertia_trigger.remains<=0.5|cooldown.the_hunt.remains<=0.5)&active_enemies<=2
Felblade:Callback("fs-felblade-tier34", function(spell)
    if not Felblade:InRange(target) then return end
    local tier34_2piece = gameState.TWW2has2P or false
    if not tier34_2piece then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not (player:Buff(buffs.immolation_aura) or player:BuffRemains(buffs.inertia_trigger) <= 500 or TheHunt.cd <= 500) then return end
    if gameState.activeEnemies > 2 then return end

    return spell:Cast(target)
end)

-- actions.fs+=/fel_rush,if=variable.fs_tier34_2piece&talent.inertia&buff.inertia_trigger.up&(buff.immolation_aura.up|buff.inertia_trigger.remains<=gcd.max|cooldown.the_hunt.remains<=gcd.max)&(active_enemies>2|cooldown.felblade.remains>buff.inertia_trigger.remains)
FelRush:Callback("fs-felrush-tier34", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.enemiesInMelee < 1 then return end
    local tier34_2piece = gameState.TWW2has2P or false
    if not tier34_2piece then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not (player:Buff(buffs.immolation_aura) or player:BuffRemains(buffs.inertia_trigger) <= MakGcd() or TheHunt.cd <= MakGcd()) then return end
    if not (gameState.activeEnemies > 2 or Felblade.cd > player:BuffRemains(buffs.inertia_trigger)) then return end

    return spell:Cast(player)
end)

--[[
-- END FelScarred
--]]

--------
--------

Blur:Callback("defensive", function(spell)
    local defensiveSelect = A.GetToggle(2, "defensiveSelect")
    if not defensiveSelect[1] then return end 
    if not player.inCombat then return end
    
    if shouldDefensive() or player.hp < A.GetToggle(2, "blurHP") then 
        return spell:Cast(player)
    end
end)

Netherwalk:Callback("defensive", function(spell)
    if not player.inCombat then return end
    
    if shouldDefensive() or player.hp < A.GetToggle(2, "netherwalkHP") then 
        return spell:Cast(player)
    end
end)

Darkness:Callback("defensive", function(spell)
    if not player.inCombat then return end
    
    if shouldDefensive() or player.hp < A.GetToggle(2, "darknessHP") then 
        return spell:Cast(player)
    end
end)

Felblade:Callback("jump-back", function(spell)
    if VengefulRetreat.used > 0 and VengefulRetreat.used < 1500 then return end
    --if not ChaosStrike:InRange(target) then return end

    return spell:Cast(target)
end)

--[[
NEW ALDRACHI REAVER CALLBACKS (SimC APL)
]]--

-- actions.ar+=/chaos_strike,if=buff.rending_strike.up&buff.glaive_flurry.up&(variable.rg_ds=2|active_enemies>2)&time>10
ChaosStrike:Callback("ar-chaosstrike-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.rending_strike) then return end
    if not player:Buff(buffs.glaive_flurry) then return end
    if not (gameState.activeEnemies > 2) then return end -- Simplified rg_ds=2 check
    if player.combatTime <= 10 then return end

    return spell:Cast(target)
end)

-- actions.ar+=/annihilation,if=buff.rending_strike.up&buff.glaive_flurry.up&(variable.rg_ds=2|active_enemies>2)
Annihilation:Callback("ar-annihilation-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:Buff(buffs.rending_strike) then return end
    if not player:Buff(buffs.glaive_flurry) then return end
    if not (gameState.activeEnemies > 2) then return end -- Simplified rg_ds=2 check

    return spell:Cast(target)
end)

-- actions.ar+=/reavers_glaive,if=buff.glaive_flurry.down&buff.rending_strike.down&buff.thrill_of_the_fight_damage.remains<gcd.max*4+(variable.rg_ds=2)+(cooldown.the_hunt.remains<gcd.max*3)*3+(cooldown.eye_beam.remains<gcd.max*3&talent.shattered_destiny)*3&(variable.rg_ds=0|variable.rg_ds=1&cooldown.blade_dance.up|variable.rg_ds=2&cooldown.blade_dance.remains)&(buff.thrill_of_the_fight_damage.up|!prev_gcd.1.death_sweep|!variable.rg_inc)&active_enemies<3&!action.reavers_glaive.last_used<5&debuff.essence_break.down&(buff.metamorphosis.remains>2|cooldown.eye_beam.remains<10|fight_remains<10)&(target.time_to_die>=10|fight_remains<=10)|fight_remains<=10
ReaversGlaive:Callback("ar-reaversglaive-new", function(spell)
    if not ReaversGlaive:InRange(target) then return end
    if player:Buff(buffs.glaive_flurry) then return end
    if player:Buff(buffs.rending_strike) then return end

    local thrillRemains = player:BuffRemains(buffs.thrill_of_the_fight_damage)
    local gcdMax = MakGcd()
    local timeThreshold = gcdMax * 4 + (TheHunt.cd < gcdMax * 3 and 3 or 0) +
                         (EyeBeam.cd < gcdMax * 3 and player:TalentKnown(A.ShatteredDestiny.ID) and 3 or 0)

    if thrillRemains >= timeThreshold then return end
    if not (player:Buff(buffs.thrill_of_the_fight_damage) or DeathSweep.lastUsed >= 1000 or not gameState.rgInc) then return end
    if gameState.activeEnemies >= 3 then return end
    if ReaversGlaive.used < 5000 then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (player:BuffRemains(buffs.metamorphosis) > 2000 or EyeBeam.cd < 10000 or target.ttd < 10000) then return end
    if target.ttd < 10000 and target.ttd >= 10000 then return end -- Simplified fight_remains logic

    return spell:Cast(target)
end)

-- actions.ar+=/reavers_glaive,if=buff.glaive_flurry.down&buff.rending_strike.down&buff.thrill_of_the_fight_damage.remains<4&(buff.thrill_of_the_fight_damage.up|!prev_gcd.1.death_sweep|!variable.rg_inc)&active_enemies>2&target.time_to_die>=10&debuff.essence_break.down|fight_remains<=10
ReaversGlaive:Callback("ar-reaversglaive-new2", function(spell)
    if not ReaversGlaive:InRange(target) then return end
    if player:Buff(buffs.glaive_flurry) then return end
    if player:Buff(buffs.rending_strike) then return end
    if player:BuffRemains(buffs.thrill_of_the_fight_damage) >= 4000 then return end
    if not (player:Buff(buffs.thrill_of_the_fight_damage) or DeathSweep.lastUsed >= 1000 or not gameState.rgInc) then return end
    if gameState.activeEnemies <= 2 then return end
    if target.ttd < 10000 then return end
    if target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/sigil_of_spite,if=debuff.essence_break.down&cooldown.blade_dance.remains&debuff.reavers_mark.remains>=2-talent.quickened_sigils&(buff.necessary_sacrifice.remains>=2-talent.quickened_sigils|!set_bonus.thewarwithin_season_2_4pc|cooldown.eye_beam.remains>8)&(buff.metamorphosis.down|buff.metamorphosis.remains+talent.shattered_destiny>=buff.necessary_sacrifice.remains+2-talent.quickened_sigils)|fight_remains<20
SigilOfSpite:Callback("ar-sigilofspite-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if BladeDance.cd == 0 then return end

    local quickenedSigilsReduction = player:TalentKnown(A.QuickenedSigils.ID) and 1000 or 0
    if target:DebuffRemains(debuffs.reavers_mark) < (2000 - quickenedSigilsReduction) then return end

    local necessarySacrificeCondition = player:BuffRemains(buffs.necessary_sacrifice) >= (2000 - quickenedSigilsReduction) or
                                       not gameState.TWW2has4P or EyeBeam.cd > 8000
    if not necessarySacrificeCondition then return end

    local metamorphosisCondition = not player:Buff(buffs.metamorphosis) or
                                  (player:BuffRemains(buffs.metamorphosis) + (player:TalentKnown(A.ShatteredDestiny.ID) and 1000 or 0)) >=
                                  (player:BuffRemains(buffs.necessary_sacrifice) + 2000 - quickenedSigilsReduction)
    if not metamorphosisCondition and target.ttd >= 20000 then return end

    return spell:Cast(player)
end)

-- actions.ar+=/immolation_aura,if=active_enemies>2&talent.ragefire&(!talent.fel_barrage|cooldown.fel_barrage.remains>recharge_time)&debuff.essence_break.down&(buff.metamorphosis.down|buff.metamorphosis.remains>5)
ImmolationAura:Callback("ar-immolationaura-new", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if gameState.activeEnemies <= 2 then return end
    if not player:TalentKnown(A.Ragefire.ID) then return end
    if player:TalentKnown(A.FelBarrage.ID) and FelBarrage.cd <= spell:TimeToFullCharges() then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.metamorphosis) and player:BuffRemains(buffs.metamorphosis) <= 5000 then return end

    return spell:Cast(player)
end)

-- actions.ar+=/immolation_aura,if=active_enemies>2&talent.ragefire&raid_event.adds.up&raid_event.adds.remains<15&raid_event.adds.remains>5&debuff.essence_break.down
ImmolationAura:Callback("ar-immolationaura-new2", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if gameState.activeEnemies <= 2 then return end
    if not player:TalentKnown(A.Ragefire.ID) then return end
    if target:Debuff(debuffs.essence_break) then return end
    -- Simplified raid event logic - just check for multiple enemies

    return spell:Cast(player)
end)

-- actions.ar+=/vengeful_retreat,if=talent.initiative&talent.tactical_retreat&time>20&(cooldown.eye_beam.up&(talent.restless_hunter|cooldown.metamorphosis.remains>10))&(!talent.inertia&buff.unbound_chaos.down|buff.inertia_trigger.down&buff.metamorphosis.down)
VengefulRetreat:Callback("ar-vengefulretreat-new", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:TalentKnown(A.Initiative.ID) then return end
    if not player:TalentKnown(A.TacticalRetreat.ID) then return end
    if player.combatTime <= 20 then return end
    if EyeBeam.cd > 0 then return end
    if not (player:TalentKnown(A.RestlessHunter.ID) or Metamorphosis.cd > 10000) then return end
    if not (not player:TalentKnown(A.Inertia.ID) and not player:Buff(buffs.unbound_chaos) or
            not player:Buff(buffs.inertia_trigger) and not player:Buff(buffs.metamorphosis)) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/vengeful_retreat,use_off_gcd=1,if=talent.initiative&!talent.tactical_retreat&(cooldown.eye_beam.remains>15&gcd.remains<0.3|gcd.remains<0.2&cooldown.eye_beam.remains<=gcd.remains&cooldown.metamorphosis.remains>10)&(!variable.trinket1_steroids&!variable.trinket2_steroids|variable.trinket1_steroids&(trinket.1.stat.any.cooldown_remains<gcd.max*3|trinket.1.stat.any.cooldown_remains>30)|variable.trinket2_steroids&(trinket.2.stat.any.cooldown_remains<gcd.max*3|trinket.2.stat.any.cooldown_remains>30))&time>20&(!talent.inertia&buff.unbound_chaos.down|buff.inertia_trigger.down&buff.metamorphosis.down)
VengefulRetreat:Callback("ar-vengefulretreat-new2", function(spell)
    if not Annihilation:InRange(target) then return end
    if not player:TalentKnown(A.Initiative.ID) then return end
    if player:TalentKnown(A.TacticalRetreat.ID) then return end
    if not (EyeBeam.cd > 15000 and MakGcd() < 300 or MakGcd() < 200 and EyeBeam.cd <= MakGcd() and Metamorphosis.cd > 10000) then return end
    -- Simplified trinket logic
    if player.combatTime <= 20 then return end
    if not (not player:TalentKnown(A.Inertia.ID) and not player:Buff(buffs.unbound_chaos) or
            not player:Buff(buffs.inertia_trigger) and not player:Buff(buffs.metamorphosis)) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/felblade,if=!talent.inertia&active_enemies=1&buff.unbound_chaos.up&buff.initiative.up&debuff.essence_break.down&buff.metamorphosis.down
Felblade:Callback("ar-felblade-new", function(spell)
    if not Felblade:InRange(target) then return end
    if player:TalentKnown(A.Inertia.ID) then return end
    if gameState.activeEnemies ~= 1 then return end
    if not player:Buff(buffs.unbound_chaos) then return end
    if not player:Buff(buffs.initiative) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:Buff(buffs.metamorphosis) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/felblade,if=buff.inertia_trigger.up&talent.inertia&cooldown.eye_beam.remains<=0.5&(cooldown.metamorphosis.remains&talent.looks_can_kill|active_enemies>1)
Felblade:Callback("ar-felblade-new2", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if EyeBeam.cd > 500 then return end
    if not (Metamorphosis.cd > 0 and player:TalentKnown(A.LooksCanKill.ID) or gameState.activeEnemies > 1) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/felblade,if=buff.inertia_trigger.up&talent.inertia&buff.inertia.down&cooldown.blade_dance.remains<4&(cooldown.eye_beam.remains>5&cooldown.eye_beam.remains>buff.unbound_chaos.remains|cooldown.eye_beam.remains<=gcd.max&cooldown.vengeful_retreat.remains<=gcd.max+1)
Felblade:Callback("ar-felblade-new3", function(spell)
    if not Felblade:InRange(target) then return end
    if not player:Buff(buffs.inertia_trigger) then return end
    if not player:TalentKnown(A.Inertia.ID) then return end
    if player:Buff(buffs.inertia) then return end
    if BladeDance.cd >= 4000 then return end
    local eyeBeamCondition = (EyeBeam.cd > 5000 and EyeBeam.cd > player:BuffRemains(buffs.unbound_chaos)) or
                            (EyeBeam.cd <= MakGcd() and VengefulRetreat.cd <= MakGcd() + 1000)
    if not eyeBeamCondition then return end

    return spell:Cast(target)
end)

-- actions.ar+=/immolation_aura,if=talent.a_fire_inside&talent.burning_wound&full_recharge_time<gcd.max*2&(raid_event.adds.in>full_recharge_time|active_enemies>desired_targets)
ImmolationAura:Callback("ar-immolationaura-new3", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not player:TalentKnown(A.AFireInside.ID) then return end
    if not player:TalentKnown(A.BurningWound.ID) then return end
    if spell:TimeToFullCharges() >= MakGcd() * 2 then return end
    if gameState.activeEnemies <= 3 then return end -- Simplified desired_targets logic

    return spell:Cast(player)
end)

-- actions.ar+=/immolation_aura,if=active_enemies>desired_targets&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>full_recharge_time)
ImmolationAura:Callback("ar-immolationaura-new4", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if gameState.activeEnemies <= 3 then return end -- Simplified desired_targets logic

    return spell:Cast(player)
end)

-- actions.ar+=/immolation_aura,if=fight_remains<15&cooldown.blade_dance.remains&talent.ragefire
ImmolationAura:Callback("ar-immolationaura-new5", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if target.ttd >= 15000 then return end
    if BladeDance.cd == 0 then return end
    if not player:TalentKnown(A.Ragefire.ID) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/eye_beam,if=!talent.essence_break&(!talent.chaotic_transformation|cooldown.metamorphosis.remains<5+3*talent.shattered_destiny|cooldown.metamorphosis.remains>10)&(active_enemies>desired_targets*2|raid_event.adds.in>30-talent.cycle_of_hatred.rank*2.5*buff.cycle_of_hatred.stack)&(!talent.initiative|cooldown.vengeful_retreat.remains>5|cooldown.vengeful_retreat.up&active_enemies>2|talent.shattered_destiny)
EyeBeam:Callback("ar-eyebeam-new", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.moving then return end
    if player:TalentKnown(A.EssenceBreakk.ID) then return end

    local metamorphosisCondition = not player:TalentKnown(A.ChaoticTransformation.ID) or
                                  Metamorphosis.cd < (5000 + 3000 * (player:TalentKnown(A.ShatteredDestiny.ID) and 1 or 0)) or
                                  Metamorphosis.cd > 10000
    if not metamorphosisCondition then return end

    if gameState.activeEnemies <= 6 then return end -- Simplified desired_targets*2 logic

    local initiativeCondition = not player:TalentKnown(A.Initiative.ID) or VengefulRetreat.cd > 5000 or
                               (VengefulRetreat.cd == 0 and gameState.activeEnemies > 2) or
                               player:TalentKnown(A.ShatteredDestiny.ID)
    if not initiativeCondition then return end

    return spell:Cast(player)
end)

-- actions.ar+=/eye_beam,if=(cooldown.blade_dance.remains<7|raid_event.adds.up)&(active_enemies>desired_targets*2&(buff.thrill_of_the_fight_damage.up|buff.rending_strike.down&buff.glaive_flurry.down)|raid_event.adds.in>30-buff.cycle_of_hatred.stack*5|fight_style.dungeonroute&!raid_event.adds.in<=40-buff.cycle_of_hatred.stack*5)&(!variable.trinket1_steroids&!variable.trinket2_steroids|variable.trinket1_steroids&(trinket.1.stat.any.cooldown_remains<gcd.max*3|trinket.1.stat.any.cooldown_remains>30-buff.cycle_of_hatred.stack*5)|variable.trinket2_steroids&(trinket.2.stat.any.cooldown_remains<gcd.max*3|trinket.2.stat.any.cooldown_remains>30-buff.cycle_of_hatred.stack*5))|fight_remains<10
EyeBeam:Callback("ar-eyebeam-new2", function(spell)
    if not Disrupt:InRange(target) then return end
    if player.moving then return end

    local bladeDanceCondition = BladeDance.cd < 7000
    if not bladeDanceCondition then return end

    local enemyCondition = (gameState.activeEnemies > 6 and
                           (player:Buff(buffs.thrill_of_the_fight_damage) or
                            (not player:Buff(buffs.rending_strike) and not player:Buff(buffs.glaive_flurry))))
    local fightRemainsCondition = target.ttd < 10000

    if not (enemyCondition or fightRemainsCondition) then return end
    -- Simplified trinket logic

    return spell:Cast(player)
end)

-- actions.ar+=/blade_dance,if=(cooldown.eye_beam.remains>=gcd.max*2|active_enemies>=2&buff.glaive_flurry.up&(raid_event.adds.in>30-buff.cycle_of_hatred.stack*5|raid_event.adds.remains>=cooldown.eye_beam.remains&cooldown.eye_beam.remains<gcd.max*2))&buff.rending_strike.down
BladeDance:Callback("ar-bladedance-new", function(spell)
    if not ChaosStrike:InRange(target) then return end

    local eyeBeamCondition = EyeBeam.cd >= MakGcd() * 2 or
                            (gameState.activeEnemies >= 2 and player:Buff(buffs.glaive_flurry))
    if not eyeBeamCondition then return end
    if player:Buff(buffs.rending_strike) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/chaos_strike,if=buff.rending_strike.up
ChaosStrike:Callback("ar-chaosstrike-new2", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not player:Buff(buffs.rending_strike) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/sigil_of_flame,if=active_enemies>3|debuff.essence_break.down
SigilOfFlame:Callback("ar-sigilofflame-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not (gameState.activeEnemies > 3 or not target:Debuff(debuffs.essence_break)) then return end

    return spell:Cast(player)
end)

-- actions.ar+=/felblade,if=fury.deficit>=40+variable.fury_gen*0.5&!buff.inertia_trigger.up&(!talent.blind_fury|cooldown.eye_beam.remains>5)
Felblade:Callback("ar-felblade-new4", function(spell)
    if not Felblade:InRange(target) then return end
    local furyDeficit = 100 - player.fury
    local furyGenThreshold = 40 + (furyGen or 0) * 0.5
    if furyDeficit < furyGenThreshold then return end
    if player:Buff(buffs.inertia_trigger) then return end
    if player:TalentKnown(A.BlindFury.ID) and EyeBeam.cd <= 5000 then return end

    return spell:Cast(target)
end)

-- actions.ar+=/glaive_tempest,if=active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in>10
GlaiveTempest:Callback("ar-glaivetempest-new", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if gameState.activeEnemies < 3 then return end -- Simplified desired_targets logic

    return spell:Cast(player)
end)

-- actions.ar+=/chaos_strike,if=debuff.essence_break.up
ChaosStrike:Callback("ar-chaosstrike-new3", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if not target:Debuff(debuffs.essence_break) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/throw_glaive,if=active_enemies>2&talent.furious_throws&talent.soulscar&(!talent.screaming_brutality|charges=2|full_recharge_time<cooldown.blade_dance.remains)
ThrowGlaive:Callback("ar-throwglaive-new", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if gameState.activeEnemies <= 2 then return end
    if not player:TalentKnown(A.FuriousThrows.ID) then return end
    if not player:TalentKnown(A.Soulscar.ID) then return end
    if player:TalentKnown(A.ScreamingBrutality.ID) and spell.charges < 2 and spell:TimeToFullCharges() >= BladeDance.cd then return end

    return spell:Cast(target)
end)

-- actions.ar+=/chaos_strike,if=cooldown.eye_beam.remains>gcd.max*4|fury>=70-variable.fury_gen*gcd.max-talent.blind_fury.rank*15
ChaosStrike:Callback("ar-chaosstrike-new4", function(spell)
    if not ChaosStrike:InRange(target) then return end
    local eyeBeamCondition = EyeBeam.cd > MakGcd() * 4
    local furyThreshold = 70 - (furyGen or 0) * MakGcd() / 1000 - (player:TalentKnown(A.BlindFury.ID) and 15 or 0)
    local furyCondition = player.fury >= furyThreshold
    if not (eyeBeamCondition or furyCondition) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/felblade,if=!talent.a_fire_inside&fury<40
Felblade:Callback("ar-felblade-new5", function(spell)
    if not Felblade:InRange(target) then return end
    if player:TalentKnown(A.AFireInside.ID) then return end
    if player.fury >= 40 then return end

    return spell:Cast(target)
end)

-- actions.ar+=/immolation_aura,if=raid_event.adds.in>full_recharge_time|active_enemies>desired_targets&active_enemies>2
ImmolationAura:Callback("ar-immolationaura-new6", function(spell)
    if not ImmolationAura:InRange(target) then return end
    if not (gameState.activeEnemies > 3 and gameState.activeEnemies > 2) then return end -- Simplified logic

    return spell:Cast(player)
end)

-- actions.ar+=/sigil_of_flame,if=buff.out_of_range.down&debuff.essence_break.down&(!talent.fel_barrage|cooldown.fel_barrage.remains>25|active_enemies=1&!raid_event.adds.exists)
SigilOfFlame:Callback("ar-sigilofflame-new2", function(spell)
    if not ChaosStrike:InRange(target) then return end
    if player:Buff(buffs.out_of_range) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player:TalentKnown(A.FelBarrage.ID) and FelBarrage.cd <= 25000 and gameState.activeEnemies > 1 then return end

    return spell:Cast(player)
end)

-- actions.ar+=/demons_bite
DemonsBite:Callback("ar-demonsbite-new", function(spell)
    if player:TalentKnown(A.DemonBlades.ID) then return end
    if not DemonsBite:InRange(target) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/throw_glaive,if=buff.unbound_chaos.down&recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&buff.out_of_range.down&active_enemies>1&!talent.furious_throws
ThrowGlaive:Callback("ar-throwglaive-new2", function(spell)
    if not ThrowGlaive:InRange(target) then return end
    if player:Buff(buffs.unbound_chaos) then return end
    if spell:TimeToFullCharges() >= EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.charges > 1.01) then return end
    if player:Buff(buffs.out_of_range) then return end
    if gameState.activeEnemies <= 1 then return end
    if player:TalentKnown(A.FuriousThrows.ID) then return end

    return spell:Cast(target)
end)

-- actions.ar+=/fel_rush,if=buff.unbound_chaos.down&recharge_time<cooldown.eye_beam.remains&debuff.essence_break.down&(cooldown.eye_beam.remains>8|charges_fractional>1.01)&active_enemies>1
FelRush:Callback("ar-felrush-new", function(spell)
    if Disrupt:InRange(target) then return end
    if gameState.activeEnemies < 1 then return end
    if player:Buff(buffs.unbound_chaos) then return end
    if spell:TimeToFullCharges() >= EyeBeam.cd then return end
    if target:Debuff(debuffs.essence_break) then return end
    if not (EyeBeam.cd > 8000 or spell.charges > 1.01) then return end
    if gameState.activeEnemies <= 1 then return end

    return spell:Cast(player)
end)

-- actions.ar+=/arcane_torrent,if=buff.out_of_range.down&debuff.essence_break.down&fury<100
ArcaneTorrent:Callback("ar-arcanetorrent-new", function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if player:Buff(buffs.out_of_range) then return end
    if target:Debuff(debuffs.essence_break) then return end
    if player.fury >= 100 then return end

    return spell:Cast(player)
end)

A[3] = function(icon)
	FrameworkStart(icon)
    updateGameState()

    makInterrupt(interrupts)
    if 1 == 2 then
        MakPrint(1, "Fury: ", player.fury)
        MakPrint(2, "Fel Berrage: ", gameState.felBarrage)
        MakPrint(3, "Burst: ", shouldBurst())
        MakPrint(4, "GCD: ", MakGcd())
        MakPrint(5, "Disrupt Target: ", Disrupt:InRange(target))
        MakPrint(6, "TTD", target.ttd)
        MakPrint(7, "Combat Time", player.combatTime)
    end

    if player.inCombat and not defensiveActive() then -- Defensive
        Blur("defensive")
        Netherwalk("defensive")
        Darkness("defensive")

        if player.inCombat and shouldDefensive() then
            if Trinket(13, "Defensive") then Trinket1() end
            if Trinket(14, "Defensive") then Trinket2() end
        end
    end

    -- Default actions regardless of hero tree (from SimC APL)
    -- Auto attack if not out of range
    if target.exists and target.canAttack and not player:Buff(buffs.out_of_range) then
        -- Auto attack is handled by the game engine
    end

    -- Disrupt
    Disrupt("default")

    -- Retarget auto attack for Burning Wound spread
    -- Note: Retargeting is handled by the framework's target selection

    -- Variables for fury generation and trinket checks
    local furyGen = 0
    if player:TalentKnown(A.DemonBlades.ID) then
        local attackHaste = 1 -- Simplified, would need actual attack haste calculation
        local demonBladesFury = 1 / (2.6 * attackHaste) * ((player:TalentKnown(A.Demonsurge.ID) and player:Buff(buffs.metamorphosis) and 3 or 0) + 12)
        furyGen = furyGen + demonBladesFury
    end
    furyGen = furyGen + (player:HasBuffCount(buffs.immolation_aura) * 6)
    furyGen = furyGen + (player:Buff(buffs.tactical_retreat) and 10 or 0)

    -- Trinket pacemaker proc check
    local trinketPacemakerProc = true -- Simplified for now

    -- Tier 33 4-piece checks
    local tier334piece = (player:Buff(buffs.initiative) or not player:TalentKnown(A.Initiative.ID) or
                         (player:HasBuffCount(buffs.necessary_sacrifice) >= 5 and player:BuffRemains(buffs.necessary_sacrifice) < 500 + VengefulRetreat.cd)) and
                        (player:Buff(buffs.necessary_sacrifice) or not gameState.TWW2has4P or EyeBeam.cd + 2000 > player:BuffRemains(buffs.initiative))

    local tier334pieceMagnet = (player:Buff(buffs.initiative) or not player:TalentKnown(A.Initiative.ID)) and
                              (player:Buff(buffs.necessary_sacrifice) or not gameState.TWW2has4P) and trinketPacemakerProc

    -- Double on-use trinket logic (simplified)
    local doubleOnUse = true -- Simplified for now

    if target.exists and target.canAttack then
        Felblade("jump-back")
        -- Separate actionlists for each hero tree
        if player:TalentKnown(AldrachiReaver.id) then
            -- Run Aldrachi Reaver action list (New SimC APL)

            -- Variable: rg_inc
            gameState.rgInc = (not player:Buff(buffs.rending_strike) and player:Buff(buffs.glaive_flurry) and BladeDance.cd == 0 and MakGcd() == 0) or
                             (gameState.rgInc and DeathSweep.lastUsed < 1000)

            -- Pick up fragments
            if player.fury <= 90 then
                -- Fragment pickup is handled by the framework
            end

            -- Variable: fel_barrage (updated from gameState)
            gameState.felBarrage = player:TalentKnown(A.FelBarrage.ID) and
                                  (FelBarrage.cd < MakGcd() * 7 and
                                   (gameState.activeEnemies >= 3 or gameState.activeEnemies > 2) and
                                   (Metamorphosis.cd > 0 or gameState.activeEnemies > 2) or
                                   player:Buff(buffs.fel_barrage)) and
                                  not (gameState.activeEnemies == 1)

            -- Chaos Strike with rending strike and glaive flurry
            ChaosStrike("ar-chaosstrike-new")

            -- Annihilation with rending strike and glaive flurry
            Annihilation("ar-annihilation-new")

            -- Reavers Glaive priority casts
            ReaversGlaive("ar-reaversglaive-new")
            ReaversGlaive("ar-reaversglaive-new2")

            -- Cooldowns
            if shouldBurst() and target.ttd > 1000 then
                if target:Distance() < 4 then
                    if Trinket(1, "Damage") then Trinket1() end
                    if Trinket(2, "Damage") then Trinket2() end
                end
                ar_cooldown()
            end

            -- Opener
            if (EyeBeam.cd == 0 or Metamorphosis.cd == 0 or EssenceBreakk.cd == 0) and player.combatTime < 15 then
                ar_opener()
            end

            -- Sigil of Spite
            SigilOfSpite("ar-sigilofspite-new")

            -- Fel Barrage action lists
            if gameState.felBarrage then
                ar_fel_barrage()
            end

            -- Immolation Aura casts
            ImmolationAura("ar-immolationaura-new")
            ImmolationAura("ar-immolationaura-new2")

            -- Vengeful Retreat casts
            VengefulRetreat("ar-vengefulretreat-new")
            VengefulRetreat("ar-vengefulretreat-new2")

            -- Fel Barrage condition check
            if gameState.felBarrage or (not player:TalentKnown(A.DemonBlades.ID) and player:TalentKnown(A.FelBarrage.ID) and
               (player:Buff(buffs.fel_barrage) or FelBarrage.cd == 0) and not player:Buff(buffs.metamorphosis)) then
                ar_fel_barrage()
            end

            -- Felblade casts
            Felblade("ar-felblade-new")
            Felblade("ar-felblade-new2")

            -- Metamorphosis action list
            if player:Buff(buffs.metamorphosis) then
                ar_meta()
            end

            -- Additional Felblade
            Felblade("ar-felblade-new3")

            -- More Immolation Aura
            ImmolationAura("ar-immolationaura-new3")
            ImmolationAura("ar-immolationaura-new4")
            ImmolationAura("ar-immolationaura-new5")

            -- Eye Beam casts
            EyeBeam("ar-eyebeam-new")
            EyeBeam("ar-eyebeam-new2")

            -- Blade Dance
            BladeDance("ar-bladedance-new")

            -- Chaos Strike priority
            ChaosStrike("ar-chaosstrike-new2")

            -- Sigil of Flame
            SigilOfFlame("ar-sigilofflame-new")

            -- Felblade for fury
            Felblade("ar-felblade-new4")

            -- Glaive Tempest
            GlaiveTempest("ar-glaivetempest-new")

            -- Chaos Strike with essence break
            ChaosStrike("ar-chaosstrike-new3")

            -- Throw Glaive
            ThrowGlaive("ar-throwglaive-new")

            -- Chaos Strike for fury management
            ChaosStrike("ar-chaosstrike-new4")

            -- Felblade for A Fire Inside
            Felblade("ar-felblade-new5")

            -- More Immolation Aura
            ImmolationAura("ar-immolationaura-new6")

            -- Sigil of Flame
            SigilOfFlame("ar-sigilofflame-new2")

            -- Demons Bite
            DemonsBite("ar-demonsbite-new")

            -- Throw Glaive for movement
            ThrowGlaive("ar-throwglaive-new2")

            -- Fel Rush for movement
            FelRush("ar-felrush-new")

            -- Arcane Torrent
            ArcaneTorrent("ar-arcanetorrent-new")
        else
            -- Run Fel-Scarred action list (New SimC APL)

            -- actions.fs=pick_up_fragment,type=all,use_off_gcd=1
            -- Fragment pickup is handled by the framework

            -- actions.fs+=/variable,name=fel_barrage,op=set,value=talent.fel_barrage&(cooldown.fel_barrage.remains<gcd.max*7&(active_enemies>=desired_targets+raid_event.adds.count|raid_event.adds.in<gcd.max*7|raid_event.adds.in>90)&(cooldown.metamorphosis.remains|active_enemies>2)|buff.fel_barrage.up)&!(active_enemies=1&!raid_event.adds.exists)
            gameState.felBarrage = player:TalentKnown(A.FelBarrage.ID) and
                                  (FelBarrage.cd < MakGcd() * 7 and
                                   (gameState.activeEnemies >= 3 or gameState.activeEnemies > 2) and
                                   (Metamorphosis.cd > 0 or gameState.activeEnemies > 2) or
                                   player:Buff(buffs.fel_barrage)) and
                                  not (gameState.activeEnemies == 1)

            -- actions.fs+=/call_action_list,name=fs_cooldown
            if shouldBurst() and target.ttd > 1000 then
                if target:Distance() < 4 then
                    if Trinket(1, "Damage") then Trinket1() end
                    if Trinket(2, "Damage") then Trinket2() end
                end
                fs_cooldown()
            end

            -- actions.fs+=/run_action_list,name=fs_opener,if=(cooldown.eye_beam.up|cooldown.metamorphosis.up|cooldown.essence_break.up|buff.demonsurge.stack<3+talent.student_of_suffering+talent.a_fire_inside)&time<15&raid_event.adds.in>40
            if (EyeBeam.cd == 0 or Metamorphosis.cd == 0 or EssenceBreakk.cd == 0 or
                player:HasBuffCount(buffs.demonsurge) < 3 + num(player:TalentKnown(A.StudentOfSuffering.ID)) + num(player:TalentKnown(A.AFireInside.ID))) and
               player.combatTime < 15 then
                fs_opener()
            end

            -- actions.fs+=/run_action_list,name=fs_fel_barrage,if=variable.fel_barrage&raid_event.adds.up
            if gameState.felBarrage then
                fs_fel_barrage()
            end

            -- New FS APL abilities
            ImmolationAura("fs-immolationaura-new")
            ImmolationAura("fs-immolationaura-new2")
            Felblade("fs-felblade-new")
            FelRush("fs-felrush-new")

            -- actions.fs+=/run_action_list,name=fs_meta,if=buff.metamorphosis.up
            if player:Buff(buffs.metamorphosis) then
                fs_meta()
            end

            VengefulRetreat("fs-vengefulretreat-new")

            -- actions.fs+=/run_action_list,name=fs_fel_barrage,if=variable.fel_barrage|!talent.demon_blades&talent.fel_barrage&(buff.fel_barrage.up|cooldown.fel_barrage.up)&buff.metamorphosis.down
            if gameState.felBarrage or (not player:TalentKnown(A.DemonBlades.ID) and player:TalentKnown(A.FelBarrage.ID) and
               (player:Buff(buffs.fel_barrage) or FelBarrage.cd == 0) and not player:Buff(buffs.metamorphosis)) then
                fs_fel_barrage()
            end

            -- New FS APL abilities (continued)
            ImmolationAura("fs-immolationaura-tier34")
            ImmolationAura("fs-immolationaura-afire")
            SigilOfFlame("fs-sigilofflame-student")
            EyeBeam("fs-eyebeam-new")
            Felblade("fs-felblade-tier34")
            FelRush("fs-felrush-tier34")
            EssenceBreakk("fs-essencebreak-new")
            BladeDance("fs-bladedance-new")
            ChaosStrike("fs-chaosstrike-tier34")
            BladeDance("fs-bladedance-new2")
            GlaiveTempest("fs-glaivetempest-new")
            SigilOfFlame("fs-sigilofflame-new")
            ChaosStrike("fs-chaosstrike-new")
            Felblade("fs-felblade-fury")
            ChaosStrike("fs-chaosstrike-fury")
            ImmolationAura("fs-immolationaura-general")
            Felblade("fs-felblade-range")
            SigilOfFlame("fs-sigilofflame-general")
            DemonsBite("fs-demonsbite-new")
            ThrowGlaive("fs-throwglaive-new")
            FelRush("fs-felrush-movement")
            ArcaneTorrent("fs-arcanetorrent-new")

            ImmolationAura("fs-immolationaura3")
            ImmolationAura("fs-immolationaura4")
            ImmolationAura("fs-immolationaura5")
            SigilOfFlame("fs-sigilofflame")
            EyeBeam("fs-eyebeam")
            BladeDance("fs-bladedance")
            GlaiveTempest("fs-glaivetempest")
            SigilOfFlame("fs-sigilofflame2")
            ChaosStrike("fs-chaosstrike")
            ImmolationAura("fs-immolationaura6")
            Felblade("fs-felblade2")
            ChaosStrike("fs-chaosstrike2")
            ImmolationAura("fs-immolationaura7")
            Felblade("fs-felblade3")
            SigilOfFlame("fs-sigilofflame3")
            ThrowGlaive("fs-throwglaive")
            FelRush("fs-felrush2")
            ArcaneTorrent("fs-arcaneTorrent")
        end
    end

	return FrameworkEnd()
end

function partyDanger()
    return ConstCell:GetOrSet("partyDanger", function()
        local partyUnits = {party1, party2, player}

        for _, unit in ipairs(partyUnits) do
            if unit.exists and unit.hp > 0 and unit.hp < 50 and not unit:BuffFrom(MakLists.Defensive) then
                return true
            end
        end

        return false
    end)
end

local function enemiesAlive()
    return ConstCell:GetOrSet("enemiesAlive", function()
        local aliveEnemies = 0

        local arenaUnits = {arena1, arena2, arena3}

        for _, unit in ipairs(arenaUnits) do
            if unit.exists and unit.hp > 0 then
                aliveEnemies = aliveEnemies + 1
                if unit.isHealer then
                    healerAlive = true
                end
            end
        end

        return aliveEnemies
    end)
end

ConsumeMagic:Callback("arena", function(spell, enemy)
    if not enemy:BuffFrom(MakLists.purgeableBuffs) then return end

    return Debounce("CM", 1000, 2500, spell, enemy)
end)

-- Default Disrupt callback for PvE
Disrupt:Callback("default", function(spell)
    if not Disrupt:InRange(target) then return end
    if not target:ShouldInterrupt(spell) then return end

    return spell:Cast(target)
end)

Disrupt:Callback("arena", function(spell, enemy)
    if not player:TalentKnown(Disrupt.id) then return end
    if not enemy.pvpKick then return end

    return spell:Cast(enemy)
end)

Imprison:Callback("arena", function(spell, enemy)
    if enemy.incapacitateDr < 0.5 then return end
    if enemy.isTarget then return end
    if enemy.cc then return end
    if not target.exists then return end

    if gameState.healerAlive and enemy.isHealer and target.hp <= 60 then
        if spell.cd < 300 then
            return spell:Cast(enemy)
        end
    elseif enemiesAlive() <= 2 and enemy.hp > 70 and target.hp <= 60 then
        if spell.cd < 300 then
            return spell:Cast(enemy)
        end
    end

    if partyDanger() then
        if enemy.cds and enemy.hp > 60 then
            if spell.cd < 300 then
                return spell:Cast(enemy)
            end
        end
    end
end)

ImprisonDetainment:Callback("arena", function(spell, enemy)
    if enemy.incapacitateDr < 0.5 then return end
    if enemy.isTarget then return end
    if enemy.cc then return end
    if not target.exists then return end

    if gameState.healerAlive and enemy.isHealer and target.hp <= 60 then
        if spell.cd < 300 then
            return spell:Cast(enemy)
        end
    elseif enemiesAlive() <= 2 and enemy.hp > 70 and target.hp <= 60 then
        if spell.cd < 300 then
            return spell:Cast(enemy)
        end
    end

    if partyDanger() then
        if enemy.cds and enemy.hp > 60 then
            if spell.cd < 300 then
                return spell:Cast(enemy)
            end
        end
    end
end)

FelEruption:Callback("arena", function(spell, enemy)
    if enemy.stunDr < 0.5 then return end
    if enemy.cc then return end
    if not target.exists then return end

    if enemy.isHealer and target.hp <= 40 then
        if spell.cd < 300 then
            return spell:Cast(enemy)
        end
    end

    if partyDanger() then
        if enemy.cds then
            if spell.cd < 300 then
                return spell:Cast(enemy)
            end
        end
    end
end)

local enemyRotation = function(enemy)
	if not enemy.exists then return end

    ConsumeMagic("arena", enemy)
    Disrupt("arena", enemy)
    Imprison("arena", enemy)
    ImprisonDetainment("arena", enemy)
    FelEruption("arena", enemy)
end


local partyRotation = function(friendly)
    if not friendly.exists then return end

end

A[6] = function(icon)
	RegisterIcon(icon)
    if A.GetToggle(2, "autotarget") and targetForInterrupt(interrupts) then return TabTarget() end
    if autoTarget() then return TabTarget() end
	enemyRotation(arena1)
	partyRotation(party1)

	return FrameworkEnd()
end

A[7] = function(icon)
	RegisterIcon(icon)
	enemyRotation(arena2)
	partyRotation(party2)

	return FrameworkEnd()
end

A[8] = function(icon)
	RegisterIcon(icon)
	enemyRotation(arena3)
	partyRotation(party3)

	return FrameworkEnd()
end

A[9] = function(icon)
	RegisterIcon(icon)
	partyRotation(party4)

	return FrameworkEnd()
end

A[10] = function(icon)
	RegisterIcon(icon)
	partyRotation(player)

	return FrameworkEnd()
end
