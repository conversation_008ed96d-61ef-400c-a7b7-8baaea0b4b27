if not <PERSON><PERSON>luValidCheck() then return true end
if not <PERSON><PERSON><PERSON>_magic_number == 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 261 then return end -- Edit here

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local TableToLocal     = MakuluFramework.tableToLocal
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local cacheContext     = MakuluFramework.Cache
local Aware            = MakuluFramework.Aware
local ConstCell        = cacheContext:getConstCacheCell()

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits

local BossMods         = Action.BossMods

local _G, setmetatable = _G, setmetatable


local ActionID = {
    CheapShot = { ID = 1833, MAKULU_INFO = { damageType = "physical", cc = true } },
    
    -- Potions, healthstones, racials etc auto added
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
	local result = {}
	for k, v in pairs(A) do
		result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
	end
	return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_ROGUE_SUBTLETY] = A -- Edit here

TableToLocal(M, getfenv(1))
Aware:enable()


local player = ConstUnit.player
local target = ConstUnit.target
local focus = ConstUnit.focus
local mouseover = ConstUnit.mouseover
local pet = ConstUnit.pet
local arena1 = ConstUnit.arena1
local arena2 = ConstUnit.arena2
local arena3 = ConstUnit.arena3
local party1 = ConstUnit.party1
local party2 = ConstUnit.party2
local party3 = ConstUnit.party3
local party4 = ConstUnit.party4
local healer = ConstUnit.healer
local enemyHealer = ConstUnit.enemyHealer

local gameState = {}

local buffs = {
    arena_preparation = 32727,
    power_infusion = 10060,
}

local debuffs = {
    exhaustion = 57723,
}

local interrupts = { -- Edit here
    --{ spell = Kick },
    --{ spell = CheapShot, isCC = true },
}

local function num(val)
    if val then return 1 else return 0 end
end

local function shouldBurst()
    if Action.BurstIsON("target") then
        return true
    end
    return false
end

local function EnemiesInSpellRange(makulu_spell)
    return ConstCell:GetOrSet("enemiesIn" .. makulu_spell.name, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and not enemy.isPet and not enemy.isFriendly then
                total = total + 1
            end
        end
        return total
    end)
end

local function TotemsInSpellRange(makulu_spell)
    return ConstCell:GetOrSet("totemsIn" .. makulu_spell.name, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and enemy:IsTotem() and not enemy.isFriendly then
                return true
            end
        end 
        return false
    end)
end

local function AutoTarget()
    if not player.inCombat then return false end

    for _, spellInfo in ipairs(interrupts) do
        if target:ShouldInterrupt(spellInfo.spell, spellInfo.isCC, spellInfo.aoe, spellInfo.distance) then
            return false
        end
    end

    if TotemsInSpellRange(MELEE_ABILITY_HERE) and not target:IsTotem() then -- Edit here
        return true
    end

    --if A.GetToggle(2, "autoRupture") and gs.ruptureCount < gs.backstabEnemies and target:DebuffRemains(debuffs.rupture, true) > 6000 and not gs.skipRupture then
    --    return true
    --end

    if MELEE_ABILITY_HERE:InRange(target) and target.exists then return false end  -- Edit here

    if EnemiesInSpellRange(MELEE_ABILITY_HERE) > 0 then -- Edit here
        return true
    end
end

local function hasIncomingDamage()
    return incBigDmgIn() < 2000 or incModDmgIn() < 2000
end

local function defensiveActive()
    return player:BuffFrom(MakLists.Defensive) or UnitGetTotalAbsorbs("player") >= player.maxHealth * 0.15
end

local function shouldDefensive()
    local incomingDamage = hasIncomingDamage()

    return incomingDamage and not defensiveActive()
end

local function myCast()
    local casting = player.castOrChannelInfo
    local currentCast = casting and casting.spellId

    return currentCast
end

local function updateGameState()
    gameState.TWW1has2P = player:Has2Set()
    gameState.TWW1has4P = player:Has4Set()
end

A[3] = function(icon)
	FrameworkStart(icon)
    updateGameState()

    makInterrupt(interrupts)

    if player.inCombat and shouldDefensive() then
        if MakTrinket(13, "Defensive") then
            Trinket1()
        end
        if MakTrinket(14, "Defensive") then
            Trinket2()
        end

        if MakuluFramework.CanUseHealthStone() then
            HealthStone()
        end

        if MakuluFramework.CanUseHealthPotion() then
            HealthPotion()
        end
    end

    if target.exists and target.canAttack then
        -- pew pew

        if MakTrinket(13, "Damage") then
            Trinket1()
        end
        if MakTrinket(14, "Damage") then
            Trinket2()
        end
    end

	return FrameworkEnd()
end



local enemyRotation = function(enemy)
	if not enemy.exists then return end
    if enemy.hp <= 0 then return end
    if player.mounted then return end
    if player.stealthed then return end

end


local partyRotation = function(friendly)
    if not friendly.exists then return end
    if friendly.hp <= 0 then return end
    if player.mounted then return end
    if player.stealthed then return end
    if IsResting() then return end

end

A[6] = function(icon)
	RegisterIcon(icon)
    if targetForInterrupt(interrupts) then return TabTarget() end
    if AutoTarget() then return TabTarget() end
	enemyRotation(arena1)
	partyRotation(party1)

	return FrameworkEnd()
end

A[7] = function(icon)
	RegisterIcon(icon)
	enemyRotation(arena2)
	partyRotation(party2)

	return FrameworkEnd()
end

A[8] = function(icon)
	RegisterIcon(icon)
	enemyRotation(arena3)
	partyRotation(party3)

	return FrameworkEnd()
end

A[9] = function(icon)
	RegisterIcon(icon)
	partyRotation(party4)

	return FrameworkEnd()
end

A[10] = function(icon)
	RegisterIcon(icon)
	partyRotation(player)

	return FrameworkEnd()
end