local Action = _G.Action

local A                = Action

local CONST                                                              = Action.Const

local ACTION_CONST_DEATHKNIGHT_BLOOD                                = CONST.DEATHKNIGHT_BLOOD
local ACTION_CONST_DEATHKNIGHT_FROST                                = CONST.DEATHKNIGHT_FROST
local ACTION_CONST_DEATHKNIGHT_UNHOLY                               = CONST.DEATHKNIGHT_UNHOLY

LPH_ENCNUM = function(val) return val end

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "Makulu MoP v1.0.0 (7/29/2025)",
    -- Class settings
    [2] = {
        {
            {
                E = "Header",
                L = {
                    ANY = " ====== Makulu - MoP Death Knight ====== ",
                },
            },
        },
        {
            { -- AOE
                E = "Checkbox", 
                DB = "AoE",
                DBV = true,
                L = { 
                    enUS = "Use AoE", 
                    ruRU = "Использовать AoE", 
                    frFR = "Utiliser l'AoE",
                }, 
                TT = { 
                    enUS = "Enable multiunits actions", 
                    ruRU = "Включает действия для нескольких целей", 
                    frFR = "Activer les actions multi-unités",
                }, 
                M = {},
            },
            { -- Auto Disease Spread
                E = "Checkbox", 
                DB = "autoDisease",
                DBV = false,
                L = { 
                    ANY = "Auto Disease Spread", 
                }, 
                TT = { 
                    ANY = "Automatically swap targets to spread diseases when appropriate.", 
                }, 
                M = {},
            },
            { -- Rune Management
                E = "Checkbox", 
                DB = "runeManagement",
                DBV = true,
                L = { 
                    ANY = "Smart Rune Management", 
                }, 
                TT = { 
                    ANY = "Optimize rune usage and prevent rune capping."
                }, 
                M = {},
            },   
        },
        { -- Spacer
            
            {
                E = "LayoutSpace",
            },
        },
        { -- Potions
            { -- useDamagePotion
                E = "Checkbox", 
                DB = "damagePotion",
                DBV = true,
                L = { 
                    ANY = "Damage Potion"
                }, 
                TT = { 
                    ANY = "Use Damage Potion", 
                }, 
                M = {},
            },
            { -- potionBossOnly
                E = "Checkbox", 
                DB = "potionLustOnly",
                DBV = true,
                L = { 
                    ANY = "Damage Potion Bloodlust/TimeWarp Only", 
                }, 
                TT = { 
                    ANY = "Only use Damage Potion when any kind of Bloodlust/Warp active."
                }, 
                M = {},
            },
        },
        {
            { -- potionExhausted
                E = "Checkbox", 
                DB = "potionExhausted",
                DBV = true,
                L = { 
                    ANY = "Damage Potion With Exhaustion", 
                }, 
                TT = { 
                    ANY = "Use Damage Potion while Exhausted (can't use Bloodlust)."
                }, 
                M = {},
            },
            { -- potionExhaustedSlider
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,   
                Precision = 1,                         
                DB = "potionExhaustedSlider",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Exhaustion Time Remaining",
                },
                TT = { 
                    ANY = "Time in minutes left on the Exhaustion Debuff to consider using Damage Potion.", 
                },                     
                M = {},
            },
        },
        { -- LAYOUT SPACE   
            {
                E = "LayoutSpace",                                                                         
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Cooldowns",
                },
            },
        },
        {
            {
                E = "Dropdown",                                                         
                H = 20,
                OT = {
                    { text = "Army of the Dead", value = 1 }, 
                    { text = "Summon Gargoyle", value = 2 },     
                    { text = "Vampiric Blood", value = 3 },
                    { text = "Bone Shield", value = 4 },
                    { text = "Pillar of Frost", value = 5 },
                    { text = "Unholy Frenzy", value = 6 },   
                },
                MULT = true,
                DB = "cooldownSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                    [5] = true,
                    [6] = true,
                },  
                L = { 
                    ANY = "Cooldown Abilities", 
                }, 
                TT = { 
                    ANY = "Select what abilities you want the rotation to obey the burst toggle.\nIf a spell is unchecked, it will be used even when burst is turned off!", 
                }, 
                M = {},                                    
            },  
        }, 
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { 
            {-- Burst Sensitivity
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "burstSens",
                DBV = 18,
                ONOFF = false,
                L = { 
                    ANY = "Burst Mode Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to use cooldowns. A lower number means cooldowns used closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },  
            {-- Disease Refresh TTD
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "diseaseRefresh",
                DBV = 12,
                ONOFF = false,
                L = { 
                    ANY = "Disease Refresh Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to refresh diseases. A lower number means diseases refreshed closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- DEATH KNIGHT HEADER
            {
                E = "Header",
                L = {
                    ANY = "INTERRUPTS",
                },
            },
        },
        {    
            { -- Automatic Interrupt
                E = "Checkbox", 
                DB = "AutoInterrupt",
                DBV = true,
                L = { 
                    ANY = "Switch Targets Interrupt",
                }, 
                TT = { 
                    ANY = "Automatically switches targets to interrupt.",
                }, 
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- DEATH KNIGHT HEADER
            {
                E = "Header",
                L = {
                    ANY = "DEFENSIVES",
                },
            },
        },
        {
            { -- Icebound Fortitude HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "IceboundFortitudeHP",
                DBV = 50,
                ONOFF = false,
                L = { 
                    ANY = "Icebound Fortitude HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Icebound Fortitude on yourself.", 
                },                     
                M = {},
            },    
            { -- Anti-Magic Shell HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "AntiMagicShellHP",
                DBV = 70,
                ONOFF = false,
                L = { 
                    ANY = "Anti-Magic Shell HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Anti-Magic Shell on yourself.", 
                },                     
                M = {},
            },    
        },
        {
            {-- Death Pact HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "deathPactHP",
                DBV = 30,
                ONOFF = false,
                L = { 
                    ANY = "Death Pact HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Death Pact for emergency healing.", 
                },                     
                M = {},
            },    
        },
        {
            {
                E = "Dropdown",                                                         
                H = 20,
                OT = {
                    { text = "Icebound Fortitude", value = 1 }, 
                    { text = "Anti-Magic Shell", value = 2 },  
                    { text = "Death Pact", value = 3 },
                    { text = "Vampiric Blood", value = 4 },    
                },
                MULT = true,
                DB = "defensiveSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },  
                L = { 
                    ANY = "Defensive Reactions", 
                }, 
                TT = { 
                    ANY = "Select what spells to be used when reacting to incoming damage in dungeons.", 
                }, 
                M = {},                                    
            },
        },
        { -- Spacer

            {
                E = "LayoutSpace",
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Debug/Aware Options",
                },
            },
        },
        {
            { -- Debug
                E = "Checkbox",
                DB = "makDebug",
                DBV = false,
                L = {
                    ANY = "Enable debug options",
                },
                TT = {
                    ANY = "Show a box with various debug data.\nIt takes a couple of seconds to get rid of the box when you disable this.",
                },
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Rune Reminder", value = 1 },
                    { text = "Army of the Dead Ready", value = 2 },
                    { text = "Gargoyle Ready", value = 3 },
                    { text = "Disease Alert", value = 4 },
                },
                MULT = true,
                DB = "makAware",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Aware Text Alert Reminders",
                },
                TT = {
                    ANY = "Select what text alert reminders you would like.\nThese will appear in the center of your screen.",
                },
                M = {},
            },
        },
    },
}
