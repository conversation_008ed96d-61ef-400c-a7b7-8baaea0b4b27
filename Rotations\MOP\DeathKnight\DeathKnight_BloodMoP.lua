-- APL UPDATE MoP Blood Death Knight
-- Mists of Pandaria Blood Death Knight Rotation

-- Check if Ma<PERSON>luValidCheck exists before calling it
if Ma<PERSON>luValidCheck and not <PERSON><PERSON>luValidCheck() then return true end
if Ma<PERSON><PERSON>_magic_number and Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Blood spec (talent tree 1 for Death Knight in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 1 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Blood Death Knight Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- Core Blood Abilities
    DeathStrike = { ID = 49998, MAKULU_INFO = { damageType = "physical", heal = true } },
    HeartStrike = { ID = 55050, MAKULU_INFO = { damageType = "physical" } },
    BloodBoil = { ID = 48721, MAKULU_INFO = { damageType = "shadow" } },
    DeathCoil = { ID = 47541, MAKULU_INFO = { damageType = "shadow", heal = true } },
    PlagueStrike = { ID = 45462, MAKULU_INFO = { damageType = "physical" } },
    
    -- Defensive Abilities
    VampiricBlood = { ID = 55233, MAKULU_INFO = { targeted = false } },
    BoneShield = { ID = 49222, MAKULU_INFO = { targeted = false } },
    IceboundFortitude = { ID = 48792, MAKULU_INFO = { targeted = false } },
    AntiMagicShell = { ID = 48707, MAKULU_INFO = { targeted = false } },
    RuneTap = { ID = 48982, MAKULU_INFO = { targeted = false } },
    
    -- Utility Abilities
    DeathGrip = { ID = 49576, MAKULU_INFO = { targeted = true } },
    DarkCommand = { ID = 56222, MAKULU_INFO = { targeted = true } },
    DeathAndDecay = { ID = 43265, MAKULU_INFO = { damageType = "shadow", targeted = false } },
    ChainsOfIce = { ID = 45524, MAKULU_INFO = { targeted = true } },
    MindFreeze = { ID = 47528, MAKULU_INFO = { targeted = true } },
    Strangulate = { ID = 47476, MAKULU_INFO = { targeted = true } },
    
    -- Presence
    BloodPresence = { ID = 48263, MAKULU_INFO = { targeted = false } },
    
    -- Diseases
    BloodPlague = { ID = 55078, MAKULU_INFO = { damageType = "disease" } },
    FrostFever = { ID = 55095, MAKULU_INFO = { damageType = "disease" } },
    
    -- MoP Talents
    DeathPact = { ID = 48743, MAKULU_INFO = { heal = true, targeted = false } },
    Necrosis = { ID = 51460, MAKULU_INFO = { damageType = "shadow" } },
    DeathSiphon = { ID = 108196, MAKULU_INFO = { damageType = "shadow", heal = true } },
    
    -- Cooldowns
    DancingRuneWeapon = { ID = 49028, MAKULU_INFO = { targeted = false } },
    Lichborne = { ID = 49039, MAKULU_INFO = { targeted = false } },
    EmpowerRuneWeapon = { ID = 47568, MAKULU_INFO = { targeted = false } },
    
    -- Pet
    RaiseDead = { ID = 46584, MAKULU_INFO = { targeted = false } },

    -- Potions and consumables
    TemperedPotion1 = { Type = "Potion", ID = 171263, QueueForbidden = true },
    TemperedPotion2 = { Type = "Potion", ID = 171264, QueueForbidden = true },
    TemperedPotion3 = { Type = "Potion", ID = 171265, QueueForbidden = true },
    PotionofUnwaveringFocus1 = { Type = "Potion", ID = 171266, QueueForbidden = true },
    PotionofUnwaveringFocus2 = { Type = "Potion", ID = 171267, QueueForbidden = true },
    PotionofUnwaveringFocus3 = { Type = "Potion", ID = 171268, QueueForbidden = true },

    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 47528, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 47476, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local function buildMakuluFrameworkSpells(ActionList)
    local result = {}
    for k, v in pairs(ActionList) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end

-- Build Makulu framework spells and make them available directly
TableToLocal(buildMakuluFrameworkSpells(A), getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")
local pet = MakUnit:new("pet")

-- MoP Blood Death Knight Buffs
local buffs = {
    vampiricBlood = 55233,
    boneShield = 49222,
    iceboundFortitude = 48792,
    antiMagicShell = 48707,
    runeTap = 48982,
    bloodPresence = 48263,
    deathPact = 48743,
    dancingRuneWeapon = 49028,
    lichborne = 49039,
    empowerRuneWeapon = 47568,
    willOfTheNecropolis = 52284,
    scent = 50421,
    crimsonScourge = 81141,
    bloodShield = 77535,
    scarletFever = 81132,
    deathSiphon = 108196,
}

-- MoP Blood Death Knight Debuffs
local debuffs = {
    bloodPlague = 55078,
    frostFever = 55095,
    chainsOfIce = 45524,
    strangulate = 47476,
    necrosis = 51460,
    scarletFever = 81132,
    weakenedBlows = 115798,
}

-- Game state tracking (enhanced with tank-specific functionality)
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    runicPower = 0,
    bloodRunes = 0,
    frostRunes = 0,
    unholyRunes = 0,
    deathRunes = 0,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
    shouldBurst = false,
    fightRemains = 999,
    boneShieldStacks = 0,
    shouldAoE = false,
    shouldCleave = false,
    diseaseCount = 0,
    imCasting = nil,
    needsTaunt = false,
    partyHealth = 100,
    tankingTarget = false,
    bloodShieldAmount = 0,
}

local function updategs()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.runicPower = player.runicPower or 0
    gameState.bloodRunes = player:GetRuneCount(1) or 0
    gameState.frostRunes = player:GetRuneCount(3) or 0
    gameState.unholyRunes = player:GetRuneCount(4) or 0
    gameState.deathRunes = player:GetRuneCount(5) or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.channeling = player:IsCasting() or player:IsChanneling()
    gameState.shouldBurst = A.GetToggle(2, "BurstMode")

    -- Enhanced tracking
    gameState.fightRemains = target.exists and target.timeToDie or 999
    gameState.shouldAoE = gameState.activeEnemies > 2
    gameState.shouldCleave = gameState.activeEnemies > 1
    gameState.boneShieldStacks = player:BuffStacks(buffs.boneShield) or 0
    gameState.imCasting = player:IsCasting() and player:GetCastingSpell() or nil

    -- Disease tracking
    local diseases = 0
    if target.exists then
        if target:HasDebuff(debuffs.bloodPlague) then diseases = diseases + 1 end
        if target:HasDebuff(debuffs.frostFever) then diseases = diseases + 1 end
    end
    gameState.diseaseCount = diseases

    -- Tanking status
    gameState.tankingTarget = target.exists and target:IsTargeting(player)
    gameState.needsTaunt = target.exists and not target:IsTargeting(player) and target.canAttack

    -- Blood Shield tracking
    gameState.bloodShieldAmount = player:BuffAmount(buffs.bloodShield) or 0

    -- Party health tracking
    local totalHp = player.hp
    local members = 1
    for i = 1, 4 do
        local member = MakUnit:new("party" .. i)
        if member.exists then
            totalHp = totalHp + member.hp
            members = members + 1
        end
    end
    gameState.partyHealth = totalHp / members

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Alias for compatibility
local updateGameState = updategs
local gs = gameState

-- Utility functions (enhanced for tanking)
local function shouldBurst()
    return gameState.shouldBurst
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsDefensive()
    return player.hp < 60 or gameState.bloodShieldAmount < player.maxHealth * 0.3
end

local function needsBoneShield()
    return gameState.boneShieldStacks < 3
end

local function needsDiseases()
    return gameState.diseaseCount < 2
end

local function hasRunes(blood, frost, unholy, death)
    return (gameState.bloodRunes >= (blood or 0)) and
           (gameState.frostRunes >= (frost or 0)) and
           (gameState.unholyRunes >= (unholy or 0)) and
           (gameState.deathRunes >= (death or 0))
end

local function isSpellInFlight(spell, range)
    return spell:IsSpellInFlight() or false
end

local function makInterrupt(interrupts)
    if not interrupts then return end
    for _, interrupt in pairs(interrupts) do
        if interrupt and interrupt:IsReady() then
            interrupt()
        end
    end
end

-- PvP utility functions
local function shouldInterrupt(enemy)
    if not enemy or not enemy.exists then return false end
    return enemy:IsCasting() and enemy:IsInterruptible()
end

local function shouldCC(enemy)
    if not enemy or not enemy.exists then return false end
    return not enemy:HasDebuff(debuffs.strangulate) and enemy.hp > 30
end

-- Core tanking callbacks
DeathStrike:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if player.hp > 80 and gameState.bloodShieldAmount > player.maxHealth * 0.5 then return end
    if not hasRunes(0, 1, 1, 0) and gameState.runicPower < 40 then return end

    return spell:Cast(target)
end)

HeartStrike:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if not hasRunes(1, 0, 0, 0) then return end

    return spell:Cast(target)
end)

BloodBoil:Callback(function(spell)
    if not shouldAoE() then return end
    if not target.exists or not target.canAttack then return end
    if not hasRunes(1, 0, 0, 0) then return end

    return spell:Cast(target)
end)

PlagueStrike:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDebuff(debuffs.bloodPlague) then return end
    if not hasRunes(0, 0, 1, 0) then return end

    return spell:Cast(target)
end)

DeathCoil:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gameState.runicPower < 40 then return end
    if player.hp < 60 then
        -- Heal self
        return spell:Cast(player)
    else
        -- Damage enemy
        return spell:Cast(target)
    end
end)

-- Defensive abilities
VampiricBlood:Callback(function(spell)
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

BoneShield:Callback(function(spell)
    if needsBoneShield() then
        return spell:Cast(player)
    end
end)

IceboundFortitude:Callback(function(spell)
    if player.hp > 40 then return end

    return spell:Cast(player)
end)

AntiMagicShell:Callback(function(spell)
    if player.hp > 60 then return end
    -- Should also check for incoming magic damage

    return spell:Cast(player)
end)

RuneTap:Callback(function(spell)
    if player.hp > 70 then return end
    if not hasRunes(1, 0, 0, 0) then return end

    return spell:Cast(player)
end)

-- Utility abilities
DeathGrip:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target.distance < 8 then return end

    return spell:Cast(target)
end)

DarkCommand:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gameState.tankingTarget then return end

    return spell:Cast(target)
end)

MindFreeze:Callback(function(spell)
    if not target.exists or not target:IsCasting() then return end
    if not target:IsInterruptible() then return end

    return spell:Cast(target)
end)

Strangulate:Callback(function(spell)
    if not target.exists or not target:IsCasting() then return end
    if not target:IsInterruptible() then return end
    if target:HasDebuff(debuffs.strangulate) then return end

    return spell:Cast(target)
end)

ChainsOfIce:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDebuff(debuffs.chainsOfIce) then return end

    return spell:Cast(target)
end)

DeathAndDecay:Callback(function(spell)
    if not shouldAoE() then return end
    if not hasRunes(0, 1, 1, 0) then return end

    return spell:Cast(player)
end)

-- Enhanced PvP callbacks
MindFreeze:Callback("arena", function(spell, enemy)
    if not enemy.pvpKick then return end

    return spell:Cast(enemy)
end)

Strangulate:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDebuff(debuffs.strangulate) then return end
    if enemy.hp < 30 then return end

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Strangulate - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Strangulate - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

DeathGrip:Callback("arena", function(spell, enemy)
    if enemy.distance < 8 then return end
    if enemy.distance > 30 then return end

    -- Use for positioning or peeling
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty then
        Aware:displayMessage("Death Grip - Peel", "Red", 1)
        return spell:Cast(enemy)
    end
end)

-- Enhanced rotation functions
local function st()
    -- Single target tanking rotation
    updategs()

    -- Maintain threat
    if gs.needsTaunt then
        DarkCommand()
    end

    -- Maintain bone shield
    if needsBoneShield() then
        BoneShield()
    end

    -- Apply diseases
    if needsDiseases() then
        PlagueStrike()
    end

    -- Death Strike for healing and blood shield
    if player.hp < 80 or gs.bloodShieldAmount < player.maxHealth * 0.3 then
        DeathStrike()
    end

    -- Generate threat
    HeartStrike()

    -- Spend runic power
    if gs.runicPower > 80 then
        DeathCoil()
    end

    -- Defensive priorities
    if needsDefensive() then
        VampiricBlood()
        RuneTap()
    end
end

local function aoe()
    -- AoE tanking rotation
    updategs()

    -- Maintain bone shield
    if needsBoneShield() then
        BoneShield()
    end

    -- AoE threat generation
    BloodBoil()
    DeathAndDecay()

    -- Apply diseases to primary target
    if needsDiseases() then
        PlagueStrike()
    end

    -- Death Strike for survival
    if player.hp < 70 then
        DeathStrike()
    end

    -- Spend runic power
    if gs.runicPower > 80 then
        DeathCoil()
    end

    -- Defensive priorities
    if needsDefensive() then
        VampiricBlood()
        RuneTap()
    end
end

local function cleave()
    -- Cleave tanking (2-3 enemies)
    updategs()

    -- Maintain bone shield
    if needsBoneShield() then
        BoneShield()
    end

    -- Generate threat
    HeartStrike()
    BloodBoil()

    -- Apply diseases
    if needsDiseases() then
        PlagueStrike()
    end

    -- Death Strike for survival
    if player.hp < 80 then
        DeathStrike()
    end

    -- Spend runic power
    if gs.runicPower > 80 then
        DeathCoil()
    end

    -- Defensive priorities
    if needsDefensive() then
        VampiricBlood()
        RuneTap()
    end
end

local function ogcd()
    -- Off-global cooldown abilities
    if shouldBurst() then
        DancingRuneWeapon()
        EmpowerRuneWeapon()
    end

    -- Emergency defensives
    if player.hp < 40 then
        IceboundFortitude()
        VampiricBlood()
        AntiMagicShell()
    end

    -- Racial abilities
    racials()
end

local function eof()
    -- End of fight logic
    if gs.fightRemains < 30000 then
        -- Burn runic power
        if gs.runicPower > 40 then
            DeathCoil()
        end
        -- Use remaining runes
        HeartStrike()
        BloodBoil()
    end
end

local function pvpenis()
    -- PvP specific logic placeholder
    -- This would contain PvP-specific rotation logic
end

-- Alias for compatibility
local function singleTargetRotation()
    st()
end

-- AoE rotation
local function aoeRotation()
    updateGameState()
    aoe()
end

-- PvP specific rotation
local function pvpRotation()
    updateGameState()

    -- Interrupt priority
    MindFreeze()
    Strangulate()

    -- CC and positioning
    DeathGrip()
    ChainsOfIce()

    -- Tanking priorities
    if gs.needsTaunt then
        DarkCommand()
    end

    -- Maintain bone shield
    if needsBoneShield() then
        BoneShield()
    end

    -- Apply diseases
    if needsDiseases() then
        PlagueStrike()
    end

    -- Death Strike for survival
    if player.hp < 70 then
        DeathStrike()
    end

    -- Generate threat
    HeartStrike()

    -- Defensive priorities
    if needsDefensive() then
        VampiricBlood()
        IceboundFortitude()
    end
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Pre-position and prepare for adds
    if gs.timeToAdds < 8000 and gs.timeToAdds > 0 then
        -- Build runic power for incoming adds
        if gs.runicPower < 80 then
            HeartStrike()
        end

        -- Prepare cooldowns
        if gs.timeToAdds < 3000 then
            BoneShield()
            VampiricBlood()
        end
    end

    -- During adds phase
    if gs.activeEnemies >= 3 then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives always take priority
    if player.hp < 20 then
        IceboundFortitude()
        VampiricBlood()
        return
    end

    -- Bone Shield maintenance
    if needsBoneShield() then
        BoneShield()
    end

    -- Taunt priority
    if gs.needsTaunt then
        DarkCommand()
    end

    -- Pet management
    if not pet.exists then
        RaiseDead()
    end

    -- Cooldowns during burst or emergency
    if shouldBurst() or player.hp < 50 then
        DancingRuneWeapon()
        EmpowerRuneWeapon()
        VampiricBlood()
    end

    -- TimeToAdds logic
    if gs.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gs.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- Enhanced A[3] function with new functionality
A[3] = function(icon)
    -- Safety check for framework initialization
    if not FrameworkStart then return end

    FrameworkStart(icon)
    updategs()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Runic Power: ", gs.runicPower)
        MakPrint(2, "Blood Runes: ", gs.bloodRunes)
        MakPrint(3, "Frost Runes: ", gs.frostRunes)
        MakPrint(4, "Unholy Runes: ", gs.unholyRunes)
        MakPrint(5, "Death Runes: ", gs.deathRunes)
        MakPrint(6, "Bone Shield Stacks: ", gs.boneShieldStacks)
        MakPrint(7, "Disease Count: ", gs.diseaseCount)
        MakPrint(8, "Blood Shield Amount: ", gs.bloodShieldAmount)
        MakPrint(9, "Tanking Target: ", gs.tankingTarget)
        MakPrint(10, "Mind Freeze Learned: ", MindFreeze:IsKnown())
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then -- Dancing Rune Weapon ready
        if DancingRuneWeapon:IsReady() and shouldBurst() and player.inCombat then
            Aware:displayMessage("DANCING RUNE WEAPON READY", "Purple", 1)
        end
    end

    -- Interrupt handling
    local interrupts = {MindFreeze, Strangulate}
    makInterrupt(interrupts)

    -- Emergency tanking and utility management
    BoneShield()
    VampiricBlood()
    DeathPact()

    if target.exists and target.canAttack and (HeartStrike:InRange(target) or DeathStrike:InRange(target)) then
        if A.IsInPvP then
            MindFreeze("bg")
            Strangulate("bg")
            DeathGrip("bg")
            ChainsOfIce("bg")
            pvpenis()
        end

        if player.channeling and gs.imCasting and gs.imCasting == DeathAndDecay.id then return end

        local damagePotion = Action.GetToggle(2, "damagePotion")
        local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
        local potionExhausted = Action.GetToggle(2, "potionExhausted")
        local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")
        local damagePotionObject = Action.DetermineUsableObject("player", nil, nil, true, nil, A.TemperedPotion1, A.TemperedPotion2, A.TemperedPotion3, A.PotionofUnwaveringFocus1, A.PotionofUnwaveringFocus2, A.PotionofUnwaveringFocus3)

        if damagePotionObject and damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
            local shouldPot = gs.diseaseCount >= 2 and gs.boneShieldStacks >= 3
            if shouldPot then
                return damagePotionObject:Show(icon)
            end
        end

        if gs.shouldAoE then
            aoe()
        end

        ogcd()
        eof()

        if gs.shouldCleave then
            cleave()
        end

        st()

    end

    return FrameworkEnd()
end

-- Enhanced enemy and party rotation functions
local enemyRotation = function(enemy)
    if not enemy.exists then return end
    if A.Zone ~= "arena" then return end
    MindFreeze("arena", enemy)
    Strangulate("arena", enemy)
    DeathGrip("arena", enemy)
    ChainsOfIce("arena", enemy)
end

local partyRotation = function(friendly)
    if not friendly.exists then return end
    -- Death Knight doesn't have direct healing, focus on protection
    if friendly.hp < 50 then
        -- Use Death Pact if available and pet exists
        if pet.exists and friendly.hp < 30 then
            DeathPact("arena", friendly)
        end
    end
end

-- Arena functions
A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        MindFreeze()
    end
    if Action.Zone == "arena" then
        enemyRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and gameState.runicPower < 50 then
        return spell:Cast(player)
    end
end)

Berserking:Callback(function(spell)
    if shouldBurst() and (target.hp > 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

BloodFury:Callback(function(spell)
    if shouldBurst() and (target.hp > 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

WillOfTheForsaken:Callback(function(spell)
    if player:HasDebuffType("Fear") or player:HasDebuffType("Charm") then
        return spell:Cast(player)
    end
end)

QuakingPalm:Callback(function(spell)
    if target.exists and target.canAttack and target:IsCasting() then
        return spell:Cast(target)
    end
end)

Stoneform:Callback(function(spell)
    if player:HasDebuffType("Poison") or player:HasDebuffType("Disease") or player:HasDebuffType("Bleed") then
        return spell:Cast(player)
    end
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    WillOfTheForsaken()
    QuakingPalm()
    Stoneform()
end

local function mopTalents()
    DeathPact()
    DeathSiphon()
    DancingRuneWeapon()
    EmpowerRuneWeapon()
end

local function baseStuff()
    VampiricBlood()
    BoneShield()
    IceboundFortitude()
    AntiMagicShell()
    RuneTap()
end

-- Enhanced utility for MoP
local function mopUtility()
    MindFreeze()
    Strangulate()
    DeathGrip()
    DarkCommand()
    ChainsOfIce()
    DeathAndDecay()
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local function buildMakuluFrameworkSpells(ActionList)
    local result = {}
    for k, v in pairs(ActionList) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end

-- Build Makulu framework spells and make them available directly
TableToLocal(buildMakuluFrameworkSpells(A), getfenv(1))
Aware:enable()

-- Set up commonly used units
local player = ConstUnit.player
local target = ConstUnit.target

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP Blood Death Knight Buffs
local buffs = {
    bloodPresence = 48263,
    boneShield = 49222,
    vampiricBlood = 55233,
    iceboundFortitude = 48792,
    antiMagicShell = 48707,
    runeTap = 48982,
    dancingRuneWeapon = 49028,
    lichborne = 49039,
    empowerRuneWeapon = 47568,
    willOfTheForsaken = 7744,
    shadowmeld = 58984,
}

-- MoP Blood Death Knight Debuffs
local debuffs = {
    bloodPlague = 55078,
    frostFever = 55095,
    chainsOfIce = 45524,
    strangulate = 47476,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    runicPower = 0,
    runes = 0,
    timeToAdds = 999,
    isPvP = false,
    boneShieldStacks = 0,
}

local function updateGameState()
    gameState.inCombat = player.inCombat
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.runicPower = player.runicPower or 0
    gameState.runes = player.runes or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.boneShieldStacks = player:BuffStacks(buffs.boneShield) or 0

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsBloodPresence()
    return not player:Buff(buffs.bloodPresence)
end

local function needsBoneShield()
    return gameState.boneShieldStacks < 3
end

local function needsDisease()
    return not target:DeBuff(debuffs.bloodPlague) or not target:DeBuff(debuffs.frostFever)
end

-- Presence management
BloodPresence:Callback(function(spell)
    if needsBloodPresence() then
        return spell:Cast()
    end
end)

-- Bone Shield maintenance
BoneShield:Callback(function(spell)
    if needsBoneShield() then
        return spell:Cast()
    end
end)

-- Disease application
PlagueStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if target:DeBuff(debuffs.bloodPlague) then return end

    return spell:Cast(target)
end)

-- Core rotation abilities
DeathStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if player.hp > 80 and gameState.runicPower < 80 then return end

    return spell:Cast(target)
end)

HeartStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if gameState.runes < 1 then return end

    return spell:Cast(target)
end)

BloodBoil:Callback(function(spell)
    if gameState.runes < 1 then return end
    if gameState.activeEnemies < 2 and target:DeBuff(debuffs.bloodPlague) then return end

    return spell:Cast()
end)

DeathCoil:Callback(function(spell)
    if gameState.runicPower < 40 then return end
    
    -- Heal self if low health
    if player.hp <= 60 then
        return spell:Cast(player)
    end
    
    -- Damage enemy
    if target.exists and target.distance <= 30 then
        return spell:Cast(target)
    end
end)

-- Defensive abilities
VampiricBlood:Callback(function(spell)
    if player.hp <= 50 then
        return spell:Cast()
    end
end)

IceboundFortitude:Callback(function(spell)
    if player.hp <= 40 then
        return spell:Cast()
    end
end)

AntiMagicShell:Callback(function(spell)
    if player.hp <= 60 and target.casting then
        return spell:Cast()
    end
end)

RuneTap:Callback(function(spell)
    if player.hp <= 70 and gameState.runes >= 1 then
        return spell:Cast()
    end
end)

-- Utility abilities
DeathGrip:Callback(function(spell)
    if not target.exists then return end
    if target.distance <= 8 then return end
    if target.distance > 30 then return end

    return spell:Cast(target)
end)

DarkCommand:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if target.target == player then return end

    return spell:Cast(target)
end)

DeathAndDecay:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if gameState.runes < 1 then return end

    return spell:Cast()
end)

ChainsOfIce:Callback(function(spell)
    if not target.exists then return end
    if target:DeBuff(debuffs.chainsOfIce) then return end
    if target.distance > 20 then return end

    return spell:Cast(target)
end)

-- Interrupts
MindFreeze:Callback(function(spell)
    if not target.exists then return end
    if not target.casting then return end
    if not target:IsInterruptible() then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

Strangulate:Callback(function(spell)
    if not target.exists then return end
    if not target.casting then return end
    if not target:IsInterruptible() then return end
    if target.distance > 20 then return end

    return spell:Cast(target)
end)

-- Cooldowns
DancingRuneWeapon:Callback(function(spell)
    if not shouldBurst() then return end
    if not target.exists then return end

    return spell:Cast()
end)

EmpowerRuneWeapon:Callback(function(spell)
    if gameState.runes > 2 then return end
    if gameState.runicPower > 60 then return end

    return spell:Cast()
end)

-- Pet management
RaiseDead:Callback(function(spell)
    if pet.exists then return end
    if player.moving then return end

    return spell:Cast()
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain Blood Presence
    if BloodPresence() then return true end

    -- Maintain Bone Shield
    if BoneShield() then return true end

    -- Apply diseases
    if needsDisease() then
        if PlagueStrike() then return true end
    end

    -- Death Strike for healing and threat
    if player.hp <= 80 or gameState.runicPower >= 80 then
        if DeathStrike() then return true end
    end

    -- Heart Strike for damage and threat
    if HeartStrike() then return true end

    -- Blood Boil if diseases are up
    if target:DeBuff(debuffs.bloodPlague) then
        if BloodBoil() then return true end
    end

    -- Death Coil for runic power dump
    if gameState.runicPower >= 60 then
        if DeathCoil() then return true end
    end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain Blood Presence
    if BloodPresence() then return true end

    -- Maintain Bone Shield
    if BoneShield() then return true end

    -- Death and Decay for AoE threat
    if DeathAndDecay() then return true end

    -- Blood Boil for AoE damage and disease spread
    if BloodBoil() then return true end

    -- Death Strike for survival
    if player.hp <= 70 then
        if DeathStrike() then return true end
    end

    -- Heart Strike on primary target
    if HeartStrike() then return true end

    -- Death Coil for runic power dump
    if gameState.runicPower >= 60 then
        if DeathCoil() then return true end
    end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 40 then
        if VampiricBlood() then return true end
        if IceboundFortitude() then return true end
    end

    if player.hp <= 60 then
        if AntiMagicShell() then return true end
        if RuneTap() then return true end
    end

    -- Interrupt priority
    if MindFreeze() then return true end
    if Strangulate() then return true end

    -- Maintain Blood Presence
    if BloodPresence() then return true end

    -- Maintain Bone Shield
    if BoneShield() then return true end

    if target.exists and target.alive then
        -- Taunt if needed
        if DarkCommand() then return true end

        -- Pull distant targets
        if target.distance > 10 then
            if DeathGrip() then return true end
        end

        -- Slow target
        if not target:DeBuff(debuffs.chainsOfIce) then
            if ChainsOfIce() then return true end
        end

        -- Apply diseases
        if needsDisease() then
            if PlagueStrike() then return true end
        end

        -- Death Strike for healing
        if player.hp <= 80 or gameState.runicPower >= 80 then
            if DeathStrike() then return true end
        end

        -- Heart Strike for damage
        if HeartStrike() then return true end

        -- Blood Boil for pressure
        if BloodBoil() then return true end

        -- Death Coil for damage/healing
        if gameState.runicPower >= 60 then
            if DeathCoil() then return true end
        end
    end

    return false
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Save resources for AoE abilities
        if gameState.runes < 3 then
            -- Generate runes
            if EmpowerRuneWeapon() then return true end
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                if DancingRuneWeapon() then return true end
            end
        end

        -- Maintain current target
        if HeartStrike() then return true end
        if needsDisease() then
            if PlagueStrike() then return true end
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 30 then
        if VampiricBlood() then return true end
        if IceboundFortitude() then return true end
    end

    if player.hp <= 50 then
        if AntiMagicShell() then return true end
        if RuneTap() then return true end
    end

    -- Pet management
    if not pet.exists and not player.moving then
        if RaiseDead() then return true end
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    if not MakuluFramework.start() then
        enhancedMainRotation()
    end
    return MakuluFramework.endFunc()
end

-- Enhanced A[3] function for advanced rotation with burst and cooldowns
A[3] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    updateGameState()

    -- Enhanced defensive priority
    if player.hp <= 30 then
        if VampiricBlood() then return MakuluFramework.endFunc() end
        if IceboundFortitude() then return MakuluFramework.endFunc() end
        if DeathPact() then return MakuluFramework.endFunc() end
    end

    if player.hp <= 50 then
        if AntiMagicShell() then return MakuluFramework.endFunc() end
        if RuneTap() then return MakuluFramework.endFunc() end
    end

    -- Pet management
    if not pet.exists and not player.moving then
        if RaiseDead() then return MakuluFramework.endFunc() end
    end

    if target.exists and target.alive then
        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if MindFreeze() then return MakuluFramework.endFunc() end
                if Strangulate() then return MakuluFramework.endFunc() end
                if ChainsOfIce() then return MakuluFramework.endFunc() end
                if DeathGrip() then return MakuluFramework.endFunc() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if DancingRuneWeapon() then return MakuluFramework.endFunc() end
            if EmpowerRuneWeapon() then return MakuluFramework.endFunc() end

            -- Racial abilities during burst
            if QuakingPalm() then return MakuluFramework.endFunc() end
            if BloodFury() then return MakuluFramework.endFunc() end
            if Berserking() then return MakuluFramework.endFunc() end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return MakuluFramework.endFunc()
end

-- Arena functions
A[6] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if A.GetToggle(2, "AutoInterrupt") and target.casting then
        if MindFreeze() then return MakuluFramework.endFunc() end
        if Strangulate() then return MakuluFramework.endFunc() end
    end
    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena1)
        partyRotation(ConstUnit.party1)
    end

    return MakuluFramework.endFunc()
end

A[7] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena2)
        partyRotation(ConstUnit.party2)
    end

    return MakuluFramework.endFunc()
end

A[8] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena3)
        partyRotation(ConstUnit.party3)
    end

    return MakuluFramework.endFunc()
end

-- Arena-specific callback functions for MoP Blood Death Knight
MindFreeze:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Strangulate:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 20 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

DeathGrip:Callback("arena", function(spell, enemy)
    if enemy.distance <= 8 then return end
    if enemy.distance > 30 then return end
    if enemy.hp < 30 then return end -- Don't grip low targets

    -- Use on healers or ranged DPS
    if enemy.isHealer or enemy.distance > 15 then
        Aware:displayMessage("Death Grip - Priority Target", "Green", 1)
        return spell:Cast(enemy)
    end
end)

ChainsOfIce:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:DeBuff(debuffs.chainsOfIce) then return end

    return spell:Cast(enemy)
end)

DeathStrike:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if player.hp > 80 and gameState.runicPower < 80 then return end

    -- Priority healing when low
    if player.hp < 50 then
        Aware:displayMessage("Priority Death Strike", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

HeartStrike:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.runes < 1 then return end

    return spell:Cast(enemy)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    MindFreeze("arena", enemy)
    Strangulate("arena", enemy)

    -- Utility abilities
    DeathGrip("arena", enemy)
    ChainsOfIce("arena", enemy)

    -- Damage abilities
    DeathStrike("arena", enemy)
    HeartStrike("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end

    -- Death Coil healing for party members
    if friendly.hp < 50 and gameState.runicPower >= 40 then
        DeathCoil:Cast(friendly)
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end
