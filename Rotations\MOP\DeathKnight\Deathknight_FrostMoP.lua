-- APL UPDATE MoP Frost Death Knight
-- Mists of Pandaria Frost Death Knight Rotation

if not <PERSON><PERSON>luValidCheck() then return true end
if Ma<PERSON><PERSON>_magic_number ~= 2347956243324 then return true end

-- Check if player is <PERSON> spec (talent tree 2 for Death Knight in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 2 then return end

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local ConstUnit        = MakuluFramework.ConstUnits
local cacheContext     = MakuluFramework.Cache
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits

local _G, setmetatable = _G, setmetatable

-- MoP Frost Death Knight Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Frost Core Abilities
    Obliterate = { ID = 49020, MAKULU_INFO = { damageType = "physical" } },
    FrostStrike = { ID = 49143, MAKULU_INFO = { damageType = "frost" } },
    HowlingBlast = { ID = 49184, MAKULU_INFO = { damageType = "frost" } },
    PlagueStrike = { ID = 45462, MAKULU_INFO = { damageType = "physical" } },
    IcyTouch = { ID = 45477, MAKULU_INFO = { damageType = "frost" } },
    DeathCoil = { ID = 47541, MAKULU_INFO = { damageType = "shadow", heal = true } },
    DeathStrike = { ID = 49998, MAKULU_INFO = { damageType = "physical", heal = true } },
    
    -- MoP Frost Cooldowns
    PillarOfFrost = { ID = 51271, MAKULU_INFO = { targeted = false } },
    SoulReaper = { ID = 114866, MAKULU_INFO = { damageType = "shadow" } },
    EmpowerRuneWeapon = { ID = 47568, MAKULU_INFO = { targeted = false } },
    UnholyFrenzy = { ID = 49016, MAKULU_INFO = { targeted = false } },
    
    -- MoP Defensive Abilities
    IceboundFortitude = { ID = 48792, MAKULU_INFO = { targeted = false } },
    AntiMagicShell = { ID = 48707, MAKULU_INFO = { targeted = false } },
    Lichborne = { ID = 49039, MAKULU_INFO = { targeted = false } },
    
    -- MoP Utility Abilities
    DeathGrip = { ID = 49576, MAKULU_INFO = { targeted = true } },
    DarkCommand = { ID = 56222, MAKULU_INFO = { targeted = true } },
    DeathAndDecay = { ID = 43265, MAKULU_INFO = { damageType = "shadow", targeted = false } },
    ChainsOfIce = { ID = 45524, MAKULU_INFO = { targeted = true } },
    MindFreeze = { ID = 47528, MAKULU_INFO = { targeted = true } },
    Strangulate = { ID = 47476, MAKULU_INFO = { targeted = true } },
    
    -- MoP Presence
    FrostPresence = { ID = 48266, MAKULU_INFO = { targeted = false } },
    UnholyPresence = { ID = 48265, MAKULU_INFO = { targeted = false } },
    
    -- MoP Diseases
    BloodPlague = { ID = 55078, MAKULU_INFO = { damageType = "disease" } },
    FrostFever = { ID = 55095, MAKULU_INFO = { damageType = "disease" } },
    
    -- MoP Talents
    Necrosis = { ID = 51460, MAKULU_INFO = { damageType = "shadow" } },
    DeathSiphon = { ID = 108196, MAKULU_INFO = { damageType = "shadow", heal = true } },
    Conversion = { ID = 119975, MAKULU_INFO = { targeted = false } },
    DeathPact = { ID = 48743, MAKULU_INFO = { heal = true, targeted = false } },
    
    -- MoP Pet
    RaiseDead = { ID = 46584, MAKULU_INFO = { targeted = false } },
    ArmyOfTheDead = { ID = 42650, MAKULU_INFO = { targeted = false } },
}

local ConstSpells = {
    -- Trinkets
    Trinket1 = { ID = 13 },
    Trinket2 = { ID = 14 },
    
    -- Potions
    HealthPotion = { ID = 431 },
}

local function createAction(actionData)
    return Action.Create({
        Type = actionData.Type or "Spell",
        ID = actionData.ID,
        Texture = actionData.Texture,
        MAKULU_INFO = actionData.MAKULU_INFO,
    })
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local function buildMakuluFrameworkSpells(ActionList)
    local result = {}
    for k, v in pairs(ActionList) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end

-- Build Makulu framework spells and make them available directly
TableToLocal(buildMakuluFrameworkSpells(A), getfenv(1))
Aware:enable()

-- Set up commonly used units
local player = ConstUnit.player
local target = ConstUnit.target

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP Frost Death Knight Buffs
local buffs = {
    frostPresence = 48266,
    unholyPresence = 48265,
    pillarOfFrost = 51271,
    killingMachine = 51124,
    rime = 59052,
    iceboundFortitude = 48792,
    antiMagicShell = 48707,
    lichborne = 49039,
    empowerRuneWeapon = 47568,
    unholyFrenzy = 49016,
    conversion = 119975,
    willOfTheForsaken = 7744,
    shadowmeld = 58984,
    necroticStrike = 73975,
    darkSuccor = 101568,
}

-- MoP Frost Death Knight Debuffs
local debuffs = {
    bloodPlague = 55078,
    frostFever = 55095,
    chainsOfIce = 45524,
    strangulate = 47476,
    soulReaper = 114866,
    necroticStrike = 73975,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    runicPower = 0,
    runes = 0,
    timeToAdds = 999,
    isPvP = false,
    killingMachineStacks = 0,
    rimeStacks = 0,
}

local function updateGameState()
    gameState.inCombat = player.inCombat
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.runicPower = player.runicPower or 0
    gameState.runes = player.runes or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.killingMachineStacks = player:BuffStacks(buffs.killingMachine) or 0
    gameState.rimeStacks = player:BuffStacks(buffs.rime) or 0

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsFrostPresence()
    return not player:Buff(buffs.frostPresence) and not player:Buff(buffs.unholyPresence)
end

local function needsDisease()
    return not target:DeBuff(debuffs.bloodPlague) or not target:DeBuff(debuffs.frostFever)
end

local function shouldUseKillingMachine()
    return gameState.killingMachineStacks > 0
end

local function shouldUseRime()
    return gameState.rimeStacks > 0
end

-- Presence management
FrostPresence:Callback(function(spell)
    if needsFrostPresence() then
        return spell:Cast()
    end
end)

-- Disease application
PlagueStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if target:DeBuff(debuffs.bloodPlague) then return end

    return spell:Cast(target)
end)

IcyTouch:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if target:DeBuff(debuffs.frostFever) then return end

    return spell:Cast(target)
end)

-- Core rotation abilities
Obliterate:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if gameState.runes < 2 then return end

    -- Prioritize with Killing Machine
    if shouldUseKillingMachine() then
        return spell:Cast(target)
    end

    -- Use normally when runes available
    if gameState.runes >= 2 then
        return spell:Cast(target)
    end
end)

FrostStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if gameState.runicPower < 40 then return end

    return spell:Cast(target)
end)

HowlingBlast:Callback(function(spell)
    if gameState.runes < 1 then return end

    -- Use with Rime proc
    if shouldUseRime() then
        return spell:Cast()
    end

    -- Use for AoE
    if gameState.activeEnemies >= 2 then
        return spell:Cast()
    end

    -- Use to apply Frost Fever
    if not target:DeBuff(debuffs.frostFever) then
        return spell:Cast()
    end
end)

DeathStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if player.hp > 80 and gameState.runicPower < 80 then return end

    return spell:Cast(target)
end)

DeathCoil:Callback(function(spell)
    if gameState.runicPower < 40 then return end

    -- Heal self if low health
    if player.hp <= 60 then
        return spell:Cast(player)
    end

    -- Damage enemy
    if target.exists and target.distance <= 30 then
        return spell:Cast(target)
    end
end)

-- Cooldowns
PillarOfFrost:Callback(function(spell)
    if not shouldBurst() then return end
    if not target.exists then return end

    return spell:Cast()
end)

SoulReaper:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if target.hp > 35 then return end
    if target:DeBuff(debuffs.soulReaper) then return end

    return spell:Cast(target)
end)

EmpowerRuneWeapon:Callback(function(spell)
    if gameState.runes > 2 then return end
    if gameState.runicPower > 60 then return end

    return spell:Cast()
end)

UnholyFrenzy:Callback(function(spell)
    if not shouldBurst() then return end
    if not target.exists then return end

    return spell:Cast()
end)

-- Defensive abilities
IceboundFortitude:Callback(function(spell)
    if player.hp <= 40 then
        return spell:Cast()
    end
end)

AntiMagicShell:Callback(function(spell)
    if player.hp <= 60 and target.casting then
        return spell:Cast()
    end
end)

Lichborne:Callback(function(spell)
    if player.hp <= 50 then
        return spell:Cast()
    end
end)

-- Utility abilities
DeathGrip:Callback(function(spell)
    if not target.exists then return end
    if target.distance <= 8 then return end
    if target.distance > 30 then return end

    return spell:Cast(target)
end)

DarkCommand:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if target.target == player then return end

    return spell:Cast(target)
end)

DeathAndDecay:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if gameState.runes < 1 then return end

    return spell:Cast()
end)

ChainsOfIce:Callback(function(spell)
    if not target.exists then return end
    if target:DeBuff(debuffs.chainsOfIce) then return end
    if target.distance > 20 then return end

    return spell:Cast(target)
end)

-- Interrupts
MindFreeze:Callback(function(spell)
    if not target.exists then return end
    if not target.casting then return end
    if not target:IsInterruptible() then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

Strangulate:Callback(function(spell)
    if not target.exists then return end
    if not target.casting then return end
    if not target:IsInterruptible() then return end
    if target.distance > 20 then return end

    return spell:Cast(target)
end)

-- Pet management
RaiseDead:Callback(function(spell)
    if pet.exists then return end
    if player.moving then return end

    return spell:Cast()
end)

ArmyOfTheDead:Callback(function(spell)
    if not shouldBurst() then return end
    if player.moving then return end
    if not target.exists then return end

    return spell:Cast()
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.runicPower <= 70 then
        return spell:Cast()
    end
end)

GiftOfTheNaaru:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if player.hp <= 60 then
        return spell:Cast(player)
    end
end)

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain Frost Presence
    if FrostPresence() then return true end

    -- Apply diseases
    if needsDisease() then
        if PlagueStrike() then return true end
        if IcyTouch() then return true end
    end

    -- Soul Reaper on low health targets
    if SoulReaper() then return true end

    -- Killing Machine priority
    if shouldUseKillingMachine() then
        if Obliterate() then return true end
    end

    -- Rime priority
    if shouldUseRime() then
        if HowlingBlast() then return true end
    end

    -- Core rotation
    if Obliterate() then return true end
    if FrostStrike() then return true end
    if HowlingBlast() then return true end

    -- Death Strike for healing
    if player.hp <= 70 then
        if DeathStrike() then return true end
    end

    -- Death Coil for runic power dump
    if gameState.runicPower >= 80 then
        if DeathCoil() then return true end
    end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain Frost Presence
    if FrostPresence() then return true end

    -- Death and Decay for AoE
    if DeathAndDecay() then return true end

    -- Howling Blast for AoE
    if HowlingBlast() then return true end

    -- Obliterate for cleave
    if Obliterate() then return true end

    -- Frost Strike for runic power dump
    if gameState.runicPower >= 60 then
        if FrostStrike() then return true end
    end

    -- Death Strike for survival
    if player.hp <= 70 then
        if DeathStrike() then return true end
    end

    -- Death Coil for runic power dump
    if gameState.runicPower >= 80 then
        if DeathCoil() then return true end
    end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 40 then
        if IceboundFortitude() then return true end
        if Lichborne() then return true end
    end

    if player.hp <= 60 then
        if AntiMagicShell() then return true end
    end

    -- Interrupt priority
    if MindFreeze() then return true end
    if Strangulate() then return true end

    -- Maintain Frost Presence
    if FrostPresence() then return true end

    if target.exists and target.alive then
        -- Pull distant targets
        if target.distance > 10 then
            if DeathGrip() then return true end
        end

        -- Slow target
        if not target:DeBuff(debuffs.chainsOfIce) then
            if ChainsOfIce() then return true end
        end

        -- Apply diseases
        if needsDisease() then
            if PlagueStrike() then return true end
            if IcyTouch() then return true end
        end

        -- Soul Reaper for execute
        if SoulReaper() then return true end

        -- Killing Machine priority
        if shouldUseKillingMachine() then
            if Obliterate() then return true end
        end

        -- Core damage
        if Obliterate() then return true end
        if FrostStrike() then return true end
        if HowlingBlast() then return true end

        -- Death Strike for healing
        if player.hp <= 70 then
            if DeathStrike() then return true end
        end

        -- Death Coil for damage/healing
        if gameState.runicPower >= 60 then
            if DeathCoil() then return true end
        end
    end

    return false
end

-- TimeToAdds specific logic with burst preparation
local function timeToAddsRotation()
    updateGameState()

    -- Burst preparation phase (15-8 seconds before adds)
    if gameState.timeToAdds < 15000 and gameState.timeToAdds > 8000 then
        Aware:displayMessage("Preparing for Adds - Building Resources", "Yellow", 1)

        -- Build runic power for burst
        if gameState.runicPower < 80 then
            if Obliterate() then return true end
            if HowlingBlast() then return true end
        end

        -- Maintain diseases for damage
        if needsDisease() then
            if PlagueStrike() then return true end
            if IcyTouch() then return true end
        end

        -- Save runes for burst
        if gameState.runes < 4 then
            if EmpowerRuneWeapon() then return true end
        end
    end

    -- Pre-adds burst setup (8-3 seconds)
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 3000 then
        Aware:displayMessage("Pre-Adds Burst Setup", "Orange", 1)

        -- Prepare cooldowns but don't use yet
        if gameState.runes < 4 then
            if EmpowerRuneWeapon() then return true end
        end

        -- Build resources
        if Obliterate() then return true end
        if FrostStrike() then return true end
    end

    -- Immediate burst phase (3-0 seconds)
    if gameState.timeToAdds < 3000 and gameState.timeToAdds > 0 then
        Aware:displayMessage("Adds Incoming - Burst Ready!", "Red", 1)

        -- Activate all burst cooldowns
        if shouldBurst() then
            if PillarOfFrost() then return true end
            if UnholyFrenzy() then return true end
            if ArmyOfTheDead() then return true end
        end

        -- Position for AoE
        if DeathAndDecay() then return true end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        Aware:displayMessage("Adds Phase - AoE Burst", "Green", 1)
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 30 then
        if IceboundFortitude() then return true end
        if Lichborne() then return true end
    end

    if player.hp <= 50 then
        if AntiMagicShell() then return true end
    end

    -- Pet management
    if not pet.exists and not player.moving then
        if RaiseDead() then return true end
    end

    -- Buff maintenance
    if FrostPresence() then return true end

    -- TimeToAdds logic
    if gameState.timeToAdds < 15000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    if not MakuluFramework.start() then
        enhancedMainRotation()
    end
    return MakuluFramework.endFunc()
end

-- Enhanced A[3] function for advanced rotation with burst and cooldowns
A[3] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    updateGameState()

    -- Enhanced defensive priority
    if player.hp <= 25 then
        if IceboundFortitude() then return MakuluFramework.endFunc() end
        if Lichborne() then return MakuluFramework.endFunc() end
        if DeathPact() then return MakuluFramework.endFunc() end
    end

    if player.hp <= 40 then
        if AntiMagicShell() then return MakuluFramework.endFunc() end
    end

    -- Pet management
    if not pet.exists and not player.moving then
        if RaiseDead() then return MakuluFramework.endFunc() end
    end

    -- Buff maintenance
    if FrostPresence() then return MakuluFramework.endFunc() end

    if target.exists and target.alive then
        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if MindFreeze() then return MakuluFramework.endFunc() end
                if Strangulate() then return MakuluFramework.endFunc() end
                if ChainsOfIce() then return MakuluFramework.endFunc() end
                if DeathGrip() then return MakuluFramework.endFunc() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if PillarOfFrost() then return MakuluFramework.endFunc() end
            if UnholyFrenzy() then return MakuluFramework.endFunc() end
            if EmpowerRuneWeapon() then return MakuluFramework.endFunc() end
            if ArmyOfTheDead() then return MakuluFramework.endFunc() end

            -- Racial abilities during burst
            if QuakingPalm() then return MakuluFramework.endFunc() end
            if BloodFury() then return MakuluFramework.endFunc() end
            if Berserking() then return MakuluFramework.endFunc() end
            if GiftOfTheNaaru() then return MakuluFramework.endFunc() end
        end

        -- Disease application
        if needsDisease() then
            if PlagueStrike() then return MakuluFramework.endFunc() end
            if IcyTouch() then return MakuluFramework.endFunc() end
        end

        -- Soul Reaper for execute
        if SoulReaper() then return MakuluFramework.endFunc() end

        -- Enhanced rotation with procs
        if shouldUseKillingMachine() then
            if Obliterate() then return MakuluFramework.endFunc() end
        end

        if shouldUseRime() then
            if HowlingBlast() then return MakuluFramework.endFunc() end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 15000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return MakuluFramework.endFunc()
end

-- Arena functions
A[6] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if A.GetToggle(2, "AutoInterrupt") and target.casting then
        if MindFreeze() then return MakuluFramework.endFunc() end
        if Strangulate() then return MakuluFramework.endFunc() end
    end
    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena1)
        partyRotation(ConstUnit.party1)
    end

    return MakuluFramework.endFunc()
end

A[7] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena2)
        partyRotation(ConstUnit.party2)
    end

    return MakuluFramework.endFunc()
end

A[8] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena3)
        partyRotation(ConstUnit.party3)
    end

    return MakuluFramework.endFunc()
end

-- Arena-specific callback functions for MoP Frost Death Knight
MindFreeze:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Strangulate:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 20 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

DeathGrip:Callback("arena", function(spell, enemy)
    if enemy.distance <= 8 then return end
    if enemy.distance > 30 then return end
    if enemy.hp < 30 then return end -- Don't grip low targets

    -- Use on healers or ranged DPS
    if enemy.isHealer or enemy.distance > 15 then
        Aware:displayMessage("Death Grip - Priority Target", "Green", 1)
        return spell:Cast(enemy)
    end
end)

ChainsOfIce:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:DeBuff(debuffs.chainsOfIce) then return end

    return spell:Cast(enemy)
end)

Obliterate:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.runes < 2 then return end

    -- Priority with Killing Machine
    if shouldUseKillingMachine() then
        Aware:displayMessage("Killing Machine Obliterate", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

FrostStrike:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.runicPower < 40 then return end

    return spell:Cast(enemy)
end)

SoulReaper:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy.hp > 35 then return end
    if enemy:DeBuff(debuffs.soulReaper) then return end

    Aware:displayMessage("Soul Reaper - Execute", "Red", 1)
    return spell:Cast(enemy)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    MindFreeze("arena", enemy)
    Strangulate("arena", enemy)

    -- Utility abilities
    DeathGrip("arena", enemy)
    ChainsOfIce("arena", enemy)

    -- Execute priority
    SoulReaper("arena", enemy)

    -- Damage abilities
    Obliterate("arena", enemy)
    FrostStrike("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end

    -- Death Coil healing for party members
    if friendly.hp < 50 and gameState.runicPower >= 40 then
        DeathCoil:Cast(friendly)
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end
