-- APL UPDATE MoP Unholy Death Knight
-- Mi<PERSON> of Pandaria Unholy Death Knight Rotation

if not <PERSON><PERSON>luValid<PERSON>he<PERSON>() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Unholy spec (talent tree 3 for Death Knight in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 3 then return end

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local ConstUnit        = MakuluFramework.ConstUnits
local cacheContext     = MakuluFramework.Cache
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits

local _G, setmetatable = _G, setmetatable

-- MoP Unholy Death Knight Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Unholy Core Abilities
    ScourgeStrike = { ID = 55090, MAKULU_INFO = { damageType = "physical" } },
    FesteringStrike = { ID = 85948, MAKULU_INFO = { damageType = "physical" } },
    DeathCoil = { ID = 47541, MAKULU_INFO = { damageType = "shadow", heal = true } },
    PlagueStrike = { ID = 45462, MAKULU_INFO = { damageType = "physical" } },
    IcyTouch = { ID = 45477, MAKULU_INFO = { damageType = "frost" } },
    DeathStrike = { ID = 49998, MAKULU_INFO = { damageType = "physical", heal = true } },
    Necrosis = { ID = 51460, MAKULU_INFO = { damageType = "shadow" } },
    
    -- MoP Unholy Cooldowns
    DarkTransformation = { ID = 63560, MAKULU_INFO = { targeted = false } },
    SummonGargoyle = { ID = 49206, MAKULU_INFO = { targeted = false } },
    UnholyFrenzy = { ID = 49016, MAKULU_INFO = { targeted = false } },
    EmpowerRuneWeapon = { ID = 47568, MAKULU_INFO = { targeted = false } },
    BoneArmor = { ID = 108196, MAKULU_INFO = { targeted = false } },
    
    -- MoP Defensive Abilities
    IceboundFortitude = { ID = 48792, MAKULU_INFO = { targeted = false } },
    AntiMagicShell = { ID = 48707, MAKULU_INFO = { targeted = false } },
    Lichborne = { ID = 49039, MAKULU_INFO = { targeted = false } },
    
    -- MoP Utility Abilities
    DeathGrip = { ID = 49576, MAKULU_INFO = { targeted = true } },
    DarkCommand = { ID = 56222, MAKULU_INFO = { targeted = true } },
    DeathAndDecay = { ID = 43265, MAKULU_INFO = { damageType = "shadow", targeted = false } },
    ChainsOfIce = { ID = 45524, MAKULU_INFO = { targeted = true } },
    MindFreeze = { ID = 47528, MAKULU_INFO = { targeted = true } },
    Strangulate = { ID = 47476, MAKULU_INFO = { targeted = true } },
    
    -- MoP Presence
    UnholyPresence = { ID = 48265, MAKULU_INFO = { targeted = false } },
    BloodPresence = { ID = 48263, MAKULU_INFO = { targeted = false } },
    
    -- MoP Diseases
    BloodPlague = { ID = 55078, MAKULU_INFO = { damageType = "disease" } },
    FrostFever = { ID = 55095, MAKULU_INFO = { damageType = "disease" } },
    
    -- MoP Talents
    DeathSiphon = { ID = 108196, MAKULU_INFO = { damageType = "shadow", heal = true } },
    Conversion = { ID = 119975, MAKULU_INFO = { targeted = false } },
    DeathPact = { ID = 48743, MAKULU_INFO = { heal = true, targeted = false } },
    SoulReaper = { ID = 114866, MAKULU_INFO = { damageType = "shadow" } },
    Defile = { ID = 152280, MAKULU_INFO = { damageType = "shadow", targeted = false } },
    
    -- MoP Pet Abilities
    RaiseDead = { ID = 46584, MAKULU_INFO = { targeted = false } },
    ArmyOfTheDead = { ID = 42650, MAKULU_INFO = { targeted = false } },
    CorpseExplosion = { ID = 127344, MAKULU_INFO = { damageType = "shadow" } },
}

local ConstSpells = {
    -- Trinkets
    Trinket1 = { ID = 13 },
    Trinket2 = { ID = 14 },
    
    -- Potions
    HealthPotion = { ID = 431 },
}

local function createAction(actionData)
    return Action.Create({
        Type = actionData.Type or "Spell",
        ID = actionData.ID,
        Texture = actionData.Texture,
        MAKULU_INFO = actionData.MAKULU_INFO,
    })
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local function buildMakuluFrameworkSpells(ActionList)
    local result = {}
    for k, v in pairs(ActionList) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end

-- Build Makulu framework spells and make them available directly
TableToLocal(buildMakuluFrameworkSpells(A), getfenv(1))
Aware:enable()

-- Set up commonly used units
local player = ConstUnit.player
local target = ConstUnit.target

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP Unholy Death Knight Buffs
local buffs = {
    unholyPresence = 48265,
    bloodPresence = 48263,
    darkTransformation = 63560,
    suddenDoom = 81340,
    shadowInfusion = 91342,
    iceboundFortitude = 48792,
    antiMagicShell = 48707,
    lichborne = 49039,
    empowerRuneWeapon = 47568,
    unholyFrenzy = 49016,
    conversion = 119975,
    boneArmor = 108196,
    willOfTheForsaken = 7744,
    shadowmeld = 58984,
    necroticStrike = 73975,
    darkSuccor = 101568,
}

-- MoP Unholy Death Knight Debuffs
local debuffs = {
    bloodPlague = 55078,
    frostFever = 55095,
    chainsOfIce = 45524,
    strangulate = 47476,
    soulReaper = 114866,
    necroticStrike = 73975,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    runicPower = 0,
    runes = 0,
    timeToAdds = 999,
    isPvP = false,
    suddenDoomStacks = 0,
    shadowInfusionStacks = 0,
    petActive = false,
}

local function updateGameState()
    gameState.inCombat = player.inCombat
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.runicPower = player.runicPower or 0
    gameState.runes = player.runes or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.suddenDoomStacks = player:BuffStacks(buffs.suddenDoom) or 0
    gameState.shadowInfusionStacks = player:BuffStacks(buffs.shadowInfusion) or 0
    gameState.petActive = pet.exists and pet.alive

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsUnholyPresence()
    return not player:Buff(buffs.unholyPresence) and not player:Buff(buffs.bloodPresence)
end

local function needsDisease()
    return not target:DeBuff(debuffs.bloodPlague) or not target:DeBuff(debuffs.frostFever)
end

local function shouldUseSuddenDoom()
    return gameState.suddenDoomStacks > 0
end

local function shouldTransformPet()
    return gameState.shadowInfusionStacks >= 5 and gameState.petActive
end

-- Presence management
UnholyPresence:Callback(function(spell)
    if needsUnholyPresence() then
        return spell:Cast()
    end
end)

-- Disease application
PlagueStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if target:DeBuff(debuffs.bloodPlague) then return end

    return spell:Cast(target)
end)

IcyTouch:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if target:DeBuff(debuffs.frostFever) then return end

    return spell:Cast(target)
end)

-- Core rotation abilities
ScourgeStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if gameState.runes < 1 then return end

    -- Prioritize when diseases are up
    if target:DeBuff(debuffs.bloodPlague) and target:DeBuff(debuffs.frostFever) then
        return spell:Cast(target)
    end

    return spell:Cast(target)
end)

FesteringStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if gameState.runes < 2 then return end

    -- Use to apply diseases or when diseases are missing
    if needsDisease() then
        return spell:Cast(target)
    end

    -- Use for Shadow Infusion stacks
    if gameState.shadowInfusionStacks < 5 and gameState.petActive then
        return spell:Cast(target)
    end

    return spell:Cast(target)
end)

DeathCoil:Callback(function(spell)
    if gameState.runicPower < 40 then return end

    -- Use with Sudden Doom proc
    if shouldUseSuddenDoom() then
        if target.exists and target.distance <= 30 then
            return spell:Cast(target)
        end
    end

    -- Heal self if low health
    if player.hp <= 60 then
        return spell:Cast(player)
    end

    -- Heal pet if low health
    if gameState.petActive and pet.hp <= 50 then
        return spell:Cast(pet)
    end

    -- Damage enemy
    if target.exists and target.distance <= 30 then
        return spell:Cast(target)
    end
end)

DeathStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if player.hp > 80 and gameState.runicPower < 80 then return end

    return spell:Cast(target)
end)

Necrosis:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if gameState.runicPower < 30 then return end

    return spell:Cast(target)
end)

-- Cooldowns
DarkTransformation:Callback(function(spell)
    if not gameState.petActive then return end
    if not shouldTransformPet() then return end

    return spell:Cast()
end)

SummonGargoyle:Callback(function(spell)
    if not shouldBurst() then return end
    if not target.exists then return end
    if player.moving then return end

    return spell:Cast()
end)

UnholyFrenzy:Callback(function(spell)
    if not shouldBurst() then return end
    if not target.exists then return end

    return spell:Cast()
end)

EmpowerRuneWeapon:Callback(function(spell)
    if gameState.runes > 2 then return end
    if gameState.runicPower > 60 then return end

    return spell:Cast()
end)

BoneArmor:Callback(function(spell)
    if not player:Buff(buffs.boneArmor) then
        return spell:Cast()
    end
end)

SoulReaper:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if target.hp > 35 then return end
    if target:DeBuff(debuffs.soulReaper) then return end

    return spell:Cast(target)
end)

-- Defensive abilities
IceboundFortitude:Callback(function(spell)
    if player.hp <= 40 then
        return spell:Cast()
    end
end)

AntiMagicShell:Callback(function(spell)
    if player.hp <= 60 and target.casting then
        return spell:Cast()
    end
end)

Lichborne:Callback(function(spell)
    if player.hp <= 50 then
        return spell:Cast()
    end
end)

-- Utility abilities
DeathGrip:Callback(function(spell)
    if not target.exists then return end
    if target.distance <= 8 then return end
    if target.distance > 30 then return end

    return spell:Cast(target)
end)

DarkCommand:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if target.target == player then return end

    return spell:Cast(target)
end)

DeathAndDecay:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if gameState.runes < 1 then return end

    return spell:Cast()
end)

Defile:Callback(function(spell)
    if gameState.activeEnemies < 2 then return end
    if gameState.runes < 1 then return end

    return spell:Cast()
end)

ChainsOfIce:Callback(function(spell)
    if not target.exists then return end
    if target:DeBuff(debuffs.chainsOfIce) then return end
    if target.distance > 20 then return end

    return spell:Cast(target)
end)

-- Interrupts
MindFreeze:Callback(function(spell)
    if not target.exists then return end
    if not target.casting then return end
    if not target:IsInterruptible() then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

Strangulate:Callback(function(spell)
    if not target.exists then return end
    if not target.casting then return end
    if not target:IsInterruptible() then return end
    if target.distance > 20 then return end

    return spell:Cast(target)
end)

-- Pet management
RaiseDead:Callback(function(spell)
    if pet.exists then return end
    if player.moving then return end

    return spell:Cast()
end)

ArmyOfTheDead:Callback(function(spell)
    if not shouldBurst() then return end
    if player.moving then return end
    if not target.exists then return end

    return spell:Cast()
end)

CorpseExplosion:Callback(function(spell)
    if gameState.activeEnemies < 2 then return end
    if gameState.runicPower < 40 then return end

    return spell:Cast()
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.runicPower <= 70 then
        return spell:Cast()
    end
end)

GiftOfTheNaaru:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if player.hp <= 60 then
        return spell:Cast(player)
    end
end)

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain Unholy Presence
    if UnholyPresence() then return true end

    -- Maintain Bone Armor
    if BoneArmor() then return true end

    -- Pet management
    if not gameState.petActive then
        if RaiseDead() then return true end
    end

    -- Dark Transformation when ready
    if DarkTransformation() then return true end

    -- Apply diseases
    if needsDisease() then
        if FesteringStrike() then return true end
        if PlagueStrike() then return true end
        if IcyTouch() then return true end
    end

    -- Soul Reaper on low health targets
    if SoulReaper() then return true end

    -- Sudden Doom priority
    if shouldUseSuddenDoom() then
        if DeathCoil() then return true end
    end

    -- Core rotation
    if ScourgeStrike() then return true end
    if FesteringStrike() then return true end
    if DeathCoil() then return true end
    if Necrosis() then return true end

    -- Death Strike for healing
    if player.hp <= 70 then
        if DeathStrike() then return true end
    end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain Unholy Presence
    if UnholyPresence() then return true end

    -- Maintain Bone Armor
    if BoneArmor() then return true end

    -- Pet management
    if not gameState.petActive then
        if RaiseDead() then return true end
    end

    -- Dark Transformation for AoE
    if DarkTransformation() then return true end

    -- Death and Decay / Defile for AoE
    if Defile() then return true end
    if DeathAndDecay() then return true end

    -- Corpse Explosion for AoE
    if CorpseExplosion() then return true end

    -- Apply diseases to primary target
    if needsDisease() then
        if FesteringStrike() then return true end
    end

    -- AoE abilities
    if ScourgeStrike() then return true end
    if DeathCoil() then return true end
    if Necrosis() then return true end

    -- Death Strike for survival
    if player.hp <= 70 then
        if DeathStrike() then return true end
    end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 40 then
        if IceboundFortitude() then return true end
        if Lichborne() then return true end
    end

    if player.hp <= 60 then
        if AntiMagicShell() then return true end
    end

    -- Interrupt priority
    if MindFreeze() then return true end
    if Strangulate() then return true end

    -- Maintain Unholy Presence
    if UnholyPresence() then return true end

    -- Maintain Bone Armor
    if BoneArmor() then return true end

    -- Pet management
    if not gameState.petActive then
        if RaiseDead() then return true end
    end

    if target.exists and target.alive then
        -- Pull distant targets
        if target.distance > 10 then
            if DeathGrip() then return true end
        end

        -- Slow target
        if not target:DeBuff(debuffs.chainsOfIce) then
            if ChainsOfIce() then return true end
        end

        -- Apply diseases
        if needsDisease() then
            if FesteringStrike() then return true end
            if PlagueStrike() then return true end
            if IcyTouch() then return true end
        end

        -- Soul Reaper for execute
        if SoulReaper() then return true end

        -- Dark Transformation
        if DarkTransformation() then return true end

        -- Sudden Doom priority
        if shouldUseSuddenDoom() then
            if DeathCoil() then return true end
        end

        -- Core damage
        if ScourgeStrike() then return true end
        if FesteringStrike() then return true end
        if DeathCoil() then return true end
        if Necrosis() then return true end

        -- Death Strike for healing
        if player.hp <= 70 then
            if DeathStrike() then return true end
        end
    end

    return false
end

-- TimeToAdds specific logic with burst preparation
local function timeToAddsRotation()
    updateGameState()

    -- Pet preparation phase (20-15 seconds before adds)
    if gameState.timeToAdds < 20000 and gameState.timeToAdds > 15000 then
        Aware:displayMessage("Pet Preparation Phase", "Yellow", 1)

        -- Ensure pet is active
        if not gameState.petActive then
            if RaiseDead() then return true end
        end

        -- Build Shadow Infusion stacks
        if gameState.shadowInfusionStacks < 5 then
            if FesteringStrike() then return true end
            if ScourgeStrike() then return true end
        end
    end

    -- Burst preparation phase (15-8 seconds before adds)
    if gameState.timeToAdds < 15000 and gameState.timeToAdds > 8000 then
        Aware:displayMessage("Preparing for Adds - Building Resources", "Yellow", 1)

        -- Transform pet if ready
        if DarkTransformation() then return true end

        -- Build runic power for burst
        if gameState.runicPower < 80 then
            if ScourgeStrike() then return true end
            if FesteringStrike() then return true end
        end

        -- Maintain diseases for damage
        if needsDisease() then
            if FesteringStrike() then return true end
            if PlagueStrike() then return true end
            if IcyTouch() then return true end
        end

        -- Save runes for burst
        if gameState.runes < 4 then
            if EmpowerRuneWeapon() then return true end
        end
    end

    -- Pre-adds burst setup (8-3 seconds)
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 3000 then
        Aware:displayMessage("Pre-Adds Burst Setup", "Orange", 1)

        -- Prepare cooldowns but don't use yet
        if gameState.runes < 4 then
            if EmpowerRuneWeapon() then return true end
        end

        -- Build resources
        if ScourgeStrike() then return true end
        if DeathCoil() then return true end
    end

    -- Immediate burst phase (3-0 seconds)
    if gameState.timeToAdds < 3000 and gameState.timeToAdds > 0 then
        Aware:displayMessage("Adds Incoming - Burst Ready!", "Red", 1)

        -- Activate all burst cooldowns
        if shouldBurst() then
            if SummonGargoyle() then return true end
            if UnholyFrenzy() then return true end
            if ArmyOfTheDead() then return true end
        end

        -- Position for AoE
        if Defile() then return true end
        if DeathAndDecay() then return true end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        Aware:displayMessage("Adds Phase - AoE Burst", "Green", 1)
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 30 then
        if IceboundFortitude() then return true end
        if Lichborne() then return true end
    end

    if player.hp <= 50 then
        if AntiMagicShell() then return true end
    end

    -- Pet management
    if not gameState.petActive and not player.moving then
        if RaiseDead() then return true end
    end

    -- Buff maintenance
    if UnholyPresence() then return true end
    if BoneArmor() then return true end

    -- TimeToAdds logic
    if gameState.timeToAdds < 20000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    if not MakuluFramework.start() then
        enhancedMainRotation()
    end
    return MakuluFramework.endFunc()
end

-- Enhanced A[3] function for advanced rotation with burst and cooldowns
A[3] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    updateGameState()

    -- Enhanced defensive priority
    if player.hp <= 25 then
        if IceboundFortitude() then return MakuluFramework.endFunc() end
        if Lichborne() then return MakuluFramework.endFunc() end
        if DeathPact() then return MakuluFramework.endFunc() end
    end

    if player.hp <= 40 then
        if AntiMagicShell() then return MakuluFramework.endFunc() end
    end

    -- Pet management
    if not gameState.petActive and not player.moving then
        if RaiseDead() then return MakuluFramework.endFunc() end
    end

    -- Buff maintenance
    if UnholyPresence() then return MakuluFramework.endFunc() end
    if BoneArmor() then return MakuluFramework.endFunc() end

    if target.exists and target.alive then
        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if MindFreeze() then return MakuluFramework.endFunc() end
                if Strangulate() then return MakuluFramework.endFunc() end
                if ChainsOfIce() then return MakuluFramework.endFunc() end
                if DeathGrip() then return MakuluFramework.endFunc() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if DarkTransformation() then return MakuluFramework.endFunc() end
            if SummonGargoyle() then return MakuluFramework.endFunc() end
            if UnholyFrenzy() then return MakuluFramework.endFunc() end
            if EmpowerRuneWeapon() then return MakuluFramework.endFunc() end
            if ArmyOfTheDead() then return MakuluFramework.endFunc() end

            -- Racial abilities during burst
            if QuakingPalm() then return MakuluFramework.endFunc() end
            if BloodFury() then return MakuluFramework.endFunc() end
            if Berserking() then return MakuluFramework.endFunc() end
            if GiftOfTheNaaru() then return MakuluFramework.endFunc() end
        end

        -- Disease application
        if needsDisease() then
            if FesteringStrike() then return MakuluFramework.endFunc() end
            if PlagueStrike() then return MakuluFramework.endFunc() end
            if IcyTouch() then return MakuluFramework.endFunc() end
        end

        -- Soul Reaper for execute
        if SoulReaper() then return MakuluFramework.endFunc() end

        -- Enhanced rotation with procs
        if shouldUseSuddenDoom() then
            if DeathCoil() then return MakuluFramework.endFunc() end
        end

        if shouldTransformPet() then
            if DarkTransformation() then return MakuluFramework.endFunc() end
        end

        -- Core rotation
        if ScourgeStrike() then return MakuluFramework.endFunc() end
        if FesteringStrike() then return MakuluFramework.endFunc() end
        if DeathCoil() then return MakuluFramework.endFunc() end
        if Necrosis() then return MakuluFramework.endFunc() end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 20000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return MakuluFramework.endFunc()
end

-- Arena functions
A[6] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if A.GetToggle(2, "AutoInterrupt") and target.casting then
        if MindFreeze() then return MakuluFramework.endFunc() end
        if Strangulate() then return MakuluFramework.endFunc() end
    end
    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena1)
        partyRotation(ConstUnit.party1)
    end

    return MakuluFramework.endFunc()
end

A[7] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena2)
        partyRotation(ConstUnit.party2)
    end

    return MakuluFramework.endFunc()
end

A[8] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena3)
        partyRotation(ConstUnit.party3)
    end

    return MakuluFramework.endFunc()
end

-- Arena-specific callback functions for MoP Unholy Death Knight
MindFreeze:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Strangulate:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 20 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

DeathGrip:Callback("arena", function(spell, enemy)
    if enemy.distance <= 8 then return end
    if enemy.distance > 30 then return end
    if enemy.hp < 30 then return end -- Don't grip low targets

    -- Use on healers or ranged DPS
    if enemy.isHealer or enemy.distance > 15 then
        Aware:displayMessage("Death Grip - Priority Target", "Green", 1)
        return spell:Cast(enemy)
    end
end)

ChainsOfIce:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:DeBuff(debuffs.chainsOfIce) then return end

    return spell:Cast(enemy)
end)

ScourgeStrike:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.runes < 1 then return end

    -- Priority when diseases are up
    if enemy:DeBuff(debuffs.bloodPlague) and enemy:DeBuff(debuffs.frostFever) then
        Aware:displayMessage("Scourge Strike - Disease Burst", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

FesteringStrike:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.runes < 2 then return end

    -- Use to apply diseases
    if not enemy:DeBuff(debuffs.bloodPlague) or not enemy:DeBuff(debuffs.frostFever) then
        Aware:displayMessage("Festering Strike - Disease Application", "Yellow", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

DeathCoil:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if gameState.runicPower < 40 then return end

    -- Priority with Sudden Doom
    if shouldUseSuddenDoom() then
        Aware:displayMessage("Death Coil - Sudden Doom", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

SoulReaper:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy.hp > 35 then return end
    if enemy:DeBuff(debuffs.soulReaper) then return end

    Aware:displayMessage("Soul Reaper - Execute", "Red", 1)
    return spell:Cast(enemy)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    MindFreeze("arena", enemy)
    Strangulate("arena", enemy)

    -- Utility abilities
    DeathGrip("arena", enemy)
    ChainsOfIce("arena", enemy)

    -- Execute priority
    SoulReaper("arena", enemy)

    -- Disease application
    FesteringStrike("arena", enemy)

    -- Damage abilities
    ScourgeStrike("arena", enemy)
    DeathCoil("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end

    -- Death Coil healing for party members
    if friendly.hp < 50 and gameState.runicPower >= 40 then
        DeathCoil:Cast(friendly)
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end
