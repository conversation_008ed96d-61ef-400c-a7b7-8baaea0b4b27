local Action = _G.Action

local A                = Action

local CONST                                                              = Action.Const

local ACTION_CONST_DRUID_BALANCE                                     = CONST.DRUID_BALANCE
local ACTION_CONST_DRUID_FERAL                                       = CONST.DRUID_FERAL
local ACTION_CONST_DRUID_GUARDIAN                                    = CONST.DRUID_GUAR<PERSON>AN
local ACTION_CONST_DRUID_RESTORATION                                 = CONST.DRUID_RESTORATION

LPH_ENCNUM = function(val) return val end

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "Makulu MoP v1.0.0 (7/29/2025)",
    -- Class settings
    [2] = {
        {
            {
                E = "Header",
                L = {
                    ANY = " ====== Makulu - MoP Druid ====== ",
                },
            },
        },
        {
            { -- AOE
                E = "Checkbox", 
                DB = "AoE",
                DBV = true,
                L = { 
                    enUS = "Use AoE", 
                    ruRU = "Использовать AoE", 
                    frFR = "Utiliser l'AoE",
                }, 
                TT = { 
                    enUS = "Enable multiunits actions", 
                    ruRU = "Включает действия для нескольких целей", 
                    frFR = "Activer les actions multi-unités",
                }, 
                M = {},
            },
            { -- Auto DOT Spread
                E = "Checkbox", 
                DB = "autoDOT",
                DBV = false,
                L = { 
                    ANY = "Auto DOT Spread", 
                }, 
                TT = { 
                    ANY = "Automatically swap targets to spread DoTs when appropriate.", 
                }, 
                M = {},
            },
            { -- Auto Shapeshift
                E = "Checkbox", 
                DB = "autoShapeshift",
                DBV = true,
                L = { 
                    ANY = "Auto Shapeshift Management", 
                }, 
                TT = { 
                    ANY = "Automatically manage shapeshifting based on situation."
                }, 
                M = {},
            },   
        },
        { -- Spacer
            
            {
                E = "LayoutSpace",
            },
        },
        { -- Potions
            { -- useDamagePotion
                E = "Checkbox", 
                DB = "damagePotion",
                DBV = true,
                L = { 
                    ANY = "Damage Potion"
                }, 
                TT = { 
                    ANY = "Use Damage Potion", 
                }, 
                M = {},
            },
            { -- potionBossOnly
                E = "Checkbox", 
                DB = "potionLustOnly",
                DBV = true,
                L = { 
                    ANY = "Damage Potion Bloodlust/TimeWarp Only", 
                }, 
                TT = { 
                    ANY = "Only use Damage Potion when any kind of Bloodlust/Warp active."
                }, 
                M = {},
            },
        },
        {
            { -- potionExhausted
                E = "Checkbox", 
                DB = "potionExhausted",
                DBV = true,
                L = { 
                    ANY = "Damage Potion With Exhaustion", 
                }, 
                TT = { 
                    ANY = "Use Damage Potion while Exhausted (can't use Bloodlust)."
                }, 
                M = {},
            },
            { -- potionExhaustedSlider
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,   
                Precision = 1,                         
                DB = "potionExhaustedSlider",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Exhaustion Time Remaining",
                },
                TT = { 
                    ANY = "Time in minutes left on the Exhaustion Debuff to consider using Damage Potion.", 
                },                     
                M = {},
            },
        },
        { -- LAYOUT SPACE   
            {
                E = "LayoutSpace",                                                                         
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Cooldowns",
                },
            },
        },
        {
            {
                E = "Dropdown",                                                         
                H = 20,
                OT = {
                    { text = "Force of Nature", value = 1 }, 
                    { text = "Celestial Alignment", value = 2 },     
                    { text = "Incarnation", value = 3 },
                    { text = "Berserk", value = 4 },
                    { text = "Nature's Vigil", value = 5 },
                    { text = "Heart of the Wild", value = 6 },   
                },
                MULT = true,
                DB = "cooldownSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                    [5] = true,
                    [6] = true,
                },  
                L = { 
                    ANY = "Cooldown Abilities", 
                }, 
                TT = { 
                    ANY = "Select what abilities you want the rotation to obey the burst toggle.\nIf a spell is unchecked, it will be used even when burst is turned off!", 
                }, 
                M = {},                                    
            },  
        }, 
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { 
            {-- Burst Sensitivity
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "burstSens",
                DBV = 18,
                ONOFF = false,
                L = { 
                    ANY = "Burst Mode Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to use cooldowns. A lower number means cooldowns used closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },  
            {-- DOT Refresh TTD
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "dotRefresh",
                DBV = 12,
                ONOFF = false,
                L = { 
                    ANY = "DoT Refresh Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to refresh DoTs. A lower number means DoTs refreshed closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- DRUID HEADER
            {
                E = "Header",
                L = {
                    ANY = "INTERRUPTS",
                },
            },
        },
        {    
            { -- Automatic Interrupt
                E = "Checkbox", 
                DB = "AutoInterrupt",
                DBV = true,
                L = { 
                    ANY = "Switch Targets Interrupt",
                }, 
                TT = { 
                    ANY = "Automatically switches targets to interrupt.",
                }, 
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- DRUID HEADER
            {
                E = "Header",
                L = {
                    ANY = "DEFENSIVES",
                },
            },
        },
        {
            { -- Barkskin HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "BarkskinHP",
                DBV = 60,
                ONOFF = false,
                L = { 
                    ANY = "Barkskin HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Barkskin on yourself.", 
                },                     
                M = {},
            },    
            { -- Survival Instincts HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "SurvivalInstinctsHP",
                DBV = 40,
                ONOFF = false,
                L = { 
                    ANY = "Survival Instincts HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Survival Instincts on yourself.", 
                },                     
                M = {},
            },    
        },
        {
            {-- Healing Touch HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "healingTouchHP",
                DBV = 50,
                ONOFF = false,
                L = { 
                    ANY = "Healing Touch HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Healing Touch for emergency healing.", 
                },                     
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Barkskin", value = 1 },
                    { text = "Survival Instincts", value = 2 },
                    { text = "Healing Touch", value = 3 },
                    { text = "Rejuvenation", value = 4 },
                },
                MULT = true,
                DB = "defensiveSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Defensive Reactions",
                },
                TT = {
                    ANY = "Select what spells to be used when reacting to incoming damage in dungeons.",
                },
                M = {},
            },
        },
        { -- Spacer

            {
                E = "LayoutSpace",
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Debug/Aware Options",
                },
            },
        },
        {
            { -- Debug
                E = "Checkbox",
                DB = "makDebug",
                DBV = false,
                L = {
                    ANY = "Enable debug options",
                },
                TT = {
                    ANY = "Show a box with various debug data.\nIt takes a couple of seconds to get rid of the box when you disable this.",
                },
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Eclipse Reminder", value = 1 },
                    { text = "Celestial Alignment Ready", value = 2 },
                    { text = "Incarnation Ready", value = 3 },
                    { text = "DoT Alert", value = 4 },
                },
                MULT = true,
                DB = "makAware",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Aware Text Alert Reminders",
                },
                TT = {
                    ANY = "Select what text alert reminders you would like.\nThese will appear in the center of your screen.",
                },
                M = {},
            },
        },
    },
}
