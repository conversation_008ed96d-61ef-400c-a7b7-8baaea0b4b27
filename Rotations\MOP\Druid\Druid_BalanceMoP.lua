-- APL UPDATE MoP Balance Druid
-- Mists of Pandaria Balance Druid Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Balance spec (talent tree 1 for Druid in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 1 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Balance Druid Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Balance Core Abilities
    MoonkinForm = { ID = 24858, MAKULU_INFO = { targeted = false } },
    BearForm = { ID = 5487, MAKULU_INFO = { targeted = false } },
    
    -- MoP Balance Damage Spells
    Wrath = { ID = 5176, MAKULU_INFO = { damageType = "nature", castTime = 2500 } },
    Starfire = { ID = 2912, MAKULU_INFO = { damageType = "arcane", castTime = 3200 } },
    Moonfire = { ID = 8921, MAKULU_INFO = { damageType = "arcane" } },
    Sunfire = { ID = 93402, MAKULU_INFO = { damageType = "nature" } },
    Starsurge = { ID = 78674, MAKULU_INFO = { damageType = "arcane", castTime = 2000 } },
    Starfall = { ID = 48505, MAKULU_INFO = { damageType = "arcane", targeted = false } },
    Hurricane = { ID = 16914, MAKULU_INFO = { damageType = "nature", channeled = true } },
    
    -- MoP Balance Eclipse System
    CelestialAlignment = { ID = 112071, MAKULU_INFO = { targeted = false } },
    SolarBeam = { ID = 78675, MAKULU_INFO = { damageType = "nature", ignoreCasting = true } },
    
    -- MoP Balance Utility
    Innervate = { ID = 29166, MAKULU_INFO = { targeted = false } },
    MarkOfTheWild = { ID = 1126, MAKULU_INFO = { targeted = false } },
    Rebirth = { ID = 20484, MAKULU_INFO = { castTime = 2000 } },
    
    -- MoP Defensive Abilities
    Barkskin = { ID = 22812, MAKULU_INFO = { targeted = false } },
    FrenziedRegeneration = { ID = 22842, MAKULU_INFO = { heal = true, targeted = false } },
    SurvivalInstincts = { ID = 61336, MAKULU_INFO = { targeted = false } },
    
    -- MoP Healing Abilities
    HealingTouch = { ID = 5185, MAKULU_INFO = { heal = true, castTime = 2500 } },
    Rejuvenation = { ID = 774, MAKULU_INFO = { heal = true } },
    Regrowth = { ID = 8936, MAKULU_INFO = { heal = true, castTime = 2000 } },
    Tranquility = { ID = 740, MAKULU_INFO = { heal = true, channeled = true } },
    
    -- MoP Crowd Control
    Cyclone = { ID = 33786, MAKULU_INFO = { castTime = 1700 } },
    EntanglingRoots = { ID = 339, MAKULU_INFO = { castTime = 1700 } },
    Hibernate = { ID = 2637, MAKULU_INFO = { castTime = 1500 } },
    
    -- MoP Movement
    Dash = { ID = 1850, MAKULU_INFO = { targeted = false } },
    TravelForm = { ID = 783, MAKULU_INFO = { targeted = false } },
    
    -- MoP Talents
    ForceOfNature = { ID = 106737, MAKULU_INFO = { targeted = false } },
    IncarnationChosenOfElune = { ID = 102560, MAKULU_INFO = { targeted = false } },
    HeartOfTheWild = { ID = 108238, MAKULU_INFO = { targeted = false } },
    NaturesVigil = { ID = 124974, MAKULU_INFO = { targeted = false } },
    WildMushroom = { ID = 88747, MAKULU_INFO = { targeted = false } },
    WildMushroomDetonate = { ID = 88751, MAKULU_INFO = { targeted = false } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 78675, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 33786, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
    local result = {}
    for k, v in pairs(A) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_DRUID_BALANCE] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Balance Druid Buffs
local buffs = {
    moonkinForm = 24858,
    bearForm = 5487,
    travelForm = 783,
    celestialAlignment = 112071,
    solarEclipse = 48517,
    lunarEclipse = 48518,
    eclipseEnergy = 89265,
    shootingStars = 93400,
    euphoria = 81061,
    naturesGrace = 16886,
    barkskin = 22812,
    frenziedRegeneration = 22842,
    survivalInstincts = 61336,
    incarnationChosenOfElune = 102560,
    heartOfTheWild = 108238,
    naturesVigil = 124974,
    markOfTheWild = 1126,
    innervate = 29166,
    rejuvenation = 774,
    regrowth = 8936,
    wildMushroom = 88747,
    forceOfNature = 106737,
    dash = 1850,
}

-- MoP Balance Druid Debuffs
local debuffs = {
    moonfire = 8921,
    sunfire = 93402,
    cyclone = 33786,
    entanglingRoots = 339,
    hibernate = 2637,
    solarBeam = 78675,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    mana = 0,
    eclipseEnergy = 0,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
    eclipseDirection = 0, -- -1 for lunar, 1 for solar, 0 for neutral
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(40)
    gameState.mana = player.mana or 0
    gameState.channeling = player.channeling
    gameState.isPvP = Action.Zone == "arena" or Action.Zone == "pvp"
    
    -- Eclipse energy tracking (MoP specific)
    if player:Buff(buffs.solarEclipse) then
        gameState.eclipseDirection = 1
    elseif player:Buff(buffs.lunarEclipse) then
        gameState.eclipseDirection = -1
    else
        gameState.eclipseDirection = 0
    end
    
    -- TimeToAdds calculation (simplified for MoP)
    if gameState.inCombat then
        gameState.timeToAdds = PVE:TimeToAdds() or 999
    else
        gameState.timeToAdds = 999
    end
end

-- Utility functions
local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function shouldBurst()
    return gameState.timeToAdds < 15000 or (target.exists and target.hp <= 35)
end

local function needsMoonfire()
    return not target:DeBuff(debuffs.moonfire) or target:DeBuffRemains(debuffs.moonfire) < 3000
end

local function needsSunfire()
    return not target:DeBuff(debuffs.sunfire) or target:DeBuffRemains(debuffs.sunfire) < 3000
end

local function inMoonkinForm()
    return player:Buff(buffs.moonkinForm)
end

local function canShapeshift()
    return not gameState.channeling and not player.moving
end

local function inEclipse()
    return player:Buff(buffs.solarEclipse) or player:Buff(buffs.lunarEclipse)
end

local function inSolarEclipse()
    return player:Buff(buffs.solarEclipse)
end

local function inLunarEclipse()
    return player:Buff(buffs.lunarEclipse)
end

-- Core ability functions
local function MoonkinForm()
    if player:Buff(buffs.moonkinForm) then return false end
    if not canShapeshift() then return false end
    
    return MoonkinForm:Cast()
end

local function CelestialAlignment()
    if not inMoonkinForm() then return false end
    if player:Buff(buffs.celestialAlignment) then return false end
    if not shouldBurst() then return false end
    
    Aware:displayMessage("Celestial Alignment - Burst Mode", "Red", 1)
    return CelestialAlignment:Cast()
end

local function Moonfire()
    if not target.exists or target.distance > 40 then return false end
    if gameState.mana < 15 then return false end
    if not needsMoonfire() then return false end
    
    return Moonfire:Cast(target)
end

local function Sunfire()
    if not target.exists or target.distance > 40 then return false end
    if gameState.mana < 15 then return false end
    if not needsSunfire() then return false end
    
    return Sunfire:Cast(target)
end

local function Wrath()
    if not inMoonkinForm() then return false end
    if not target.exists or target.distance > 40 then return false end
    if gameState.mana < 15 then return false end
    if player.moving then return false end
    
    -- Prefer during Solar Eclipse or when moving toward Solar
    if inSolarEclipse() or gameState.eclipseDirection >= 0 then
        return Wrath:Cast(target)
    end
    
    return false
end

local function Starfire()
    if not inMoonkinForm() then return false end
    if not target.exists or target.distance > 40 then return false end
    if gameState.mana < 20 then return false end
    if player.moving then return false end
    
    -- Prefer during Lunar Eclipse or when moving toward Lunar
    if inLunarEclipse() or gameState.eclipseDirection <= 0 then
        return Starfire:Cast(target)
    end
    
    return false
end

local function Starsurge()
    if not inMoonkinForm() then return false end
    if not target.exists or target.distance > 40 then return false end
    if gameState.mana < 25 then return false end
    if player.moving then return false end
    
    -- Use for Eclipse transitions and burst
    return Starsurge:Cast(target)
end

local function Starfall()
    if not inMoonkinForm() then return false end
    if gameState.mana < 35 then return false end
    if not shouldAoE() then return false end
    
    Aware:displayMessage("Starfall - AoE Damage", "Blue", 1)
    return Starfall:Cast()
end

local function Hurricane()
    if not inMoonkinForm() then return false end
    if not target.exists or target.distance > 40 then return false end
    if gameState.mana < 20 then return false end
    if not shouldAoE() then return false end
    if not player.moving then return false end -- Use while moving
    
    return Hurricane:Cast(target)
end

local function SolarBeam()
    if not target.exists or target.distance > 40 then return false end
    if not target.casting then return false end
    if not target:IsInterruptible() then return false end

    Aware:displayMessage("Solar Beam - Interrupt", "Red", 1)
    return SolarBeam:Cast(target)
end

-- Defensive abilities
local function Barkskin()
    if player:Buff(buffs.barkskin) then return false end
    if player.hp > 60 then return false end

    return Barkskin:Cast()
end

local function SurvivalInstincts()
    if player:Buff(buffs.survivalInstincts) then return false end
    if player.hp > 40 then return false end

    Aware:displayMessage("Survival Instincts - Emergency", "Red", 1)
    return SurvivalInstincts:Cast()
end

local function FrenziedRegeneration()
    if not player:Buff(buffs.bearForm) then return false end
    if player:Buff(buffs.frenziedRegeneration) then return false end
    if player.hp > 70 then return false end

    return FrenziedRegeneration:Cast()
end

-- Talent abilities
local function ForceOfNature()
    if not shouldBurst() then return false end

    Aware:displayMessage("Force of Nature - Treants", "Green", 1)
    return ForceOfNature:Cast()
end

local function IncarnationChosenOfElune()
    if player:Buff(buffs.incarnationChosenOfElune) then return false end
    if not shouldBurst() then return false end

    Aware:displayMessage("Incarnation - Chosen of Elune", "Purple", 1)
    return IncarnationChosenOfElune:Cast()
end

local function WildMushroom()
    if not shouldAoE() then return false end
    if gameState.mana < 30 then return false end

    return WildMushroom:Cast()
end

local function WildMushroomDetonate()
    if not shouldAoE() then return false end

    return WildMushroomDetonate:Cast()
end

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain Moonkin Form
    if MoonkinForm() then return true end

    -- Apply DoTs
    if needsMoonfire() then
        if Moonfire() then return true end
    end

    if needsSunfire() then
        if Sunfire() then return true end
    end

    -- Use Starsurge for Eclipse transitions and burst
    if inEclipse() or shouldBurst() then
        if Starsurge() then return true end
    end

    -- Eclipse-based casting
    if inSolarEclipse() then
        -- Solar Eclipse: Wrath spam
        if Wrath() then return true end
    elseif inLunarEclipse() then
        -- Lunar Eclipse: Starfire spam
        if Starfire() then return true end
    else
        -- No Eclipse: Build toward one
        if gameState.eclipseDirection >= 0 then
            -- Moving toward Solar
            if Wrath() then return true end
        else
            -- Moving toward Lunar
            if Starfire() then return true end
        end
    end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain Moonkin Form
    if MoonkinForm() then return true end

    -- Apply DoTs to primary target
    if needsMoonfire() then
        if Moonfire() then return true end
    end

    if needsSunfire() then
        if Sunfire() then return true end
    end

    -- Wild Mushroom setup and detonation
    if WildMushroom() then return true end
    if WildMushroomDetonate() then return true end

    -- Starfall for AoE damage
    if Starfall() then return true end

    -- Hurricane while moving
    if player.moving then
        if Hurricane() then return true end
    end

    -- Use Starsurge for Eclipse management
    if Starsurge() then return true end

    -- Eclipse-based AoE casting
    if inSolarEclipse() then
        if Wrath() then return true end
    elseif inLunarEclipse() then
        if Starfire() then return true end
    else
        -- Build toward Lunar for AoE
        if Starfire() then return true end
    end

    return false
end

-- PvP Rotation
local function pvpRotation()
    updateGameState()

    -- Maintain Moonkin Form
    if MoonkinForm() then return true end

    -- Interrupt priority
    if SolarBeam() then return true end

    -- Apply DoTs for pressure
    if needsMoonfire() then
        if Moonfire() then return true end
    end

    if needsSunfire() then
        if Sunfire() then return true end
    end

    -- Burst damage with Starsurge
    if Starsurge() then return true end

    -- Eclipse-based damage
    if inSolarEclipse() then
        if Wrath() then return true end
    elseif inLunarEclipse() then
        if Starfire() then return true end
    else
        -- Prefer faster casts in PvP
        if Wrath() then return true end
    end

    return false
end

-- TimeToAdds rotation for burst preparation
local function timeToAddsRotation()
    updateGameState()

    -- Preparation phase (15-10 seconds)
    if gameState.timeToAdds < 15000 and gameState.timeToAdds > 10000 then
        Aware:displayMessage("Adds Soon - Prepare Burst", "Yellow", 1)

        -- Ensure DoTs are up
        if needsMoonfire() then
            if Moonfire() then return true end
        end

        if needsSunfire() then
            if Sunfire() then return true end
        end

        -- Build Eclipse energy
        if not inEclipse() then
            if Starsurge() then return true end
        end
    end

    -- Pre-burst phase (10-3 seconds)
    if gameState.timeToAdds < 10000 and gameState.timeToAdds > 3000 then
        Aware:displayMessage("Adds Incoming - Pre-Burst", "Orange", 1)

        -- Activate cooldowns
        if CelestialAlignment() then return true end
        if IncarnationChosenOfElune() then return true end
        if ForceOfNature() then return true end

        -- Prepare AoE abilities
        if WildMushroom() then return true end
    end

    -- Immediate burst phase (3-0 seconds)
    if gameState.timeToAdds < 3000 and gameState.timeToAdds > 0 then
        Aware:displayMessage("Adds Incoming - Burst Ready!", "Red", 1)

        -- Activate all burst cooldowns
        if shouldBurst() then
            if CelestialAlignment() then return true end
            if IncarnationChosenOfElune() then return true end
            if ForceOfNature() then return true end
        end

        -- Position for AoE
        if WildMushroomDetonate() then return true end
        if Starfall() then return true end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        Aware:displayMessage("Adds Phase - AoE Burst", "Green", 1)
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency responses
    if player.hp <= 20 then
        SurvivalInstincts()
        FrenziedRegeneration()
    end

    if player.hp <= 50 then
        Barkskin()
    end

    -- Form management
    if not inMoonkinForm() and canShapeshift() then
        MoonkinForm()
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 20000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    if not MakuluFramework.start() then
        enhancedMainRotation()
    end
    return MakuluFramework.endFunc()
end

-- Enhanced A[3] function for advanced rotation with burst and cooldowns
A[3] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    updateGameState()

    -- Enhanced defensive priority
    if player.hp <= 25 then
        if SurvivalInstincts() then return MakuluFramework.endFunc() end
        if Barkskin() then return MakuluFramework.endFunc() end
    end

    if player.hp <= 40 then
        if FrenziedRegeneration() then return MakuluFramework.endFunc() end
    end

    -- Form management
    if not inMoonkinForm() and canShapeshift() then
        if MoonkinForm() then return MakuluFramework.endFunc() end
    end

    if target.exists and target.alive then
        -- Interrupt priority
        if SolarBeam() then return MakuluFramework.endFunc() end

        -- PvP specific abilities
        if gameState.isPvP then
            if Action.Zone ~= "arena" then
                -- Use crowd control in battlegrounds
                if target.distance <= 30 and not target:DeBuff(debuffs.cyclone) then
                    -- Cyclone logic would go here
                end
            end
        end

        -- Burst phase
        if shouldBurst() then
            CelestialAlignment()
            IncarnationChosenOfElune()
            ForceOfNature()

            -- Trinket usage during burst
            local damagePotion = Action.GetToggle(2, "damagePotion")
            if damagePotion and player:Buff(buffs.celestialAlignment) then
                -- Use damage potions during burst cooldowns
            end
        end

        -- Core rotation
        if inMoonkinForm() then
            -- Apply DoTs
            if needsMoonfire() then
                Moonfire()
            end

            if needsSunfire() then
                Sunfire()
            end

            -- AoE abilities
            if shouldAoE() then
                WildMushroom()
                WildMushroomDetonate()
                Starfall()
            end

            -- Eclipse-based casting
            if inEclipse() then
                Starsurge()
            end

            if inSolarEclipse() then
                Wrath()
            elseif inLunarEclipse() then
                Starfire()
            else
                -- Build toward Eclipse
                if gameState.eclipseDirection >= 0 then
                    Wrath()
                else
                    Starfire()
                end
            end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 20000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return MakuluFramework.endFunc()
end

-- Arena functions A[6], A[7], A[8] for PvP
A[6] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena1)
        partyRotation(ConstUnit.party1)
    end

    return MakuluFramework.endFunc()
end

A[7] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena2)
        partyRotation(ConstUnit.party2)
    end

    return MakuluFramework.endFunc()
end

A[8] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena3)
        partyRotation(ConstUnit.party3)
    end

    return MakuluFramework.endFunc()
end

-- Arena-specific callback functions for MoP Balance Druid
SolarBeam:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 40 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Moonfire:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if gameState.mana < 15 then return end

    -- Apply Moonfire for pressure
    if not enemy:DeBuff(debuffs.moonfire) or enemy:DeBuffRemains(debuffs.moonfire) < 3000 then
        Aware:displayMessage("Moonfire - DoT Application", "Blue", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

Sunfire:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if gameState.mana < 15 then return end

    -- Apply Sunfire for pressure
    if not enemy:DeBuff(debuffs.sunfire) or enemy:DeBuffRemains(debuffs.sunfire) < 3000 then
        Aware:displayMessage("Sunfire - DoT Application", "Orange", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

Starsurge:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if gameState.mana < 25 then return end
    if player.moving then return end

    -- Use for burst damage
    Aware:displayMessage("Starsurge - Burst Damage", "Purple", 1)
    return spell:Cast(enemy)
end)

Wrath:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if gameState.mana < 15 then return end
    if player.moving then return end

    -- Use during Solar Eclipse or when building toward it
    if inSolarEclipse() or gameState.eclipseDirection >= 0 then
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

Starfire:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if gameState.mana < 20 then return end
    if player.moving then return end

    -- Use during Lunar Eclipse or when building toward it
    if inLunarEclipse() or gameState.eclipseDirection <= 0 then
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

Cyclone:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:DeBuff(debuffs.cyclone) then return end
    if player.moving then return end

    -- Use for crowd control
    Aware:displayMessage("Cyclone - CC", "Yellow", 1)
    return spell:Cast(enemy)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    SolarBeam("arena", enemy)

    -- Crowd control
    Cyclone("arena", enemy)

    -- DoT application
    Moonfire("arena", enemy)
    Sunfire("arena", enemy)

    -- Burst damage
    Starsurge("arena", enemy)

    -- Eclipse-based casting
    Wrath("arena", enemy)
    Starfire("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end
    if friendly.hp > 70 then return end

    -- Emergency healing
    if friendly.hp <= 30 then
        HealingTouch("party", friendly)
    elseif friendly.hp <= 50 then
        Regrowth("party", friendly)
    end

    -- HoT maintenance
    if not friendly:Buff(buffs.rejuvenation) then
        Rejuvenation("party", friendly)
    end
end

local arenaRotation = enhancedArenaRotation
local partyRotation = enhancedPartyRotation
