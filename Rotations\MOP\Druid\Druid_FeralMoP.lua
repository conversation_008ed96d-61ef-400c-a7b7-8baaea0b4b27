-- APL UPDATE MoP Feral Druid
-- Mists of Pandaria Feral Druid Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Feral spec (talent tree 2 for Druid in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 2 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Feral Druid Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Feral Core Abilities
    CatForm = { ID = 768, MAKULU_INFO = { targeted = false } },
    BearForm = { ID = 5487, MAKULU_INFO = { targeted = false } },
    Prowl = { ID = 5215, MAKULU_INFO = { targeted = false } },
    
    -- MoP Feral Damage Abilities
    Shred = { ID = 5221, MAKULU_INFO = { damageType = "physical" } },
    Rake = { ID = 1822, MAKULU_INFO = { damageType = "physical" } },
    Rip = { ID = 1079, MAKULU_INFO = { damageType = "physical" } },
    FerociousBite = { ID = 22568, MAKULU_INFO = { damageType = "physical" } },
    Mangle = { ID = 33917, MAKULU_INFO = { damageType = "physical" } },
    Thrash = { ID = 106830, MAKULU_INFO = { damageType = "physical" } },
    Swipe = { ID = 62078, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP Feral Buffs and Cooldowns
    SavageRoar = { ID = 52610, MAKULU_INFO = { targeted = false } },
    TigersFury = { ID = 5217, MAKULU_INFO = { targeted = false } },
    Berserk = { ID = 106951, MAKULU_INFO = { targeted = false } },
    NaturesVigil = { ID = 124974, MAKULU_INFO = { targeted = false } },
    
    -- MoP Feral Utility
    FaerieFire = { ID = 770, MAKULU_INFO = { damageType = "nature" } },
    SkullBash = { ID = 106839, MAKULU_INFO = { damageType = "physical", ignoreCasting = true } },
    Dash = { ID = 1850, MAKULU_INFO = { targeted = false } },
    
    -- MoP Defensive Abilities
    SurvivalInstincts = { ID = 61336, MAKULU_INFO = { targeted = false } },
    Barkskin = { ID = 22812, MAKULU_INFO = { targeted = false } },
    FrenziedRegeneration = { ID = 22842, MAKULU_INFO = { heal = true, targeted = false } },
    
    -- MoP Healing Abilities
    HealingTouch = { ID = 5185, MAKULU_INFO = { heal = true, castTime = 2500 } },
    Rejuvenation = { ID = 774, MAKULU_INFO = { heal = true } },
    Regrowth = { ID = 8936, MAKULU_INFO = { heal = true, castTime = 2000 } },
    
    -- MoP Crowd Control
    Cyclone = { ID = 33786, MAKULU_INFO = { castTime = 1700 } },
    EntanglingRoots = { ID = 339, MAKULU_INFO = { castTime = 1700 } },
    Hibernate = { ID = 2637, MAKULU_INFO = { castTime = 1500 } },
    
    -- MoP Talents
    ForceOfNature = { ID = 106737, MAKULU_INFO = { targeted = false } },
    IncarnationKingOfBeasts = { ID = 102543, MAKULU_INFO = { targeted = false } },
    HeartOfTheWild = { ID = 108238, MAKULU_INFO = { targeted = false } },
    MasterShapeshifter = { ID = 48412, MAKULU_INFO = { targeted = false } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 106839, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 33786, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
    local result = {}
    for k, v in pairs(A) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_DRUID_FERAL] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Feral Druid Buffs
local buffs = {
    catForm = 768,
    bearForm = 5487,
    prowl = 5215,
    savageRoar = 52610,
    tigersFury = 5217,
    berserk = 106951,
    naturesVigil = 124974,
    survivalInstincts = 61336,
    barkskin = 22812,
    frenziedRegeneration = 22842,
    dash = 1850,
    incarnationKingOfBeasts = 102543,
    heartOfTheWild = 108238,
    masterShapeshifter = 48412,
    predatorySwiftness = 69369,
    clearcasting = 135700,
    omenOfClarity = 16864,
    rejuvenation = 774,
    regrowth = 8936,
    healingTouch = 5185,
}

-- MoP Feral Druid Debuffs
local debuffs = {
    rake = 1822,
    rip = 1079,
    thrash = 106830,
    faerieFire = 770,
    cyclone = 33786,
    entanglingRoots = 339,
    hibernate = 2637,
    mangle = 33917,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    energy = 0,
    comboPoints = 0,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
    petActive = false,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.energy = player.energy or 0
    gameState.comboPoints = player.comboPoints or 0
    gameState.channeling = player.channeling
    gameState.isPvP = Action.Zone == "arena" or Action.Zone == "pvp"
    
    -- TimeToAdds calculation (simplified for MoP)
    if gameState.inCombat then
        gameState.timeToAdds = PVE:TimeToAdds() or 999
    else
        gameState.timeToAdds = 999
    end
end

-- Utility functions
local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function shouldBurst()
    return gameState.timeToAdds < 15000 or (target.exists and target.hp <= 35)
end

local function needsRake()
    return not target:DeBuff(debuffs.rake) or target:DeBuffRemains(debuffs.rake) < 3000
end

local function needsRip()
    return not target:DeBuff(debuffs.rip) or target:DeBuffRemains(debuffs.rip) < 3000
end

local function needsSavageRoar()
    return not player:Buff(buffs.savageRoar) or player:BuffRemains(buffs.savageRoar) < 3000
end

local function needsThrash()
    return not target:DeBuff(debuffs.thrash) or target:DeBuffRemains(debuffs.thrash) < 3000
end

local function inCatForm()
    return player:Buff(buffs.catForm)
end

local function canShapeshift()
    return not gameState.channeling and not player.moving
end

-- Core ability functions
local function CatForm()
    if player:Buff(buffs.catForm) then return false end
    if not canShapeshift() then return false end
    
    return CatForm:Cast()
end

local function TigersFury()
    if not inCatForm() then return false end
    if player:Buff(buffs.tigersFury) then return false end
    if gameState.energy >= 80 then return false end
    
    Aware:displayMessage("Tiger's Fury - Energy Boost", "Yellow", 1)
    return TigersFury:Cast()
end

local function Berserk()
    if not inCatForm() then return false end
    if player:Buff(buffs.berserk) then return false end
    if not shouldBurst() then return false end
    
    Aware:displayMessage("Berserk - Burst Mode", "Red", 1)
    return Berserk:Cast()
end

local function SavageRoar()
    if not inCatForm() then return false end
    if gameState.comboPoints < 1 then return false end
    if not needsSavageRoar() then return false end
    
    Aware:displayMessage("Savage Roar - Damage Buff", "Orange", 1)
    return SavageRoar:Cast()
end

local function Rake()
    if not inCatForm() then return false end
    if not target.exists or target.distance > 5 then return false end
    if gameState.energy < 35 then return false end
    if not needsRake() then return false end
    
    return Rake:Cast(target)
end

local function Rip()
    if not inCatForm() then return false end
    if not target.exists or target.distance > 5 then return false end
    if gameState.comboPoints < 1 then return false end
    if not needsRip() then return false end
    
    return Rip:Cast(target)
end

local function FerociousBite()
    if not inCatForm() then return false end
    if not target.exists or target.distance > 5 then return false end
    if gameState.comboPoints < 1 then return false end
    if gameState.energy < 25 then return false end
    
    -- Use on low health targets or when at max combo points
    if target.hp <= 25 or gameState.comboPoints >= 5 then
        return FerociousBite:Cast(target)
    end
    
    return false
end

local function Shred()
    if not inCatForm() then return false end
    if not target.exists or target.distance > 5 then return false end
    if gameState.energy < 40 then return false end
    if gameState.comboPoints >= 5 then return false end
    
    return Shred:Cast(target)
end

local function Mangle()
    if not inCatForm() then return false end
    if not target.exists or target.distance > 5 then return false end
    if gameState.energy < 35 then return false end
    if gameState.comboPoints >= 5 then return false end
    
    -- Use when behind target is not possible (for Shred)
    return Mangle:Cast(target)
end

local function Thrash()
    if not inCatForm() then return false end
    if not target.exists or target.distance > 8 then return false end
    if gameState.energy < 50 then return false end
    if not shouldAoE() then return false end
    if not needsThrash() then return false end
    
    return Thrash:Cast()
end

local function Swipe()
    if not inCatForm() then return false end
    if not target.exists or target.distance > 8 then return false end
    if gameState.energy < 45 then return false end
    if not shouldAoE() then return false end
    
    return Swipe:Cast()
end

local function FaerieFire()
    if not target.exists or target.distance > 30 then return false end
    if target:DeBuff(debuffs.faerieFire) then return false end
    
    return FaerieFire:Cast(target)
end

local function SkullBash()
    if not target.exists or target.distance > 13 then return false end
    if not target.casting then return false end
    if not target:IsInterruptible() then return false end

    Aware:displayMessage("Skull Bash - Interrupt", "Red", 1)
    return SkullBash:Cast(target)
end

-- Defensive abilities
local function SurvivalInstincts()
    if player:Buff(buffs.survivalInstincts) then return false end
    if player.hp > 40 then return false end

    Aware:displayMessage("Survival Instincts - Emergency", "Red", 1)
    return SurvivalInstincts:Cast()
end

local function Barkskin()
    if player:Buff(buffs.barkskin) then return false end
    if player.hp > 60 then return false end

    return Barkskin:Cast()
end

local function FrenziedRegeneration()
    if not player:Buff(buffs.bearForm) then return false end
    if player:Buff(buffs.frenziedRegeneration) then return false end
    if player.hp > 70 then return false end

    return FrenziedRegeneration:Cast()
end

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain Cat Form
    if CatForm() then return true end

    -- Energy management
    if TigersFury() then return true end

    -- Maintain Savage Roar
    if needsSavageRoar() and gameState.comboPoints >= 1 then
        if SavageRoar() then return true end
    end

    -- Apply Rake
    if needsRake() then
        if Rake() then return true end
    end

    -- Apply Faerie Fire for armor reduction
    if FaerieFire() then return true end

    -- Spend combo points on Rip
    if gameState.comboPoints >= 4 and needsRip() then
        if Rip() then return true end
    end

    -- Execute with Ferocious Bite
    if target.hp <= 25 or gameState.comboPoints >= 5 then
        if FerociousBite() then return true end
    end

    -- Build combo points
    if gameState.comboPoints < 5 then
        -- Prefer Shred from behind, Mangle from front
        if Shred() then return true end
        if Mangle() then return true end
    end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain Cat Form
    if CatForm() then return true end

    -- Energy management
    if TigersFury() then return true end

    -- Maintain Savage Roar
    if needsSavageRoar() and gameState.comboPoints >= 1 then
        if SavageRoar() then return true end
    end

    -- Apply Thrash for AoE DoT
    if needsThrash() then
        if Thrash() then return true end
    end

    -- Apply Rake to primary target
    if needsRake() then
        if Rake() then return true end
    end

    -- Spend combo points on Rip for primary target
    if gameState.comboPoints >= 4 and needsRip() then
        if Rip() then return true end
    end

    -- AoE damage with Swipe
    if Swipe() then return true end

    -- Build combo points with Mangle (easier to hit multiple targets)
    if gameState.comboPoints < 5 then
        if Mangle() then return true end
    end

    return false
end

-- PvP Rotation
local function pvpRotation()
    updateGameState()

    -- Maintain Cat Form
    if CatForm() then return true end

    -- Interrupt priority
    if SkullBash() then return true end

    -- Energy management
    if TigersFury() then return true end

    -- Maintain Savage Roar
    if needsSavageRoar() and gameState.comboPoints >= 1 then
        if SavageRoar() then return true end
    end

    -- Apply Rake for pressure
    if needsRake() then
        if Rake() then return true end
    end

    -- Apply Faerie Fire for armor reduction and visibility
    if FaerieFire() then return true end

    -- Spend combo points strategically
    if gameState.comboPoints >= 4 then
        -- Use Rip for sustained pressure
        if needsRip() then
            if Rip() then return true end
        end

        -- Use Ferocious Bite for burst
        if target.hp <= 35 or gameState.comboPoints >= 5 then
            if FerociousBite() then return true end
        end
    end

    -- Build combo points
    if gameState.comboPoints < 5 then
        if Shred() then return true end
        if Mangle() then return true end
    end

    return false
end

-- TimeToAdds rotation for burst preparation
local function timeToAddsRotation()
    updateGameState()

    -- Preparation phase (15-10 seconds)
    if gameState.timeToAdds < 15000 and gameState.timeToAdds > 10000 then
        Aware:displayMessage("Adds Soon - Prepare Burst", "Yellow", 1)

        -- Ensure we have energy and combo points ready
        if TigersFury() then return true end

        -- Build combo points for burst
        if gameState.comboPoints < 5 then
            if Shred() then return true end
            if Mangle() then return true end
        end

        -- Maintain Savage Roar
        if needsSavageRoar() and gameState.comboPoints >= 1 then
            if SavageRoar() then return true end
        end
    end

    -- Pre-burst phase (10-3 seconds)
    if gameState.timeToAdds < 10000 and gameState.timeToAdds > 3000 then
        Aware:displayMessage("Adds Incoming - Pre-Burst", "Orange", 1)

        -- Activate cooldowns
        if Berserk() then return true end
        if NaturesVigil() then return true end

        -- Ensure DoTs are up on current target
        if needsRake() then
            if Rake() then return true end
        end

        if gameState.comboPoints >= 4 and needsRip() then
            if Rip() then return true end
        end
    end

    -- Immediate burst phase (3-0 seconds)
    if gameState.timeToAdds < 3000 and gameState.timeToAdds > 0 then
        Aware:displayMessage("Adds Incoming - Burst Ready!", "Red", 1)

        -- Activate all burst cooldowns
        if shouldBurst() then
            if Berserk() then return true end
            if IncarnationKingOfBeasts() then return true end
            if ForceOfNature() then return true end
        end

        -- Position for AoE
        if Thrash() then return true end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        Aware:displayMessage("Adds Phase - AoE Burst", "Green", 1)
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency responses
    if player.hp <= 20 then
        SurvivalInstincts()
        FrenziedRegeneration()
    end

    if player.hp <= 50 then
        Barkskin()
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 20000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    if not MakuluFramework.start() then
        enhancedMainRotation()
    end
    return MakuluFramework.endFunc()
end

-- Enhanced A[3] function for advanced rotation with burst and cooldowns
A[3] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    updateGameState()

    -- Enhanced defensive priority
    if player.hp <= 25 then
        if SurvivalInstincts() then return MakuluFramework.endFunc() end
        if Barkskin() then return MakuluFramework.endFunc() end
    end

    if player.hp <= 40 then
        if FrenziedRegeneration() then return MakuluFramework.endFunc() end
    end

    -- Form management
    if not inCatForm() and canShapeshift() then
        if CatForm() then return MakuluFramework.endFunc() end
    end

    if target.exists and target.alive then
        -- Interrupt priority
        if SkullBash() then return MakuluFramework.endFunc() end

        -- PvP specific abilities
        if gameState.isPvP then
            if Action.Zone ~= "arena" then
                -- Use crowd control in battlegrounds
                if target.distance <= 30 and not target:DeBuff(debuffs.cyclone) then
                    -- Cyclone logic would go here
                end
            end
        end

        -- Burst phase
        if shouldBurst() then
            TigersFury()
            Berserk()
            IncarnationKingOfBeasts()
            NaturesVigil()

            -- Trinket usage during burst
            local damagePotion = Action.GetToggle(2, "damagePotion")
            if damagePotion and player:Buff(buffs.berserk) then
                -- Use damage potions during burst cooldowns
            end
        end

        -- Core rotation
        if inCatForm() then
            -- Maintain Savage Roar
            if needsSavageRoar() and gameState.comboPoints >= 1 then
                SavageRoar()
            end

            -- Apply DoTs
            if needsRake() then
                Rake()
            end

            -- Spend combo points
            if gameState.comboPoints >= 4 then
                if needsRip() then
                    Rip()
                else
                    FerociousBite()
                end
            end

            -- Build combo points
            if gameState.comboPoints < 5 then
                Shred()
                Mangle()
            end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 20000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return MakuluFramework.endFunc()
end

-- Arena functions A[6], A[7], A[8] for PvP
A[6] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena1)
        partyRotation(ConstUnit.party1)
    end

    return MakuluFramework.endFunc()
end

A[7] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena2)
        partyRotation(ConstUnit.party2)
    end

    return MakuluFramework.endFunc()
end

A[8] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena3)
        partyRotation(ConstUnit.party3)
    end

    return MakuluFramework.endFunc()
end

-- Arena-specific callback functions for MoP Feral Druid
SkullBash:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 13 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Rake:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.energy < 35 then return end

    -- Apply Rake for pressure
    if not enemy:DeBuff(debuffs.rake) or enemy:DeBuffRemains(debuffs.rake) < 3000 then
        Aware:displayMessage("Rake - DoT Application", "Yellow", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

Rip:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.comboPoints < 1 then return end

    -- Use Rip for sustained pressure
    if not enemy:DeBuff(debuffs.rip) or enemy:DeBuffRemains(debuffs.rip) < 3000 then
        Aware:displayMessage("Rip - Finishing Move", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

FerociousBite:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.comboPoints < 1 then return end
    if gameState.energy < 25 then return end

    -- Priority on low health targets
    if enemy.hp <= 35 or gameState.comboPoints >= 5 then
        Aware:displayMessage("Ferocious Bite - Execute", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

Cyclone:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:DeBuff(debuffs.cyclone) then return end
    if not inCatForm() then return end

    -- Use for crowd control
    Aware:displayMessage("Cyclone - CC", "Blue", 1)
    return spell:Cast(enemy)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    SkullBash("arena", enemy)

    -- Crowd control
    Cyclone("arena", enemy)

    -- DoT application
    Rake("arena", enemy)

    -- Finishing moves
    Rip("arena", enemy)
    FerociousBite("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end
    if friendly.hp > 70 then return end

    -- Emergency healing with Predatory Swiftness
    if player:Buff(buffs.predatorySwiftness) then
        if friendly.hp <= 30 then
            HealingTouch("party", friendly)
        elseif friendly.hp <= 50 then
            Regrowth("party", friendly)
        end
    end

    -- HoT maintenance
    if not friendly:Buff(buffs.rejuvenation) then
        Rejuvenation("party", friendly)
    end
end

local arenaRotation = enhancedArenaRotation
local partyRotation = enhancedPartyRotation
