-- APL UPDATE MoP Guardian Druid
-- Mists of Pandaria Guardian Druid Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Feral spec (<PERSON> was part of Feral in MoP - talent tree 2)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 2 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Guardian Druid Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Guardian Core Abilities
    BearForm = { ID = 5487, MAKULU_INFO = { targeted = false } },
    CatForm = { ID = 768, MAKULU_INFO = { targeted = false } },
    
    -- MoP Guardian Tank Abilities
    Mangle = { ID = 33917, MAKULU_INFO = { damageType = "physical" } },
    Lacerate = { ID = 33745, MAKULU_INFO = { damageType = "physical" } },
    Thrash = { ID = 77758, MAKULU_INFO = { damageType = "physical" } },
    Swipe = { ID = 213764, MAKULU_INFO = { damageType = "physical" } },
    Maul = { ID = 6807, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP Guardian Defensive Abilities
    SavageDefense = { ID = 62606, MAKULU_INFO = { targeted = false } },
    FrenziedRegeneration = { ID = 22842, MAKULU_INFO = { heal = true, targeted = false } },
    SurvivalInstincts = { ID = 61336, MAKULU_INFO = { targeted = false } },
    Barkskin = { ID = 22812, MAKULU_INFO = { targeted = false } },
    
    -- MoP Guardian Utility
    FaerieFire = { ID = 770, MAKULU_INFO = { damageType = "nature" } },
    Growl = { ID = 6795, MAKULU_INFO = { ignoreCasting = true } },
    ChallengingRoar = { ID = 5209, MAKULU_INFO = { targeted = false } },
    Enrage = { ID = 5229, MAKULU_INFO = { targeted = false } },
    
    -- MoP Guardian Interrupts
    SkullBash = { ID = 106839, MAKULU_INFO = { damageType = "physical", ignoreCasting = true } },
    
    -- MoP Guardian Movement
    Dash = { ID = 1850, MAKULU_INFO = { targeted = false } },
    Stampede = { ID = 81022, MAKULU_INFO = { targeted = false } },
    
    -- MoP Healing Abilities
    HealingTouch = { ID = 5185, MAKULU_INFO = { heal = true, castTime = 2500 } },
    Rejuvenation = { ID = 774, MAKULU_INFO = { heal = true } },
    Regrowth = { ID = 8936, MAKULU_INFO = { heal = true, castTime = 2000 } },
    
    -- MoP Crowd Control
    Cyclone = { ID = 33786, MAKULU_INFO = { castTime = 1700 } },
    EntanglingRoots = { ID = 339, MAKULU_INFO = { castTime = 1700 } },
    Hibernate = { ID = 2637, MAKULU_INFO = { castTime = 1500 } },
    
    -- MoP Talents
    ForceOfNature = { ID = 106737, MAKULU_INFO = { targeted = false } },
    IncarnationGuardianOfUrsoc = { ID = 102558, MAKULU_INFO = { targeted = false } },
    HeartOfTheWild = { ID = 108238, MAKULU_INFO = { targeted = false } },
    NaturesVigil = { ID = 124974, MAKULU_INFO = { targeted = false } },
    Renewal = { ID = 108238, MAKULU_INFO = { heal = true, targeted = false } },
    
    -- MoP Utility
    MarkOfTheWild = { ID = 1126, MAKULU_INFO = { targeted = false } },
    Rebirth = { ID = 20484, MAKULU_INFO = { castTime = 2000 } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 106839, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 33786, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
    local result = {}
    for k, v in pairs(A) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_DRUID_GUARDIAN] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Guardian Druid Buffs
local buffs = {
    bearForm = 5487,
    catForm = 768,
    savageDefense = 62606,
    frenziedRegeneration = 22842,
    survivalInstincts = 61336,
    barkskin = 22812,
    enrage = 5229,
    incarnationGuardianOfUrsoc = 102558,
    heartOfTheWild = 108238,
    naturesVigil = 124974,
    markOfTheWild = 1126,
    dash = 1850,
    stampede = 81022,
    predatorySwiftness = 69369,
    clearcasting = 135700,
    rejuvenation = 774,
    regrowth = 8936,
    healingTouch = 5185,
    forceOfNature = 106737,
    renewal = 108238,
}

-- MoP Guardian Druid Debuffs
local debuffs = {
    lacerate = 33745,
    thrash = 77758,
    faerieFire = 770,
    cyclone = 33786,
    entanglingRoots = 339,
    hibernate = 2637,
    mangle = 33917,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    rage = 0,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
    isTanking = false,
    threatLevel = 0,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.rage = player.rage or 0
    gameState.channeling = player.channeling
    gameState.isPvP = Action.Zone == "arena" or Action.Zone == "pvp"
    gameState.isTanking = player.isTanking or false
    gameState.threatLevel = target.threat or 0
    
    -- TimeToAdds calculation (simplified for MoP)
    if gameState.inCombat then
        gameState.timeToAdds = PVE:TimeToAdds() or 999
    else
        gameState.timeToAdds = 999
    end
end

-- Utility functions
local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function shouldBurst()
    return gameState.timeToAdds < 15000 or (target.exists and target.hp <= 35)
end

local function needsLacerate()
    return not target:DeBuff(debuffs.lacerate) or target:DeBuffRemains(debuffs.lacerate) < 3000
end

local function needsThrash()
    return not target:DeBuff(debuffs.thrash) or target:DeBuffRemains(debuffs.thrash) < 3000
end

local function inBearForm()
    return player:Buff(buffs.bearForm)
end

local function canShapeshift()
    return not gameState.channeling and not player.moving
end

local function needsThreat()
    return gameState.threatLevel < 80 and gameState.isTanking
end

local function isLowHealth()
    return player.hp <= 60
end

local function isCriticalHealth()
    return player.hp <= 30
end

-- Core ability functions
local function BearForm()
    if player:Buff(buffs.bearForm) then return false end
    if not canShapeshift() then return false end
    
    return BearForm:Cast()
end

local function SavageDefense()
    if not inBearForm() then return false end
    if player:Buff(buffs.savageDefense) then return false end
    if gameState.rage < 60 then return false end
    
    Aware:displayMessage("Savage Defense - Damage Reduction", "Blue", 1)
    return SavageDefense:Cast()
end

local function FrenziedRegeneration()
    if not inBearForm() then return false end
    if player:Buff(buffs.frenziedRegeneration) then return false end
    if not isLowHealth() then return false end
    
    Aware:displayMessage("Frenzied Regeneration - Healing", "Green", 1)
    return FrenziedRegeneration:Cast()
end

local function SurvivalInstincts()
    if player:Buff(buffs.survivalInstincts) then return false end
    if not isCriticalHealth() then return false end
    
    Aware:displayMessage("Survival Instincts - Emergency", "Red", 1)
    return SurvivalInstincts:Cast()
end

local function Barkskin()
    if player:Buff(buffs.barkskin) then return false end
    if not isLowHealth() then return false end
    
    return Barkskin:Cast()
end

local function Mangle()
    if not inBearForm() then return false end
    if not target.exists or target.distance > 5 then return false end
    if gameState.rage < 15 then return false end
    
    return Mangle:Cast(target)
end

local function Lacerate()
    if not inBearForm() then return false end
    if not target.exists or target.distance > 5 then return false end
    if gameState.rage < 15 then return false end
    if not needsLacerate() then return false end
    
    return Lacerate:Cast(target)
end

local function Thrash()
    if not inBearForm() then return false end
    if not target.exists or target.distance > 8 then return false end
    if gameState.rage < 25 then return false end
    if not shouldAoE() then return false end
    if not needsThrash() then return false end
    
    return Thrash:Cast()
end

local function Swipe()
    if not inBearForm() then return false end
    if not target.exists or target.distance > 8 then return false end
    if gameState.rage < 20 then return false end
    if not shouldAoE() then return false end
    
    return Swipe:Cast()
end

local function Maul()
    if not inBearForm() then return false end
    if not target.exists or target.distance > 5 then return false end
    if gameState.rage < 30 then return false end
    
    -- Use when rage is high or need threat
    if gameState.rage >= 80 or needsThreat() then
        return Maul:Cast(target)
    end
    
    return false
end

local function FaerieFire()
    if not target.exists or target.distance > 30 then return false end
    if target:DeBuff(debuffs.faerieFire) then return false end
    
    return FaerieFire:Cast(target)
end

local function Growl()
    if not target.exists or target.distance > 30 then return false end
    if not needsThreat() then return false end
    
    Aware:displayMessage("Growl - Taunt", "Red", 1)
    return Growl:Cast(target)
end

local function SkullBash()
    if not target.exists or target.distance > 13 then return false end
    if not target.casting then return false end
    if not target:IsInterruptible() then return false end

    Aware:displayMessage("Skull Bash - Interrupt", "Red", 1)
    return SkullBash:Cast(target)
end

-- Cooldown abilities
local function Enrage()
    if not inBearForm() then return false end
    if player:Buff(buffs.enrage) then return false end
    if gameState.rage >= 60 then return false end -- Don't use when rage is high

    return Enrage:Cast()
end

local function IncarnationGuardianOfUrsoc()
    if player:Buff(buffs.incarnationGuardianOfUrsoc) then return false end
    if not shouldBurst() then return false end

    Aware:displayMessage("Incarnation - Guardian of Ursoc", "Purple", 1)
    return IncarnationGuardianOfUrsoc:Cast()
end

local function ForceOfNature()
    if not shouldBurst() then return false end

    Aware:displayMessage("Force of Nature - Treants", "Green", 1)
    return ForceOfNature:Cast()
end

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain Bear Form
    if BearForm() then return true end

    -- Defensive priority
    if isCriticalHealth() then
        if SurvivalInstincts() then return true end
        if FrenziedRegeneration() then return true end
    end

    if isLowHealth() then
        if Barkskin() then return true end
        if SavageDefense() then return true end
    end

    -- Threat management
    if needsThreat() then
        if Growl() then return true end
    end

    -- Rage generation
    if gameState.rage < 40 then
        if Enrage() then return true end
    end

    -- Apply Faerie Fire for armor reduction
    if FaerieFire() then return true end

    -- Apply Lacerate for DoT and threat
    if needsLacerate() then
        if Lacerate() then return true end
    end

    -- Mangle for threat and damage
    if Mangle() then return true end

    -- Maul for high rage dump or threat
    if Maul() then return true end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain Bear Form
    if BearForm() then return true end

    -- Defensive priority
    if isCriticalHealth() then
        if SurvivalInstincts() then return true end
        if FrenziedRegeneration() then return true end
    end

    if isLowHealth() then
        if Barkskin() then return true end
        if SavageDefense() then return true end
    end

    -- Rage generation
    if gameState.rage < 40 then
        if Enrage() then return true end
    end

    -- Apply Thrash for AoE DoT
    if needsThrash() then
        if Thrash() then return true end
    end

    -- Apply Lacerate to primary target
    if needsLacerate() then
        if Lacerate() then return true end
    end

    -- Swipe for AoE damage
    if Swipe() then return true end

    -- Mangle for threat
    if Mangle() then return true end

    -- Maul for rage dump
    if Maul() then return true end

    return false
end

-- PvP Rotation
local function pvpRotation()
    updateGameState()

    -- Maintain Bear Form for survivability
    if BearForm() then return true end

    -- Interrupt priority
    if SkullBash() then return true end

    -- Defensive priority
    if isCriticalHealth() then
        if SurvivalInstincts() then return true end
        if FrenziedRegeneration() then return true end
    end

    if isLowHealth() then
        if Barkskin() then return true end
        if SavageDefense() then return true end
    end

    -- Rage generation
    if gameState.rage < 40 then
        if Enrage() then return true end
    end

    -- Apply Faerie Fire for armor reduction and visibility
    if FaerieFire() then return true end

    -- Apply pressure with DoTs
    if needsLacerate() then
        if Lacerate() then return true end
    end

    -- Damage abilities
    if Mangle() then return true end
    if Maul() then return true end

    return false
end

-- TimeToAdds rotation for burst preparation
local function timeToAddsRotation()
    updateGameState()

    -- Preparation phase (15-10 seconds)
    if gameState.timeToAdds < 15000 and gameState.timeToAdds > 10000 then
        Aware:displayMessage("Adds Soon - Prepare Burst", "Yellow", 1)

        -- Ensure we have rage ready
        if Enrage() then return true end

        -- Apply DoTs
        if needsLacerate() then
            if Lacerate() then return true end
        end

        -- Maintain defensive buffs
        if SavageDefense() then return true end
    end

    -- Pre-burst phase (10-3 seconds)
    if gameState.timeToAdds < 10000 and gameState.timeToAdds > 3000 then
        Aware:displayMessage("Adds Incoming - Pre-Burst", "Orange", 1)

        -- Activate cooldowns
        if IncarnationGuardianOfUrsoc() then return true end
        if ForceOfNature() then return true end

        -- Prepare for AoE
        if needsThrash() then
            if Thrash() then return true end
        end
    end

    -- Immediate burst phase (3-0 seconds)
    if gameState.timeToAdds < 3000 and gameState.timeToAdds > 0 then
        Aware:displayMessage("Adds Incoming - Burst Ready!", "Red", 1)

        -- Activate all burst cooldowns
        if shouldBurst() then
            if IncarnationGuardianOfUrsoc() then return true end
            if ForceOfNature() then return true end
        end

        -- Position for AoE tanking
        if Thrash() then return true end
        if Swipe() then return true end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        Aware:displayMessage("Adds Phase - AoE Tank", "Green", 1)
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency responses
    if isCriticalHealth() then
        SurvivalInstincts()
        FrenziedRegeneration()
    end

    if isLowHealth() then
        Barkskin()
        SavageDefense()
    end

    -- Form management
    if not inBearForm() and canShapeshift() then
        BearForm()
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 20000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    if not MakuluFramework.start() then
        enhancedMainRotation()
    end
    return MakuluFramework.endFunc()
end

-- Enhanced A[3] function for advanced rotation with burst and cooldowns
A[3] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    updateGameState()

    -- Enhanced defensive priority
    if isCriticalHealth() then
        if SurvivalInstincts() then return MakuluFramework.endFunc() end
        if FrenziedRegeneration() then return MakuluFramework.endFunc() end
        if Barkskin() then return MakuluFramework.endFunc() end
    end

    if isLowHealth() then
        if SavageDefense() then return MakuluFramework.endFunc() end
        if FrenziedRegeneration() then return MakuluFramework.endFunc() end
    end

    -- Form management
    if not inBearForm() and canShapeshift() then
        if BearForm() then return MakuluFramework.endFunc() end
    end

    if target.exists and target.alive then
        -- Interrupt priority
        if SkullBash() then return MakuluFramework.endFunc() end

        -- Threat management
        if needsThreat() then
            if Growl() then return MakuluFramework.endFunc() end
        end

        -- PvP specific abilities
        if gameState.isPvP then
            if Action.Zone ~= "arena" then
                -- Use crowd control in battlegrounds
                if target.distance <= 30 and not target:DeBuff(debuffs.cyclone) then
                    -- Cyclone logic would go here
                end
            end
        end

        -- Burst phase
        if shouldBurst() then
            IncarnationGuardianOfUrsoc()
            ForceOfNature()

            -- Trinket usage during burst
            local damagePotion = Action.GetToggle(2, "damagePotion")
            if damagePotion and player:Buff(buffs.incarnationGuardianOfUrsoc) then
                -- Use damage potions during burst cooldowns
            end
        end

        -- Core rotation
        if inBearForm() then
            -- Rage management
            if gameState.rage < 40 then
                Enrage()
            end

            -- Apply DoTs and debuffs
            if needsLacerate() then
                Lacerate()
            end

            if shouldAoE() and needsThrash() then
                Thrash()
            end

            -- Apply armor reduction
            FaerieFire()

            -- Damage abilities
            if shouldAoE() then
                Swipe()
            end

            Mangle()
            Maul()
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 20000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return MakuluFramework.endFunc()
end

-- Arena functions A[6], A[7], A[8] for PvP
A[6] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena1)
        partyRotation(ConstUnit.party1)
    end

    return MakuluFramework.endFunc()
end

A[7] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena2)
        partyRotation(ConstUnit.party2)
    end

    return MakuluFramework.endFunc()
end

A[8] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena3)
        partyRotation(ConstUnit.party3)
    end

    return MakuluFramework.endFunc()
end

-- Arena-specific callback functions for MoP Guardian Druid
SkullBash:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 13 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Growl:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end

    -- Use for peeling and threat
    Aware:displayMessage("Growl - Taunt/Peel", "Red", 1)
    return spell:Cast(enemy)
end)

Mangle:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.rage < 15 then return end

    -- Primary damage ability
    return spell:Cast(enemy)
end)

Lacerate:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.rage < 15 then return end

    -- Apply DoT for pressure
    if not enemy:DeBuff(debuffs.lacerate) or enemy:DeBuffRemains(debuffs.lacerate) < 3000 then
        Aware:displayMessage("Lacerate - DoT Application", "Orange", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

Maul:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.rage < 30 then return end

    -- Use for burst damage
    if gameState.rage >= 80 then
        Aware:displayMessage("Maul - Rage Dump", "Yellow", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

FaerieFire:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:DeBuff(debuffs.faerieFire) then return end

    -- Apply armor reduction and prevent stealth
    Aware:displayMessage("Faerie Fire - Armor Reduction", "Purple", 1)
    return spell:Cast(enemy)
end)

Cyclone:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:DeBuff(debuffs.cyclone) then return end
    if inBearForm() then return end -- Need to be out of bear form

    -- Use for crowd control
    Aware:displayMessage("Cyclone - CC", "Blue", 1)
    return spell:Cast(enemy)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    SkullBash("arena", enemy)

    -- Threat/peel abilities
    Growl("arena", enemy)

    -- Crowd control (when not in bear form)
    if not inBearForm() then
        Cyclone("arena", enemy)
    end

    -- Debuff application
    FaerieFire("arena", enemy)
    Lacerate("arena", enemy)

    -- Damage abilities
    Mangle("arena", enemy)
    Maul("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end
    if friendly.hp > 70 then return end

    -- Emergency healing with Predatory Swiftness
    if player:Buff(buffs.predatorySwiftness) then
        if friendly.hp <= 30 then
            HealingTouch("party", friendly)
        elseif friendly.hp <= 50 then
            Regrowth("party", friendly)
        end
    end

    -- HoT maintenance
    if not friendly:Buff(buffs.rejuvenation) then
        Rejuvenation("party", friendly)
    end
end

local arenaRotation = enhancedArenaRotation
local partyRotation = enhancedPartyRotation
