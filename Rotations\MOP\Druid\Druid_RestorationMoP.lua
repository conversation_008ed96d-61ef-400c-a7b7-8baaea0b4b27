-- APL UPDATE MoP Restoration Druid
-- Mists of Pandaria Restoration Druid Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Restoration spec (talent tree 3 for Druid in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 3 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Restoration Druid Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Restoration Core Abilities
    Rejuvenation = { ID = 774, MAKULU_INFO = { heal = true } },
    Regrowth = { ID = 8936, MAKULU_INFO = { heal = true, castTime = 2000 } },
    Lifebloom = { ID = 33763, MAKULU_INFO = { heal = true } },
    WildGrowth = { ID = 48438, MAKULU_INFO = { heal = true, castTime = 1500 } },
    Swiftmend = { ID = 18562, MAKULU_INFO = { heal = true } },
    HealingTouch = { ID = 5185, MAKULU_INFO = { heal = true, castTime = 2500 } },
    Nourish = { ID = 50464, MAKULU_INFO = { heal = true, castTime = 1500 } },
    
    -- MoP Restoration Specific
    TreeOfLife = { ID = 33891, MAKULU_INFO = { targeted = false } },
    NaturesSwiftness = { ID = 17116, MAKULU_INFO = { targeted = false } },
    Tranquility = { ID = 740, MAKULU_INFO = { heal = true, channeled = true } },
    Innervate = { ID = 29166, MAKULU_INFO = { targeted = false } },
    
    -- MoP Utility Spells
    NaturesCure = { ID = 88423, MAKULU_INFO = { targeted = true } },
    RemoveCurse = { ID = 2782, MAKULU_INFO = { targeted = true } },
    Rebirth = { ID = 20484, MAKULU_INFO = { castTime = 2000 } },
    
    -- MoP Damage Spells
    Wrath = { ID = 5176, MAKULU_INFO = { damageType = "nature", castTime = 2500 } },
    Starfire = { ID = 2912, MAKULU_INFO = { damageType = "arcane", castTime = 3000 } },
    Moonfire = { ID = 8921, MAKULU_INFO = { damageType = "arcane" } },
    Sunfire = { ID = 93402, MAKULU_INFO = { damageType = "nature" } },
    Insect = { ID = 5570, MAKULU_INFO = { damageType = "nature" } },
    
    -- MoP Forms
    BearForm = { ID = 5487, MAKULU_INFO = { targeted = false } },
    CatForm = { ID = 768, MAKULU_INFO = { targeted = false } },
    TravelForm = { ID = 783, MAKULU_INFO = { targeted = false } },
    AquaticForm = { ID = 1066, MAKULU_INFO = { targeted = false } },
    
    -- MoP Cat Form Abilities
    Rake = { ID = 1822, MAKULU_INFO = { damageType = "physical" } },
    Rip = { ID = 1079, MAKULU_INFO = { damageType = "physical" } },
    FerociousBite = { ID = 22568, MAKULU_INFO = { damageType = "physical" } },
    Shred = { ID = 5221, MAKULU_INFO = { damageType = "physical" } },
    Claw = { ID = 1082, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP Bear Form Abilities
    Maul = { ID = 6807, MAKULU_INFO = { damageType = "physical" } },
    Swipe = { ID = 779, MAKULU_INFO = { damageType = "physical" } },
    Thrash = { ID = 77758, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP Defensive Abilities
    Barkskin = { ID = 22812, MAKULU_INFO = { targeted = false } },
    FrenziedRegeneration = { ID = 22842, MAKULU_INFO = { targeted = false } },
    SurvivalInstincts = { ID = 61336, MAKULU_INFO = { targeted = false } },
    
    -- MoP Movement and Utility
    Dash = { ID = 1850, MAKULU_INFO = { targeted = false } },
    Prowl = { ID = 5215, MAKULU_INFO = { targeted = false } },
    
    -- MoP CC and Utility
    Cyclone = { ID = 33786, MAKULU_INFO = { castTime = 1700 } },
    Hibernate = { ID = 2637, MAKULU_INFO = { castTime = 1500 } },
    EntanglingRoots = { ID = 339, MAKULU_INFO = { castTime = 1700 } },
    
    -- MoP Talents
    ForceOfNature = { ID = 106737, MAKULU_INFO = { targeted = false } },
    CenarionWard = { ID = 102351, MAKULU_INFO = { heal = true } },
    NaturesVigil = { ID = 124974, MAKULU_INFO = { targeted = false } },
    HeartOfTheWild = { ID = 108238, MAKULU_INFO = { targeted = false } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 78675, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 33786, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
    local result = {}
    for k, v in pairs(A) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_DRUID_RESTORATION] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Restoration Druid Buffs
local buffs = {
    rejuvenation = 774,
    regrowth = 8936,
    lifebloom = 33763,
    wildGrowth = 48438,
    treeOfLife = 33891,
    naturesSwiftness = 17116,
    tranquility = 740,
    innervate = 29166,
    barkskin = 22812,
    frenziedRegeneration = 22842,
    survivalInstincts = 61336,
    bearForm = 5487,
    catForm = 768,
    travelForm = 783,
    aquaticForm = 1066,
    dash = 1850,
    prowl = 5215,
    cenarionWard = 102351,
    naturesVigil = 124974,
    heartOfTheWild = 108238,
    shadowmeld = 58984,
    clearCasting = 16870,
}

-- MoP Restoration Druid Debuffs
local debuffs = {
    moonfire = 8921,
    sunfire = 93402,
    insect = 5570,
    rake = 1822,
    rip = 1079,
    cyclone = 33786,
    hibernate = 2637,
    entanglingRoots = 339,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    inTreeForm = false,
    naturesSwiftnessActive = false,
    clearCastingActive = false,
    injuredAllies = 0,
    criticalAllies = 0,
    inBurstPhase = false,
    currentForm = "humanoid",
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.inTreeForm = player:HasBuff(buffs.treeOfLife)
    gameState.naturesSwiftnessActive = player:HasBuff(buffs.naturesSwiftness)
    gameState.clearCastingActive = player:HasBuff(buffs.clearCasting)
    gameState.inBurstPhase = gameState.inTreeForm or player:HasBuff(buffs.naturesVigil)
    
    -- Determine current form
    if player:HasBuff(buffs.bearForm) then
        gameState.currentForm = "bear"
    elseif player:HasBuff(buffs.catForm) then
        gameState.currentForm = "cat"
    elseif player:HasBuff(buffs.travelForm) then
        gameState.currentForm = "travel"
    else
        gameState.currentForm = "humanoid"
    end
    
    -- Count injured allies
    gameState.injuredAllies = 0
    gameState.criticalAllies = 0
    
    if player.hp < 80 then
        gameState.injuredAllies = gameState.injuredAllies + 1
        if player.hp < 40 then
            gameState.criticalAllies = gameState.criticalAllies + 1
        end
    end
    
    for i = 1, 4 do
        local unit = MakUnit:new("party" .. i)
        if unit.exists and unit.hp < 80 then
            gameState.injuredAllies = gameState.injuredAllies + 1
            if unit.hp < 40 then
                gameState.criticalAllies = gameState.criticalAllies + 1
            end
        end
    end
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function shouldHeal()
    return gameState.injuredAllies > 0 or gameState.criticalAllies > 0
end

local function needsTreeForm()
    return shouldBurst() and gameState.criticalAllies >= 2
end

local function shouldUseNaturesSwiftness()
    return gameState.criticalAllies > 0 and not gameState.naturesSwiftnessActive
end

local function getHealingTarget()
    -- Priority: critical health > lowest health > player
    local criticalTarget = nil
    local lowestTarget = nil
    local lowestHealth = 100
    
    -- Check player first
    if player.hp < 40 then
        criticalTarget = player
    elseif player.hp < lowestHealth then
        lowestTarget = player
        lowestHealth = player.hp
    end
    
    -- Check party members
    for i = 1, 4 do
        local unit = MakUnit:new("party" .. i)
        if unit.exists then
            if unit.hp < 40 and not criticalTarget then
                criticalTarget = unit
            elseif unit.hp < lowestHealth then
                lowestTarget = unit
                lowestHealth = unit.hp
            end
        end
    end
    
    return criticalTarget or lowestTarget
end

local function getDamageTarget()
    if target.exists and target.alive and not target.friendly then
        return target
    end
    
    -- Find nearest enemy
    for i = 1, 3 do
        local unit = MakUnit:new("arena" .. i)
        if unit.exists and unit.alive and not unit.friendly then
            return unit
        end
    end
    
    return nil
end

-- Core healing abilities
Rejuvenation:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget:HasBuff(buffs.rejuvenation) then return end
    if healTarget.hp > 85 then return end

    return spell:Cast(healTarget)
end)

Regrowth:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 60 then return end
    if player.moving and not gameState.naturesSwiftnessActive and not gameState.clearCastingActive then return end

    return spell:Cast(healTarget)
end)

Lifebloom:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget:HasBuff(buffs.lifebloom) then return end
    if healTarget.hp > 70 then return end

    return spell:Cast(healTarget)
end)

WildGrowth:Callback(function(spell)
    if gameState.injuredAllies < 3 then return end
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if player.moving then return end

    return spell:Cast(healTarget)
end)

Swiftmend:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 40 then return end
    if not healTarget:HasBuff(buffs.rejuvenation) and not healTarget:HasBuff(buffs.regrowth) and not healTarget:HasBuff(buffs.wildGrowth) then return end

    return spell:Cast(healTarget)
end)

HealingTouch:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 50 then return end
    if player.moving and not gameState.naturesSwiftnessActive then return end

    return spell:Cast(healTarget)
end)

Nourish:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 70 then return end
    if player.moving then return end
    if not healTarget:HasBuff(buffs.rejuvenation) and not healTarget:HasBuff(buffs.regrowth) and not healTarget:HasBuff(buffs.lifebloom) then return end

    return spell:Cast(healTarget)
end)

-- Cooldowns
TreeOfLife:Callback(function(spell)
    if not needsTreeForm() then return end

    return spell:Cast(player)
end)

NaturesSwiftness:Callback(function(spell)
    if not shouldUseNaturesSwiftness() then return end

    return spell:Cast(player)
end)

Tranquility:Callback(function(spell)
    if gameState.criticalAllies < 3 then return end
    if player.moving then return end

    return spell:Cast(player)
end)

Innervate:Callback(function(spell)
    if gameState.mana > 50 then return end

    return spell:Cast(player)
end)

-- Utility abilities
NaturesCure:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if not healTarget:HasDispellableDebuff() then return end

    return spell:Cast(healTarget)
end)

RemoveCurse:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if not healTarget:HasDispellableDebuff() then return end

    return spell:Cast(healTarget)
end)

Rebirth:Callback(function(spell)
    -- Find dead party member
    for i = 1, 4 do
        local unit = MakUnit:new("party" .. i)
        if unit.exists and unit.dead then
            if player.moving then return end
            return spell:Cast(unit)
        end
    end
end)

-- Damage abilities
Wrath:Callback(function(spell)
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 30 then return end
    if player.moving then return end
    if shouldHeal() then return end

    return spell:Cast(damageTarget)
end)

Starfire:Callback(function(spell)
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 30 then return end
    if player.moving then return end
    if shouldHeal() then return end

    return spell:Cast(damageTarget)
end)

Moonfire:Callback(function(spell)
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 30 then return end
    if damageTarget:HasDeBuff(debuffs.moonfire) then return end
    if shouldHeal() then return end

    return spell:Cast(damageTarget)
end)

Sunfire:Callback(function(spell)
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 30 then return end
    if damageTarget:HasDeBuff(debuffs.sunfire) then return end
    if shouldHeal() then return end

    return spell:Cast(damageTarget)
end)

Insect:Callback(function(spell)
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 30 then return end
    if damageTarget:HasDeBuff(debuffs.insect) then return end
    if shouldHeal() then return end

    return spell:Cast(damageTarget)
end)

-- Form abilities
BearForm:Callback(function(spell)
    if gameState.currentForm == "bear" then return end
    if not gameState.isPvP then return end
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

CatForm:Callback(function(spell)
    if gameState.currentForm == "cat" then return end
    if not gameState.isPvP then return end
    if shouldHeal() then return end

    return spell:Cast(player)
end)

TravelForm:Callback(function(spell)
    if gameState.currentForm == "travel" then return end
    if gameState.inCombat then return end
    if not player.moving then return end

    return spell:Cast(player)
end)

-- Cat form abilities
Rake:Callback(function(spell)
    if gameState.currentForm ~= "cat" then return end
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 5 then return end
    if damageTarget:HasDeBuff(debuffs.rake) then return end

    return spell:Cast(damageTarget)
end)

Rip:Callback(function(spell)
    if gameState.currentForm ~= "cat" then return end
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 5 then return end
    if damageTarget:HasDeBuff(debuffs.rip) then return end
    if player.comboPoints < 1 then return end

    return spell:Cast(damageTarget)
end)

FerociousBite:Callback(function(spell)
    if gameState.currentForm ~= "cat" then return end
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 5 then return end
    if player.comboPoints < 1 then return end

    return spell:Cast(damageTarget)
end)

Shred:Callback(function(spell)
    if gameState.currentForm ~= "cat" then return end
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 5 then return end

    return spell:Cast(damageTarget)
end)

Claw:Callback(function(spell)
    if gameState.currentForm ~= "cat" then return end
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 5 then return end

    return spell:Cast(damageTarget)
end)

-- Bear form abilities
Maul:Callback(function(spell)
    if gameState.currentForm ~= "bear" then return end
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 5 then return end

    return spell:Cast(damageTarget)
end)

Swipe:Callback(function(spell)
    if gameState.currentForm ~= "bear" then return end
    if gameState.activeEnemies < 2 then return end

    return spell:Cast(player)
end)

Thrash:Callback(function(spell)
    if gameState.currentForm ~= "bear" then return end
    if gameState.activeEnemies < 2 then return end

    return spell:Cast(player)
end)

-- Defensive abilities
Barkskin:Callback(function(spell)
    if player:HasBuff(buffs.barkskin) then return end
    if player.hp > 60 then return end

    return spell:Cast(player)
end)

FrenziedRegeneration:Callback(function(spell)
    if gameState.currentForm ~= "bear" then return end
    if player:HasBuff(buffs.frenziedRegeneration) then return end
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

SurvivalInstincts:Callback(function(spell)
    if gameState.currentForm ~= "bear" and gameState.currentForm ~= "cat" then return end
    if player:HasBuff(buffs.survivalInstincts) then return end
    if player.hp > 30 then return end

    return spell:Cast(player)
end)

-- Movement
Dash:Callback(function(spell)
    if gameState.currentForm ~= "cat" then return end
    if player:HasBuff(buffs.dash) then return end
    if not player.moving then return end

    return spell:Cast(player)
end)

Prowl:Callback(function(spell)
    if gameState.currentForm ~= "cat" then return end
    if player:HasBuff(buffs.prowl) then return end
    if gameState.inCombat then return end

    return spell:Cast(player)
end)

-- CC abilities
Cyclone:Callback(function(spell)
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 20 then return end
    if not gameState.isPvP then return end
    if player.moving then return end

    return spell:Cast(damageTarget)
end)

Hibernate:Callback(function(spell)
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 30 then return end
    if not gameState.isPvP then return end
    if player.moving then return end

    return spell:Cast(damageTarget)
end)

EntanglingRoots:Callback(function(spell)
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 30 then return end
    if not gameState.isPvP then return end
    if player.moving then return end

    return spell:Cast(damageTarget)
end)

-- Talent abilities
ForceOfNature:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

CenarionWard:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget:HasBuff(buffs.cenarionWard) then return end
    if healTarget.hp > 70 then return end

    return spell:Cast(healTarget)
end)

NaturesVigil:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

HeartOfTheWild:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    local damageTarget = getDamageTarget()
    if not damageTarget then return end
    if damageTarget.distance > 5 then return end

    return spell:Cast(damageTarget)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.mana > 80 then return end

    return spell:Cast(player)
end)

-- PvE Healing Rotation
local function healingRotation()
    updateGameState()

    -- Emergency healing priority
    if gameState.criticalAllies > 0 then
        -- Nature's Swiftness for instant cast
        if NaturesSwiftness() then return true end

        -- Swiftmend for instant heal
        if Swiftmend() then return true end

        -- Regrowth for fast heal
        if Regrowth() then return true end

        -- Healing Touch with Nature's Swiftness
        if HealingTouch() then return true end
    end

    -- Group healing
    if gameState.injuredAllies >= 3 then
        -- Tree of Life for major healing
        if TreeOfLife() then return true end

        -- Tranquility for emergency group heal
        if Tranquility() then return true end

        -- Wild Growth for group HoT
        if WildGrowth() then return true end
    end

    -- Maintain HoTs
    if Rejuvenation() then return true end
    if Lifebloom() then return true end
    if CenarionWard() then return true end

    -- Efficient healing
    if Nourish() then return true end
    if Regrowth() then return true end
    if HealingTouch() then return true end

    return false
end

-- PvE Damage Rotation
local function damageRotation()
    updateGameState()

    -- Only DPS if no healing needed
    if shouldHeal() then return false end

    -- Apply DoTs
    if Moonfire() then return true end
    if Sunfire() then return true end
    if Insect() then return true end

    -- Nuke spells
    if Wrath() then return true end
    if Starfire() then return true end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 30 then
        if Barkskin() then return true end
        if BearForm() then return true end
        if FrenziedRegeneration() then return true end
        if SurvivalInstincts() then return true end
    end

    -- Healing priority
    if shouldHeal() then
        if healingRotation() then return true end
    end

    -- Utility and dispels
    if NaturesCure() then return true end
    if RemoveCurse() then return true end

    -- CC abilities
    if Cyclone() then return true end
    if Hibernate() then return true end
    if EntanglingRoots() then return true end

    -- Form-based combat
    if gameState.currentForm == "cat" then
        if Rake() then return true end
        if Rip() then return true end
        if FerociousBite() then return true end
        if Shred() then return true end
        if Claw() then return true end
    elseif gameState.currentForm == "bear" then
        if Maul() then return true end
        if Swipe() then return true end
        if Thrash() then return true end
    else
        -- Humanoid form damage
        if damageRotation() then return true end
    end

    return false
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Prepare mana
        if gameState.mana < 80 then
            if Innervate() then return true end
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                if TreeOfLife() then return true end
                if NaturesVigil() then return true end
                if ForceOfNature() then return true end
                if HeartOfTheWild() then return true end
            end
        end

        -- Pre-HoT the group
        if Rejuvenation() then return true end
        if Lifebloom() then return true end
        if CenarionWard() then return true end
    end

    -- During adds phase - prioritize healing
    if gameState.activeEnemies >= 3 then
        return healingRotation()
    else
        return healingRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency healing always takes priority
    if gameState.criticalAllies > 0 then
        return healingRotation()
    end

    -- Mana management
    if gameState.mana < 20 then
        if Innervate() then return true end
    end

    -- Dispel priority
    if NaturesCure() then return true end
    if RemoveCurse() then return true end

    -- Resurrection priority
    if Rebirth() then return true end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- PvE healing priority
    if shouldHeal() then
        return healingRotation()
    else
        return damageRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Mana: ", gameState.mana)
        MakPrint(3, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(4, "In Combat: ", gameState.inCombat)
        MakPrint(5, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(6, "Is PvP: ", gameState.isPvP)
        MakPrint(7, "In Tree Form: ", gameState.inTreeForm)
        MakPrint(8, "Nature's Swiftness: ", gameState.naturesSwiftnessActive)
        MakPrint(9, "Clearcasting: ", gameState.clearCastingActive)
        MakPrint(10, "Injured Allies: ", gameState.injuredAllies)
        MakPrint(11, "Critical Allies: ", gameState.criticalAllies)
        MakPrint(12, "Current Form: ", gameState.currentForm)
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if TreeOfLife:IsReady() and shouldBurst() then
            Aware:displayMessage("TREE OF LIFE READY", "Red", 1)
        end
        if NaturesSwiftness:IsReady() and shouldBurst() then
            Aware:displayMessage("NATURE'S SWIFTNESS READY", "Blue", 1)
        end
        if Tranquility:IsReady() and gameState.criticalAllies >= 3 then
            Aware:displayMessage("TRANQUILITY READY", "Green", 1)
        end
        if gameState.criticalAllies > 0 then
            Aware:displayMessage("CRITICAL ALLIES: " .. gameState.criticalAllies, "Red", 1)
        end
        if gameState.injuredAllies > 0 then
            Aware:displayMessage("INJURED ALLIES: " .. gameState.injuredAllies, "Yellow", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
        if gameState.inTreeForm then
            Aware:displayMessage("TREE OF LIFE ACTIVE", "Green", 1)
        end
        if gameState.naturesSwiftnessActive then
            Aware:displayMessage("NATURE'S SWIFTNESS ACTIVE", "Blue", 1)
        end
        if gameState.mana < 30 then
            Aware:displayMessage("LOW MANA", "Red", 1)
        end
        if gameState.clearCastingActive then
            Aware:displayMessage("CLEARCASTING ACTIVE", "Green", 1)
        end
        if needsTreeForm() then
            Aware:displayMessage("USE TREE OF LIFE", "Green", 1)
        end
        if shouldUseNaturesSwiftness() then
            Aware:displayMessage("USE NATURE'S SWIFTNESS", "Blue", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 15 then
        if Barkskin:IsReady() then return FrameworkEnd() end
        if BearForm:IsReady() then return FrameworkEnd() end
    end

    if player.hp <= 30 then
        if SurvivalInstincts:IsReady() then return FrameworkEnd() end
        if FrenziedRegeneration:IsReady() then return FrameworkEnd() end
    end

    -- Emergency healing priority
    if gameState.criticalAllies > 0 then
        if NaturesSwiftness() then return FrameworkEnd() end
        if Swiftmend() then return FrameworkEnd() end
        if Regrowth() then return FrameworkEnd() end
        if HealingTouch() then return FrameworkEnd() end
    end

    -- Group emergency
    if gameState.criticalAllies >= 3 then
        if TreeOfLife() then return FrameworkEnd() end
        if Tranquility() then return FrameworkEnd() end
    end

    -- Mana management
    if gameState.mana < 20 then
        if Innervate() then return FrameworkEnd() end
        if ArcaneTorrent() then return FrameworkEnd() end
    end

    -- Burst phase
    if shouldBurst() then
        if TreeOfLife() then return FrameworkEnd() end
        if NaturesVigil() then return FrameworkEnd() end
        if ForceOfNature() then return FrameworkEnd() end
        if HeartOfTheWild() then return FrameworkEnd() end

        -- Racial abilities during burst
        if BloodFury() then return FrameworkEnd() end
        if Berserking() then return FrameworkEnd() end
    end

    -- TimeToAdds preparation
    if gameState.timeToAdds < 10000 then
        timeToAddsRotation()
    else
        -- Enhanced rotation selection
        if gameState.isPvP then
            pvpRotation()
        elseif shouldHeal() then
            healingRotation()
        else
            damageRotation()
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaHealing(friendly)
    if not friendly.exists then return end

    -- Emergency healing priority
    if friendly.hp < 30 then
        NaturesSwiftness("arena")
        Swiftmend("arena", friendly)
        Regrowth("arena", friendly)
        HealingTouch("arena", friendly)
    elseif friendly.hp < 60 then
        Regrowth("arena", friendly)
        HealingTouch("arena", friendly)
    elseif friendly.hp < 80 then
        Rejuvenation("arena", friendly)
        Lifebloom("arena", friendly)
        Nourish("arena", friendly)
    end

    -- Dispel priority
    NaturesCure("arena", friendly)
    RemoveCurse("arena", friendly)

    -- Protective abilities
    CenarionWard("arena", friendly)
end

local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- CC abilities
    Cyclone("arena", enemy)
    Hibernate("arena", enemy)
    EntanglingRoots("arena", enemy)

    -- Burst damage
    if shouldBurst() then
        TreeOfLife("arena")
        NaturesVigil("arena")
        ForceOfNature("arena")
        HeartOfTheWild("arena")
    end

    -- Apply DoTs
    Moonfire("arena", enemy)
    Sunfire("arena", enemy)
    Insect("arena", enemy)

    -- Form-based combat
    if gameState.currentForm == "cat" then
        Rake("arena", enemy)
        Rip("arena", enemy)
        FerociousBite("arena", enemy)
        Shred("arena", enemy)
        Claw("arena", enemy)
    elseif gameState.currentForm == "bear" then
        Maul("arena", enemy)
        Swipe("arena", enemy)
        Thrash("arena", enemy)
    else
        Wrath("arena", enemy)
        Starfire("arena", enemy)
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedArenaHealing(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Restoration Druid
Rejuvenation:Callback("arena", function(spell, friendly)
    if not friendly.exists then return end
    if friendly:HasBuff(buffs.rejuvenation) then return end
    if friendly.hp > 85 then return end

    return spell:Cast(friendly)
end)

Regrowth:Callback("arena", function(spell, friendly)
    if not friendly.exists then return end
    if friendly.hp > 60 then return end

    -- Emergency healing in arena
    if friendly.hp < 40 then
        Aware:displayMessage("Emergency Regrowth", "Red", 1)
    end
    return spell:Cast(friendly)
end)

Lifebloom:Callback("arena", function(spell, friendly)
    if not friendly.exists then return end
    if friendly:HasBuff(buffs.lifebloom) then return end
    if friendly.hp > 70 then return end

    return spell:Cast(friendly)
end)

Swiftmend:Callback("arena", function(spell, friendly)
    if not friendly.exists then return end
    if friendly.hp > 40 then return end
    if not friendly:HasBuff(buffs.rejuvenation) and not friendly:HasBuff(buffs.regrowth) and not friendly:HasBuff(buffs.wildGrowth) then return end

    -- Instant heal in arena
    Aware:displayMessage("Swiftmend - Instant Heal", "Green", 1)
    return spell:Cast(friendly)
end)

HealingTouch:Callback("arena", function(spell, friendly)
    if not friendly.exists then return end
    if friendly.hp > 50 then return end

    -- Big heal in arena
    if friendly.hp < 30 then
        Aware:displayMessage("Healing Touch - Big Heal", "Blue", 1)
    end
    return spell:Cast(friendly)
end)

NaturesCure:Callback("arena", function(spell, friendly)
    if not friendly.exists then return end
    if not friendly:HasDispellableDebuff() then return end

    -- Dispel priority in arena
    Aware:displayMessage("Nature's Cure - Dispel", "Green", 1)
    return spell:Cast(friendly)
end)

CenarionWard:Callback("arena", function(spell, friendly)
    if not friendly.exists then return end
    if friendly:HasBuff(buffs.cenarionWard) then return end
    if friendly.hp > 70 then return end

    -- Protective ward in arena
    Aware:displayMessage("Cenarion Ward - Protection", "Blue", 1)
    return spell:Cast(friendly)
end)

-- Arena enemy callbacks
Cyclone:Callback("arena", function(spell, enemy)
    if not enemy.exists then return end
    if enemy.distance > 20 then return end
    if player.moving then return end

    -- CC in arena
    Aware:displayMessage("Cyclone - CC", "Purple", 1)
    return spell:Cast(enemy)
end)

Hibernate:Callback("arena", function(spell, enemy)
    if not enemy.exists then return end
    if enemy.distance > 30 then return end
    if player.moving then return end

    -- Beast CC in arena
    Aware:displayMessage("Hibernate - Beast CC", "Yellow", 1)
    return spell:Cast(enemy)
end)

EntanglingRoots:Callback("arena", function(spell, enemy)
    if not enemy.exists then return end
    if enemy.distance > 30 then return end
    if player.moving then return end

    -- Root in arena
    Aware:displayMessage("Entangling Roots - Root", "Brown", 1)
    return spell:Cast(enemy)
end)

-- Initialize
Action[ACTION_CONST_DRUID_RESTORATION] = A
