local Action = _G.Action

local A                = Action

local CONST                                                              = Action.Const

local ACTION_CONST_HUNTER_BEASTMASTERY                              = CONST.HUNTER_BEASTMASTERY
local ACTION_CONST_HUNTER_MARKSMANSHIP                              = CONST.HUNTER_MARKSMANSHIP
local ACTION_CONST_HUNTER_SURVIVAL                                   = CONST.HUNTER_SURVIVAL

LPH_ENCNUM = function(val) return val end

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "Makulu MoP v1.0.0 (7/29/2025)",
    -- Class settings
    [2] = {
        {
            {
                E = "Header",
                L = {
                    ANY = " ====== Makulu - MoP Hunter ====== ",
                },
            },
        },
        {
            { -- AOE
                E = "Checkbox", 
                DB = "AoE",
                DBV = true,
                L = { 
                    enUS = "Use AoE", 
                    ruRU = "Использовать AoE", 
                    frFR = "Utiliser l'AoE",
                }, 
                TT = { 
                    enUS = "Enable multiunits actions", 
                    ruRU = "Включает действия для нескольких целей", 
                    frFR = "Activer les actions multi-unités",
                }, 
                M = {},
            },
            { -- Auto Hunter's Mark
                E = "Checkbox", 
                DB = "autoMark",
                DBV = true,
                L = { 
                    ANY = "Auto Hunter's Mark", 
                }, 
                TT = { 
                    ANY = "Automatically apply Hunter's Mark to targets.", 
                }, 
                M = {},
            },
            { -- Pet Management
                E = "Checkbox", 
                DB = "petManagement",
                DBV = true,
                L = { 
                    ANY = "Auto Pet Management", 
                }, 
                TT = { 
                    ANY = "Automatically summon and manage pets based on situation."
                }, 
                M = {},
            },   
        },
        { -- Spacer
            
            {
                E = "LayoutSpace",
            },
        },
        { -- Potions
            { -- useDamagePotion
                E = "Checkbox", 
                DB = "damagePotion",
                DBV = true,
                L = { 
                    ANY = "Damage Potion"
                }, 
                TT = { 
                    ANY = "Use Damage Potion", 
                }, 
                M = {},
            },
            { -- potionBossOnly
                E = "Checkbox", 
                DB = "potionLustOnly",
                DBV = true,
                L = { 
                    ANY = "Damage Potion Bloodlust/TimeWarp Only", 
                }, 
                TT = { 
                    ANY = "Only use Damage Potion when any kind of Bloodlust/Warp active."
                }, 
                M = {},
            },
        },
        {
            { -- potionExhausted
                E = "Checkbox", 
                DB = "potionExhausted",
                DBV = true,
                L = { 
                    ANY = "Damage Potion With Exhaustion", 
                }, 
                TT = { 
                    ANY = "Use Damage Potion while Exhausted (can't use Bloodlust)."
                }, 
                M = {},
            },
            { -- potionExhaustedSlider
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,   
                Precision = 1,                         
                DB = "potionExhaustedSlider",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Exhaustion Time Remaining",
                },
                TT = { 
                    ANY = "Time in minutes left on the Exhaustion Debuff to consider using Damage Potion.", 
                },                     
                M = {},
            },
        },
        { -- LAYOUT SPACE   
            {
                E = "LayoutSpace",                                                                         
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Cooldowns",
                },
            },
        },
        {
            {
                E = "Dropdown",                                                         
                H = 20,
                OT = {
                    { text = "Bestial Wrath", value = 1 }, 
                    { text = "Rapid Fire", value = 2 },     
                    { text = "Stampede", value = 3 },
                    { text = "Dire Beast", value = 4 },
                    { text = "Lynx Rush", value = 5 },
                    { text = "Fervor", value = 6 },   
                },
                MULT = true,
                DB = "cooldownSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                    [5] = true,
                    [6] = true,
                },  
                L = { 
                    ANY = "Cooldown Abilities", 
                }, 
                TT = { 
                    ANY = "Select what abilities you want the rotation to obey the burst toggle.\nIf a spell is unchecked, it will be used even when burst is turned off!", 
                }, 
                M = {},                                    
            },  
        }, 
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { 
            {-- Burst Sensitivity
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "burstSens",
                DBV = 18,
                ONOFF = false,
                L = { 
                    ANY = "Burst Mode Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to use cooldowns. A lower number means cooldowns used closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },  
            {-- Hunter's Mark Refresh TTD
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "markRefresh",
                DBV = 12,
                ONOFF = false,
                L = { 
                    ANY = "Hunter's Mark Refresh Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to refresh Hunter's Mark. A lower number means Hunter's Mark refreshed closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- HUNTER HEADER
            {
                E = "Header",
                L = {
                    ANY = "INTERRUPTS",
                },
            },
        },
        {    
            { -- Automatic Interrupt
                E = "Checkbox", 
                DB = "AutoInterrupt",
                DBV = true,
                L = { 
                    ANY = "Switch Targets Interrupt",
                }, 
                TT = { 
                    ANY = "Automatically switches targets to interrupt.",
                }, 
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- HUNTER HEADER
            {
                E = "Header",
                L = {
                    ANY = "DEFENSIVES",
                },
            },
        },
        {
            { -- Deterrence HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "DeterrenceHP",
                DBV = 50,
                ONOFF = false,
                L = { 
                    ANY = "Deterrence HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Deterrence on yourself.", 
                },                     
                M = {},
            },    
            { -- Aspect of the Cheetah
                E = "Checkbox", 
                DB = "aspectCheetah",
                DBV = true,
                L = { 
                    ANY = "Auto Aspect of the Cheetah", 
                }, 
                TT = { 
                    ANY = "Automatically use Aspect of the Cheetah for movement.", 
                }, 
                M = {},
            },    
        },
        {
            {-- Mend Pet HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "mendPetHP",
                DBV = 60,
                ONOFF = false,
                L = { 
                    ANY = "Mend Pet HP (%)",
                },
                TT = { 
                    ANY = "Pet HP (%) to use Mend Pet for pet healing.", 
                },                     
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Deterrence", value = 1 },
                    { text = "Disengage", value = 2 },
                    { text = "Mend Pet", value = 3 },
                    { text = "Concussive Shot", value = 4 },
                },
                MULT = true,
                DB = "defensiveSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Defensive Reactions",
                },
                TT = {
                    ANY = "Select what spells to be used when reacting to incoming damage in dungeons.",
                },
                M = {},
            },
        },
        { -- Spacer

            {
                E = "LayoutSpace",
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Debug/Aware Options",
                },
            },
        },
        {
            { -- Debug
                E = "Checkbox",
                DB = "makDebug",
                DBV = false,
                L = {
                    ANY = "Enable debug options",
                },
                TT = {
                    ANY = "Show a box with various debug data.\nIt takes a couple of seconds to get rid of the box when you disable this.",
                },
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Pet Reminder", value = 1 },
                    { text = "Bestial Wrath Ready", value = 2 },
                    { text = "Rapid Fire Ready", value = 3 },
                    { text = "Focus Alert", value = 4 },
                },
                MULT = true,
                DB = "makAware",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Aware Text Alert Reminders",
                },
                TT = {
                    ANY = "Select what text alert reminders you would like.\nThese will appear in the center of your screen.",
                },
                M = {},
            },
        },
    },
}
