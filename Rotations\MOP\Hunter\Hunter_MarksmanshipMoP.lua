-- APL UPDATE MoP Marksmanship Hunter
-- Mists of Pandaria Marksmanship Hunter Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Marksmanship spec (talent tree 2 for <PERSON> in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 2 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Marksmanship Hunter Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Marksmanship Core Abilities
    AimedShot = { ID = 19434, MAKULU_INFO = { damageType = "physical", castTime = 2500 } },
    SteadyShot = { ID = 56641, MAKULU_INFO = { damageType = "physical", castTime = 2000 } },
    ArcaneShot = { ID = 3044, MAKULU_INFO = { damageType = "arcane" } },
    MultiShot = { ID = 2643, MAKULU_INFO = { damageType = "physical" } },
    KillShot = { ID = 53351, MAKULU_INFO = { damageType = "physical" } },
    ChimaeraShot = { ID = 53209, MAKULU_INFO = { damageType = "nature" } },
    ExplosiveShot = { ID = 53301, MAKULU_INFO = { damageType = "fire" } },
    
    -- MoP Hunter Utility
    HuntersMark = { ID = 1130, MAKULU_INFO = { targeted = true } },
    ConcussiveShot = { ID = 5116, MAKULU_INFO = { targeted = true } },
    Disengage = { ID = 781, MAKULU_INFO = { targeted = false } },
    FeignDeath = { ID = 5384, MAKULU_INFO = { targeted = false } },
    SilencingShot = { ID = 34490, MAKULU_INFO = { damageType = "physical", ignoreCasting = true } },
    
    -- MoP Traps
    ExplosiveTrap = { ID = 13813, MAKULU_INFO = { damageType = "fire" } },
    FreezingTrap = { ID = 1499, MAKULU_INFO = { targeted = true } },
    IceTrap = { ID = 13809, MAKULU_INFO = { targeted = false } },
    SnakeTrap = { ID = 34600, MAKULU_INFO = { targeted = false } },
    
    -- MoP Aspects
    AspectOfTheHawk = { ID = 13165, MAKULU_INFO = { targeted = false } },
    AspectOfTheCheetah = { ID = 5118, MAKULU_INFO = { targeted = false } },
    AspectOfThePack = { ID = 13159, MAKULU_INFO = { targeted = false } },
    
    -- MoP Pet Abilities (Marksmanship can have pets in MoP)
    CallPet = { ID = 883, MAKULU_INFO = { targeted = false } },
    CallPet1 = { ID = 883, MAKULU_INFO = { targeted = false } },
    CallPet2 = { ID = 83242, MAKULU_INFO = { targeted = false } },
    CallPet3 = { ID = 83243, MAKULU_INFO = { targeted = false } },
    DismissPet = { ID = 2641, MAKULU_INFO = { targeted = false } },
    MendPet = { ID = 136, MAKULU_INFO = { heal = true, targeted = false } },
    RevivePet = { ID = 982, MAKULU_INFO = { targeted = false } },
    
    -- MoP Marksmanship Specific
    TrueshotAura = { ID = 19506, MAKULU_INFO = { targeted = false } },
    ImprovedTracking = { ID = 19878, MAKULU_INFO = { targeted = false } },
    MasterMarksman = { ID = 34485, MAKULU_INFO = { targeted = false } },
    
    -- MoP Talents
    AMurderOfCrows = { ID = 131894, MAKULU_INFO = { damageType = "physical" } },
    GlaiveToss = { ID = 117050, MAKULU_INFO = { damageType = "physical" } },
    Stampede = { ID = 121818, MAKULU_INFO = { targeted = false } },
    DireBeast = { ID = 120679, MAKULU_INFO = { targeted = false } },
    ThrillOfTheHunt = { ID = 109306, MAKULU_INFO = { targeted = false } },
    Fervor = { ID = 82726, MAKULU_INFO = { targeted = false } },
    CarefulAim = { ID = 34482, MAKULU_INFO = { targeted = false } },
    
    -- MoP Cooldowns
    RapidFire = { ID = 3045, MAKULU_INFO = { targeted = false } },
    Readiness = { ID = 23989, MAKULU_INFO = { targeted = false } },
    
    -- MoP Defensive Abilities
    Deterrence = { ID = 19263, MAKULU_INFO = { targeted = false } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 34490, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 1499, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
    local result = {}
    for k, v in pairs(A) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_HUNTER_MARKSMANSHIP] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local pet = MakUnit:new("pet")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Marksmanship Hunter Buffs
local buffs = {
    aspectOfTheHawk = 13165,
    aspectOfTheCheetah = 5118,
    aspectOfThePack = 13159,
    huntersMark = 1130,
    trueshotAura = 19506,
    lockAndLoad = 56453,
    improvedTracking = 19878,
    masterMarksman = 34485,
    carefulAim = 34482,
    rapidFire = 3045,
    feignDeath = 5384,
    deterrence = 19263,
    shadowmeld = 58984,
    thrillOfTheHunt = 109306,
    fervor = 82726,
    readiness = 23989,
}

-- MoP Marksmanship Hunter Debuffs
local debuffs = {
    huntersMark = 1130,
    concussiveShot = 5116,
    freezingTrap = 1499,
    explosiveTrap = 13813,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    focus = 0,
    timeToAdds = 999,
    isPvP = false,
    executeRange = false,
    petExists = false,
    petHealth = 100,
    aimedShotCharges = 0,
    steadyShotCastTime = 2000,
    aimedShotCastTime = 2500,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.focus = player.focus or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.executeRange = target.exists and target.hp <= 20
    gameState.petExists = pet.exists
    gameState.petHealth = pet.exists and pet.hp or 0
    gameState.aimedShotCharges = AimedShot:GetCharges() or 0
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function needsAspectOfTheHawk()
    return not player:HasBuff(buffs.aspectOfTheHawk)
end

local function needsHuntersMark()
    return target.exists and not target:HasDeBuff(debuffs.huntersMark)
end

local function petNeedsHealing()
    return gameState.petExists and gameState.petHealth <= 50
end

local function shouldUseAimedShot()
    return not player.moving or player:HasBuff(buffs.lockAndLoad) or player:HasBuff(buffs.masterMarksman)
end

local function shouldUseSteadyShot()
    return gameState.focus < 80 or not AimedShot:IsReady()
end

-- Aspect management
AspectOfTheHawk:Callback(function(spell)
    if needsAspectOfTheHawk() then
        return spell:Cast(player)
    end
end)

AspectOfTheCheetah:Callback(function(spell)
    if not gameState.inCombat and player.moving then
        return spell:Cast(player)
    end
end)

TrueshotAura:Callback(function(spell)
    if not player:HasBuff(buffs.trueshotAura) then
        return spell:Cast(player)
    end
end)

-- Hunter's Mark application
HuntersMark:Callback(function(spell)
    if not target.exists then return end
    if target:HasDeBuff(debuffs.huntersMark) then return end
    if target.distance > 100 then return end

    return spell:Cast(target)
end)

-- Core Marksmanship abilities
AimedShot:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 40 then return end
    if not shouldUseAimedShot() then return end
    if gameState.focus < 50 then return end

    return spell:Cast(target)
end)

SteadyShot:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 40 then return end
    if not shouldUseSteadyShot() then return end

    return spell:Cast(target)
end)

ChimaeraShot:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 40 then return end
    if gameState.focus < 50 then return end

    return spell:Cast(target)
end)

ArcaneShot:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 35 then return end
    if gameState.focus < 25 then return end

    return spell:Cast(target)
end)

MultiShot:Callback(function(spell)
    if gameState.activeEnemies < 2 then return end
    if gameState.focus < 40 then return end

    return spell:Cast(target)
end)

KillShot:Callback(function(spell)
    if not target.exists then return end
    if not gameState.executeRange then return end
    if target.distance > 35 then return end

    return spell:Cast(target)
end)

ExplosiveShot:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 40 then return end
    if gameState.focus < 40 then return end

    return spell:Cast(target)
end)

-- Pet management
CallPet:Callback(function(spell)
    if gameState.petExists then return end
    if player:IsCasting() then return end

    return spell:Cast(player)
end)

CallPet1:Callback(function(spell)
    if gameState.petExists then return end
    if player:IsCasting() then return end

    return spell:Cast(player)
end)

MendPet:Callback(function(spell)
    if not petNeedsHealing() then return end
    if player:IsCasting() then return end

    return spell:Cast(pet)
end)

RevivePet:Callback(function(spell)
    if gameState.petExists then return end
    if player:IsCasting() then return end

    return spell:Cast(player)
end)

DismissPet:Callback(function(spell)
    if not gameState.petExists then return end
    if gameState.inCombat then return end

    return spell:Cast(player)
end)

-- Trap abilities
ExplosiveTrap:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end

    return spell:Cast(target)
end)

FreezingTrap:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if gameState.inCombat then return end -- Don't use in combat

    return spell:Cast(target)
end)

-- Utility abilities
ConcussiveShot:Callback(function(spell)
    if not target.exists then return end
    if target:HasDeBuff(debuffs.concussiveShot) then return end
    if target.distance > 35 then return end

    return spell:Cast(target)
end)

Disengage:Callback(function(spell)
    if target.distance > 8 then return end
    if player.hp > 70 then return end

    return spell:Cast(player)
end)

FeignDeath:Callback(function(spell)
    if player.hp > 30 then return end
    if player:HasBuff(buffs.feignDeath) then return end

    return spell:Cast(player)
end)

Deterrence:Callback(function(spell)
    if player.hp > 50 then return end
    if player:HasBuff(buffs.deterrence) then return end

    return spell:Cast(player)
end)

-- Interrupt
SilencingShot:Callback(function(spell)
    if not target.exists then return end
    if not target:IsCasting() then return end
    if not target:IsInterruptible() then return end
    if target.distance > 40 then return end

    return spell:Cast(target)
end)

-- Cooldowns
RapidFire:Callback(function(spell)
    if not shouldBurst() then return end
    if not target.exists then return end

    return spell:Cast(player)
end)

Readiness:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

-- Talent abilities
AMurderOfCrows:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end

    return spell:Cast(target)
end)

GlaiveToss:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end

    return spell:Cast(target)
end)

Stampede:Callback(function(spell)
    if not shouldBurst() then return end
    if not target.exists then return end

    return spell:Cast(target)
end)

DireBeast:Callback(function(spell)
    if not target.exists then return end

    return spell:Cast(target)
end)

ThrillOfTheHunt:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Fervor:Callback(function(spell)
    if gameState.focus > 50 then return end

    return spell:Cast(player)
end)

CarefulAim:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.focus > 80 then return end

    return spell:Cast(player)
end)

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain aspects and auras
    if AspectOfTheHawk() then return true end
    if TrueshotAura() then return true end

    -- Pet management
    if not gameState.petExists then
        if CallPet1() then return true end
        if CallPet() then return true end
        if RevivePet() then return true end
    end

    if petNeedsHealing() then
        if MendPet() then return true end
    end

    -- Apply Hunter's Mark
    if HuntersMark() then return true end

    -- Kill Shot priority below 20%
    if gameState.executeRange then
        if KillShot() then return true end
    end

    -- Aimed Shot priority (main damage ability)
    if shouldUseAimedShot() and AimedShot:IsReady() then
        if AimedShot() then return true end
    end

    -- Chimera Shot for high damage
    if ChimaeraShot() then return true end

    -- A Murder of Crows
    if AMurderOfCrows() then return true end

    -- Explosive Shot
    if ExplosiveShot() then return true end

    -- Dire Beast
    if DireBeast() then return true end

    -- Glaive Toss
    if GlaiveToss() then return true end

    -- Arcane Shot if we have focus
    if gameState.focus >= 50 then
        if ArcaneShot() then return true end
    end

    -- Steady Shot as filler and focus generator
    if SteadyShot() then return true end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain aspects and auras
    if AspectOfTheHawk() then return true end
    if TrueshotAura() then return true end

    -- Pet management
    if not gameState.petExists then
        if CallPet1() then return true end
        if CallPet() then return true end
        if RevivePet() then return true end
    end

    if petNeedsHealing() then
        if MendPet() then return true end
    end

    -- Multi-Shot for AoE damage
    if MultiShot() then return true end

    -- Explosive Trap for AoE damage
    if ExplosiveTrap() then return true end

    -- Explosive Shot for AoE
    if ExplosiveShot() then return true end

    -- A Murder of Crows on priority target
    if AMurderOfCrows() then return true end

    -- Dire Beast
    if DireBeast() then return true end

    -- Glaive Toss for cleave
    if GlaiveToss() then return true end

    -- Aimed Shot on priority target if we can cast it
    if shouldUseAimedShot() and AimedShot:IsReady() then
        if AimedShot() then return true end
    end

    -- Chimera Shot on priority target
    if ChimaeraShot() then return true end

    -- Multi-Shot to spend focus
    if gameState.focus >= 60 then
        if MultiShot() then return true end
    end

    -- Steady Shot as filler
    if SteadyShot() then return true end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 30 then
        if FeignDeath() then return true end
        if Disengage() then return true end
        if Deterrence() then return true end
    end

    -- Interrupt priority
    if SilencingShot() then return true end

    -- Maintain aspects and auras
    if AspectOfTheHawk() then return true end
    if TrueshotAura() then return true end

    -- Pet management
    if not gameState.petExists then
        if CallPet1() then return true end
        if CallPet() then return true end
        if RevivePet() then return true end
    end

    if petNeedsHealing() then
        if MendPet() then return true end
    end

    if target.exists and target.alive then
        -- Apply Hunter's Mark
        if HuntersMark() then return true end

        -- CC abilities
        if not gameState.inCombat then
            if FreezingTrap() then return true end
        end

        -- Slow target
        if not target:HasDeBuff(debuffs.concussiveShot) then
            if ConcussiveShot() then return true end
        end

        -- Kill Shot priority
        if gameState.executeRange then
            if KillShot() then return true end
        end

        -- Aimed Shot priority
        if shouldUseAimedShot() and AimedShot:IsReady() then
            if AimedShot() then return true end
        end

        -- Chimera Shot for burst
        if ChimaeraShot() then return true end

        -- Explosive Shot
        if ExplosiveShot() then return true end

        -- A Murder of Crows
        if AMurderOfCrows() then return true end

        -- Dire Beast
        if DireBeast() then return true end

        -- Arcane Shot
        if ArcaneShot() then return true end

        -- Steady Shot filler
        if SteadyShot() then return true end
    end

    return false
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Save focus for AoE abilities
        if gameState.focus < 80 then
            if SteadyShot() then return true end
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                if RapidFire() then return true end
                if Stampede() then return true end
                if CarefulAim() then return true end
            end
        end

        -- Maintain current target
        if shouldUseAimedShot() and AimedShot:IsReady() then
            if AimedShot() then return true end
        end

        if ChimaeraShot() then return true end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 30 then
        if FeignDeath() then return true end
        if Disengage() then return true end
        if Deterrence() then return true end
    end

    -- Pet management priority
    if not gameState.petExists and not player:IsCasting() then
        if CallPet1() then return true end
        if CallPet() then return true end
        if RevivePet() then return true end
    end

    if petNeedsHealing() then
        if MendPet() then return true end
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Focus: ", gameState.focus)
        MakPrint(3, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(4, "In Combat: ", gameState.inCombat)
        MakPrint(5, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(6, "Is PvP: ", gameState.isPvP)
        MakPrint(7, "Pet Exists: ", gameState.petExists)
        MakPrint(8, "Pet Health: ", gameState.petHealth)
        MakPrint(9, "Aimed Shot Charges: ", gameState.aimedShotCharges)
        MakPrint(10, "Execute Range: ", gameState.executeRange)
        MakPrint(11, "Trueshot Aura Active: ", player:HasBuff(buffs.trueshotAura))
        MakPrint(12, "Aspect of the Hawk Active: ", player:HasBuff(buffs.aspectOfTheHawk))
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if AimedShot:IsReady() and shouldUseAimedShot() then
            Aware:displayMessage("AIMED SHOT READY", "Red", 1)
        end
        if not gameState.petExists then
            Aware:displayMessage("NO PET ACTIVE", "Orange", 1)
        end
        if petNeedsHealing() then
            Aware:displayMessage("PET NEEDS HEALING", "Yellow", 1)
        end
        if gameState.executeRange then
            Aware:displayMessage("EXECUTE RANGE", "Purple", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
        if not player:HasBuff(buffs.aspectOfTheHawk) then
            Aware:displayMessage("NO ASPECT ACTIVE", "White", 1)
        end
        if not player:HasBuff(buffs.trueshotAura) then
            Aware:displayMessage("NO TRUESHOT AURA", "Blue", 1)
        end
        if player.moving and not player:HasBuff(buffs.lockAndLoad) and not player:HasBuff(buffs.masterMarksman) then
            Aware:displayMessage("MOVING - CAN'T CAST", "Orange", 1)
        end
        if gameState.focus < 25 then
            Aware:displayMessage("LOW FOCUS", "Red", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 15 then
        if FeignDeath:IsReady() then return FrameworkEnd() end
        if Deterrence:IsReady() then return FrameworkEnd() end
    end

    if player.hp <= 30 then
        if Disengage:IsReady() then return FrameworkEnd() end
    end

    -- Pet management priority
    if not gameState.petExists and not player:IsCasting() then
        if CallPet1() then return FrameworkEnd() end
        if CallPet() then return FrameworkEnd() end
        if RevivePet() then return FrameworkEnd() end
    end

    if petNeedsHealing() then
        if MendPet() then return FrameworkEnd() end
    end

    if target.exists and target.alive then
        -- Enhanced interrupt priority
        if target:IsCasting() and target:IsInterruptible() then
            if SilencingShot() then return FrameworkEnd() end
        end

        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if FreezingTrap() then return FrameworkEnd() end
                if ConcussiveShot() then return FrameworkEnd() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if RapidFire() then return FrameworkEnd() end
            if Stampede() then return FrameworkEnd() end
            if CarefulAim() then return FrameworkEnd() end

            -- Racial abilities during burst
            if BloodFury() then return FrameworkEnd() end
            if Berserking() then return FrameworkEnd() end
        end

        -- Talent abilities
        if AMurderOfCrows() then return FrameworkEnd() end
        if GlaiveToss() then return FrameworkEnd() end
        if DireBeast() then return FrameworkEnd() end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    SilencingShot("arena", enemy)

    -- CC abilities
    FreezingTrap("arena", enemy)
    ConcussiveShot("arena", enemy)

    -- Burst damage
    if shouldBurst() then
        RapidFire("arena")
        Stampede("arena")
        CarefulAim("arena")
    end

    -- Execute priority
    if enemy.hp <= 20 then
        KillShot("arena", enemy)
    end

    -- Core rotation
    if shouldUseAimedShot() then
        AimedShot("arena", enemy)
    end
    ChimaeraShot("arena", enemy)
    ExplosiveShot("arena", enemy)
    AMurderOfCrows("arena", enemy)
    ArcaneShot("arena", enemy)
    SteadyShot("arena", enemy)
end

local function enhancedPartyRotation(friendly)
    if not friendly.exists then return end

    -- Pet utility for party members
    if friendly.hp < 30 and gameState.petExists then
        -- Use pet abilities to help party members if available
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        SilencingShot()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Marksmanship Hunter
SilencingShot:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 40 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

FreezingTrap:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.freezingTrap) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets
    if gameState.inCombat then return end -- Don't use in combat

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Freezing Trap - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

ConcussiveShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 35 then return end
    if enemy:HasDeBuff(debuffs.concussiveShot) then return end

    return spell:Cast(enemy)
end)

AimedShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if not shouldUseAimedShot() then return end
    if gameState.focus < 50 then return end

    -- Priority on low health targets
    if enemy.hp < 40 then
        Aware:displayMessage("Priority Aimed Shot", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

ChimaeraShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if gameState.focus < 50 then return end

    return spell:Cast(enemy)
end)

ExplosiveShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if gameState.focus < 40 then return end

    return spell:Cast(enemy)
end)

AMurderOfCrows:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end

    return spell:Cast(enemy)
end)

ArcaneShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 35 then return end
    if gameState.focus < 25 then return end

    return spell:Cast(enemy)
end)

SteadyShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end

    return spell:Cast(enemy)
end)

KillShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 35 then return end
    if enemy.hp > 20 then return end

    -- Execute range priority
    Aware:displayMessage("Kill Shot - Execute", "Purple", 1)
    return spell:Cast(enemy)
end)

RapidFire:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Rapid Fire - Burst", "Blue", 1)
    return spell:Cast(player)
end)

Stampede:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Stampede - Burst", "Orange", 1)
    return spell:Cast(target)
end)

CarefulAim:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Burst cooldown
    Aware:displayMessage("Careful Aim - Burst", "Yellow", 1)
    return spell:Cast(player)
end)

-- Initialize
Action[ACTION_CONST_HUNTER_MARKSMANSHIP] = A
