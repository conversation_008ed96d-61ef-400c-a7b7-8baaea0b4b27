-- APL UPDATE MoP Survival Hunter
-- Mists of Pandaria Survival Hunter Rotation

if not <PERSON><PERSON>luValidCheck() then return true end
if Ma<PERSON><PERSON>_magic_number ~= 2347956243324 then return true end

-- Check if player is Survival spec (talent tree 3 for <PERSON> in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 3 then return end

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local ConstUnit        = MakuluFramework.ConstUnits
local cacheContext     = MakuluFramework.Cache
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits

local _G, setmetatable = _G, setmetatable

-- MoP Survival Hunter Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- Core Abilities
    ExplosiveShot = { ID = 53301, MAKULU_INFO = { damageType = "fire" } },
    BlackArrow = { ID = 3674, MAKULU_INFO = { damageType = "shadow" } },
    SerpentSting = { ID = 1978, MAKULU_INFO = { damageType = "nature" } },
    ArcaneShot = { ID = 3044, MAKULU_INFO = { damageType = "arcane" } },
    CobraShot = { ID = 77767, MAKULU_INFO = { damageType = "nature" } },
    MultiShot = { ID = 2643, MAKULU_INFO = { damageType = "physical" } },
    KillShot = { ID = 53351, MAKULU_INFO = { damageType = "physical" } },
    
    -- Traps
    ExplosiveTrap = { ID = 13813, MAKULU_INFO = { damageType = "fire" } },
    FreezingTrap = { ID = 1499, MAKULU_INFO = { targeted = true } },
    IceTrap = { ID = 13809, MAKULU_INFO = { targeted = false } },
    SnakeTrap = { ID = 34600, MAKULU_INFO = { targeted = false } },
    
    -- Utility Abilities
    HuntersMark = { ID = 1130, MAKULU_INFO = { targeted = true } },
    ConcussiveShot = { ID = 5116, MAKULU_INFO = { targeted = true } },
    Disengage = { ID = 781, MAKULU_INFO = { targeted = false } },
    FeignDeath = { ID = 5384, MAKULU_INFO = { targeted = false } },
    
    -- Defensive Abilities
    DeterrentShot = { ID = 19263, MAKULU_INFO = { targeted = false } },
    
    -- Aspects
    AspectOfTheHawk = { ID = 13165, MAKULU_INFO = { targeted = false } },
    AspectOfTheCheetah = { ID = 5118, MAKULU_INFO = { targeted = false } },
    AspectOfThePack = { ID = 13159, MAKULU_INFO = { targeted = false } },
    
    -- Pet Abilities
    CallPet = { ID = 883, MAKULU_INFO = { targeted = false } },
    DismissPet = { ID = 2641, MAKULU_INFO = { targeted = false } },
    MendPet = { ID = 136, MAKULU_INFO = { heal = true, targeted = false } },
    
    -- MoP Talents
    AMurderOfCrows = { ID = 131894, MAKULU_INFO = { damageType = "physical" } },
    GlaiveToss = { ID = 117050, MAKULU_INFO = { damageType = "physical" } },
    Stampede = { ID = 121818, MAKULU_INFO = { targeted = false } },
    
    -- Cooldowns
    RapidFire = { ID = 3045, MAKULU_INFO = { targeted = false } },
    Readiness = { ID = 23989, MAKULU_INFO = { targeted = false } },
    
    -- Interrupts
    SilencingShot = { ID = 34490, MAKULU_INFO = { targeted = true } },
}

local ConstSpells = {
    -- Trinkets
    Trinket1 = { ID = 13 },
    Trinket2 = { ID = 14 },
    
    -- Potions
    HealthPotion = { ID = 431 },
    ManaPotion = { ID = 431 },
}

local function createAction(actionData)
    return Action.Create({
        Type = actionData.Type or "Spell",
        ID = actionData.ID,
        Texture = actionData.Texture,
        MAKULU_INFO = actionData.MAKULU_INFO,
    })
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local function buildMakuluFrameworkSpells(ActionList)
    local result = {}
    for k, v in pairs(ActionList) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end

-- Build Makulu framework spells and make them available directly
TableToLocal(buildMakuluFrameworkSpells(A), getfenv(1))
Aware:enable()

-- Set up commonly used units
local player = ConstUnit.player
local target = ConstUnit.target
local pet = ConstUnit.pet



-- MoP Survival Hunter Buffs
local buffs = {
    aspectOfTheHawk = 13165,
    huntersMark = 1130,
    lockAndLoad = 56453,
    improvedTracking = 19878,
    rapidFire = 3045,
    feignDeath = 5384,
    shadowmeld = 58984,
}

-- MoP Survival Hunter Debuffs
local debuffs = {
    serpentSting = 1978,
    blackArrow = 3674,
    huntersMark = 1130,
    concussiveShot = 5116,
    freezingTrap = 1499,
    explosiveTrap = 13813,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    focus = 0,
    timeToAdds = 999,
    isPvP = false,
    lockAndLoadStacks = 0,
}

local function updateGameState()
    gameState.inCombat = player.inCombat
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.focus = player.focus or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.lockAndLoadStacks = player:BuffStacks(buffs.lockAndLoad) or 0

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsAspectOfTheHawk()
    return not player:Buff(buffs.aspectOfTheHawk)
end



-- Aspect management
AspectOfTheHawk:Callback(function(spell)
    if needsAspectOfTheHawk() then
        return spell:Cast()
    end
end)

-- Hunter's Mark application
HuntersMark:Callback(function(spell)
    if not target.exists then return end
    if target:DeBuff(debuffs.huntersMark) then return end
    if target.distance > 100 then return end

    return spell:Cast(target)
end)

-- Core rotation abilities
ExplosiveShot:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 40 then return end
    if gameState.focus < 25 then return end

    return spell:Cast(target)
end)

BlackArrow:Callback(function(spell)
    if not target.exists then return end
    if target:DeBuff(debuffs.blackArrow) then return end
    if target.distance > 35 then return end
    if gameState.focus < 35 then return end

    return spell:Cast(target)
end)

SerpentSting:Callback(function(spell)
    if not target.exists then return end
    if target:DeBuff(debuffs.serpentSting) then return end
    if target.distance > 35 then return end
    if gameState.focus < 25 then return end

    return spell:Cast(target)
end)

ArcaneShot:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 35 then return end
    if gameState.focus < 25 then return end

    return spell:Cast(target)
end)

CobraShot:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 35 then return end

    return spell:Cast(target)
end)

MultiShot:Callback(function(spell)
    if gameState.activeEnemies < 2 then return end
    if gameState.focus < 40 then return end

    return spell:Cast()
end)

KillShot:Callback(function(spell)
    if not target.exists then return end
    if target.hp > 20 then return end
    if target.distance > 35 then return end

    return spell:Cast(target)
end)

-- Trap abilities
ExplosiveTrap:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end

    return spell:Cast()
end)

FreezingTrap:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if gameState.inCombat then return end -- Don't use in combat

    return spell:Cast(target)
end)

-- Utility abilities
ConcussiveShot:Callback(function(spell)
    if not target.exists then return end
    if target:DeBuff(debuffs.concussiveShot) then return end
    if target.distance > 35 then return end

    return spell:Cast(target)
end)

Disengage:Callback(function(spell)
    if target.distance > 8 then return end
    if player.hp > 70 then return end

    return spell:Cast()
end)

FeignDeath:Callback(function(spell)
    if player.hp > 30 then return end
    if player:Buff(buffs.feignDeath) then return end

    return spell:Cast()
end)

-- Cooldowns
RapidFire:Callback(function(spell)
    if not shouldBurst() then return end
    if not target.exists then return end

    return spell:Cast()
end)

AMurderOfCrows:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end

    return spell:Cast(target)
end)

GlaiveToss:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end

    return spell:Cast(target)
end)

Stampede:Callback(function(spell)
    if not shouldBurst() then return end
    if not target.exists then return end

    return spell:Cast()
end)

-- Interrupt
SilencingShot:Callback(function(spell)
    if not target.exists then return end
    if not target.casting then return end
    if not target:IsInterruptible() then return end
    if target.distance > 35 then return end

    return spell:Cast(target)
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain aspects
    if AspectOfTheHawk() then return true end

    -- Apply Hunter's Mark
    if HuntersMark() then return true end

    -- Maintain Serpent Sting
    if not target:DeBuff(debuffs.serpentSting) or target:DeBuffRemains(debuffs.serpentSting) < 3000 then
        if SerpentSting() then return true end
    end

    -- Kill Shot priority below 20%
    if target.hp <= 20 then
        if KillShot() then return true end
    end

    -- Lock and Load procs - use Explosive Shot
    if gameState.lockAndLoadStacks > 0 then
        if ExplosiveShot() then return true end
    end

    -- Explosive Shot on cooldown
    if ExplosiveShot() then return true end

    -- A Murder of Crows
    if AMurderOfCrows() then return true end

    -- Black Arrow DoT
    if not target:DeBuff(debuffs.blackArrow) or target:DeBuffRemains(debuffs.blackArrow) < 3000 then
        if BlackArrow() then return true end
    end

    -- Glaive Toss
    if GlaiveToss() then return true end

    -- Arcane Shot if we have focus
    if gameState.focus >= 50 then
        if ArcaneShot() then return true end
    end

    -- Cobra Shot as filler
    if CobraShot() then return true end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain aspects
    if AspectOfTheHawk() then return true end

    -- Multi-Shot to apply Serpent Sting to multiple targets
    if MultiShot() then return true end

    -- Explosive Trap for AoE damage
    if ExplosiveTrap() then return true end

    -- A Murder of Crows on priority target
    if AMurderOfCrows() then return true end

    -- Black Arrow on priority target
    if not target:DeBuff(debuffs.blackArrow) or target:DeBuffRemains(debuffs.blackArrow) < 3000 then
        if BlackArrow() then return true end
    end

    -- Glaive Toss for cleave
    if GlaiveToss() then return true end

    -- Lock and Load Explosive Shot procs
    if gameState.lockAndLoadStacks > 0 then
        if ExplosiveShot() then return true end
    end

    -- Multi-Shot to spend focus
    if gameState.focus >= 60 then
        if MultiShot() then return true end
    end

    -- Cobra Shot as filler
    if CobraShot() then return true end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 30 then
        if FeignDeath() then return true end
        if Disengage() then return true end
    end

    -- Interrupt priority
    if SilencingShot() then return true end

    -- Maintain aspects
    if AspectOfTheHawk() then return true end

    if target.exists and target.alive then
        -- Apply Hunter's Mark
        if HuntersMark() then return true end

        -- CC abilities
        if not gameState.inCombat then
            if FreezingTrap() then return true end
        end

        -- Slow target
        if not target:DeBuff(debuffs.concussiveShot) then
            if ConcussiveShot() then return true end
        end

        -- Kill Shot priority
        if target.hp <= 20 then
            if KillShot() then return true end
        end

        -- Maintain Serpent Sting
        if not target:DeBuff(debuffs.serpentSting) or target:DeBuffRemains(debuffs.serpentSting) < 3000 then
            if SerpentSting() then return true end
        end

        -- Lock and Load procs
        if gameState.lockAndLoadStacks > 0 then
            if ExplosiveShot() then return true end
        end

        -- Explosive Shot
        if ExplosiveShot() then return true end

        -- Black Arrow
        if not target:DeBuff(debuffs.blackArrow) or target:DeBuffRemains(debuffs.blackArrow) < 3000 then
            if BlackArrow() then return true end
        end

        -- Arcane Shot
        if ArcaneShot() then return true end

        -- Cobra Shot filler
        if CobraShot() then return true end
    end

    return false
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Save focus for AoE abilities
        if gameState.focus < 80 then
            if CobraShot() then return true end
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                if RapidFire() then return true end
                if Stampede() then return true end
            end
        end

        -- Maintain current target
        if ExplosiveShot() then return true end
        if not target:DeBuff(debuffs.serpentSting) then
            if SerpentSting() then return true end
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 30 then
        if FeignDeath() then return true end
        if Disengage() then return true end
    end

    -- Pet management
    if not pet.exists and not player.moving then
        if CallPet() then return true end
    end

    if pet.exists and pet.hp <= 50 then
        if MendPet() then return true end
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function()
    if not MakuluFramework.start() then
        enhancedMainRotation()
    end
    return MakuluFramework.endFunc()
end

-- Enhanced A[3] function for advanced rotation with burst and cooldowns
A[3] = function()
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    updateGameState()

    -- Enhanced defensive priority
    if player.hp <= 30 then
        if FeignDeath() then return MakuluFramework.endFunc() end
        if Disengage() then return MakuluFramework.endFunc() end
    end

    -- Pet management
    if not pet.exists and not player.moving then
        if CallPet() then return MakuluFramework.endFunc() end
    end

    if pet.exists and pet.hp <= 50 then
        if MendPet() then return MakuluFramework.endFunc() end
    end

    if target.exists and target.alive then
        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if SilencingShot() then return MakuluFramework.endFunc() end
                if FreezingTrap() then return MakuluFramework.endFunc() end
                if ConcussiveShot() then return MakuluFramework.endFunc() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if RapidFire() then return MakuluFramework.endFunc() end
            if Stampede() then return MakuluFramework.endFunc() end

            -- Racial abilities during burst
            if QuakingPalm() then return MakuluFramework.endFunc() end
            if BloodFury() then return MakuluFramework.endFunc() end
            if Berserking() then return MakuluFramework.endFunc() end
        end

        -- Talent abilities
        if AMurderOfCrows() then return MakuluFramework.endFunc() end
        if GlaiveToss() then return MakuluFramework.endFunc() end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return MakuluFramework.endFunc()
end

-- Arena functions
A[6] = function()
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if A.GetToggle(2, "AutoInterrupt") and target.casting then
        if SilencingShot() then return MakuluFramework.endFunc() end
    end
    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena1)
        partyRotation(ConstUnit.party1)
    end

    return MakuluFramework.endFunc()
end

A[7] = function()
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena2)
        partyRotation(ConstUnit.party2)
    end

    return MakuluFramework.endFunc()
end

A[8] = function()
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena3)
        partyRotation(ConstUnit.party3)
    end

    return MakuluFramework.endFunc()
end

-- Arena-specific callback functions for MoP Survival Hunter
SilencingShot:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 35 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

FreezingTrap:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:DeBuff(debuffs.freezingTrap) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets
    if gameState.inCombat then return end -- Don't use in combat

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Freezing Trap - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

ConcussiveShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 35 then return end
    if enemy:DeBuff(debuffs.concussiveShot) then return end

    return spell:Cast(enemy)
end)

ExplosiveShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if gameState.focus < 25 then return end

    -- Priority on low health targets
    if enemy.hp < 40 then
        Aware:displayMessage("Priority Explosive Shot", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

BlackArrow:Callback("arena", function(spell, enemy)
    if enemy.distance > 35 then return end
    if enemy:DeBuff(debuffs.blackArrow) then return end
    if gameState.focus < 35 then return end

    return spell:Cast(enemy)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    SilencingShot("arena", enemy)

    -- CC abilities
    FreezingTrap("arena", enemy)
    ConcussiveShot("arena", enemy)

    -- Damage abilities
    ExplosiveShot("arena", enemy)
    BlackArrow("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end

    -- Pet utility for party members
    if friendly.hp < 30 and pet.exists then
        -- Could implement pet abilities to help party members here
        return true
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end
