local Action = _G.Action

local A                = Action

local CONST                                                              = Action.Const

local ACTION_CONST_MAGE_ARCANE                                       = CONST.MAGE_ARCANE
local ACTION_CONST_MAGE_FIRE                                         = CONST.MAGE_FIRE
local ACTION_CONST_MAGE_FROST                                        = CONST.MAGE_FROST

LPH_ENCNUM = function(val) return val end

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "Makulu MoP v1.0.0 (7/29/2025)",
    -- Class settings
    [2] = {
        {
            {
                E = "Header",
                L = {
                    ANY = " ====== Makulu - MoP Mage ====== ",
                },
            },
        },
        {
            { -- AOE
                E = "Checkbox", 
                DB = "AoE",
                DBV = true,
                L = { 
                    enUS = "Use AoE", 
                    ruRU = "Использовать AoE", 
                    frFR = "Utiliser l'AoE",
                }, 
                TT = { 
                    enUS = "Enable multiunits actions", 
                    ruRU = "Включает действия для нескольких целей", 
                    frFR = "Activer les actions multi-unités",
                }, 
                M = {},
            },
            { -- Auto Polymorph
                E = "Checkbox", 
                DB = "autoPolymorph",
                DBV = false,
                L = { 
                    ANY = "Auto Polymorph CC", 
                }, 
                TT = { 
                    ANY = "Automatically use Polymorph for crowd control when appropriate.", 
                }, 
                M = {},
            },
            { -- Mana Management
                E = "Checkbox", 
                DB = "manaManagement",
                DBV = true,
                L = { 
                    ANY = "Smart Mana Management", 
                }, 
                TT = { 
                    ANY = "Optimize mana usage and prevent mana starvation."
                }, 
                M = {},
            },   
        },
        { -- Spacer
            
            {
                E = "LayoutSpace",
            },
        },
        { -- Potions
            { -- useDamagePotion
                E = "Checkbox", 
                DB = "damagePotion",
                DBV = true,
                L = { 
                    ANY = "Damage Potion"
                }, 
                TT = { 
                    ANY = "Use Damage Potion", 
                }, 
                M = {},
            },
            { -- potionBossOnly
                E = "Checkbox", 
                DB = "potionLustOnly",
                DBV = true,
                L = { 
                    ANY = "Damage Potion Bloodlust/TimeWarp Only", 
                }, 
                TT = { 
                    ANY = "Only use Damage Potion when any kind of Bloodlust/Warp active."
                }, 
                M = {},
            },
        },
        {
            { -- potionExhausted
                E = "Checkbox", 
                DB = "potionExhausted",
                DBV = true,
                L = { 
                    ANY = "Damage Potion With Exhaustion", 
                }, 
                TT = { 
                    ANY = "Use Damage Potion while Exhausted (can't use Bloodlust)."
                }, 
                M = {},
            },
            { -- potionExhaustedSlider
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,   
                Precision = 1,                         
                DB = "potionExhaustedSlider",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Exhaustion Time Remaining",
                },
                TT = { 
                    ANY = "Time in minutes left on the Exhaustion Debuff to consider using Damage Potion.", 
                },                     
                M = {},
            },
        },
        { -- LAYOUT SPACE   
            {
                E = "LayoutSpace",                                                                         
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Cooldowns",
                },
            },
        },
        {
            {
                E = "Dropdown",                                                         
                H = 20,
                OT = {
                    { text = "Arcane Power", value = 1 }, 
                    { text = "Combustion", value = 2 },     
                    { text = "Icy Veins", value = 3 },
                    { text = "Mirror Image", value = 4 },
                    { text = "Alter Time", value = 5 },
                    { text = "Evocation", value = 6 },   
                },
                MULT = true,
                DB = "cooldownSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                    [5] = true,
                    [6] = true,
                },  
                L = { 
                    ANY = "Cooldown Abilities", 
                }, 
                TT = { 
                    ANY = "Select what abilities you want the rotation to obey the burst toggle.\nIf a spell is unchecked, it will be used even when burst is turned off!", 
                }, 
                M = {},                                    
            },  
        }, 
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { 
            {-- Burst Sensitivity
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "burstSens",
                DBV = 18,
                ONOFF = false,
                L = { 
                    ANY = "Burst Mode Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to use cooldowns. A lower number means cooldowns used closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },  
            {-- Mana Threshold
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "manaThreshold",
                DBV = 20,
                ONOFF = false,
                L = { 
                    ANY = "Mana Conservation Threshold (%)",
                },
                TT = { 
                    ANY = "Mana percentage to start conserving mana and use more efficient spells.", 
                },                     
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- MAGE HEADER
            {
                E = "Header",
                L = {
                    ANY = "INTERRUPTS",
                },
            },
        },
        {    
            { -- Automatic Interrupt
                E = "Checkbox", 
                DB = "AutoInterrupt",
                DBV = true,
                L = { 
                    ANY = "Switch Targets Interrupt",
                }, 
                TT = { 
                    ANY = "Automatically switches targets to interrupt.",
                }, 
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- MAGE HEADER
            {
                E = "Header",
                L = {
                    ANY = "DEFENSIVES",
                },
            },
        },
        {
            { -- Ice Block HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "IceBlockHP",
                DBV = 25,
                ONOFF = false,
                L = { 
                    ANY = "Ice Block HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Ice Block on yourself.", 
                },                     
                M = {},
            },    
            { -- Mage Ward HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "MageWardHP",
                DBV = 70,
                ONOFF = false,
                L = { 
                    ANY = "Mage Ward HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Mage Ward on yourself.", 
                },                     
                M = {},
            },    
        },
        {
            {-- Mana Shield Mana
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "manaShieldMana",
                DBV = 50,
                ONOFF = false,
                L = { 
                    ANY = "Mana Shield Mana (%)",
                },
                TT = { 
                    ANY = "Mana (%) to use Mana Shield for damage absorption.", 
                },                     
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Ice Block", value = 1 },
                    { text = "Mage Ward", value = 2 },
                    { text = "Mana Shield", value = 3 },
                    { text = "Blink", value = 4 },
                },
                MULT = true,
                DB = "defensiveSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Defensive Reactions",
                },
                TT = {
                    ANY = "Select what spells to be used when reacting to incoming damage in dungeons.",
                },
                M = {},
            },
        },
        { -- Spacer

            {
                E = "LayoutSpace",
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Debug/Aware Options",
                },
            },
        },
        {
            { -- Debug
                E = "Checkbox",
                DB = "makDebug",
                DBV = false,
                L = {
                    ANY = "Enable debug options",
                },
                TT = {
                    ANY = "Show a box with various debug data.\nIt takes a couple of seconds to get rid of the box when you disable this.",
                },
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Mana Reminder", value = 1 },
                    { text = "Arcane Power Ready", value = 2 },
                    { text = "Combustion Ready", value = 3 },
                    { text = "Icy Veins Ready", value = 4 },
                },
                MULT = true,
                DB = "makAware",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Aware Text Alert Reminders",
                },
                TT = {
                    ANY = "Select what text alert reminders you would like.\nThese will appear in the center of your screen.",
                },
                M = {},
            },
        },
    },
}
