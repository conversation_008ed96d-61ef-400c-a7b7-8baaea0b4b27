-- APL UPDATE MoP Fire Mage
-- Mists of Pandaria Fire Mage Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Fire spec (talent tree 2 for Mage in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 2 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Fire Mage Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Fire Core Abilities
    Fireball = { ID = 133, MAKULU_INFO = { damageType = "fire", castTime = 2500 } },
    Pyroblast = { ID = 11366, MAKULU_INFO = { damageType = "fire", castTime = 4000 } },
    FireBlast = { ID = 2136, MAKULU_INFO = { damageType = "fire" } },
    Scorch = { ID = 2948, MAKULU_INFO = { damageType = "fire", castTime = 1500 } },
    Flamestrike = { ID = 2120, MAKULU_INFO = { damageType = "fire", castTime = 2000 } },
    
    -- MoP Fire Specific
    HotStreak = { ID = 48108, MAKULU_INFO = { targeted = false } },
    HeatingUp = { ID = 48107, MAKULU_INFO = { targeted = false } },
    Combustion = { ID = 11129, MAKULU_INFO = { targeted = false } },
    CriticalMass = { ID = 117216, MAKULU_INFO = { targeted = false } },
    Ignite = { ID = 12654, MAKULU_INFO = { damageType = "fire" } },
    
    -- MoP Utility Spells
    Counterspell = { ID = 2139, MAKULU_INFO = { damageType = "arcane", ignoreCasting = true } },
    Spellsteal = { ID = 30449, MAKULU_INFO = { targeted = true } },
    DispelMagic = { ID = 30449, MAKULU_INFO = { targeted = true } },
    Polymorph = { ID = 118, MAKULU_INFO = { castTime = 1500 } },
    
    -- MoP Frost Spells (available to Fire)
    FrostNova = { ID = 122, MAKULU_INFO = { damageType = "frost" } },
    ConeOfCold = { ID = 120, MAKULU_INFO = { damageType = "frost" } },
    Frostbolt = { ID = 116, MAKULU_INFO = { damageType = "frost", castTime = 2500 } },
    IceLance = { ID = 30455, MAKULU_INFO = { damageType = "frost" } },
    
    -- MoP Arcane Spells (available to Fire)
    ArcaneExplosion = { ID = 1449, MAKULU_INFO = { damageType = "arcane" } },
    ArcaneIntellect = { ID = 1459, MAKULU_INFO = { targeted = false } },
    
    -- MoP Movement and Utility
    Blink = { ID = 1953, MAKULU_INFO = { targeted = false } },
    SlowFall = { ID = 130, MAKULU_INFO = { targeted = true } },
    Teleport = { ID = 3561, MAKULU_INFO = { castTime = 10000 } },
    Portal = { ID = 10059, MAKULU_INFO = { castTime = 10000 } },
    
    -- MoP Defensive Abilities
    IceBlock = { ID = 45438, MAKULU_INFO = { targeted = false } },
    IceBarrier = { ID = 11426, MAKULU_INFO = { targeted = false } },
    MirrorImage = { ID = 55342, MAKULU_INFO = { targeted = false } },
    Invisibility = { ID = 66, MAKULU_INFO = { castTime = 3000, targeted = false } },
    
    -- MoP Fire Armor and Buffs
    MoltenArmor = { ID = 30482, MAKULU_INFO = { targeted = false } },
    MageArmor = { ID = 6117, MAKULU_INFO = { targeted = false } },
    FrostArmor = { ID = 7301, MAKULU_INFO = { targeted = false } },
    ManaShield = { ID = 1463, MAKULU_INFO = { targeted = false } },
    
    -- MoP Fire Talents
    LivingBomb = { ID = 44457, MAKULU_INFO = { damageType = "fire" } },
    BlastWave = { ID = 11113, MAKULU_INFO = { damageType = "fire" } },
    DragonsBreath = { ID = 31661, MAKULU_INFO = { damageType = "fire" } },
    ImpactNova = { ID = 64343, MAKULU_INFO = { damageType = "fire" } },
    Meteor = { ID = 153561, MAKULU_INFO = { damageType = "fire", castTime = 3000 } },
    
    -- MoP Fire DoTs and Effects
    Pyroblast = { ID = 11366, MAKULU_INFO = { damageType = "fire", castTime = 4000 } },
    IgniteDoT = { ID = 12654, MAKULU_INFO = { damageType = "fire" } },
    
    -- MoP Conjure Abilities
    ConjureFood = { ID = 587, MAKULU_INFO = { castTime = 3000 } },
    ConjureWater = { ID = 5504, MAKULU_INFO = { castTime = 3000 } },
    ConjureManaGem = { ID = 759, MAKULU_INFO = { castTime = 3000 } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 2139, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 118, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
    local result = {}
    for k, v in pairs(A) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_MAGE_FIRE] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Fire Mage Buffs
local buffs = {
    hotStreak = 48108,
    heatingUp = 48107,
    combustion = 11129,
    criticalMass = 117216,
    moltenArmor = 30482,
    mageArmor = 6117,
    frostArmor = 7301,
    arcaneIntellect = 1459,
    manaShield = 1463,
    iceBarrier = 11426,
    mirrorImage = 55342,
    invisibility = 66,
    slowFall = 130,
    shadowmeld = 58984,
    impactNova = 64343,
}

-- MoP Fire Mage Debuffs
local debuffs = {
    ignite = 12654,
    livingBomb = 44457,
    polymorph = 118,
    frostNova = 122,
    dragonsBreath = 31661,
    pyroblastDoT = 11366,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    hotStreak = false,
    heatingUp = false,
    combustionActive = false,
    criticalMassStacks = 0,
    igniteActive = false,
    inBurstPhase = false,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.hotStreak = player:HasBuff(buffs.hotStreak)
    gameState.heatingUp = player:HasBuff(buffs.heatingUp)
    gameState.combustionActive = player:HasBuff(buffs.combustion)
    gameState.criticalMassStacks = player:GetBuffStacks(buffs.criticalMass) or 0
    gameState.igniteActive = target:HasDeBuff(debuffs.ignite)
    gameState.inBurstPhase = gameState.combustionActive or gameState.hotStreak
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function needsArmor()
    return not player:HasBuff(buffs.moltenArmor) and not player:HasBuff(buffs.mageArmor) and not player:HasBuff(buffs.frostArmor)
end

local function shouldUsePyroblast()
    return gameState.hotStreak or not player.moving
end

local function shouldUseFireBlast()
    return gameState.heatingUp or (not gameState.hotStreak and not gameState.heatingUp)
end

local function needsCombustion()
    return shouldBurst() and gameState.igniteActive and gameState.criticalMassStacks >= 3
end

local function getHealingTarget()
    -- Priority: player > party members by health
    if player.hp < 50 then
        return player
    end
    
    local lowestUnit = nil
    local lowestHealth = 100
    
    for i = 1, 4 do
        local unit = MakUnit:new("party" .. i)
        if unit.exists and unit.hp < lowestHealth and unit.hp < 80 then
            lowestUnit = unit
            lowestHealth = unit.hp
        end
    end
    
    return lowestUnit
end

-- Buff management
ArcaneIntellect:Callback(function(spell)
    if not player:HasBuff(buffs.arcaneIntellect) then
        return spell:Cast(player)
    end
end)

MoltenArmor:Callback(function(spell)
    if needsArmor() then
        return spell:Cast(player)
    end
end)

MageArmor:Callback(function(spell)
    if needsArmor() and gameState.isPvP then
        return spell:Cast(player)
    end
end)

ManaShield:Callback(function(spell)
    if not player:HasBuff(buffs.manaShield) and player.hp < 70 then
        return spell:Cast(player)
    end
end)

-- Core damage abilities
Fireball:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 35 then return end
    if player.moving then return end
    if gameState.hotStreak then return end -- Use Pyroblast instead

    return spell:Cast(target)
end)

Pyroblast:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 35 then return end
    if not shouldUsePyroblast() then return end

    return spell:Cast(target)
end)

FireBlast:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if not shouldUseFireBlast() then return end

    return spell:Cast(target)
end)

Scorch:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if player.moving then return end

    return spell:Cast(target)
end)

Flamestrike:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if player.moving then return end

    return spell:Cast(target)
end)

-- Cooldowns
Combustion:Callback(function(spell)
    if not needsCombustion() then return end

    return spell:Cast(player)
end)

-- Utility abilities
Counterspell:Callback(function(spell)
    if not target.exists then return end
    if not target:IsCasting() then return end
    if not target:IsInterruptible() then return end
    if target.distance > 24 then return end

    return spell:Cast(target)
end)

Spellsteal:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not target:HasDispellableBuff() then return end

    return spell:Cast(target)
end)

Polymorph:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not gameState.isPvP then return end
    if player.moving then return end

    return spell:Cast(target)
end)

-- Frost utility spells
FrostNova:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 8 then return end
    if not gameState.isPvP then return end

    return spell:Cast(player)
end)

ConeOfCold:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 10 then return end
    if not gameState.isPvP then return end

    return spell:Cast(player)
end)

IceLance:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not player.moving then return end -- Use while moving

    return spell:Cast(target)
end)

Frostbolt:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if player.moving then return end
    if not gameState.isPvP then return end

    return spell:Cast(target)
end)

-- Arcane utility spells
ArcaneExplosion:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if target.distance > 10 then return end

    return spell:Cast(player)
end)

-- Defensive abilities
IceBlock:Callback(function(spell)
    if player.hp > 30 then return end

    return spell:Cast(player)
end)

IceBarrier:Callback(function(spell)
    if player:HasBuff(buffs.iceBarrier) then return end
    if player.hp > 70 then return end

    return spell:Cast(player)
end)

MirrorImage:Callback(function(spell)
    if not shouldBurst() then return end
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

Invisibility:Callback(function(spell)
    if not gameState.isPvP then return end
    if player.hp > 40 then return end
    if player.moving then return end

    return spell:Cast(player)
end)

Blink:Callback(function(spell)
    if not gameState.isPvP then return end
    if player.hp > 60 then return end

    return spell:Cast(player)
end)

-- Talent abilities
LivingBomb:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if target:HasDeBuff(debuffs.livingBomb) then return end

    return spell:Cast(target)
end)

BlastWave:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if target.distance > 8 then return end

    return spell:Cast(player)
end)

DragonsBreath:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 8 then return end
    if not gameState.isPvP then return end

    return spell:Cast(player)
end)

ImpactNova:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if not gameState.hotStreak then return end

    return spell:Cast(player)
end)

Meteor:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 35 then return end
    if player.moving then return end
    if not shouldBurst() then return end

    return spell:Cast(target)
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.mana > 80 then return end

    return spell:Cast(player)
end)

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain buffs
    if ArcaneIntellect() then return true end
    if MoltenArmor() then return true end

    -- Apply Living Bomb if talented
    if not target:HasDeBuff(debuffs.livingBomb) then
        if LivingBomb() then return true end
    end

    -- Hot Streak priority - use instant Pyroblast
    if gameState.hotStreak then
        if Pyroblast() then return true end
    end

    -- Fire Blast to generate Heating Up or convert to Hot Streak
    if shouldUseFireBlast() then
        if FireBlast() then return true end
    end

    -- Scorch while moving
    if player.moving then
        if Scorch() then return true end
    end

    -- Pyroblast with Hot Streak or when stationary
    if shouldUsePyroblast() then
        if Pyroblast() then return true end
    end

    -- Fireball as filler
    if Fireball() then return true end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain buffs
    if ArcaneIntellect() then return true end
    if MoltenArmor() then return true end

    -- Apply Living Bomb to primary target
    if not target:HasDeBuff(debuffs.livingBomb) then
        if LivingBomb() then return true end
    end

    -- Hot Streak Flamestrike
    if gameState.hotStreak then
        if Flamestrike() then return true end
    end

    -- Impact Nova with Hot Streak
    if ImpactNova() then return true end

    -- Blast Wave for AoE
    if BlastWave() then return true end

    -- Arcane Explosion for close AoE
    if ArcaneExplosion() then return true end

    -- Fire Blast to generate procs
    if shouldUseFireBlast() then
        if FireBlast() then return true end
    end

    -- Flamestrike for AoE
    if Flamestrike() then return true end

    -- Scorch while moving
    if player.moving then
        if Scorch() then return true end
    end

    -- Fireball as filler
    if Fireball() then return true end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 30 then
        if IceBlock() then return true end
        if Invisibility() then return true end
        if Blink() then return true end
    end

    -- Interrupt priority
    if Counterspell() then return true end

    -- Maintain buffs
    if ArcaneIntellect() then return true end
    if MageArmor() then return true end
    if ManaShield() then return true end
    if IceBarrier() then return true end

    if target.exists and target.alive then
        -- CC abilities
        if Polymorph() then return true end
        if FrostNova() then return true end
        if DragonsBreath() then return true end

        -- Spellsteal enemy buffs
        if Spellsteal() then return true end

        -- Apply Living Bomb
        if not target:HasDeBuff(debuffs.livingBomb) then
            if LivingBomb() then return true end
        end

        -- Hot Streak priority
        if gameState.hotStreak then
            if Pyroblast() then return true end
        end

        -- Fire Blast for procs
        if shouldUseFireBlast() then
            if FireBlast() then return true end
        end

        -- Instant spells while moving
        if player.moving then
            if IceLance() then return true end
            if Scorch() then return true end
        end

        -- Pyroblast when stationary
        if shouldUsePyroblast() then
            if Pyroblast() then return true end
        end

        -- Backup spells
        if Frostbolt() then return true end
        if Fireball() then return true end
    end

    return false
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                if Combustion() then return true end
                if MirrorImage() then return true end
                if Meteor() then return true end
            end
        end

        -- Maintain current target
        if not target:HasDeBuff(debuffs.livingBomb) then
            if LivingBomb() then return true end
        end

        -- Build Hot Streak for burst
        if not gameState.hotStreak then
            if shouldUseFireBlast() then
                if FireBlast() then return true end
            end
            if Fireball() then return true end
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 30 then
        if IceBlock() then return true end
        if Invisibility() then return true end
        if Blink() then return true end
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Mana: ", gameState.mana)
        MakPrint(3, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(4, "In Combat: ", gameState.inCombat)
        MakPrint(5, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(6, "Is PvP: ", gameState.isPvP)
        MakPrint(7, "Hot Streak: ", gameState.hotStreak)
        MakPrint(8, "Heating Up: ", gameState.heatingUp)
        MakPrint(9, "Combustion Active: ", gameState.combustionActive)
        MakPrint(10, "Critical Mass Stacks: ", gameState.criticalMassStacks)
        MakPrint(11, "Ignite Active: ", gameState.igniteActive)
        MakPrint(12, "In Burst Phase: ", gameState.inBurstPhase)
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if Combustion:IsReady() and shouldBurst() then
            Aware:displayMessage("COMBUSTION READY", "Red", 1)
        end
        if Meteor:IsReady() and shouldBurst() then
            Aware:displayMessage("METEOR READY", "Blue", 1)
        end
        if needsArmor() then
            Aware:displayMessage("ARMOR NEEDED", "Yellow", 1)
        end
        if gameState.hotStreak then
            Aware:displayMessage("HOT STREAK ACTIVE", "Orange", 1)
        end
        if gameState.heatingUp then
            Aware:displayMessage("HEATING UP ACTIVE", "Green", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
        if not player:HasBuff(buffs.arcaneIntellect) then
            Aware:displayMessage("NO ARCANE INTELLECT", "White", 1)
        end
        if gameState.combustionActive then
            Aware:displayMessage("COMBUSTION ACTIVE", "Red", 1)
        end
        if gameState.igniteActive then
            Aware:displayMessage("IGNITE ACTIVE", "Orange", 1)
        end
        if gameState.criticalMassStacks >= 3 then
            Aware:displayMessage("CRITICAL MASS: " .. gameState.criticalMassStacks, "Blue", 1)
        end
        if shouldUseFireBlast() then
            Aware:displayMessage("USE FIRE BLAST", "Red", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 15 then
        if IceBlock:IsReady() then return FrameworkEnd() end
        if Invisibility:IsReady() then return FrameworkEnd() end
    end

    if player.hp <= 30 then
        if Blink:IsReady() then return FrameworkEnd() end
        if MirrorImage:IsReady() then return FrameworkEnd() end
    end

    if target.exists and target.alive then
        -- Enhanced interrupt priority
        if target:IsCasting() and target:IsInterruptible() then
            if Counterspell() then return FrameworkEnd() end
        end

        -- PvP specific abilities
        if gameState.isPvP then
            if Polymorph() then return FrameworkEnd() end
            if FrostNova() then return FrameworkEnd() end
            if Spellsteal() then return FrameworkEnd() end
            if DragonsBreath() then return FrameworkEnd() end
        end

        -- Burst phase
        if shouldBurst() then
            if Combustion() then return FrameworkEnd() end
            if Meteor() then return FrameworkEnd() end
            if MirrorImage() then return FrameworkEnd() end

            -- Racial abilities during burst
            if BloodFury() then return FrameworkEnd() end
            if Berserking() then return FrameworkEnd() end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    Counterspell("arena", enemy)

    -- CC abilities
    Polymorph("arena", enemy)
    FrostNova("arena", enemy)
    DragonsBreath("arena", enemy)

    -- Offensive dispel
    Spellsteal("arena", enemy)

    -- Burst damage
    if shouldBurst() then
        Combustion("arena")
        Meteor("arena")
        MirrorImage("arena")
    end

    -- Core rotation
    if not enemy:HasDeBuff(debuffs.livingBomb) then
        LivingBomb("arena", enemy)
    end

    if gameState.hotStreak then
        Pyroblast("arena", enemy)
    end

    if shouldUseFireBlast() then
        FireBlast("arena", enemy)
    end

    if shouldUsePyroblast() then
        Pyroblast("arena", enemy)
    end

    Fireball("arena", enemy)

    -- Movement spells
    if player.moving then
        IceLance("arena", enemy)
        Scorch("arena", enemy)
    end
end

local function enhancedPartyRotation(friendly)
    if not friendly.exists then return end

    -- No direct healing for Fire Mage, focus on utility
    -- Could add utility spells here if needed
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        Counterspell()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Fire Mage
Counterspell:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 24 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Polymorph:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.polymorph) then return end
    if player.moving then return end

    -- Use on healers or high priority targets
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Polymorph - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

FrostNova:Callback("arena", function(spell, enemy)
    if enemy.distance > 8 then return end

    -- Root in arena
    Aware:displayMessage("Frost Nova - Root", "Blue", 1)
    return spell:Cast(player)
end)

DragonsBreath:Callback("arena", function(spell, enemy)
    if enemy.distance > 8 then return end

    -- Cone CC in arena
    Aware:displayMessage("Dragon's Breath - Cone", "Orange", 1)
    return spell:Cast(player)
end)

Spellsteal:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if not enemy:HasDispellableBuff() then return end

    -- Offensive dispel in arena
    Aware:displayMessage("Spellsteal - Dispel", "Purple", 1)
    return spell:Cast(enemy)
end)

Fireball:Callback("arena", function(spell, enemy)
    if enemy.distance > 35 then return end
    if player.moving then return end
    if gameState.hotStreak then return end -- Use Pyroblast instead

    return spell:Cast(enemy)
end)

Pyroblast:Callback("arena", function(spell, enemy)
    if enemy.distance > 35 then return end
    if not shouldUsePyroblast() then return end

    -- Priority with Hot Streak
    if gameState.hotStreak then
        Aware:displayMessage("Pyroblast - Hot Streak", "Red", 1)
    end
    return spell:Cast(enemy)
end)

FireBlast:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if not shouldUseFireBlast() then return end

    -- Instant damage and proc generation
    Aware:displayMessage("Fire Blast - Instant", "Orange", 1)
    return spell:Cast(enemy)
end)

LivingBomb:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.livingBomb) then return end

    return spell:Cast(enemy)
end)

Scorch:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if player.moving then return end

    -- Movement spell in arena
    Aware:displayMessage("Scorch - Movement", "Yellow", 1)
    return spell:Cast(enemy)
end)

IceLance:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end

    -- Movement spell in arena
    Aware:displayMessage("Ice Lance - Movement", "Cyan", 1)
    return spell:Cast(enemy)
end)

Combustion:Callback("arena", function(spell)
    if not needsCombustion() then return end

    -- Major burst cooldown
    Aware:displayMessage("Combustion - Burst", "Red", 1)
    return spell:Cast(player)
end)

Meteor:Callback("arena", function(spell)
    if not shouldBurst() then return end
    if player.moving then return end

    -- Major damage cooldown
    Aware:displayMessage("Meteor - Burst", "Blue", 1)
    return spell:Cast(target)
end)

MirrorImage:Callback("arena", function(spell)
    if not shouldBurst() then return end
    if player.hp > 50 then return end

    -- Defensive/burst cooldown
    Aware:displayMessage("Mirror Image - Defensive", "Purple", 1)
    return spell:Cast(player)
end)

IceBlock:Callback("arena", function(spell)
    if player.hp > 30 then return end

    -- Emergency defensive
    Aware:displayMessage("Ice Block - Emergency", "White", 1)
    return spell:Cast(player)
end)

Blink:Callback("arena", function(spell)
    if player.hp > 60 then return end

    -- Mobility in arena
    Aware:displayMessage("Blink - Escape", "Yellow", 1)
    return spell:Cast(player)
end)

Invisibility:Callback("arena", function(spell)
    if player.hp > 40 then return end
    if player.moving then return end

    -- Stealth escape
    Aware:displayMessage("Invisibility - Stealth", "Gray", 1)
    return spell:Cast(player)
end)

Flamestrike:Callback("arena", function(spell, enemy)
    if gameState.activeEnemies < 3 then return end
    if player.moving then return end

    -- AoE damage in arena
    if gameState.hotStreak then
        Aware:displayMessage("Flamestrike - Hot Streak AoE", "Red", 1)
    end
    return spell:Cast(enemy)
end)

-- Initialize
Action[ACTION_CONST_MAGE_FIRE] = A
