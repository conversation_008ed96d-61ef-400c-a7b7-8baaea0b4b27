local Action = _G.Action

local A                = Action

local CONST                                                              = Action.Const

local ACTION_CONST_MONK_BREWMASTER                                   = CONST.MONK_BREWMASTER
local ACTION_CONST_MONK_MISTWEAVER                                   = CONST.MONK_MISTWEAVER
local ACTION_CONST_MONK_WINDWALKER                                   = CONST.MONK_WINDWALKER

LPH_ENCNUM = function(val) return val end

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "Makulu MoP v1.0.0 (7/29/2025)",
    -- Class settings
    [2] = {
        {
            {
                E = "Header",
                L = {
                    ANY = " ====== Makulu - MoP Monk ====== ",
                },
            },
        },
        {
            { -- AOE
                E = "Checkbox", 
                DB = "AoE",
                DBV = true,
                L = { 
                    enUS = "Use AoE", 
                    ruRU = "Использовать AoE", 
                    frFR = "Utiliser l'AoE",
                }, 
                TT = { 
                    enUS = "Enable multiunits actions", 
                    ruRU = "Включает действия для нескольких целей", 
                    frFR = "Activer les actions multi-unités",
                }, 
                M = {},
            },
            { -- Auto Roll/Chi Torpedo
                E = "Checkbox", 
                DB = "autoRoll",
                DBV = true,
                L = { 
                    ANY = "Auto Roll/Chi Torpedo", 
                }, 
                TT = { 
                    ANY = "Automatically use Roll or Chi Torpedo for mobility when appropriate.", 
                }, 
                M = {},
            },
            { -- Chi Management
                E = "Checkbox", 
                DB = "chiManagement",
                DBV = true,
                L = { 
                    ANY = "Smart Chi Management", 
                }, 
                TT = { 
                    ANY = "Optimize chi usage and prevent chi capping."
                }, 
                M = {},
            },   
        },
        { -- Spacer
            
            {
                E = "LayoutSpace",
            },
        },
        { -- Potions
            { -- useDamagePotion
                E = "Checkbox", 
                DB = "damagePotion",
                DBV = true,
                L = { 
                    ANY = "Damage Potion"
                }, 
                TT = { 
                    ANY = "Use Damage Potion", 
                }, 
                M = {},
            },
            { -- potionBossOnly
                E = "Checkbox", 
                DB = "potionLustOnly",
                DBV = true,
                L = { 
                    ANY = "Damage Potion Bloodlust/TimeWarp Only", 
                }, 
                TT = { 
                    ANY = "Only use Damage Potion when any kind of Bloodlust/Warp active."
                }, 
                M = {},
            },
        },
        {
            { -- potionExhausted
                E = "Checkbox", 
                DB = "potionExhausted",
                DBV = true,
                L = { 
                    ANY = "Damage Potion With Exhaustion", 
                }, 
                TT = { 
                    ANY = "Use Damage Potion while Exhausted (can't use Bloodlust)."
                }, 
                M = {},
            },
            { -- potionExhaustedSlider
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,   
                Precision = 1,                         
                DB = "potionExhaustedSlider",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Exhaustion Time Remaining",
                },
                TT = { 
                    ANY = "Time in minutes left on the Exhaustion Debuff to consider using Damage Potion.", 
                },                     
                M = {},
            },
        },
        { -- LAYOUT SPACE   
            {
                E = "LayoutSpace",                                                                         
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Cooldowns",
                },
            },
        },
        {
            {
                E = "Dropdown",                                                         
                H = 20,
                OT = {
                    { text = "Invoke Xuen", value = 1 }, 
                    { text = "Storm, Earth, and Fire", value = 2 },     
                    { text = "Fortifying Brew", value = 3 },
                    { text = "Touch of Death", value = 4 },
                    { text = "Energizing Brew", value = 5 },
                    { text = "Chi Brew", value = 6 },   
                },
                MULT = true,
                DB = "cooldownSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                    [5] = true,
                    [6] = true,
                },  
                L = { 
                    ANY = "Cooldown Abilities", 
                }, 
                TT = { 
                    ANY = "Select what abilities you want the rotation to obey the burst toggle.\nIf a spell is unchecked, it will be used even when burst is turned off!", 
                }, 
                M = {},                                    
            },  
        }, 
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { 
            {-- Burst Sensitivity
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "burstSens",
                DBV = 18,
                ONOFF = false,
                L = { 
                    ANY = "Burst Mode Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to use cooldowns. A lower number means cooldowns used closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },  
            {-- Chi Threshold
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 6,                            
                DB = "chiThreshold",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Chi Conservation Threshold",
                },
                TT = { 
                    ANY = "Chi amount to start conserving chi and use more efficient abilities.", 
                },                     
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- MONK HEADER
            {
                E = "Header",
                L = {
                    ANY = "INTERRUPTS",
                },
            },
        },
        {    
            { -- Automatic Interrupt
                E = "Checkbox", 
                DB = "AutoInterrupt",
                DBV = true,
                L = { 
                    ANY = "Switch Targets Interrupt",
                }, 
                TT = { 
                    ANY = "Automatically switches targets to interrupt.",
                }, 
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- MONK HEADER
            {
                E = "Header",
                L = {
                    ANY = "DEFENSIVES",
                },
            },
        },
        {
            { -- Fortifying Brew HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "FortifyingBrewHP",
                DBV = 50,
                ONOFF = false,
                L = { 
                    ANY = "Fortifying Brew HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Fortifying Brew on yourself.", 
                },                     
                M = {},
            },    
            { -- Dampen Harm HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "DampenHarmHP",
                DBV = 60,
                ONOFF = false,
                L = { 
                    ANY = "Dampen Harm HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Dampen Harm on yourself.", 
                },                     
                M = {},
            },    
        },
        {
            {-- Healing Sphere HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "healingSphereHP",
                DBV = 70,
                ONOFF = false,
                L = { 
                    ANY = "Healing Sphere HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Healing Sphere for emergency healing.", 
                },                     
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Fortifying Brew", value = 1 },
                    { text = "Dampen Harm", value = 2 },
                    { text = "Healing Sphere", value = 3 },
                    { text = "Roll", value = 4 },
                },
                MULT = true,
                DB = "defensiveSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Defensive Reactions",
                },
                TT = {
                    ANY = "Select what spells to be used when reacting to incoming damage in dungeons.",
                },
                M = {},
            },
        },
        { -- Spacer

            {
                E = "LayoutSpace",
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Debug/Aware Options",
                },
            },
        },
        {
            { -- Debug
                E = "Checkbox",
                DB = "makDebug",
                DBV = false,
                L = {
                    ANY = "Enable debug options",
                },
                TT = {
                    ANY = "Show a box with various debug data.\nIt takes a couple of seconds to get rid of the box when you disable this.",
                },
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Chi Reminder", value = 1 },
                    { text = "Invoke Xuen Ready", value = 2 },
                    { text = "Touch of Death Ready", value = 3 },
                    { text = "Energy Alert", value = 4 },
                },
                MULT = true,
                DB = "makAware",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Aware Text Alert Reminders",
                },
                TT = {
                    ANY = "Select what text alert reminders you would like.\nThese will appear in the center of your screen.",
                },
                M = {},
            },
        },
    },
}
