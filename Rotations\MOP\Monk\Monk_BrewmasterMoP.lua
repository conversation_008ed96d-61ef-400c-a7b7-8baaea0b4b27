-- APL UPDATE MoP Brewmaster Monk
-- Mists of Pandaria Brewmaster Monk Rotation

-- Check if MakuluValidCheck exists before calling it
if MakuluValidCheck and not MakuluValidCheck() then return true end
if Ma<PERSON><PERSON>_magic_number and Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Brewmaster spec (talent tree 1 for Monk in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 1 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Brewmaster Monk Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- Core Abilities
    TigerPalm = { ID = 100780, MAKULU_INFO = { damageType = "physical" } },
    BlackoutKick = { ID = 100784, MAKULU_INFO = { damageType = "physical" } },
    KegSmash = { ID = 121253, MAKULU_INFO = { damageType = "physical" } },
    BreathOfFire = { ID = 115181, MAKULU_INFO = { damageType = "fire" } },
    SpinningCraneKick = { ID = 101546, MAKULU_INFO = { damageType = "physical" } },
    CracklingJadeLightning = { ID = 117952, MAKULU_INFO = { damageType = "nature", castTime = 4000 } },
    
    -- Defensive Abilities
    Guard = { ID = 115295, MAKULU_INFO = { targeted = false } },
    PurifyingBrew = { ID = 119582, MAKULU_INFO = { targeted = false } },
    FortifyingBrew = { ID = 115203, MAKULU_INFO = { targeted = false } },
    DampenHarm = { ID = 122278, MAKULU_INFO = { targeted = false } },
    DiffuseMagic = { ID = 122783, MAKULU_INFO = { targeted = false } },
    TouchOfKarma = { ID = 122470, MAKULU_INFO = { targeted = false } },
    ZenMeditation = { ID = 115176, MAKULU_INFO = { targeted = false, castTime = 8000 } },
    
    -- Utility Abilities
    Provoke = { ID = 115546, MAKULU_INFO = { targeted = true } },
    SpearHandStrike = { ID = 116705, MAKULU_INFO = { targeted = true } },
    Paralysis = { ID = 115078, MAKULU_INFO = { targeted = true, castTime = 4000 } },
    LegSweep = { ID = 119381, MAKULU_INFO = { targeted = false } },
    DizzyingHaze = { ID = 115180, MAKULU_INFO = { targeted = true } },
    Clash = { ID = 122057, MAKULU_INFO = { targeted = true } },
    Roll = { ID = 109132, MAKULU_INFO = { targeted = false } },
    ExpelHarm = { ID = 115072, MAKULU_INFO = { heal = true, targeted = false } },
    
    -- MoP Talents
    ChiWave = { ID = 115098, MAKULU_INFO = { heal = true } },
    ChiBurst = { ID = 123986, MAKULU_INFO = { heal = true, castTime = 1000 } },
    ZenSphere = { ID = 124081, MAKULU_INFO = { heal = true } },
    PowerStrikes = { ID = 121817, MAKULU_INFO = { targeted = false } },
    Ascension = { ID = 115396, MAKULU_INFO = { targeted = false } },
    RingOfPeace = { ID = 116844, MAKULU_INFO = { targeted = false } },
    ChargingOxWave = { ID = 119392, MAKULU_INFO = { damageType = "physical" } },
    HealingElixirs = { ID = 122280, MAKULU_INFO = { heal = true, targeted = false } },
    InvokeXuen = { ID = 123904, MAKULU_INFO = { targeted = false } },
    
    -- Cooldowns
    ElixirOfPeace = { ID = 115308, MAKULU_INFO = { targeted = false } },
    ChiBrew = { ID = 115399, MAKULU_INFO = { targeted = false } },
    
    -- Detox
    Detox = { ID = 115450, MAKULU_INFO = { heal = true, targeted = true } },

    -- Potions and consumables
    TemperedPotion1 = { Type = "Potion", ID = 171263, QueueForbidden = true },
    TemperedPotion2 = { Type = "Potion", ID = 171264, QueueForbidden = true },
    TemperedPotion3 = { Type = "Potion", ID = 171265, QueueForbidden = true },
    PotionofUnwaveringFocus1 = { Type = "Potion", ID = 171266, QueueForbidden = true },
    PotionofUnwaveringFocus2 = { Type = "Potion", ID = 171267, QueueForbidden = true },
    PotionofUnwaveringFocus3 = { Type = "Potion", ID = 171268, QueueForbidden = true },

    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 116705, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 115078, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local function buildMakuluFrameworkSpells(ActionList)
    local result = {}
    for k, v in pairs(ActionList) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end

-- Build Makulu framework spells and make them available directly
TableToLocal(buildMakuluFrameworkSpells(A), getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Brewmaster Monk Buffs
local buffs = {
    guard = 115295,
    shuffle = 115307,
    powerStrikes = 129914,
    tigerPower = 125359,
    fortifyingBrew = 115203,
    dampenHarm = 122278,
    diffuseMagic = 122783,
    touchOfKarma = 122470,
    zenMeditation = 115176,
    chiWave = 115098,
    chiBurst = 123986,
    zenSphere = 124081,
    elixirOfPeace = 115308,
    legacyOfTheEmperor = 117666,
    legacyOfTheWhiteTiger = 116781,
    transcendence = 101643,
    teachingsOfTheMonastery = 202090,
    expelHarm = 115072,
    chiBrew = 115399,
    invokeXuen = 123904,
    healingElixirs = 122280,
    ringOfPeace = 116844,
}

-- MoP Brewmaster Monk Debuffs
local debuffs = {
    kegSmash = 121253,
    breathOfFire = 115181,
    dizzyingHaze = 115180,
    paralysis = 115078,
    legSweep = 119381,
    spearHandStrike = 116705,
    dizzying = 115180,
}

-- Game state tracking (enhanced with tank-specific functionality)
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    chi = 0,
    energy = 0,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
    shouldBurst = false,
    fightRemains = 999,
    shuffleRemains = 0,
    guardRemains = 0,
    shouldAoE = false,
    shouldCleave = false,
    staggerAmount = 0,
    staggerPercent = 0,
    imCasting = nil,
    needsTaunt = false,
    partyHealth = 100,
    tankingTarget = false,
}

local function updategs()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.chi = player.chi or 0
    gameState.energy = player.energy or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.channeling = player:IsCasting() or player:IsChanneling()
    gameState.shouldBurst = A.GetToggle(2, "BurstMode")

    -- Enhanced tracking
    gameState.fightRemains = target.exists and target.timeToDie or 999
    gameState.shouldAoE = gameState.activeEnemies > 2
    gameState.shouldCleave = gameState.activeEnemies > 1
    gameState.shuffleRemains = player:BuffRemains(buffs.shuffle) or 0
    gameState.guardRemains = player:BuffRemains(buffs.guard) or 0
    gameState.imCasting = player:IsCasting() and player:GetCastingSpell() or nil

    -- Stagger tracking (tank-specific)
    gameState.staggerAmount = player:GetStagger() or 0
    gameState.staggerPercent = player.maxHealth > 0 and (gameState.staggerAmount / player.maxHealth * 100) or 0

    -- Tanking status
    gameState.tankingTarget = target.exists and target:IsTargeting(player)
    gameState.needsTaunt = target.exists and not target:IsTargeting(player) and target.canAttack

    -- Party health tracking
    local totalHp = player.hp
    local members = 1
    for i = 1, 4 do
        local member = MakUnit:new("party" .. i)
        if member.exists then
            totalHp = totalHp + member.hp
            members = members + 1
        end
    end
    gameState.partyHealth = totalHp / members

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Alias for compatibility
local updateGameState = updategs
local gs = gameState

-- Utility functions (enhanced for tanking)
local function shouldBurst()
    return gameState.shouldBurst
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsDefensive()
    return player.hp < 60 or gameState.staggerPercent > 40
end

local function needsShuffle()
    return gameState.shuffleRemains < 3000
end

local function needsGuard()
    return gameState.guardRemains < 1000 and player.hp < 80
end

local function isSpellInFlight(spell, range)
    return spell:IsSpellInFlight() or false
end

local function makInterrupt(interrupts)
    if not interrupts then return end
    for _, interrupt in pairs(interrupts) do
        if interrupt and interrupt:IsReady() then
            interrupt()
        end
    end
end

-- PvP utility functions
local function shouldInterrupt(enemy)
    if not enemy or not enemy.exists then return false end
    return enemy:IsCasting() and enemy:IsInterruptible()
end

local function shouldCC(enemy)
    if not enemy or not enemy.exists then return false end
    return not enemy:HasDebuff(debuffs.paralysis) and enemy.hp > 30
end

-- Core tanking callbacks
KegSmash:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gameState.chi >= 4 then return end

    return spell:Cast(target)
end)

TigerPalm:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if player:BuffRemains(buffs.tigerPower) > 3000 then return end
    if gameState.energy < 40 then return end

    return spell:Cast(target)
end)

BlackoutKick:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if needsShuffle() and gameState.chi >= 2 then
        return spell:Cast(target)
    end
end)

BreathOfFire:Callback(function(spell)
    if not shouldAoE() then return end
    if not target.exists or not target.canAttack then return end
    if gameState.chi < 2 then return end

    return spell:Cast(target)
end)

SpinningCraneKick:Callback(function(spell)
    if not shouldAoE() then return end
    if gameState.chi < 2 then return end
    if gameState.channeling then return end

    return spell:Cast(player)
end)

-- Defensive abilities
Guard:Callback(function(spell)
    if needsGuard() and gameState.chi >= 2 then
        return spell:Cast(player)
    end
end)

PurifyingBrew:Callback(function(spell)
    if gameState.staggerPercent < 30 then return end

    return spell:Cast(player)
end)

FortifyingBrew:Callback(function(spell)
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

DampenHarm:Callback(function(spell)
    if player.hp > 60 then return end

    return spell:Cast(player)
end)

TouchOfKarma:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if player.hp > 40 then return end

    return spell:Cast(target)
end)

-- Utility abilities
Provoke:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gameState.tankingTarget then return end

    return spell:Cast(target)
end)

SpearHandStrike:Callback(function(spell)
    if not target.exists or not target:IsCasting() then return end
    if not target:IsInterruptible() then return end

    return spell:Cast(target)
end)

Paralysis:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDebuff(debuffs.paralysis) then return end
    if gameState.channeling then return end

    return spell:Cast(target)
end)

LegSweep:Callback(function(spell)
    if not shouldAoE() then return end
    if not target.exists or not target.canAttack then return end

    return spell:Cast(player)
end)

ExpelHarm:Callback(function(spell)
    if player.hp > 70 then return end

    return spell:Cast(player)
end)

-- Enhanced PvP callbacks
SpearHandStrike:Callback("arena", function(spell, enemy)
    if not enemy.pvpKick then return end

    return spell:Cast(enemy)
end)

Paralysis:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:HasDebuff(debuffs.paralysis) then return end
    if enemy.hp < 30 then return end
    if gs.imCasting and gs.imCasting == spell.id then return end

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Paralysis - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Paralysis - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

LegSweep:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy:HasDebuff(debuffs.legSweep) then return end

    return spell:Cast(enemy)
end)

-- Enhanced rotation functions
local function st()
    -- Single target tanking rotation
    updategs()

    -- Maintain threat
    if gs.needsTaunt then
        Provoke()
    end

    -- Maintain shuffle
    if needsShuffle() and gs.chi >= 2 then
        BlackoutKick()
    end

    -- Generate chi
    if gs.chi < 3 then
        KegSmash()
        TigerPalm()
    end

    -- Maintain tiger power
    TigerPalm()

    -- Defensive priorities
    if needsDefensive() then
        Guard()
        PurifyingBrew()
    end

    -- Damage abilities
    BlackoutKick()
end

local function aoe()
    -- AoE tanking rotation
    updategs()

    -- AoE threat generation
    KegSmash()
    BreathOfFire()

    -- Maintain shuffle
    if needsShuffle() and gs.chi >= 2 then
        BlackoutKick()
    end

    -- AoE damage
    if gs.chi >= 2 then
        SpinningCraneKick()
    end

    -- Defensive priorities
    if needsDefensive() then
        Guard()
        PurifyingBrew()
    end
end

local function cleave()
    -- Cleave tanking (2-3 enemies)
    updategs()

    -- Generate chi and threat
    KegSmash()
    TigerPalm()

    -- Maintain shuffle
    if needsShuffle() and gs.chi >= 2 then
        BlackoutKick()
    end

    -- Breath of Fire for cleave
    if gs.chi >= 2 then
        BreathOfFire()
    end

    -- Defensive priorities
    if needsDefensive() then
        Guard()
        PurifyingBrew()
    end
end

local function ogcd()
    -- Off-global cooldown abilities
    if shouldBurst() then
        InvokeXuen()
        ChiBrew()
    end

    -- Emergency defensives
    if player.hp < 40 then
        FortifyingBrew()
        DampenHarm()
        TouchOfKarma()
    end

    -- Racial abilities
    racials()
end

local function eof()
    -- End of fight logic
    if gs.fightRemains < 30000 then
        -- Burn chi on damage
        if gs.chi >= 2 then
            BlackoutKick()
            BreathOfFire()
        end
    end
end

local function pvpenis()
    -- PvP specific logic placeholder
    -- This would contain PvP-specific rotation logic
end

-- Alias for compatibility
local function singleTargetRotation()
    st()
end

-- AoE rotation
local function aoeRotation()
    updateGameState()
    aoe()
end

-- PvP specific rotation
local function pvpRotation()
    updateGameState()

    -- Interrupt priority
    SpearHandStrike()

    -- CC for control
    Paralysis()
    LegSweep()

    -- Tanking priorities
    if gs.needsTaunt then
        Provoke()
    end

    -- Maintain shuffle
    if needsShuffle() and gs.chi >= 2 then
        BlackoutKick()
    end

    -- Generate chi
    KegSmash()
    TigerPalm()

    -- Defensive priorities
    if needsDefensive() then
        Guard()
        FortifyingBrew()
    end
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Pre-position and prepare for adds
    if gs.timeToAdds < 8000 and gs.timeToAdds > 0 then
        -- Build chi for incoming adds
        if gs.chi < 4 then
            KegSmash()
            TigerPalm()
        end

        -- Prepare cooldowns
        if gs.timeToAdds < 3000 then
            Guard()
            ChiBrew()
        end
    end

    -- During adds phase
    if gs.activeEnemies >= 3 then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives always take priority
    if player.hp < 20 then
        FortifyingBrew()
        DampenHarm()
        return
    end

    -- Stagger management
    if gs.staggerPercent > 60 then
        PurifyingBrew()
    end

    -- Taunt priority
    if gs.needsTaunt then
        Provoke()
    end

    -- Cooldowns during burst or emergency
    if shouldBurst() or player.hp < 50 then
        InvokeXuen()
        ChiBrew()
        TouchOfKarma()
    end

    -- TimeToAdds logic
    if gs.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gs.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- Enhanced A[3] function with new functionality
A[3] = function(icon)
    -- Safety check for framework initialization
    if not FrameworkStart then return end

    FrameworkStart(icon)
    updategs()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Chi: ", gs.chi)
        MakPrint(2, "Energy: ", gs.energy)
        MakPrint(3, "Shuffle Remains: ", gs.shuffleRemains)
        MakPrint(4, "Guard Remains: ", gs.guardRemains)
        MakPrint(5, "Stagger Percent: ", gs.staggerPercent)
        MakPrint(6, "Fight Remains: ", gs.fightRemains)
        MakPrint(7, "Should AoE: ", gs.shouldAoE)
        MakPrint(8, "Tanking Target: ", gs.tankingTarget)
        MakPrint(9, "Time to Adds: ", gs.timeToAdds)
        MakPrint(10, "Spear Hand Strike Learned: ", SpearHandStrike:IsKnown())
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then -- Guard ready
        if Guard:IsReady() and gs.chi >= 2 and player.hp < 80 and player.inCombat then
            Aware:displayMessage("GUARD READY", "Purple", 1)
        end
    end

    -- Interrupt handling
    local interrupts = {SpearHandStrike}
    makInterrupt(interrupts)

    -- Emergency tanking and utility management
    Guard()
    PurifyingBrew()
    ExpelHarm()

    if target.exists and target.canAttack and (KegSmash:InRange(target) or TigerPalm:InRange(target)) then
        if A.IsInPvP then
            SpearHandStrike("bg")
            Paralysis("bg")
            LegSweep("bg")
            pvpenis()
        end

        if player.channeling and gs.imCasting and gs.imCasting == SpinningCraneKick.id then return end

        local damagePotion = Action.GetToggle(2, "damagePotion")
        local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
        local potionExhausted = Action.GetToggle(2, "potionExhausted")
        local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")
        local damagePotionObject = Action.DetermineUsableObject("player", nil, nil, true, nil, A.TemperedPotion1, A.TemperedPotion2, A.TemperedPotion3, A.PotionofUnwaveringFocus1, A.PotionofUnwaveringFocus2, A.PotionofUnwaveringFocus3)

        if damagePotionObject and damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
            local shouldPot = gs.chi >= 3 and gs.shuffleRemains > 5000
            if shouldPot then
                return damagePotionObject:Show(icon)
            end
        end

        if gs.shouldAoE then
            aoe()
        end

        ogcd()
        eof()

        if gs.shouldCleave then
            cleave()
        end

        st()

    end

    return FrameworkEnd()
end

-- Enhanced enemy and party rotation functions
local enemyRotation = function(enemy)
    if not enemy.exists then return end
    if A.Zone ~= "arena" then return end
    SpearHandStrike("arena", enemy)
    Paralysis("arena", enemy)
    LegSweep("arena", enemy)
    KegSmash("arena", enemy)
end

local partyRotation = function(friendly)
    if not friendly.exists then return end
    -- Brewmaster doesn't have direct healing, focus on protection
    if friendly.hp < 50 then
        -- Use Guard on low health party members if talented
        -- This would be expansion-specific
    end
end

-- Arena functions
A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        SpearHandStrike()
    end
    if Action.Zone == "arena" then
        enemyRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and gameState.energy < 50 then
        return spell:Cast(player)
    end
end)

Berserking:Callback(function(spell)
    if shouldBurst() and (target.hp > 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

BloodFury:Callback(function(spell)
    if shouldBurst() and (target.hp > 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

WillOfTheForsaken:Callback(function(spell)
    if player:HasDebuffType("Fear") or player:HasDebuffType("Charm") then
        return spell:Cast(player)
    end
end)

QuakingPalm:Callback(function(spell)
    if target.exists and target.canAttack and target:IsCasting() then
        return spell:Cast(target)
    end
end)

Stoneform:Callback(function(spell)
    if player:HasDebuffType("Poison") or player:HasDebuffType("Disease") or player:HasDebuffType("Bleed") then
        return spell:Cast(player)
    end
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    WillOfTheForsaken()
    QuakingPalm()
    Stoneform()
end

local function mopTalents()
    ChiWave()
    ChiBurst()
    ZenSphere()
    InvokeXuen()
    RingOfPeace()
    ChargingOxWave()
end

local function baseStuff()
    Guard()
    PurifyingBrew()
    FortifyingBrew()
    DampenHarm()
    TouchOfKarma()
end

-- Enhanced utility for MoP
local function mopUtility()
    SpearHandStrike()
    Paralysis()
    LegSweep()
    Provoke()
    ExpelHarm()
    Detox()
end
    muscleMemory = 139598,
}

-- MoP Brewmaster Monk Debuffs
local debuffs = {
    lightStagger = 124275,
    moderateStagger = 124274,
    heavyStagger = 124273,
    breathOfFire = 123725,
    dizzyingHaze = 115180,
    paralysis = 115078,
    legSweep = 119381,
    disableRoot = 116706,
    disableSlow = 116095,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    chi = 0,
    energy = 0,
    timeToAdds = 999,
    isPvP = false,
    staggerLevel = "None",
    stagger = 0,
    staggerPct = 0,
}

local function updateGameState()
    gameState.inCombat = player.inCombat
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.chi = player.chi or 0
    gameState.energy = player.energy or 0
    gameState.isPvP = Action.IsInPvP or false

    -- Stagger tracking
    if player:Buff(debuffs.heavyStagger) then
        gameState.staggerLevel = "Heavy"
        gameState.stagger = player:BuffStacks(debuffs.heavyStagger)
    elseif player:Buff(debuffs.moderateStagger) then
        gameState.staggerLevel = "Moderate"
        gameState.stagger = player:BuffStacks(debuffs.moderateStagger)
    elseif player:Buff(debuffs.lightStagger) then
        gameState.staggerLevel = "Light"
        gameState.stagger = player:BuffStacks(debuffs.lightStagger)
    else
        gameState.staggerLevel = "None"
        gameState.stagger = 0
    end

    gameState.staggerPct = gameState.stagger > 0 and (gameState.stagger / player.healthMax * 100) or 0

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsStaggerPurge()
    return gameState.staggerLevel == "Heavy" or 
           (gameState.staggerLevel == "Moderate" and gameState.staggerPct > 60) or
           gameState.staggerPct > 80
end

local function shouldGuard()
    return player.hp <= 70 and not player:Buff(buffs.guard)
end

-- Defensive Callback functions
Guard:Callback(function(spell)
    if not shouldGuard() then return end
    if player:Buff(buffs.guard) then return end

    return spell:Cast()
end)

PurifyingBrew:Callback(function(spell)
    if not needsStaggerPurge() then return end
    if gameState.stagger == 0 then return end

    return spell:Cast()
end)

FortifyingBrew:Callback(function(spell)
    if player.hp > 50 then return end
    if player:Buff(buffs.fortifyingBrew) then return end

    return spell:Cast()
end)

DampenHarm:Callback(function(spell)
    if player.hp > 40 then return end
    if player:Buff(buffs.dampenHarm) then return end

    return spell:Cast()
end)

DiffuseMagic:Callback(function(spell)
    if player.hp > 30 then return end
    if player:Buff(buffs.diffuseMagic) then return end

    return spell:Cast()
end)

TouchOfKarma:Callback(function(spell)
    if player.hp > 60 then return end
    if player:Buff(buffs.touchOfKarma) then return end
    if not target.exists then return end

    return spell:Cast(target)
end)

ZenMeditation:Callback(function(spell)
    if player.moving then return end
    if player.hp > 20 then return end
    if player:Buff(buffs.zenMeditation) then return end

    return spell:Cast()
end)

-- Offensive Callback functions
KegSmash:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 15 then return end

    return spell:Cast(target)
end)

BreathOfFire:Callback(function(spell)
    if gameState.activeEnemies < 1 then return end
    if target.distance > 8 then return end

    return spell:Cast()
end)

BlackoutKick:Callback(function(spell)
    if not target.exists then return end
    if gameState.chi < 2 then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

TigerPalm:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

SpinningCraneKick:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if gameState.chi < 1 then return end

    return spell:Cast()
end)

-- Interrupt and Utility Callback functions
SpearHandStrike:Callback(function(spell)
    if not target.exists then return end
    if not target.casting then return end
    if not target:IsInterruptible() then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

LegSweep:Callback(function(spell)
    if gameState.activeEnemies < 2 then return end
    if target.distance > 5 then return end

    return spell:Cast()
end)

Paralysis:Callback(function(spell)
    if not target.exists then return end
    if target:DeBuff(debuffs.paralysis) then return end
    if target.distance > 20 then return end
    if gameState.inCombat then return end -- Don't use in combat

    return spell:Cast(target)
end)

-- Utility abilities
Provoke:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if player:GetThreatSituation(target) >= 3 then return end -- Already have threat

    return spell:Cast(target)
end)

ExpelHarm:Callback(function(spell)
    if player.hp > 80 then return end

    return spell:Cast()
end)

-- Chi management
ChiBrew:Callback(function(spell)
    if gameState.chi >= 3 then return end

    return spell:Cast()
end)

-- Talent abilities
ChiWave:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 25 then return end

    return spell:Cast(target)
end)

ChiBurst:Callback(function(spell)
    if gameState.activeEnemies < 2 then return end
    if target.distance > 40 then return end

    return spell:Cast(target)
end)

InvokeXuen:Callback(function(spell)
    if not shouldBurst() then return end
    if not target.exists then return end

    return spell:Cast(target)
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Defensive priority
    if Guard() then return true end
    if PurifyingBrew() then return true end

    -- Maintain Shuffle buff through Blackout Kick
    if not player:Buff(buffs.shuffle) or player:BuffRemains(buffs.shuffle) < 3000 then
        if BlackoutKick() then return true end
    end

    -- Keg Smash for threat and damage
    if KegSmash() then return true end

    -- Chi spenders
    if gameState.chi >= 2 then
        if BlackoutKick() then return true end
    end

    -- Breath of Fire for debuff
    if not target:DeBuff(debuffs.breathOfFire) then
        if BreathOfFire() then return true end
    end

    -- Chi builders
    if TigerPalm() then return true end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Defensive priority
    if Guard() then return true end
    if PurifyingBrew() then return true end

    -- AoE abilities
    if gameState.chi >= 1 then
        if SpinningCraneKick() then return true end
    end

    -- Keg Smash for AoE threat
    if KegSmash() then return true end

    -- Breath of Fire for AoE damage
    if BreathOfFire() then return true end

    -- Maintain Shuffle
    if not player:Buff(buffs.shuffle) or player:BuffRemains(buffs.shuffle) < 3000 then
        if BlackoutKick() then return true end
    end

    -- Chi builders
    if TigerPalm() then return true end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 50 then
        if FortifyingBrew() then return true end
        if DampenHarm() then return true end
        if TouchOfKarma() then return true end
    end

    if player.hp <= 30 then
        if DiffuseMagic() then return true end
        if Guard() then return true end
    end

    -- Interrupt priority
    if SpearHandStrike() then return true end

    -- CC abilities
    if LegSweep() then return true end

    if target.exists and target.alive then
        -- Apply debuffs
        if not target:DeBuff(debuffs.breathOfFire) then
            if BreathOfFire() then return true end
        end

        -- Maintain pressure
        if KegSmash() then return true end

        if gameState.chi >= 2 then
            if BlackoutKick() then return true end
        end

        if TigerPalm() then return true end
    end

    return false
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Save chi for AoE abilities
        if gameState.chi < 3 then
            if TigerPalm() then return true end
            if ChiBrew() then return true end
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                if InvokeXuen() then return true end
            end
        end

        -- Maintain current target
        if KegSmash() then return true end
        if gameState.chi >= 2 then
            if BlackoutKick() then return true end
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 20 then
        if ZenMeditation() then return true end
        if Guard() then return true end
    end

    if player.hp <= 40 then
        if FortifyingBrew() then return true end
        if DampenHarm() then return true end
        if TouchOfKarma() then return true end
    end

    if player.hp <= 60 then
        if DiffuseMagic() then return true end
    end

    -- Resource management
    if ExpelHarm() then return true end
    if gameState.chi <= 1 then
        if ChiBrew() then return true end
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    if not MakuluFramework.start() then
        enhancedMainRotation()
    end
    return MakuluFramework.endFunc()
end

-- Enhanced A[3] function for advanced rotation with burst and cooldowns
A[3] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    updateGameState()

    -- Enhanced defensive priority
    if player.hp <= 30 then
        if ZenMeditation() then return MakuluFramework.endFunc() end
        if Guard() then return MakuluFramework.endFunc() end
        if FortifyingBrew() then return MakuluFramework.endFunc() end
        if DampenHarm() then return MakuluFramework.endFunc() end
    end

    if player.hp <= 50 then
        if TouchOfKarma() then return MakuluFramework.endFunc() end
        if DiffuseMagic() then return MakuluFramework.endFunc() end
    end

    -- Enhanced stagger management
    if PurifyingBrew() then return MakuluFramework.endFunc() end

    -- Resource management
    if ExpelHarm() then return MakuluFramework.endFunc() end
    if ChiBrew() then return MakuluFramework.endFunc() end

    if target.exists and target.alive then
        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if SpearHandStrike() then return MakuluFramework.endFunc() end
                if LegSweep() then return MakuluFramework.endFunc() end
                if Paralysis() then return MakuluFramework.endFunc() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if InvokeXuen() then return MakuluFramework.endFunc() end

            -- Racial abilities during burst
            if QuakingPalm() then return MakuluFramework.endFunc() end
            if BloodFury() then return MakuluFramework.endFunc() end
            if Berserking() then return MakuluFramework.endFunc() end
        end

        -- Talent abilities
        if ChiWave() then return MakuluFramework.endFunc() end
        if ChiBurst() then return MakuluFramework.endFunc() end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return MakuluFramework.endFunc()
end

-- Arena functions
A[6] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if A.GetToggle(2, "AutoInterrupt") and target.casting then
        if SpearHandStrike() then return MakuluFramework.endFunc() end
    end
    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena1)
        partyRotation(ConstUnit.party1)
    end

    return MakuluFramework.endFunc()
end

A[7] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena2)
        partyRotation(ConstUnit.party2)
    end

    return MakuluFramework.endFunc()
end

A[8] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena3)
        partyRotation(ConstUnit.party3)
    end

    return MakuluFramework.endFunc()
end

-- Arena-specific callback functions for MoP Brewmaster Monk
SpearHandStrike:Callback("arena", function(spell, enemy)
    if not enemy.casting then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

LegSweep:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy:DeBuff(debuffs.legSweep) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets

    -- Use for peeling when party members are low
    local peelParty = (ConstUnit.party1.exists and ConstUnit.party1.hp < 40) or (ConstUnit.party2.exists and ConstUnit.party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Leg Sweep - Peeling", "Yellow", 1)
        return spell:Cast()
    end

    -- Use on multiple enemies
    if gameState.activeEnemies >= 2 then
        Aware:displayMessage("Leg Sweep - AoE CC", "Green", 1)
        return spell:Cast()
    end
end)

Paralysis:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:DeBuff(debuffs.paralysis) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets
    if gameState.inCombat then return end -- Don't use in combat

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Paralysis - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

KegSmash:Callback("arena", function(spell, enemy)
    if enemy.distance > 15 then return end

    return spell:Cast(enemy)
end)

BreathOfFire:Callback("arena", function(spell, enemy)
    if enemy.distance > 8 then return end
    if enemy:DeBuff(debuffs.breathOfFire) then return end

    return spell:Cast()
end)

BlackoutKick:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.chi < 2 then return end

    -- Priority on low health targets
    if enemy.hp < 40 then
        Aware:displayMessage("Priority Blackout Kick", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    SpearHandStrike("arena", enemy)

    -- CC abilities
    LegSweep("arena", enemy)
    Paralysis("arena", enemy)

    -- Damage and threat
    KegSmash("arena", enemy)
    BreathOfFire("arena", enemy)
    BlackoutKick("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end

    -- Detox for party members
    if friendly:DeBuff("Disease") or friendly:DeBuff("Poison") then
        Detox("arena", friendly)
    end

    -- Emergency healing
    if friendly.hp < 30 then
        ExpelHarm("arena", friendly)
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end
