-- APL UPDATE MoP Mistweaver Monk
-- Mists of Pandaria Mistweaver Monk Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 270 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Mistweaver Monk Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Mistweaver Core Healing Abilities
    SoothingMist = { ID = 115175, MAKULU_INFO = { heal = true, channeled = true } },
    RenewingMist = { ID = 115151, MAKULU_INFO = { heal = true } },
    Uplift = { ID = 116670, MAKULU_INFO = { heal = true } },
    Surging = { ID = 116694, MAKULU_INFO = { heal = true } },
    Enveloping = { ID = 132120, MAKULU_INFO = { heal = true, channeled = true } },
    HealingSphere = { ID = 115460, MAKULU_INFO = { heal = true } },
    
    -- MoP Instant Heals
    ExpelHarm = { ID = 115072, MAKULU_INFO = { heal = true, targeted = false } },
    ChiBrew = { ID = 115399, MAKULU_INFO = { targeted = false } },
    ManaTea = { ID = 115294, MAKULU_INFO = { targeted = false } },
    
    -- MoP Cooldowns
    Revival = { ID = 115310, MAKULU_INFO = { heal = true, targeted = false } },
    LifeCocoon = { ID = 116849, MAKULU_INFO = { heal = true } },
    ThunderFocusTea = { ID = 116680, MAKULU_INFO = { targeted = false } },
    
    -- MoP Damage Abilities
    TigerPalm = { ID = 100780, MAKULU_INFO = { damageType = "physical" } },
    BlackoutKick = { ID = 100784, MAKULU_INFO = { damageType = "physical" } },
    RisingSunKick = { ID = 107428, MAKULU_INFO = { damageType = "physical" } },
    SpinningCraneKick = { ID = 101546, MAKULU_INFO = { damageType = "physical", channeled = true } },
    CracklingJadeLightning = { ID = 117952, MAKULU_INFO = { damageType = "nature", channeled = true } },
    
    -- MoP Utility and Movement
    Roll = { ID = 109132, MAKULU_INFO = { targeted = false } },
    ChiTorpedo = { ID = 115008, MAKULU_INFO = { targeted = false } },
    Transcendence = { ID = 101643, MAKULU_INFO = { targeted = false } },
    TranscendenceTransfer = { ID = 119996, MAKULU_INFO = { targeted = false } },
    
    -- MoP Defensive Abilities
    FortifyingBrew = { ID = 115203, MAKULU_INFO = { targeted = false } },
    TouchOfKarma = { ID = 122470, MAKULU_INFO = { targeted = false } },
    DiffuseMagic = { ID = 122783, MAKULU_INFO = { targeted = false } },
    DampenHarm = { ID = 122278, MAKULU_INFO = { targeted = false } },
    
    -- MoP Crowd Control
    Paralysis = { ID = 115078, MAKULU_INFO = { castTime = 1000 } },
    LegSweep = { ID = 119381, MAKULU_INFO = { damageType = "physical" } },
    SpearHandStrike = { ID = 116705, MAKULU_INFO = { damageType = "physical", ignoreCasting = true } },
    
    -- MoP Talents
    ChiWave = { ID = 115098, MAKULU_INFO = { heal = true } },
    ChiBurst = { ID = 123986, MAKULU_INFO = { heal = true, castTime = 1000 } },
    ZenSphere = { ID = 124081, MAKULU_INFO = { heal = true } },
    PowerStrikes = { ID = 121817, MAKULU_INFO = { targeted = false } },
    Ascension = { ID = 115396, MAKULU_INFO = { targeted = false } },
    RingOfPeace = { ID = 116844, MAKULU_INFO = { targeted = false } },
    ChargingOxWave = { ID = 119392, MAKULU_INFO = { damageType = "physical" } },
    HealingElixirs = { ID = 122280, MAKULU_INFO = { heal = true, targeted = false } },
    InvokeXuen = { ID = 123904, MAKULU_INFO = { targeted = false } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 116705, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 115078, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
    local result = {}
    for k, v in pairs(A) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_MONK_MISTWEAVER] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Mistweaver Monk Buffs
local buffs = {
    renewingMist = 119611,
    soothingMist = 115175,
    envelopingMist = 132120,
    uplift = 116670,
    manaTea = 115867,
    thunderFocusTea = 116680,
    lifeCocoon = 116849,
    fortifyingBrew = 115203,
    touchOfKarma = 122470,
    diffuseMagic = 122783,
    dampenHarm = 122278,
    powerStrikes = 129914,
    chiWave = 115098,
    chiBurst = 123986,
    zenSphere = 124081,
    transcendence = 101643,
    teachingsOfTheMonastery = 202090,
    vitalMists = 118674,
    serpentsZeal = 127722,
    muscleMemory = 139598,
    tigerPower = 125359,
    legacyOfTheEmperor = 117666,
    legacyOfTheWhiteTiger = 116781,
}

-- MoP Mistweaver Monk Debuffs
local debuffs = {
    risingKick = 107428,
    paralysis = 115078,
    legSweep = 119381,
    disableRoot = 116706,
    disableSlow = 116095,
    dizzying = 116330,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    chi = 0,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.chi = player.chi or 0
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.channeling = player:IsCasting() or player:IsChanneling()
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsHealing(unit, threshold)
    return unit.exists and unit.hp < threshold
end

local function getLowestHealthPartyMember()
    local lowest = player
    local lowestHP = player.hp
    
    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember.hp < lowestHP then
            lowest = partyMember
            lowestHP = partyMember.hp
        end
    end
    
    return lowest
end

local function getPartyMembersWithRenewingMist()
    local count = 0
    if player:HasBuff(buffs.renewingMist) then count = count + 1 end
    
    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember:HasBuff(buffs.renewingMist) then
            count = count + 1
        end
    end
    
    return count
end

local function getInjuredPartyCount(threshold)
    local count = 0
    if player.hp < threshold then count = count + 1 end

    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember.hp < threshold then
            count = count + 1
        end
    end

    return count
end

-- Core healing callbacks
SoothingMist:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 80 and not gameState.channeling then
        return spell:Cast(healTarget)
    end
end)

RenewingMist:Callback(function(spell)
    -- Priority on targets without Renewing Mist
    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember.hp <= 90 and not partyMember:HasBuff(buffs.renewingMist) then
            return spell:Cast(partyMember)
        end
    end

    if player.hp <= 90 and not player:HasBuff(buffs.renewingMist) then
        return spell:Cast(player)
    end
end)

Uplift:Callback(function(spell)
    if gameState.chi < 2 then return end

    local renewingMistCount = getPartyMembersWithRenewingMist()
    local injuredCount = getInjuredPartyCount(80)

    -- Use Uplift when we have multiple Renewing Mists and injured party members
    if renewingMistCount >= 2 and injuredCount >= 2 then
        return spell:Cast(player)
    end
end)

Surging:Callback(function(spell)
    if gameState.chi < 1 then return end

    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 60 then
        return spell:Cast(healTarget)
    end
end)

Enveloping:Callback(function(spell)
    if gameState.chi < 3 then return end

    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 50 and not healTarget:HasBuff(buffs.envelopingMist) then
        return spell:Cast(healTarget)
    end
end)

HealingSphere:Callback(function(spell)
    if gameState.chi < 2 then return end

    local injuredCount = getInjuredPartyCount(70)
    if injuredCount >= 2 then
        return spell:Cast(target)
    end
end)

ExpelHarm:Callback(function(spell)
    if player.hp <= 70 then
        return spell:Cast(player)
    end
end)

-- Cooldown callbacks
Revival:Callback(function(spell)
    local injuredCount = getInjuredPartyCount(50)
    if injuredCount >= 3 or (gameState.isPvP and injuredCount >= 2) then
        return spell:Cast(player)
    end
end)

LifeCocoon:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 25 then
        return spell:Cast(healTarget)
    end
end)

ThunderFocusTea:Callback(function(spell)
    if shouldBurst() or getLowestHealthPartyMember().hp <= 40 then
        return spell:Cast(player)
    end
end)

ManaTea:Callback(function(spell)
    if gameState.mana <= 50 and player:HasBuff(buffs.manaTea) then
        return spell:Cast(player)
    end
end)

ChiBrew:Callback(function(spell)
    if gameState.chi <= 1 and (shouldBurst() or getLowestHealthPartyMember().hp <= 50) then
        return spell:Cast(player)
    end
end)

-- Defensive callbacks
FortifyingBrew:Callback(function(spell)
    if player.hp <= 50 then
        return spell:Cast(player)
    end
end)

TouchOfKarma:Callback(function(spell)
    if player.hp <= 65 and target.exists and target.canAttack then
        return spell:Cast(target)
    end
end)

DiffuseMagic:Callback(function(spell)
    if player.hp <= 40 and player:HasDebuffType("Magic") then
        return spell:Cast(player)
    end
end)

DampenHarm:Callback(function(spell)
    if player.hp <= 50 then
        return spell:Cast(player)
    end
end)

-- Damage callbacks
TigerPalm:Callback(function(spell)
    if target.exists and target.canAttack and gameState.chi < 4 then
        return spell:Cast(target)
    end
end)

BlackoutKick:Callback(function(spell)
    if target.exists and target.canAttack and gameState.chi >= 2 then
        return spell:Cast(target)
    end
end)

RisingSunKick:Callback(function(spell)
    if target.exists and target.canAttack and gameState.chi >= 2 then
        return spell:Cast(target)
    end
end)

SpinningCraneKick:Callback(function(spell)
    if shouldAoE() and gameState.activeEnemies >= 3 and gameState.chi >= 1 then
        return spell:Cast(player)
    end
end)

CracklingJadeLightning:Callback(function(spell)
    if target.exists and target.canAttack and target.distance > 5 and not gameState.channeling then
        return spell:Cast(target)
    end
end)

-- Utility callbacks
Paralysis:Callback(function(spell)
    if target.exists and target.canAttack and target:IsCasting() and target:IsInterruptible() then
        return spell:Cast(target)
    end
end)

SpearHandStrike:Callback(function(spell)
    if target.exists and target:IsCasting() and target:IsInterruptible() then
        return spell:Cast(target)
    end
end)

LegSweep:Callback(function(spell)
    if shouldAoE() and gameState.activeEnemies >= 2 then
        return spell:Cast(player)
    end
end)

-- Talent callbacks
ChiWave:Callback(function(spell)
    local injuredCount = getInjuredPartyCount(80)
    if injuredCount >= 2 or (target.exists and target.canAttack) then
        return spell:Cast(target.exists and target.canAttack and target or player)
    end
end)

ChiBurst:Callback(function(spell)
    local injuredCount = getInjuredPartyCount(70)
    if injuredCount >= 3 then
        return spell:Cast(target.exists and target.canAttack and target or player)
    end
end)

ZenSphere:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 70 and not healTarget:HasBuff(buffs.zenSphere) then
        return spell:Cast(healTarget)
    end
end)

InvokeXuen:Callback(function(spell)
    if shouldBurst() and target.exists and target.canAttack then
        return spell:Cast(target)
    end
end)

-- Main rotation function
local function mainRotation()
    updateGameState()

    -- Emergency healing
    if player.hp <= 20 then
        LifeCocoon()
        Revival()
    end

    -- Defensive abilities
    if player.hp <= 50 then
        FortifyingBrew()
        DampenHarm()
        TouchOfKarma()
    end

    if player.hp <= 40 then
        DiffuseMagic()
    end

    -- Mana management
    ManaTea()

    -- Chi management
    if gameState.chi <= 1 then
        ChiBrew()
    end

    -- Core healing rotation
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 30 then
        if gameState.chi >= 3 then
            Enveloping()
        elseif gameState.chi >= 2 then
            Surging()
        else
            SoothingMist()
        end
    end

    -- Renewing Mist maintenance
    RenewingMist()

    -- AoE healing
    local injuredCount = getInjuredPartyCount(80)
    if injuredCount >= 2 then
        Uplift()
        HealingSphere()
        ChiWave()
        ChiBurst()
    end

    -- Zen Sphere maintenance
    ZenSphere()

    -- Self healing
    ExpelHarm()

    -- Combat rotation
    if target.exists and target.alive then
        -- Interrupt priority
        SpearHandStrike()
        Paralysis()

        -- Cooldowns
        if shouldBurst() then
            ThunderFocusTea()
            InvokeXuen()
        end

        -- Chi generation and damage
        if gameState.chi < 4 then
            TigerPalm()
        end

        -- Chi spenders
        if shouldAoE() then
            SpinningCraneKick()
        else
            BlackoutKick()
            RisingSunKick()
        end

        -- Ranged damage
        CracklingJadeLightning()

        -- AoE CC
        LegSweep()
    end
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Pre-heal before adds spawn
    if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
        -- Top everyone off before adds
        local healTarget = getLowestHealthPartyMember()
        if healTarget and healTarget.hp <= 95 then
            if gameState.chi >= 3 then
                Enveloping()
            elseif gameState.chi >= 2 then
                Surging()
            else
                SoothingMist()
            end
        end

        -- Spread Renewing Mist
        RenewingMist()

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            ThunderFocusTea()
            ChiBrew()
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        -- AoE healing priority
        Uplift()
        HealingSphere()
        Revival()

        -- AoE damage
        SpinningCraneKick()
        LegSweep()
        ChiBurst()
    end
end

-- PvP specific functions
local function pvpRotation()
    updateGameState()

    -- Emergency healing in PvP
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 25 then
        LifeCocoon()
        if gameState.chi >= 3 then
            Enveloping()
        else
            SoothingMist()
        end
    end

    -- Defensive priority in PvP
    if player.hp <= 40 then
        FortifyingBrew()
        DampenHarm()
        TouchOfKarma()
        DiffuseMagic()
    end

    -- Mobility and utility
    Roll()
    ChiTorpedo()

    -- Offensive capabilities in PvP
    if target.exists and target.canAttack then
        SpearHandStrike()
        Paralysis()
        LegSweep()

        -- Damage rotation
        if gameState.chi < 4 then
            TigerPalm()
        end

        BlackoutKick()
        RisingSunKick()
        CracklingJadeLightning()
    end

    -- Healing rotation
    RenewingMist()
    Uplift()
    Surging()
    ChiWave()
    ExpelHarm()
end

-- Enhanced rotation for single target
local function singleTargetRotation()
    -- Priority healing
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 70 then
        if gameState.chi >= 3 then
            Enveloping()
        elseif gameState.chi >= 2 then
            Surging()
        else
            SoothingMist()
        end
    end

    -- Renewing Mist maintenance
    RenewingMist()

    -- Zen Sphere
    ZenSphere()

    -- Self healing
    ExpelHarm()

    -- Damage rotation when healing not needed
    if target.exists and target.canAttack then
        if gameState.chi < 4 then
            TigerPalm()
        end

        BlackoutKick()
        RisingSunKick()
        CracklingJadeLightning()
    end
end

-- Enhanced rotation for AoE
local function aoeRotation()
    -- AoE healing priority
    Uplift()
    HealingSphere()
    Revival()

    -- Individual healing for critical targets
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 40 then
        if gameState.chi >= 3 then
            Enveloping()
        elseif gameState.chi >= 2 then
            Surging()
        else
            SoothingMist()
        end
    end

    -- Renewing Mist spread
    RenewingMist()

    -- Talent AoE heals
    ChiBurst()
    ChiWave()

    -- AoE damage
    SpinningCraneKick()
    LegSweep()
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency responses
    if player.hp <= 20 then
        LifeCocoon()
        Revival()
    end

    if player.hp <= 50 then
        FortifyingBrew()
        DampenHarm()
        TouchOfKarma()
    end

    if player.hp <= 40 then
        DiffuseMagic()
    end

    -- Resource management
    ManaTea()
    if gameState.chi <= 1 then
        ChiBrew()
    end

    -- Self healing
    ExpelHarm()

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Mana: ", gameState.mana)
        MakPrint(3, "Chi: ", gameState.chi)
        MakPrint(4, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(5, "In Combat: ", gameState.inCombat)
        MakPrint(6, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(7, "Is PvP: ", gameState.isPvP)
        MakPrint(8, "Channeling: ", gameState.channeling)
        MakPrint(9, "Renewing Mist Count: ", getPartyMembersWithRenewingMist())
        MakPrint(10, "Thunder Focus Tea: ", player:HasBuff(buffs.thunderFocusTea))

        local healTarget = getLowestHealthPartyMember()
        if healTarget then
            MakPrint(11, "Lowest HP Target: ", healTarget.hp)
        end
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if Revival:IsReady() and getInjuredPartyCount(50) >= 3 then
            Aware:displayMessage("REVIVAL READY", "Yellow", 1)
        end
        if LifeCocoon:IsReady() and getLowestHealthPartyMember().hp <= 25 then
            Aware:displayMessage("LIFE COCOON READY", "Blue", 1)
        end
        if ThunderFocusTea:IsReady() and shouldBurst() then
            Aware:displayMessage("THUNDER FOCUS TEA READY", "Red", 1)
        end
        if gameState.chi >= 4 then
            Aware:displayMessage("MAX CHI", "Green", 1)
        end
        if player:HasBuff(buffs.thunderFocusTea) then
            Aware:displayMessage("THUNDER FOCUS TEA ACTIVE", "White", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Purple", 1)
        end
        if getPartyMembersWithRenewingMist() >= 3 then
            Aware:displayMessage("UPLIFT READY", "Cyan", 1)
        end
    end

    -- Enhanced healing priority
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 25 then
        LifeCocoon()
        if gameState.chi >= 3 then
            Enveloping()
        else
            SoothingMist()
        end
        Revival()
    end

    -- Emergency cooldowns
    if player.hp <= 20 then
        FortifyingBrew()
        DampenHarm()
        TouchOfKarma()
    end

    if player.hp <= 40 then
        DiffuseMagic()
    end

    -- Resource management
    ManaTea()
    ChiBrew()

    if target.exists and target.alive then
        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                SpearHandStrike()
                Paralysis()
                LegSweep()
            end
        end

        -- Burst phase
        if shouldBurst() then
            ThunderFocusTea()
            InvokeXuen()

            -- Trinket usage during burst
            local damagePotion = Action.GetToggle(2, "damagePotion")
            if damagePotion and player:HasBuff(buffs.thunderFocusTea) then
                -- Use damage potions during burst cooldowns
            end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        SpearHandStrike()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Mistweaver Monk
SpearHandStrike:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Paralysis:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:HasDeBuff(115078) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Paralysis - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Paralysis - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

LegSweep:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy:HasDeBuff(119381) then return end
    if enemy.hp < 40 then return end

    -- Use on multiple enemies
    if gameState.activeEnemies >= 2 then
        Aware:displayMessage("Leg Sweep - AoE", "Red", 1)
        return spell:Cast(enemy)
    end
end)

SoothingMist:Callback("arena", function(spell, friendly)
    -- Prioritize healing in arena
    if friendly.distance > 40 then return end
    if friendly.hp > 70 then return end
    if gameState.channeling then return end

    return spell:Cast(friendly)
end)

RenewingMist:Callback("arena", function(spell, friendly)
    if friendly.distance > 40 then return end
    if friendly:HasBuff(buffs.renewingMist) then return end
    if friendly.hp > 90 then return end

    -- Priority on low health targets
    if friendly.hp < 60 then
        Aware:displayMessage("Priority Renewing Mist", "Blue", 1)
        return spell:Cast(friendly)
    end

    return spell:Cast(friendly)
end)

Surging:Callback("arena", function(spell, friendly)
    if friendly.distance > 40 then return end
    if gameState.chi < 1 then return end
    if friendly.hp > 60 then return end

    -- Emergency healing
    if friendly.hp < 30 then
        Aware:displayMessage("Surging Mist - Emergency", "Red", 1)
        return spell:Cast(friendly)
    end

    return spell:Cast(friendly)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    SpearHandStrike("arena", enemy)

    -- CC abilities
    Paralysis("arena", enemy)
    LegSweep("arena", enemy)

    -- Damage
    if gameState.chi < 4 then
        TigerPalm("arena", enemy)
    end
    BlackoutKick("arena", enemy)
    RisingSunKick("arena", enemy)
    CracklingJadeLightning("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end

    -- Healing priority
    SoothingMist("arena", friendly)
    RenewingMist("arena", friendly)
    Surging("arena", friendly)

    -- Emergency healing
    if friendly.hp < 30 then
        if gameState.chi >= 3 then
            Enveloping("arena", friendly)
        end
        LifeCocoon("arena", friendly)
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and gameState.mana < 50 then
        return spell:Cast(player)
    end
end)

Berserking:Callback(function(spell)
    if shouldBurst() and (getLowestHealthPartyMember().hp <= 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

BloodFury:Callback(function(spell)
    if shouldBurst() and (getLowestHealthPartyMember().hp <= 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

GiftOfTheNaaru:Callback(function(spell)
    if player.hp <= 60 then
        return spell:Cast(player)
    end
end)

QuakingPalm:Callback(function(spell)
    if target.exists and target.canAttack and target:IsCasting() then
        return spell:Cast(target)
    end
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    GiftOfTheNaaru()
    QuakingPalm()
end

local function mopTalents()
    ChiWave()
    ChiBurst()
    ZenSphere()
    InvokeXuen()
end

local function baseStuff()
    ManaTea()
    ChiBrew()
    FortifyingBrew()
    TouchOfKarma()
    DiffuseMagic()
    DampenHarm()
end

-- Enhanced utility for MoP
local function mopUtility()
    SpearHandStrike()
    Paralysis()
    LegSweep()
    Roll()
    ChiTorpedo()
    Transcendence()
    TranscendenceTransfer()
end
