-- APL UPDATE MoP Windwalker Monk
-- Mists of Pandaria Windwalker Monk Rotation

if not Ma<PERSON>luValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 269 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Windwalker Monk Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- Core Abilities - Chi Builders
    Jab = { ID = 100780, MAKULU_INFO = { damageType = "physical" } },
    ExpelHarm = { ID = 115072, MAKULU_INFO = { heal = true, targeted = false } },
    
    -- Core Abilities - Chi Spenders
    TigerPalm = { ID = 100787, MAKULU_INFO = { damageType = "physical" } },
    BlackoutKick = { ID = 100784, MAKULU_INFO = { damageType = "physical" } },
    RisingSunKick = { ID = 107428, MAKULU_INFO = { damageType = "physical" } },
    FistsOfFury = { ID = 113656, MAKULU_INFO = { damageType = "physical", castTime = 4000 } },
    
    -- AoE Abilities
    SpinningCraneKick = { ID = 101546, MAKULU_INFO = { damageType = "physical", castTime = 3000 } },
    CracklingJadeLightning = { ID = 117952, MAKULU_INFO = { damageType = "nature", castTime = 4000 } },
    
    -- Utility Abilities
    SpearHandStrike = { ID = 116705, MAKULU_INFO = { targeted = true } },
    Paralysis = { ID = 115078, MAKULU_INFO = { targeted = true, castTime = 4000 } },
    LegSweep = { ID = 119381, MAKULU_INFO = { targeted = false } },
    Roll = { ID = 109132, MAKULU_INFO = { targeted = false } },
    ChiTorpedo = { ID = 115008, MAKULU_INFO = { targeted = false } },
    FlyingSerpentKick = { ID = 101545, MAKULU_INFO = { damageType = "physical" } },
    
    -- Defensive Abilities
    FortifyingBrew = { ID = 115203, MAKULU_INFO = { targeted = false } },
    DampenHarm = { ID = 122278, MAKULU_INFO = { targeted = false } },
    DiffuseMagic = { ID = 122783, MAKULU_INFO = { targeted = false } },
    TouchOfKarma = { ID = 122470, MAKULU_INFO = { targeted = false } },
    ZenMeditation = { ID = 115176, MAKULU_INFO = { targeted = false, castTime = 8000 } },
    
    -- Cooldowns
    EnergizingBrew = { ID = 115288, MAKULU_INFO = { targeted = false } },
    TigereyeBrew = { ID = 116740, MAKULU_INFO = { targeted = false } },
    TouchOfDeath = { ID = 115080, MAKULU_INFO = { damageType = "physical" } },
    StormEarthAndFire = { ID = 137639, MAKULU_INFO = { targeted = false } },
    
    -- MoP Talents
    ChiWave = { ID = 115098, MAKULU_INFO = { heal = true } },
    ChiBurst = { ID = 123986, MAKULU_INFO = { heal = true, castTime = 1000 } },
    ZenSphere = { ID = 124081, MAKULU_INFO = { heal = true } },
    PowerStrikes = { ID = 121817, MAKULU_INFO = { targeted = false } },
    Ascension = { ID = 115396, MAKULU_INFO = { targeted = false } },
    ChiBrew = { ID = 115399, MAKULU_INFO = { targeted = false } },
    InvokeXuen = { ID = 123904, MAKULU_INFO = { targeted = false } },
    RushingJadeWind = { ID = 116847, MAKULU_INFO = { damageType = "physical" } },
    
    -- Healing
    HealingSphere = { ID = 115460, MAKULU_INFO = { heal = true, targeted = false } },
    
    -- Movement
    Transcendence = { ID = 101643, MAKULU_INFO = { targeted = false } },
    TranscendenceTransfer = { ID = 119996, MAKULU_INFO = { targeted = false } },
    
    -- Ranged
    SpinningFireBlossom = { ID = 123408, MAKULU_INFO = { damageType = "fire" } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 116705, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 115078, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
    local result = {}
    for k, v in pairs(A) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_MONK_WINDWALKER] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Windwalker Monk Buffs
local buffs = {
    tigerPower = 125359,
    stormEarthAndFire = 137639,
    energizingBrew = 115288,
    tigereyeBrew = 116740,
    fortifyingBrew = 115203,
    dampenHarm = 122278,
    diffuseMagic = 122783,
    touchOfKarma = 122470,
    powerStrikes = 129914,
    chiWave = 115098,
    chiBurst = 123986,
    zenSphere = 124081,
    transcendence = 101643,
    teachingsOfTheMonastery = 202090,
    muscleMemory = 139598,
    legacyOfTheEmperor = 117666,
    legacyOfTheWhiteTiger = 116781,
    comboBreaker = 118864,
    comboBreakerTigerPalm = 118864,
    comboBreakerBlackoutKick = 116768,
    ascension = 115396,
    rushingJadeWind = 116847,
}

-- MoP Windwalker Monk Debuffs
local debuffs = {
    risingKick = 107428,
    paralysis = 115078,
    legSweep = 119381,
    disableRoot = 116706,
    disableSlow = 116095,
    dizzying = 116330,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    energy = 0,
    chi = 0,
    timeToAdds = 999,
    isPvP = false,
    tigerPowerRemaining = 0,
    risingKickDebuffRemaining = 0,
    tigereyeBrewStacks = 0,
    comboBreakerTigerPalm = false,
    comboBreakerBlackoutKick = false,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.energy = player.energy or 0
    gameState.chi = player.chi or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.tigerPowerRemaining = player:BuffRemains(buffs.tigerPower) or 0
    gameState.risingKickDebuffRemaining = target:DeBuffRemains(debuffs.risingKick) or 0
    gameState.tigereyeBrewStacks = player:BuffStacks(buffs.tigereyeBrew) or 0
    gameState.comboBreakerTigerPalm = player:HasBuff(buffs.comboBreakerTigerPalm)
    gameState.comboBreakerBlackoutKick = player:HasBuff(buffs.comboBreakerBlackoutKick)
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function needsEnergy()
    return gameState.energy < 40
end

local function needsChi()
    return gameState.chi <= 2
end

local function hasHighChi()
    return gameState.chi >= 3
end

local function canUseFistsOfFury()
    return hasHighChi() and
           RisingSunKick:Cooldown() > 4000 and
           gameState.tigerPowerRemaining > 5000 and
           gameState.energy < 60
end

-- Core ability callbacks
Jab:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if gameState.chi >= 4 then return end -- Don't waste chi

    -- Primary chi builder
    return spell:Cast(target)
end)

ExpelHarm:Callback(function(spell)
    if player.hp >= 95 then return end
    if gameState.chi >= 4 then return end

    -- Use when injured and need chi
    return spell:Cast(player)
end)

TigerPalm:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if gameState.chi < 1 then return end

    -- Maintain Tiger Power buff or use Combo Breaker proc
    if gameState.tigerPowerRemaining < 3000 or gameState.comboBreakerTigerPalm then
        return spell:Cast(target)
    end
end)

BlackoutKick:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if gameState.chi < 2 then return end

    -- Use with Combo Breaker proc or as chi spender
    if gameState.comboBreakerBlackoutKick or (hasHighChi() and not RisingSunKick:IsReady()) then
        return spell:Cast(target)
    end
end)

RisingSunKick:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if gameState.chi < 2 then return end

    -- High priority - maintain debuff
    return spell:Cast(target)
end)

FistsOfFury:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if gameState.chi < 3 then return end

    -- Use when conditions are met
    if canUseFistsOfFury() then
        return spell:Cast(target)
    end
end)

-- AoE abilities
SpinningCraneKick:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if not shouldAoE() then return end
    if gameState.chi < 2 then return end

    return spell:Cast(target)
end)

RushingJadeWind:Callback(function(spell)
    if not shouldAoE() then return end
    if gameState.chi < 1 then return end

    -- High priority for AoE
    return spell:Cast(player)
end)

CracklingJadeLightning:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 40 then return end
    if gameState.chi >= 3 then return end -- Don't use when we have chi to spend

    -- Filler when no energy/chi
    if gameState.energy < 20 and gameState.chi < 1 then
        return spell:Cast(target)
    end
end)

-- Utility abilities
SpearHandStrike:Callback(function(spell)
    if target.distance > 5 then return end
    if not target:IsCasting() then return end
    if not target:IsInterruptible() then return end

    return spell:Cast(target)
end)

Paralysis:Callback(function(spell)
    if target.distance > 20 then return end
    if target:HasDeBuff(debuffs.paralysis) then return end
    if not gameState.isPvP then return end

    -- PvP CC
    return spell:Cast(target)
end)

LegSweep:Callback(function(spell)
    if target.distance > 5 then return end
    if not shouldAoE() and not gameState.isPvP then return end

    return spell:Cast(target)
end)

-- Movement abilities
Roll:Callback(function(spell)
    if target.distance < 8 then return end
    if not target.exists then return end

    -- Mobility when out of range
    return spell:Cast(player)
end)

FlyingSerpentKick:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 25 then return end
    if gameState.energy > 20 or gameState.chi > 0 then return end

    -- Last resort filler
    return spell:Cast(target)
end)

-- Defensive abilities
FortifyingBrew:Callback(function(spell)
    if player.hp > 60 then return end

    return spell:Cast(player)
end)

DampenHarm:Callback(function(spell)
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

DiffuseMagic:Callback(function(spell)
    if player.hp > 40 then return end

    return spell:Cast(player)
end)

TouchOfKarma:Callback(function(spell)
    if player.hp > 30 then return end
    if not target.exists or not target.canAttack then return end

    return spell:Cast(target)
end)

ZenMeditation:Callback(function(spell)
    if player.hp > 20 then return end
    if gameState.isPvP then return end -- Don't channel in PvP

    return spell:Cast(player)
end)

-- Cooldowns
EnergizingBrew:Callback(function(spell)
    if gameState.energy > 40 then return end

    -- Use to avoid energy capping
    return spell:Cast(player)
end)

TigereyeBrew:Callback(function(spell)
    if gameState.tigereyeBrewStacks < 10 then return end
    if not shouldBurst() and gameState.tigereyeBrewStacks < 20 then return end

    -- Use at 10+ stacks or when bursting
    return spell:Cast(player)
end)

TouchOfDeath:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if target.hp > player.hp then return end

    -- Instant kill when target has less HP than player
    return spell:Cast(target)
end)

StormEarthAndFire:Callback(function(spell)
    if not shouldBurst() and gameState.activeEnemies < 2 then return end
    if target.totalImmune or target.physImmune then return end

    -- Use for burst or AoE
    return spell:Cast(target)
end)

-- Talents
ChiWave:Callback(function(spell)
    if gameState.energy > 80 then return end -- Don't use when about to cap energy

    return spell:Cast(target)
end)

ChiBurst:Callback(function(spell)
    if gameState.energy > 80 then return end
    if not shouldAoE() then return end

    return spell:Cast(target)
end)

ChiBrew:Callback(function(spell)
    if gameState.chi > 2 then return end

    -- Use to generate chi when low
    return spell:Cast(player)
end)

InvokeXuen:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    -- Major DPS cooldown
    return spell:Cast(target)
end)

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if gameState.energy > 70 then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(player)
end)

BloodFury:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(player)
end)

QuakingPalm:Callback(function(spell)
    if target.distance > 5 then return end
    if not target:IsCasting() then return end

    return spell:Cast(target)
end)

-- Enhanced rotation functions
local function singleTargetRotation()
    -- Priority order for single target based on MoP Windwalker rotation

    -- Interrupt first
    SpearHandStrike()

    -- Maintain Tiger Power buff
    if gameState.tigerPowerRemaining < 3000 or gameState.comboBreakerTigerPalm then
        TigerPalm()
    end

    -- Maintain Rising Sun Kick debuff
    if gameState.risingKickDebuffRemaining < 3000 then
        RisingSunKick()
    end

    -- Use Fists of Fury when conditions are met
    if canUseFistsOfFury() then
        FistsOfFury()
    end

    -- Use Combo Breaker procs
    if gameState.comboBreakerBlackoutKick then
        BlackoutKick()
    end

    -- Chi Wave for damage/healing
    ChiWave()

    -- Chi spenders when we have chi
    if hasHighChi() then
        BlackoutKick()
    end

    -- Chi builders when we need chi
    if needsChi() then
        if player.hp < 90 then
            ExpelHarm()
        else
            Jab()
        end
    end

    -- Filler abilities
    if gameState.energy < 20 and gameState.chi < 1 then
        CracklingJadeLightning()
        FlyingSerpentKick()
    end
end

local function aoeRotation()
    -- Priority order for AoE (3+ enemies)

    -- Interrupt priority target
    SpearHandStrike()

    -- Storm, Earth, and Fire for 2+ targets
    if gameState.activeEnemies >= 2 then
        StormEarthAndFire()
    end

    -- Rushing Jade Wind for heavy AoE
    if gameState.activeEnemies >= 3 then
        RushingJadeWind()
    end

    -- Maintain Tiger Power
    if gameState.tigerPowerRemaining < 3000 then
        TigerPalm()
    end

    -- Maintain Rising Sun Kick debuff
    if gameState.risingKickDebuffRemaining < 3000 then
        RisingSunKick()
    end

    -- AoE abilities
    if gameState.activeEnemies >= 3 then
        SpinningCraneKick()
        ChiBurst()
    end

    -- Use Fists of Fury for AoE damage
    if canUseFistsOfFury() and gameState.activeEnemies >= 2 then
        FistsOfFury()
    end

    -- Chi builders
    if needsChi() then
        if player.hp < 90 then
            ExpelHarm()
        else
            Jab()
        end
    end

    -- AoE CC
    LegSweep()
end

local function pvpRotation()
    -- PvP specific rotation with enhanced burst and control

    -- Defensive priority
    if player.hp <= 25 then
        TouchOfKarma()
        ZenMeditation()
    end

    if player.hp <= 50 then
        FortifyingBrew()
        DampenHarm()
        DiffuseMagic()
    end

    -- Interrupt and control
    SpearHandStrike()
    Paralysis()
    LegSweep()

    -- Burst damage
    if shouldBurst() then
        TigereyeBrew()
        StormEarthAndFire()
        InvokeXuen()
    end

    -- Mobility
    Roll()
    ChiTorpedo()

    -- Core rotation with PvP priorities
    -- Maintain Tiger Power
    if gameState.tigerPowerRemaining < 3000 or gameState.comboBreakerTigerPalm then
        TigerPalm()
    end

    -- Maintain Rising Sun Kick debuff
    if gameState.risingKickDebuffRemaining < 3000 then
        RisingSunKick()
    end

    -- Use Touch of Death on low targets
    TouchOfDeath()

    -- Combo Breaker procs
    if gameState.comboBreakerBlackoutKick then
        BlackoutKick()
    end

    -- Chi spenders
    if hasHighChi() then
        BlackoutKick()
        FistsOfFury()
    end

    -- Chi builders
    if needsChi() then
        if player.hp < 90 then
            ExpelHarm()
        else
            Jab()
        end
    end
end

local function timeToAddsRotation()
    -- Prepare for incoming adds

    -- Save cooldowns for adds
    if gameState.timeToAdds < 5000 then
        -- Use cooldowns just before adds spawn
        TigereyeBrew()
        StormEarthAndFire()
        InvokeXuen()
    end

    -- Pool energy and chi for burst
    if gameState.energy < 80 and gameState.chi < 3 then
        EnergizingBrew()
        ChiBrew()
    else
        -- Continue normal rotation but conserve resources
        RisingSunKick()
        TigerPalm()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency responses
    if player.hp <= 20 then
        TouchOfKarma()
        ZenMeditation()
    end

    if player.hp <= 40 then
        FortifyingBrew()
        DampenHarm()
        DiffuseMagic()
    end

    -- Resource management
    if needsEnergy() then
        EnergizingBrew()
    end

    if needsChi() then
        ChiBrew()
    end

    -- Racial abilities during burst
    if shouldBurst() then
        BloodFury()
        Berserking()
        ArcaneTorrent()
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Energy: ", gameState.energy)
        MakPrint(3, "Player Chi: ", gameState.chi)
        MakPrint(4, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(5, "In Combat: ", gameState.inCombat)
        MakPrint(6, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(7, "Is PvP: ", gameState.isPvP)
        MakPrint(8, "Tiger Power Remaining: ", gameState.tigerPowerRemaining)
        MakPrint(9, "Rising Kick Debuff: ", gameState.risingKickDebuffRemaining)
        MakPrint(10, "Tigereye Brew Stacks: ", gameState.tigereyeBrewStacks)
        MakPrint(11, "Combo Breaker Tiger Palm: ", gameState.comboBreakerTigerPalm)
        MakPrint(12, "Combo Breaker Blackout Kick: ", gameState.comboBreakerBlackoutKick)
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if TigereyeBrew:IsReady() and gameState.tigereyeBrewStacks >= 10 then
            Aware:displayMessage("TIGEREYE BREW READY (" .. gameState.tigereyeBrewStacks .. " stacks)", "Red", 1)
        end
        if InvokeXuen:IsReady() and shouldBurst() then
            Aware:displayMessage("INVOKE XUEN READY", "Blue", 1)
        end
        if TouchOfDeath:IsReady() and target.exists and target.hp <= player.hp then
            Aware:displayMessage("TOUCH OF DEATH READY", "Purple", 1)
        end
        if gameState.energy >= 95 then
            Aware:displayMessage("ENERGY CAPPED", "Orange", 1)
        end
        if gameState.chi >= 4 then
            Aware:displayMessage("CHI CAPPED", "Yellow", 1)
        end
        if gameState.comboBreakerTigerPalm then
            Aware:displayMessage("COMBO BREAKER: TIGER PALM", "Green", 1)
        end
        if gameState.comboBreakerBlackoutKick then
            Aware:displayMessage("COMBO BREAKER: BLACKOUT KICK", "Green", 1)
        end
        if gameState.tigerPowerRemaining < 3000 and gameState.tigerPowerRemaining > 0 then
            Aware:displayMessage("TIGER POWER EXPIRING", "Cyan", 1)
        end
        if gameState.risingKickDebuffRemaining < 3000 and gameState.risingKickDebuffRemaining > 0 then
            Aware:displayMessage("RISING SUN KICK DEBUFF EXPIRING", "White", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 20 then
        if TouchOfKarma:IsReady() then return FrameworkEnd() end
        if ZenMeditation:IsReady() then return FrameworkEnd() end
    end

    if player.hp <= 40 then
        if FortifyingBrew:IsReady() then return FrameworkEnd() end
        if DampenHarm:IsReady() then return FrameworkEnd() end
        if DiffuseMagic:IsReady() then return FrameworkEnd() end
    end

    -- Enhanced resource management
    if needsEnergy() and EnergizingBrew:IsReady() then
        if EnergizingBrew() then return FrameworkEnd() end
    end

    if needsChi() and ChiBrew:IsReady() then
        if ChiBrew() then return FrameworkEnd() end
    end

    if target.exists and target.alive then
        -- Enhanced interrupt priority
        if target:IsCasting() and target:IsInterruptible() then
            if SpearHandStrike() then return FrameworkEnd() end
            if LegSweep() then return FrameworkEnd() end
        end

        -- Touch of Death priority
        if target.hp <= player.hp then
            if TouchOfDeath() then return FrameworkEnd() end
        end

        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if Paralysis() then return FrameworkEnd() end
                if LegSweep() then return FrameworkEnd() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if TigereyeBrew() then return FrameworkEnd() end
            if StormEarthAndFire() then return FrameworkEnd() end
            if InvokeXuen() then return FrameworkEnd() end

            -- Racial abilities during burst
            if BloodFury() then return FrameworkEnd() end
            if Berserking() then return FrameworkEnd() end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    SpearHandStrike("arena", enemy)
    LegSweep("arena", enemy)

    -- CC abilities
    Paralysis("arena", enemy)

    -- Burst damage
    if shouldBurst() then
        TigereyeBrew("arena")
        StormEarthAndFire("arena", enemy)
        InvokeXuen("arena", enemy)
    end

    -- Touch of Death priority
    if enemy.hp <= player.hp then
        TouchOfDeath("arena", enemy)
    end

    -- Core rotation
    if gameState.tigerPowerRemaining < 3000 then
        TigerPalm("arena", enemy)
    end

    if gameState.risingKickDebuffRemaining < 3000 then
        RisingSunKick("arena", enemy)
    end

    if canUseFistsOfFury() then
        FistsOfFury("arena", enemy)
    end

    if gameState.comboBreakerBlackoutKick then
        BlackoutKick("arena", enemy)
    end

    if hasHighChi() then
        BlackoutKick("arena", enemy)
    end

    if needsChi() then
        if player.hp < 90 then
            ExpelHarm("arena")
        else
            Jab("arena", enemy)
        end
    end
end

local function enhancedPartyRotation(friendly)
    if not friendly.exists then return end

    -- Healing support
    if friendly.hp < 50 then
        ChiWave("arena")
        HealingSphere("arena")
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        SpearHandStrike()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Windwalker Monk
SpearHandStrike:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Paralysis:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:HasDeBuff(debuffs.paralysis) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Paralysis - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Paralysis - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

TouchOfDeath:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy.hp > player.hp then return end

    -- High priority execute
    Aware:displayMessage("Touch of Death - Execute", "Purple", 1)
    return spell:Cast(enemy)
end)

RisingSunKick:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.chi < 2 then return end

    -- Maintain debuff
    Aware:displayMessage("Rising Sun Kick - Debuff", "Red", 1)
    return spell:Cast(enemy)
end)

TigereyeBrew:Callback("arena", function(spell)
    if gameState.tigereyeBrewStacks < 10 then return end

    -- Major burst cooldown
    Aware:displayMessage("Tigereye Brew - Burst", "Red", 1)
    return spell:Cast(player)
end)

StormEarthAndFire:Callback("arena", function(spell, enemy)
    if not shouldBurst() and gameState.activeEnemies < 2 then return end

    -- Use for burst or when multiple enemies
    if shouldBurst() then
        Aware:displayMessage("Storm, Earth, and Fire - Burst", "Orange", 1)
    else
        Aware:displayMessage("Storm, Earth, and Fire - AoE", "Yellow", 1)
    end
    return spell:Cast(enemy)
end)

InvokeXuen:Callback("arena", function(spell, enemy)
    if not shouldBurst() then return end

    -- Major DPS cooldown
    Aware:displayMessage("Invoke Xuen - Burst", "Blue", 1)
    return spell:Cast(enemy)
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    QuakingPalm()
end

local function mopTalents()
    ChiWave()
    ChiBurst()
    ZenSphere()
    InvokeXuen()
    RushingJadeWind()
end

local function baseStuff()
    EnergizingBrew()
    ChiBrew()
    FortifyingBrew()
    TouchOfKarma()
    DiffuseMagic()
    DampenHarm()
end

local function baseStuffCombat()
    SpearHandStrike()
    Roll()
    ChiTorpedo()
    ExpelHarm()
    FlyingSerpentKick()
end

-- Enhanced utility for MoP Windwalker Monk
local function mopUtility()
    SpearHandStrike()
    Paralysis()
    LegSweep()
    Roll()
    ChiTorpedo()
    Transcendence()
    TranscendenceTransfer()
end

-- Combat tracking
local function combatStart()
    gameState.tigereyeBrewStacks = 0
    gameState.comboBreakerTigerPalm = false
    gameState.comboBreakerBlackoutKick = false
end

local function combatEnd()
    gameState.tigereyeBrewStacks = 0
    gameState.comboBreakerTigerPalm = false
    gameState.comboBreakerBlackoutKick = false
end

-- Event handling
local eventFrame = CreateFrame("Frame")
eventFrame:RegisterEvent("PLAYER_REGEN_DISABLED")
eventFrame:RegisterEvent("PLAYER_REGEN_ENABLED")
eventFrame:SetScript("OnEvent", function(self, event)
    if event == "PLAYER_REGEN_DISABLED" then
        combatStart()
    elseif event == "PLAYER_REGEN_ENABLED" then
        combatEnd()
    end
end)

-- Initialize
Action[ACTION_CONST_MONK_WINDWALKER] = A
