local Action = _G.Action

local A                = Action

local CONST                                                              = Action.Const

local ACTION_CONST_PALADIN_HOLY                                      = CONST.PALADIN_HOLY
local ACTION_CONST_PALADIN_PROTECTION                               = CONST.PALADIN_PROTECTION
local ACTION_CONST_PALADIN_RETRIBUTION                              = CONST.PALADIN_RETRIBUTION

LPH_ENCNUM = function(val) return val end

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "Makulu MoP v1.0.0 (7/29/2025)",
    -- Class settings
    [2] = {
        {
            {
                E = "Header",
                L = {
                    ANY = " ====== Makulu - MoP Paladin ====== ",
                },
            },
        },
        {
            { -- AOE
                E = "Checkbox", 
                DB = "AoE",
                DBV = true,
                L = { 
                    enUS = "Use AoE", 
                    ruRU = "Использовать AoE", 
                    frFR = "Utiliser l'AoE",
                }, 
                TT = { 
                    enUS = "Enable multiunits actions", 
                    ruRU = "Включает действия для нескольких целей", 
                    frFR = "Activer les actions multi-unités",
                }, 
                M = {},
            },
            { -- Auto Blessing
                E = "Checkbox", 
                DB = "autoBlessing",
                DBV = true,
                L = { 
                    ANY = "Auto Blessing Management", 
                }, 
                TT = { 
                    ANY = "Automatically maintain appropriate blessings on party/raid members.", 
                }, 
                M = {},
            },
            { -- Holy Power Management
                E = "Checkbox", 
                DB = "holyPowerManagement",
                DBV = true,
                L = { 
                    ANY = "Smart Holy Power Management", 
                }, 
                TT = { 
                    ANY = "Optimize holy power usage and prevent holy power capping."
                }, 
                M = {},
            },   
        },
        { -- Spacer
            
            {
                E = "LayoutSpace",
            },
        },
        { -- Potions
            { -- useDamagePotion
                E = "Checkbox", 
                DB = "damagePotion",
                DBV = true,
                L = { 
                    ANY = "Damage Potion"
                }, 
                TT = { 
                    ANY = "Use Damage Potion", 
                }, 
                M = {},
            },
            { -- potionBossOnly
                E = "Checkbox", 
                DB = "potionLustOnly",
                DBV = true,
                L = { 
                    ANY = "Damage Potion Bloodlust/TimeWarp Only", 
                }, 
                TT = { 
                    ANY = "Only use Damage Potion when any kind of Bloodlust/Warp active."
                }, 
                M = {},
            },
        },
        {
            { -- potionExhausted
                E = "Checkbox", 
                DB = "potionExhausted",
                DBV = true,
                L = { 
                    ANY = "Damage Potion With Exhaustion", 
                }, 
                TT = { 
                    ANY = "Use Damage Potion while Exhausted (can't use Bloodlust)."
                }, 
                M = {},
            },
            { -- potionExhaustedSlider
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,   
                Precision = 1,                         
                DB = "potionExhaustedSlider",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Exhaustion Time Remaining",
                },
                TT = { 
                    ANY = "Time in minutes left on the Exhaustion Debuff to consider using Damage Potion.", 
                },                     
                M = {},
            },
        },
        { -- LAYOUT SPACE   
            {
                E = "LayoutSpace",                                                                         
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Cooldowns",
                },
            },
        },
        {
            {
                E = "Dropdown",                                                         
                H = 20,
                OT = {
                    { text = "Avenging Wrath", value = 1 }, 
                    { text = "Guardian of Ancient Kings", value = 2 },     
                    { text = "Divine Favor", value = 3 },
                    { text = "Zealotry", value = 4 },
                    { text = "Divine Purpose", value = 5 },
                    { text = "Inquisition", value = 6 },   
                },
                MULT = true,
                DB = "cooldownSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                    [5] = true,
                    [6] = true,
                },  
                L = { 
                    ANY = "Cooldown Abilities", 
                }, 
                TT = { 
                    ANY = "Select what abilities you want the rotation to obey the burst toggle.\nIf a spell is unchecked, it will be used even when burst is turned off!", 
                }, 
                M = {},                                    
            },  
        }, 
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { 
            {-- Burst Sensitivity
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "burstSens",
                DBV = 18,
                ONOFF = false,
                L = { 
                    ANY = "Burst Mode Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to use cooldowns. A lower number means cooldowns used closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },  
            {-- Holy Power Threshold
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,                            
                DB = "holyPowerThreshold",
                DBV = 3,
                ONOFF = false,
                L = { 
                    ANY = "Holy Power Conservation Threshold",
                },
                TT = { 
                    ANY = "Holy Power amount to start conserving and use more efficient abilities.", 
                },                     
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- PALADIN HEADER
            {
                E = "Header",
                L = {
                    ANY = "INTERRUPTS",
                },
            },
        },
        {    
            { -- Automatic Interrupt
                E = "Checkbox", 
                DB = "AutoInterrupt",
                DBV = true,
                L = { 
                    ANY = "Switch Targets Interrupt",
                }, 
                TT = { 
                    ANY = "Automatically switches targets to interrupt.",
                }, 
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- PALADIN HEADER
            {
                E = "Header",
                L = {
                    ANY = "DEFENSIVES",
                },
            },
        },
        {
            { -- Divine Shield HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "DivineShieldHP",
                DBV = 20,
                ONOFF = false,
                L = { 
                    ANY = "Divine Shield HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Divine Shield on yourself.", 
                },                     
                M = {},
            },    
            { -- Divine Protection HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "DivineProtectionHP",
                DBV = 60,
                ONOFF = false,
                L = { 
                    ANY = "Divine Protection HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Divine Protection on yourself.", 
                },                     
                M = {},
            },    
        },
        {
            {-- Lay on Hands HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "layOnHandsHP",
                DBV = 15,
                ONOFF = false,
                L = { 
                    ANY = "Lay on Hands HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Lay on Hands for emergency healing.", 
                },                     
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Divine Shield", value = 1 },
                    { text = "Divine Protection", value = 2 },
                    { text = "Lay on Hands", value = 3 },
                    { text = "Word of Glory", value = 4 },
                },
                MULT = true,
                DB = "defensiveSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Defensive Reactions",
                },
                TT = {
                    ANY = "Select what spells to be used when reacting to incoming damage in dungeons.",
                },
                M = {},
            },
        },
        { -- Spacer

            {
                E = "LayoutSpace",
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Debug/Aware Options",
                },
            },
        },
        {
            { -- Debug
                E = "Checkbox",
                DB = "makDebug",
                DBV = false,
                L = {
                    ANY = "Enable debug options",
                },
                TT = {
                    ANY = "Show a box with various debug data.\nIt takes a couple of seconds to get rid of the box when you disable this.",
                },
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Holy Power Reminder", value = 1 },
                    { text = "Avenging Wrath Ready", value = 2 },
                    { text = "Guardian Ready", value = 3 },
                    { text = "Blessing Alert", value = 4 },
                },
                MULT = true,
                DB = "makAware",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Aware Text Alert Reminders",
                },
                TT = {
                    ANY = "Select what text alert reminders you would like.\nThese will appear in the center of your screen.",
                },
                M = {},
            },
        },
    },
}
