-- APL UPDATE MoP Holy Paladin
-- Mists of Pandaria Holy Paladin Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Holy spec (talent tree 1 for Paladin in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 1 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Holy Paladin Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    
    -- MoP Holy Paladin Core Abilities
    HolyShock = { ID = 20473, MAKULU_INFO = { damageType = "holy" } },
    HolyLight = { ID = 635, MAKULU_INFO = { damageType = "holy" } },
    DivineLight = { ID = 82326, MAKULU_INFO = { damageType = "holy" } },
    FlashOfLight = { ID = 19750, MAKULU_INFO = { damageType = "holy" } },
    WordOfGlory = { ID = 85673, MAKULU_INFO = { damageType = "holy" } },
    EternalFlame = { ID = 114163, MAKULU_INFO = { damageType = "holy" } },
    LightOfDawn = { ID = 85222, MAKULU_INFO = { damageType = "holy" } },
    HolyRadiance = { ID = 82327, MAKULU_INFO = { damageType = "holy" } },
    
    -- Beacon and Utility
    BeaconOfLight = { ID = 53563, MAKULU_INFO = { targeted = false } },
    LayOnHands = { ID = 633, MAKULU_INFO = { targeted = false } },
    DivineProtection = { ID = 498, MAKULU_INFO = { targeted = false } },
    DivineShield = { ID = 642, MAKULU_INFO = { targeted = false } },
    
    -- Blessings and Auras
    BlessingOfKings = { ID = 20217, MAKULU_INFO = { targeted = false } },
    BlessingOfMight = { ID = 19740, MAKULU_INFO = { targeted = false } },
    BlessingOfWisdom = { ID = 19742, MAKULU_INFO = { targeted = false } },
    DevotionAura = { ID = 465, MAKULU_INFO = { targeted = false } },
    ConcentrationAura = { ID = 19746, MAKULU_INFO = { targeted = false } },
    RetributionAura = { ID = 7294, MAKULU_INFO = { targeted = false } },
    
    -- Seals
    SealOfInsight = { ID = 20165, MAKULU_INFO = { targeted = false } },
    SealOfTruth = { ID = 31801, MAKULU_INFO = { targeted = false } },
    SealOfJustice = { ID = 20164, MAKULU_INFO = { targeted = false } },
    
    -- Cooldowns
    AvengingWrath = { ID = 31884, MAKULU_INFO = { targeted = false } },
    GuardianOfAncientKings = { ID = 86669, MAKULU_INFO = { targeted = false } },
    AuraMastery = { ID = 31821, MAKULU_INFO = { targeted = false } },
    DivineInsight = { ID = 40880, MAKULU_INFO = { targeted = false } },
    
    -- Utility and Movement
    Consecration = { ID = 26573, MAKULU_INFO = { damageType = "holy" } },
    HammerOfWrath = { ID = 24275, MAKULU_INFO = { damageType = "holy" } },
    Judgment = { ID = 20271, MAKULU_INFO = { damageType = "holy" } },
    CrusaderStrike = { ID = 35395, MAKULU_INFO = { damageType = "physical" } },
    
    -- Interrupts and CC
    Rebuke = { ID = 96231, MAKULU_INFO = { damageType = "physical", ignoreCasting = true } },
    HammerOfJustice = { ID = 853, MAKULU_INFO = { damageType = "physical" } },
    Repentance = { ID = 20066, MAKULU_INFO = { targeted = false } },
    TurnEvil = { ID = 10326, MAKULU_INFO = { targeted = false } },
    
    -- Cleanse and Dispel
    Cleanse = { ID = 4987, MAKULU_INFO = { targeted = false } },
    
    -- Hand Spells
    HandOfProtection = { ID = 1022, MAKULU_INFO = { targeted = false } },
    HandOfFreedom = { ID = 1044, MAKULU_INFO = { targeted = false } },
    HandOfSacrifice = { ID = 6940, MAKULU_INFO = { targeted = false } },
    HandOfSalvation = { ID = 1038, MAKULU_INFO = { targeted = false } },
    
    -- MoP Specific Talents
    HolyPrism = { ID = 114165, MAKULU_INFO = { damageType = "holy" } },
    LightsHammer = { ID = 114158, MAKULU_INFO = { damageType = "holy" } },
    ExecutionSentence = { ID = 114916, MAKULU_INFO = { damageType = "holy" } },
    HolyAvenger = { ID = 105809, MAKULU_INFO = { targeted = false } },
    SanctifiedWrath = { ID = 53376, MAKULU_INFO = { targeted = false } },
    DivinePurpose = { ID = 86172, MAKULU_INFO = { targeted = false } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 96231, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 853, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Holy Paladin Buffs
local buffs = {
    beaconOfLight = 53563,
    avengingWrath = 31884,
    guardianOfAncientKings = 86669,
    auraMastery = 31821,
    divineProtection = 498,
    divineShield = 642,
    holyAvenger = 105809,
    divinePurpose = 86172,
    infusionOfLight = 54149,
    daybreak = 88821,
    speedOfLight = 85499,
    devotionAura = 465,
    concentrationAura = 19746,
    retributionAura = 7294,
    sealOfInsight = 20165,
    sealOfTruth = 31801,
    sealOfJustice = 20164,
    blessingOfKings = 20217,
    blessingOfMight = 19740,
    blessingOfWisdom = 19742,
    handOfProtection = 1022,
    handOfFreedom = 1044,
    handOfSacrifice = 6940,
    handOfSalvation = 1038,
}

-- MoP Holy Paladin Debuffs
local debuffs = {
    forbearance = 25771,
    judgment = 20271,
    consecration = 26573,
    censure = 31803,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    holyPower = 0,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.holyPower = player.holyPower or 0
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsHealing(unit, threshold)
    return unit.exists and unit.hp < threshold
end

local function getLowestHealthPartyMember()
    local lowest = player
    local lowestHP = player.hp
    
    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember.hp < lowestHP then
            lowest = partyMember
            lowestHP = partyMember.hp
        end
    end
    
    return lowest
end

-- Beacon management
BeaconOfLight:Callback(function(spell)
    local tank = MakUnit:new("tank")
    if tank.exists and not tank:HasBuff(buffs.beaconOfLight) then
        return spell:Cast(tank)
    end
    
    -- In PvP, beacon the lowest health party member
    if gameState.isPvP then
        local lowest = getLowestHealthPartyMember()
        if lowest and not lowest:HasBuff(buffs.beaconOfLight) then
            return spell:Cast(lowest)
        end
    end
end)

-- Seal management
SealOfInsight:Callback(function(spell)
    if not player:HasBuff(buffs.sealOfInsight) and not player:HasBuff(buffs.sealOfTruth) then
        return spell:Cast(player)
    end
end)

-- Aura management
DevotionAura:Callback(function(spell)
    if not player:HasBuff(buffs.devotionAura) and not player:HasBuff(buffs.concentrationAura) and not player:HasBuff(buffs.retributionAura) then
        return spell:Cast(player)
    end
end)

-- Main rotation function
local function mainRotation()
    updateGameState()
    
    -- Defensive abilities
    if player.hp <= 20 then
        LayOnHands()
        DivineShield()
    end
    
    if player.hp <= 40 then
        DivineProtection()
    end
    
    -- Seal and aura management
    SealOfInsight()
    DevotionAura()
    
    -- Beacon management
    BeaconOfLight()
    
    -- Emergency healing
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 30 then
        if gameState.holyPower >= 3 then
            WordOfGlory()
        else
            FlashOfLight()
        end
    end
    
    -- Combat rotation
    if target.exists and target.alive then
        -- Interrupt priority
        Rebuke()
        
        -- Judgment for mana return
        Judgment()
        
        -- Cooldowns
        if shouldBurst() then
            AvengingWrath()
            GuardianOfAncientKings()
            AuraMastery()
        end
        
        -- Holy Power generation
        HolyShock()
        CrusaderStrike()
        
        -- Damage abilities
        HammerOfWrath()
        Consecration()
    end
end

-- Healing callbacks
HolyShock:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 80 then
        return spell:Cast(healTarget)
    end

    -- Damage in PvP if no healing needed
    if gameState.isPvP and target.exists and target.canAttack then
        return spell:Cast(target)
    end
end)

WordOfGlory:Callback(function(spell)
    if gameState.holyPower < 3 then return end

    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 70 then
        return spell:Cast(healTarget)
    end
end)

EternalFlame:Callback(function(spell)
    if gameState.holyPower < 3 then return end

    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 60 and not healTarget:HasBuff(114163) then
        return spell:Cast(healTarget)
    end
end)

HolyLight:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 60 and not player:HasBuff(buffs.infusionOfLight) then
        return spell:Cast(healTarget)
    end
end)

DivineLight:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 40 then
        return spell:Cast(healTarget)
    end
end)

FlashOfLight:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 50 and player:HasBuff(buffs.infusionOfLight) then
        return spell:Cast(healTarget)
    end
end)

LightOfDawn:Callback(function(spell)
    if gameState.holyPower < 3 then return end

    local injuredCount = 0
    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember.hp <= 70 then
            injuredCount = injuredCount + 1
        end
    end

    if player.hp <= 70 then
        injuredCount = injuredCount + 1
    end

    if injuredCount >= 3 then
        return spell:Cast(player)
    end
end)

HolyRadiance:Callback(function(spell)
    if shouldAoE() and gameState.activeEnemies >= 3 then
        local injuredCount = 0
        for i = 1, 3 do
            local partyMember = MakUnit:new("party" .. i)
            if partyMember.exists and partyMember.hp <= 80 then
                injuredCount = injuredCount + 1
            end
        end

        if injuredCount >= 2 then
            return spell:Cast(player)
        end
    end
end)

-- Cooldowns
AvengingWrath:Callback(function(spell)
    if shouldBurst() and (gameState.isPvP or getLowestHealthPartyMember().hp <= 50) then
        return spell:Cast(player)
    end
end)

GuardianOfAncientKings:Callback(function(spell)
    if player.hp <= 30 or (shouldBurst() and gameState.isPvP) then
        return spell:Cast(player)
    end
end)

AuraMastery:Callback(function(spell)
    local injuredCount = 0
    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember.hp <= 40 then
            injuredCount = injuredCount + 1
        end
    end

    if injuredCount >= 2 or player.hp <= 30 then
        return spell:Cast(player)
    end
end)

-- Utility spells
Cleanse:Callback(function(spell)
    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember:HasDebuffType("Magic", "Disease", "Poison") then
            return spell:Cast(partyMember)
        end
    end

    if player:HasDebuffType("Magic", "Disease", "Poison") then
        return spell:Cast(player)
    end
end)

-- Hand spells
HandOfFreedom:Callback(function(spell)
    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember:HasDebuffType("Movement") then
            return spell:Cast(partyMember)
        end
    end

    if player:HasDebuffType("Movement") then
        return spell:Cast(player)
    end
end)

HandOfProtection:Callback(function(spell)
    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember.hp <= 20 and not partyMember:HasDeBuff(debuffs.forbearance) then
            return spell:Cast(partyMember)
        end
    end
end)

HandOfSacrifice:Callback(function(spell)
    for i = 1, 3 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember.hp <= 30 and player.hp > 60 then
            return spell:Cast(partyMember)
        end
    end
end)

-- Interrupt
Rebuke:Callback(function(spell)
    if target:IsCasting() and target:IsInterruptible() then
        return spell:Cast(target)
    end
end)

-- PvP specific functions
local function pvpRotation()
    updateGameState()

    -- Emergency healing in PvP
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 20 then
        if gameState.holyPower >= 3 then
            WordOfGlory()
        else
            FlashOfLight()
            DivineLight()
        end
    end

    -- Dispel priority in PvP
    Cleanse()

    -- Hand spells for utility
    HandOfFreedom()
    HandOfProtection()
    HandOfSacrifice()

    -- Offensive capabilities
    if target.exists and target.canAttack then
        Rebuke()
        HammerOfJustice()
        Repentance()

        -- Damage rotation
        HolyShock()
        Judgment()
        CrusaderStrike()
        HammerOfWrath()
        Consecration()
    end
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Pre-heal before adds spawn
    if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
        -- Top everyone off before adds
        local healTarget = getLowestHealthPartyMember()
        if healTarget and healTarget.hp <= 90 then
            if gameState.holyPower >= 3 then
                WordOfGlory()
            else
                HolyLight()
            end
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            AvengingWrath()
            AuraMastery()
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        -- AoE healing priority
        LightOfDawn()
        HolyRadiance()

        -- AoE damage
        Consecration()
        HolyShock()
    end
end

-- Enhanced rotation for single target
local function singleTargetRotation()
    -- Priority healing
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 60 then
        HolyShock()
        if gameState.holyPower >= 3 then
            WordOfGlory()
            EternalFlame()
        else
            FlashOfLight()
            HolyLight()
            DivineLight()
        end
    end

    -- Damage rotation when healing not needed
    if target.exists and target.canAttack then
        HolyShock()
        Judgment()
        CrusaderStrike()
        HammerOfWrath()
        Consecration()
    end
end

-- Enhanced rotation for AoE
local function aoeRotation()
    -- AoE healing priority
    LightOfDawn()
    HolyRadiance()

    -- Individual healing for critical targets
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 40 then
        HolyShock()
        if gameState.holyPower >= 3 then
            WordOfGlory()
        else
            FlashOfLight()
        end
    end

    -- AoE damage
    Consecration()
    HolyShock()
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Base maintenance
    SealOfInsight()
    DevotionAura()
    BeaconOfLight()

    -- Emergency responses
    if player.hp <= 20 then
        LayOnHands()
        DivineShield()
    end

    if player.hp <= 40 then
        DivineProtection()
    end

    -- Cleanse priority
    Cleanse()

    -- Hand spells
    HandOfFreedom()
    HandOfProtection()
    HandOfSacrifice()

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Mana: ", gameState.mana)
        MakPrint(3, "Holy Power: ", gameState.holyPower)
        MakPrint(4, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(5, "In Combat: ", gameState.inCombat)
        MakPrint(6, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(7, "Is PvP: ", gameState.isPvP)
        MakPrint(8, "Beacon Active: ", player:HasBuff(buffs.beaconOfLight))
        MakPrint(9, "Avenging Wrath: ", player:HasBuff(buffs.avengingWrath))
        MakPrint(10, "Infusion of Light: ", player:HasBuff(buffs.infusionOfLight))

        local healTarget = getLowestHealthPartyMember()
        if healTarget then
            MakPrint(11, "Lowest HP Target: ", healTarget.hp)
        end
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if AvengingWrath:IsReady() and shouldBurst() and player:InCombat() then
            Aware:displayMessage("AVENGING WRATH READY", "Yellow", 1)
        end
        if GuardianOfAncientKings:IsReady() and player.hp <= 40 then
            Aware:displayMessage("GUARDIAN READY", "Blue", 1)
        end
        if LayOnHands:IsReady() and getLowestHealthPartyMember().hp <= 20 then
            Aware:displayMessage("LAY ON HANDS READY", "Red", 1)
        end
        if gameState.holyPower >= 3 then
            Aware:displayMessage("3 HOLY POWER", "Green", 1)
        end
        if player:HasBuff(buffs.infusionOfLight) then
            Aware:displayMessage("INFUSION OF LIGHT", "White", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Purple", 1)
        end
    end

    -- Enhanced healing priority
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 30 then
        if gameState.holyPower >= 3 then
            WordOfGlory()
            EternalFlame()
        else
            FlashOfLight()
            DivineLight()
        end
        LayOnHands()
    end

    -- Emergency cooldowns
    if player.hp <= 20 then
        DivineShield()
        LayOnHands()
    end

    if player.hp <= 40 then
        DivineProtection()
        GuardianOfAncientKings()
    end

    -- Base maintenance
    SealOfInsight()
    DevotionAura()
    BeaconOfLight()

    if target.exists and target.alive then
        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                Rebuke()
                HammerOfJustice()
                Repentance()
                Cleanse()
                HandOfFreedom()
                HandOfProtection()
            end
        end

        -- Burst phase
        if shouldBurst() then
            AvengingWrath()
            GuardianOfAncientKings()
            AuraMastery()

            -- Trinket usage during burst
            local damagePotion = Action.GetToggle(2, "damagePotion")
            if damagePotion and player:HasBuff(buffs.avengingWrath) then
                -- Use damage potions during burst cooldowns
            end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        Rebuke()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Holy Paladin
Rebuke:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

HammerOfJustice:Callback("arena", function(spell, enemy)
    if enemy.distance > 10 then return end
    if enemy:HasDeBuff(853) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Hammer of Justice - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Hammer of Justice - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

Repentance:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(20066) then return end
    if enemy.hp < 40 then return end

    -- Use on DPS when they're bursting
    if not enemy.isHealer and enemy.hp > 70 then
        Aware:displayMessage("Repentance - DPS", "Red", 1)
        return spell:Cast(enemy)
    end
end)

HolyShock:Callback("arena", function(spell, enemy)
    -- Prioritize healing in arena
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 70 then
        return spell:Cast(healTarget)
    end

    -- Damage if no healing needed
    if enemy.distance <= 40 and enemy.hp > 0 then
        return spell:Cast(enemy)
    end
end)

Cleanse:Callback("arena", function(spell, friendly)
    if friendly.distance > 40 then return end
    if not friendly:HasDebuffType("Magic", "Disease", "Poison") then return end

    -- Priority cleanse on low health targets
    if friendly.hp < 60 then
        Aware:displayMessage("Priority Cleanse", "Blue", 1)
        return spell:Cast(friendly)
    end

    return spell:Cast(friendly)
end)

HandOfFreedom:Callback("arena", function(spell, friendly)
    if friendly.distance > 40 then return end
    if not friendly:HasDebuffType("Movement") then return end
    if friendly:HasBuff(1044) then return end

    -- Priority on low health targets being kited
    if friendly.hp < 50 then
        Aware:displayMessage("Hand of Freedom - Kited", "Green", 1)
        return spell:Cast(friendly)
    end

    return spell:Cast(friendly)
end)

HandOfProtection:Callback("arena", function(spell, friendly)
    if friendly.distance > 40 then return end
    if friendly:HasDeBuff(debuffs.forbearance) then return end
    if friendly.hp > 30 then return end

    -- Emergency protection
    Aware:displayMessage("Hand of Protection - Emergency", "Red", 1)
    return spell:Cast(friendly)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    Rebuke("arena", enemy)

    -- CC abilities
    HammerOfJustice("arena", enemy)
    Repentance("arena", enemy)

    -- Damage
    HolyShock("arena", enemy)
    Judgment("arena", enemy)
    HammerOfWrath("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end

    -- Utility spells
    Cleanse("arena", friendly)
    HandOfFreedom("arena", friendly)
    HandOfProtection("arena", friendly)

    -- Healing priority
    if friendly.hp < 60 then
        HolyShock("arena", friendly)
        if gameState.holyPower >= 3 then
            WordOfGlory("arena", friendly)
        else
            FlashOfLight("arena", friendly)
        end
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and gameState.mana < 50 then
        return spell:Cast(player)
    end
end)

Berserking:Callback(function(spell)
    if shouldBurst() and (getLowestHealthPartyMember().hp <= 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

BloodFury:Callback(function(spell)
    if shouldBurst() and (getLowestHealthPartyMember().hp <= 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

GiftOfTheNaaru:Callback(function(spell)
    if player.hp <= 60 then
        return spell:Cast(player)
    end
end)

-- MoP specific talent abilities
HolyPrism:Callback(function(spell)
    if shouldAoE() and gameState.activeEnemies >= 2 then
        return spell:Cast(target)
    end

    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 60 then
        return spell:Cast(healTarget)
    end
end)

LightsHammer:Callback(function(spell)
    if shouldAoE() and gameState.activeEnemies >= 3 then
        return spell:Cast(target)
    end
end)

ExecutionSentence:Callback(function(spell)
    if target.exists and target.hp <= 35 then
        return spell:Cast(target)
    end
end)

HolyAvenger:Callback(function(spell)
    if shouldBurst() and (getLowestHealthPartyMember().hp <= 40 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    GiftOfTheNaaru()
end

local function mopTalents()
    HolyPrism()
    LightsHammer()
    ExecutionSentence()
    HolyAvenger()
end

local function baseStuff()
    SealOfInsight()
    DevotionAura()
    BeaconOfLight()
    DivineProtection()
    LayOnHands()
    DivineShield()
end

-- Enhanced utility for MoP
local function mopUtility()
    Cleanse()
    HandOfFreedom()
    HandOfProtection()
    HandOfSacrifice()
    Rebuke()
end
