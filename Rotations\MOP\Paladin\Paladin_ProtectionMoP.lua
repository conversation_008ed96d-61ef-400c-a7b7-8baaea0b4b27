-- APL UPDATE MoP Protection Paladin
-- Mists of Pandaria Protection Paladin Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Protection spec (talent tree 2 for Paladin in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 2 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Protection Paladin Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Protection Paladin Core Abilities
    ShieldOfTheRighteous = { ID = 53600, MAKULU_INFO = { damageType = "holy" } },
    AvengersShield = { ID = 31935, MAKULU_INFO = { damageType = "holy" } },
    CrusaderStrike = { ID = 35395, MAKULU_INFO = { damageType = "physical" } },
    HammerOfTheRighteous = { ID = 53595, MAKULU_INFO = { damageType = "physical" } },
    Judgment = { ID = 20271, MAKULU_INFO = { damageType = "holy" } },
    Consecration = { ID = 26573, MAKULU_INFO = { damageType = "holy" } },
    HolyWrath = { ID = 2812, MAKULU_INFO = { damageType = "holy" } },
    HammerOfWrath = { ID = 24275, MAKULU_INFO = { damageType = "holy" } },
    
    -- MoP Healing Abilities
    EternalFlame = { ID = 114163, MAKULU_INFO = { heal = true } },
    WordOfGlory = { ID = 85673, MAKULU_INFO = { heal = true } },
    FlashOfLight = { ID = 19750, MAKULU_INFO = { heal = true, castTime = 1500 } },
    LayOnHands = { ID = 633, MAKULU_INFO = { heal = true } },
    
    -- MoP Seals
    SealOfInsight = { ID = 20165, MAKULU_INFO = { targeted = false } },
    SealOfTruth = { ID = 31801, MAKULU_INFO = { targeted = false } },
    SealOfJustice = { ID = 20164, MAKULU_INFO = { targeted = false } },
    SealOfRighteousness = { ID = 20154, MAKULU_INFO = { targeted = false } },
    
    -- MoP Defensive Cooldowns
    GuardianOfAncientKings = { ID = 86659, MAKULU_INFO = { targeted = false } },
    AvengingWrath = { ID = 31884, MAKULU_INFO = { targeted = false } },
    DivineProtection = { ID = 498, MAKULU_INFO = { targeted = false } },
    ArdentDefender = { ID = 31850, MAKULU_INFO = { targeted = false } },
    DivineShield = { ID = 642, MAKULU_INFO = { targeted = false } },
    DevotionAura = { ID = 465, MAKULU_INFO = { targeted = false } },
    
    -- MoP Utility Abilities
    Rebuke = { ID = 96231, MAKULU_INFO = { damageType = "physical", ignoreCasting = true } },
    HammerOfJustice = { ID = 853, MAKULU_INFO = { damageType = "physical" } },
    HandOfReckoning = { ID = 62124, MAKULU_INFO = { targeted = true } },
    Repentance = { ID = 20066, MAKULU_INFO = { targeted = true } },
    TurnEvil = { ID = 10326, MAKULU_INFO = { targeted = true } },
    
    -- MoP Hand Spells
    HandOfProtection = { ID = 1022, MAKULU_INFO = { targeted = true } },
    HandOfFreedom = { ID = 1044, MAKULU_INFO = { targeted = true } },
    HandOfSacrifice = { ID = 6940, MAKULU_INFO = { targeted = true } },
    HandOfSalvation = { ID = 1038, MAKULU_INFO = { targeted = true } },
    
    -- MoP Auras
    ConcentrationAura = { ID = 19746, MAKULU_INFO = { targeted = false } },
    RetributionAura = { ID = 7294, MAKULU_INFO = { targeted = false } },
    CrusaderAura = { ID = 32223, MAKULU_INFO = { targeted = false } },
    
    -- MoP Blessings
    BlessingOfKings = { ID = 20217, MAKULU_INFO = { targeted = false } },
    BlessingOfMight = { ID = 19740, MAKULU_INFO = { targeted = false } },
    BlessingOfWisdom = { ID = 19742, MAKULU_INFO = { targeted = false } },
    
    -- MoP Talents
    HolyAvenger = { ID = 105809, MAKULU_INFO = { targeted = false } },
    DivinePurpose = { ID = 86172, MAKULU_INFO = { targeted = false } },
    SacredWeapon = { ID = 114157, MAKULU_INFO = { targeted = false } },
    ExecutionSentence = { ID = 114916, MAKULU_INFO = { damageType = "holy" } },
    LightsHammer = { ID = 114158, MAKULU_INFO = { damageType = "holy" } },
    HolyPrism = { ID = 114165, MAKULU_INFO = { damageType = "holy" } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 96231, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 853, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Protection Paladin Buffs
local buffs = {
    sealOfInsight = 20165,
    sealOfTruth = 31801,
    sealOfJustice = 20164,
    sealOfRighteousness = 20154,
    shieldOfTheRighteous = 132403,
    avengingWrath = 31884,
    guardianOfAncientKings = 86659,
    divineProtection = 498,
    ardentDefender = 31850,
    divineShield = 642,
    devotionAura = 465,
    concentrationAura = 19746,
    retributionAura = 7294,
    crusaderAura = 32223,
    blessingOfKings = 20217,
    blessingOfMight = 19740,
    blessingOfWisdom = 19742,
    handOfProtection = 1022,
    handOfFreedom = 1044,
    handOfSacrifice = 6940,
    handOfSalvation = 1038,
    holyAvenger = 105809,
    divinePurpose = 86172,
    sacredWeapon = 114157,
    eternalFlame = 114163,
    grandCrusader = 85043,
    bastionOfGlory = 114637,
    forbearance = 25771,
    boundlessConviction = 115675,
}

-- MoP Protection Paladin Debuffs
local debuffs = {
    judgment = 20271,
    consecration = 26573,
    censure = 31803,
    forbearance = 25771,
    executionSentence = 114916,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    holyPower = 0,
    timeToAdds = 999,
    isPvP = false,
    grandCrusaderProc = false,
    bastionOfGloryStacks = 0,
    executeRange = false,
    needsHealing = false,
    incomingDamage = 0,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.holyPower = player.holyPower or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.grandCrusaderProc = player:HasBuff(buffs.grandCrusader)
    gameState.bastionOfGloryStacks = player:GetBuffStacks(buffs.bastionOfGlory) or 0
    gameState.executeRange = target.exists and target.hp <= 20
    
    -- Calculate incoming damage and healing needs
    gameState.incomingDamage = player.hp < 80 and 1 or 0
    gameState.needsHealing = player.hp < 70 or gameState.incomingDamage > 0
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function needsHolyPower()
    return gameState.holyPower < 3
end

local function hasMaxHolyPower()
    return gameState.holyPower >= 3
end

local function shouldUseShieldOfTheRighteous()
    return hasMaxHolyPower() and gameState.inCombat
end

local function shouldUseEternalFlame()
    return gameState.needsHealing and gameState.holyPower >= 1 and gameState.bastionOfGloryStacks >= 3
end

-- Core ability callbacks
ShieldOfTheRighteous:Callback(function(spell)
    if target.totalImmune or target.holyImmune then return end
    if target.distance > 5 then return end
    if not shouldUseShieldOfTheRighteous() then return end

    -- Primary active mitigation and threat ability
    return spell:Cast(target)
end)

AvengersShield:Callback(function(spell)
    if target.totalImmune or target.holyImmune then return end
    if target.distance > 30 then return end

    -- High priority with Grand Crusader proc or on cooldown
    if gameState.grandCrusaderProc or spell:IsReady() then
        return spell:Cast(target)
    end
end)

CrusaderStrike:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if not needsHolyPower() then return end

    -- Primary single target Holy Power generator
    return spell:Cast(target)
end)

HammerOfTheRighteous:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if not needsHolyPower() then return end
    if not shouldAoE() then return end

    -- AoE Holy Power generator
    return spell:Cast(target)
end)

Judgment:Callback(function(spell)
    if target.totalImmune or target.holyImmune then return end
    if target.distance > 20 then return end
    if not needsHolyPower() then return end

    -- Holy Power generator and debuff application
    return spell:Cast(target)
end)

Consecration:Callback(function(spell)
    if target.totalImmune or target.holyImmune then return end
    if target.distance > 8 then return end

    -- AoE damage and threat
    return spell:Cast(target)
end)

HolyWrath:Callback(function(spell)
    if target.totalImmune or target.holyImmune then return end
    if target.distance > 15 then return end

    -- AoE damage ability
    return spell:Cast(target)
end)

HammerOfWrath:Callback(function(spell)
    if target.totalImmune or target.holyImmune then return end
    if target.distance > 30 then return end
    if not gameState.executeRange then return end

    -- Execute range ability
    return spell:Cast(target)
end)

-- Healing abilities
EternalFlame:Callback(function(spell)
    if not shouldUseEternalFlame() then return end

    -- Primary self-healing with Bastion of Glory synergy
    return spell:Cast(player)
end)

WordOfGlory:Callback(function(spell)
    if player.hp > 50 then return end
    if gameState.holyPower < 1 then return end

    -- Emergency healing
    return spell:Cast(player)
end)

FlashOfLight:Callback(function(spell)
    if player.hp > 40 then return end
    if player:IsCasting() then return end

    -- Emergency cast heal
    return spell:Cast(player)
end)

LayOnHands:Callback(function(spell)
    if player.hp > 20 then return end
    if player:HasDeBuff(debuffs.forbearance) then return end

    -- Emergency full heal
    return spell:Cast(player)
end)

-- Seal management
SealOfInsight:Callback(function(spell)
    if player:HasBuff(buffs.sealOfInsight) then return end

    -- Primary tanking seal for healing
    return spell:Cast(player)
end)

SealOfTruth:Callback(function(spell)
    if player:HasBuff(buffs.sealOfTruth) then return end
    if not shouldBurst() then return end

    -- DPS seal for burst phases
    return spell:Cast(player)
end)

-- Defensive cooldowns
GuardianOfAncientKings:Callback(function(spell)
    if player.hp > 40 then return end

    -- Major defensive cooldown (50% damage reduction)
    return spell:Cast(player)
end)

AvengingWrath:Callback(function(spell)
    if not shouldBurst() and player.hp > 50 then return end

    -- Damage and healing increase
    return spell:Cast(player)
end)

DivineProtection:Callback(function(spell)
    if player.hp > 60 then return end

    -- Magic damage reduction
    return spell:Cast(player)
end)

ArdentDefender:Callback(function(spell)
    if player.hp > 50 then return end

    -- Damage reduction with cheat death
    return spell:Cast(player)
end)

DivineShield:Callback(function(spell)
    if player.hp > 15 then return end
    if player:HasDeBuff(debuffs.forbearance) then return end

    -- Emergency immunity
    return spell:Cast(player)
end)

DevotionAura:Callback(function(spell)
    if not shouldAoE() then return end
    if player.hp > 70 then return end

    -- Raid-wide magic damage reduction
    return spell:Cast(player)
end)

-- Utility abilities
Rebuke:Callback(function(spell)
    if target.distance > 5 then return end
    if not target:IsCasting() then return end
    if not target:IsInterruptible() then return end

    return spell:Cast(target)
end)

HammerOfJustice:Callback(function(spell)
    if target.distance > 10 then return end
    if not gameState.isPvP then return end

    -- PvP stun
    return spell:Cast(target)
end)

HandOfReckoning:Callback(function(spell)
    if target.distance > 30 then return end
    if not target.canAttack then return end
    if target.isTargetingMe then return end

    -- Single target taunt
    return spell:Cast(target)
end)

-- Hand spells
HandOfProtection:Callback(function(spell)
    if not party1.exists then return end
    if party1.hp > 30 then return end
    if party1:HasDeBuff(debuffs.forbearance) then return end

    -- Protect low health party member
    return spell:Cast(party1)
end)

HandOfFreedom:Callback(function(spell)
    if not party1.exists then return end
    if not party1.rooted and not party1.slowed then return end

    -- Free party member from CC
    return spell:Cast(party1)
end)

HandOfSacrifice:Callback(function(spell)
    if not party1.exists then return end
    if party1.hp > 20 then return end

    -- Damage transfer to protect ally
    return spell:Cast(party1)
end)

-- Aura management
ConcentrationAura:Callback(function(spell)
    if player:HasBuff(buffs.concentrationAura) then return end
    if not gameState.isPvP then return end

    return spell:Cast(player)
end)

RetributionAura:Callback(function(spell)
    if player:HasBuff(buffs.retributionAura) then return end
    if gameState.isPvP then return end

    return spell:Cast(player)
end)

-- Blessing management
BlessingOfKings:Callback(function(spell)
    if player:HasBuff(buffs.blessingOfKings) then return end
    if player:HasBuff(buffs.blessingOfMight) then return end

    return spell:Cast(player)
end)

BlessingOfMight:Callback(function(spell)
    if player:HasBuff(buffs.blessingOfMight) then return end
    if player:HasBuff(buffs.blessingOfKings) then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

-- Talent abilities
HolyAvenger:Callback(function(spell)
    if not shouldBurst() then return end

    -- Haste and damage increase
    return spell:Cast(player)
end)

ExecutionSentence:Callback(function(spell)
    if target.totalImmune or target.holyImmune then return end
    if target.distance > 20 then return end
    if not shouldBurst() then return end

    -- High damage DoT
    return spell:Cast(target)
end)

LightsHammer:Callback(function(spell)
    if target.distance > 30 then return end
    if not shouldAoE() then return end

    -- AoE damage and healing
    return spell:Cast(target)
end)

HolyPrism:Callback(function(spell)
    if target.distance > 40 then return end

    -- Damage or healing based on target
    if target.canAttack then
        return spell:Cast(target)
    elseif player.hp < 80 then
        return spell:Cast(player)
    end
end)

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if gameState.holyPower >= 4 then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.holyImmune then return end

    return spell:Cast(player)
end)

BloodFury:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.holyImmune then return end

    return spell:Cast(player)
end)

QuakingPalm:Callback(function(spell)
    if target.distance > 5 then return end
    if not target:IsCasting() then return end

    return spell:Cast(target)
end)

-- Enhanced rotation functions
local function singleTargetRotation()
    -- Priority order for single target based on MoP Protection rotation

    -- Interrupt first
    Rebuke()

    -- Active mitigation priority
    if shouldUseShieldOfTheRighteous() then
        ShieldOfTheRighteous()
    end

    -- Healing priority
    if shouldUseEternalFlame() then
        EternalFlame()
    end

    -- Execute range priority
    if gameState.executeRange then
        HammerOfWrath()
        if hasMaxHolyPower() then
            ShieldOfTheRighteous()
        end
        return
    end

    -- Core threat rotation
    -- Avenger's Shield on cooldown or with Grand Crusader proc
    if gameState.grandCrusaderProc or AvengersShield:IsReady() then
        AvengersShield()
    end

    -- Holy Power generation
    if needsHolyPower() then
        CrusaderStrike()
        Judgment()
    end

    -- Consecration for AoE threat
    Consecration()

    -- Holy Wrath for damage
    HolyWrath()

    -- Talent abilities
    ExecutionSentence()
    HolyPrism()
end

local function aoeRotation()
    -- Priority order for AoE (3+ enemies)

    -- Interrupt priority target
    Rebuke()

    -- Active mitigation priority
    if shouldUseShieldOfTheRighteous() then
        ShieldOfTheRighteous()
    end

    -- Healing priority
    if shouldUseEternalFlame() then
        EternalFlame()
    end

    -- AoE threat abilities
    Consecration() -- High priority for AoE

    -- Avenger's Shield for threat
    if gameState.grandCrusaderProc or AvengersShield:IsReady() then
        AvengersShield()
    end

    -- Holy Power generation for AoE
    if needsHolyPower() then
        HammerOfTheRighteous()
        Judgment()
    end

    -- Execute in execute range
    if gameState.executeRange then
        HammerOfWrath()
    end

    -- AoE damage abilities
    HolyWrath()
    LightsHammer()

    -- Talent abilities
    ExecutionSentence()
    HolyPrism()
end

local function pvpRotation()
    -- PvP specific rotation with enhanced control and survivability

    -- Defensive priority
    if player.hp <= 25 then
        DivineShield()
        LayOnHands()
    end

    if player.hp <= 40 then
        GuardianOfAncientKings()
        ArdentDefender()
    end

    if player.hp <= 60 then
        DivineProtection()
        WordOfGlory()
    end

    -- Active mitigation
    if shouldUseShieldOfTheRighteous() then
        ShieldOfTheRighteous()
    end

    -- Interrupt and control
    Rebuke()
    HammerOfJustice()

    -- Burst damage
    if shouldBurst() then
        AvengingWrath()
        HolyAvenger()
        ExecutionSentence()
    end

    -- Core rotation with PvP priorities
    if gameState.executeRange then
        HammerOfWrath()
        ShieldOfTheRighteous()
    else
        -- Standard rotation but more aggressive
        AvengersShield()
        CrusaderStrike()
        Judgment()
        Consecration()
    end

    -- Utility
    HandOfReckoning()
    HandOfFreedom()
    HandOfProtection()
    HandOfSacrifice()
end

local function timeToAddsRotation()
    -- Prepare for incoming adds

    -- Save cooldowns for adds
    if gameState.timeToAdds < 5000 then
        -- Use cooldowns just before adds spawn
        AvengingWrath()
        HolyAvenger()
        ExecutionSentence()
    end

    -- Pool Holy Power for burst
    if gameState.holyPower < 3 then
        CrusaderStrike()
        Judgment()
    else
        -- Continue normal rotation but conserve resources
        AvengersShield()
        Consecration()
    end

    -- Prepare active mitigation
    if gameState.timeToAdds < 3000 then
        ShieldOfTheRighteous()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency responses
    if player.hp <= 15 then
        DivineShield()
        LayOnHands()
    end

    if player.hp <= 30 then
        GuardianOfAncientKings()
        ArdentDefender()
    end

    if player.hp <= 50 then
        DivineProtection()
        WordOfGlory()
    end

    -- Seal management
    SealOfInsight()

    -- Blessing maintenance
    BlessingOfKings()
    BlessingOfMight()

    -- Aura management
    ConcentrationAura()
    RetributionAura()

    -- Racial abilities during burst
    if shouldBurst() then
        BloodFury()
        Berserking()
        ArcaneTorrent()
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Holy Power: ", gameState.holyPower)
        MakPrint(3, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(4, "In Combat: ", gameState.inCombat)
        MakPrint(5, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(6, "Is PvP: ", gameState.isPvP)
        MakPrint(7, "Grand Crusader Proc: ", gameState.grandCrusaderProc)
        MakPrint(8, "Bastion of Glory Stacks: ", gameState.bastionOfGloryStacks)
        MakPrint(9, "Execute Range: ", gameState.executeRange)
        MakPrint(10, "Needs Healing: ", gameState.needsHealing)
        MakPrint(11, "Shield of the Righteous Active: ", player:HasBuff(buffs.shieldOfTheRighteous))
        MakPrint(12, "Eternal Flame Active: ", player:HasBuff(buffs.eternalFlame))
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if AvengingWrath:IsReady() and shouldBurst() then
            Aware:displayMessage("AVENGING WRATH READY", "Red", 1)
        end
        if GuardianOfAncientKings:IsReady() and player.hp < 50 then
            Aware:displayMessage("GUARDIAN READY", "Blue", 1)
        end
        if AvengersShield:IsReady() and gameState.grandCrusaderProc then
            Aware:displayMessage("GRAND CRUSADER PROC", "Yellow", 1)
        end
        if gameState.holyPower >= 5 then
            Aware:displayMessage("HOLY POWER CAPPED", "Orange", 1)
        end
        if shouldUseShieldOfTheRighteous() then
            Aware:displayMessage("USE SHIELD OF THE RIGHTEOUS", "Green", 1)
        end
        if shouldUseEternalFlame() then
            Aware:displayMessage("USE ETERNAL FLAME", "Purple", 1)
        end
        if gameState.executeRange then
            Aware:displayMessage("EXECUTE RANGE", "Purple", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
        if not player:HasBuff(buffs.sealOfInsight) and not player:HasBuff(buffs.sealOfTruth) then
            Aware:displayMessage("NO SEAL ACTIVE", "White", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 15 then
        if DivineShield:IsReady() and not player:HasDeBuff(debuffs.forbearance) then return FrameworkEnd() end
        if LayOnHands:IsReady() and not player:HasDeBuff(debuffs.forbearance) then return FrameworkEnd() end
    end

    if player.hp <= 30 then
        if GuardianOfAncientKings:IsReady() then return FrameworkEnd() end
        if ArdentDefender:IsReady() then return FrameworkEnd() end
    end

    if target.exists and target.alive then
        -- Enhanced interrupt priority
        if target:IsCasting() and target:IsInterruptible() then
            if Rebuke() then return FrameworkEnd() end
        end

        -- Taunt if not targeting us
        if not target.isTargetingMe and target.canAttack then
            if HandOfReckoning() then return FrameworkEnd() end
        end

        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if HammerOfJustice() then return FrameworkEnd() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if AvengingWrath() then return FrameworkEnd() end
            if HolyAvenger() then return FrameworkEnd() end
            if ExecutionSentence() then return FrameworkEnd() end

            -- Racial abilities during burst
            if BloodFury() then return FrameworkEnd() end
            if Berserking() then return FrameworkEnd() end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    Rebuke("arena", enemy)

    -- CC abilities
    HammerOfJustice("arena", enemy)

    -- Burst damage
    if shouldBurst() then
        AvengingWrath("arena")
        HolyAvenger("arena")
        ExecutionSentence("arena", enemy)
    end

    -- Execute priority
    if enemy.hp <= 20 then
        HammerOfWrath("arena", enemy)
        ShieldOfTheRighteous("arena", enemy)
    else
        -- Standard rotation
        AvengersShield("arena", enemy)
        CrusaderStrike("arena", enemy)
        Judgment("arena", enemy)
        Consecration("arena", enemy)
    end

    -- Active mitigation
    if player.hp < 70 then
        ShieldOfTheRighteous("arena")
        EternalFlame("arena")
    end
end

local function enhancedPartyRotation(friendly)
    if not friendly.exists then return end

    -- Support abilities for Protection Paladin
    if friendly.hp < 20 then
        HandOfProtection("arena", friendly)
        HandOfSacrifice("arena", friendly)
    end

    if friendly.hp < 40 then
        HandOfFreedom("arena", friendly)
    end

    if friendly.hp < 60 then
        HolyPrism("arena", friendly)
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        Rebuke()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Protection Paladin
Rebuke:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

HammerOfJustice:Callback("arena", function(spell, enemy)
    if enemy.distance > 10 then return end
    if enemy:IsKickImmune() then return end

    -- Use for interrupt or control
    if enemy:IsCasting() and enemy:IsInterruptible() then
        Aware:displayMessage("Hammer of Justice - Interrupt", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use for control during burst
    if shouldBurst() and enemy.hp > 30 then
        Aware:displayMessage("Hammer of Justice - Control", "Red", 1)
        return spell:Cast(enemy)
    end
end)

AvengersShield:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end

    -- High threat and damage ability
    Aware:displayMessage("Avenger's Shield - Threat", "Red", 1)
    return spell:Cast(enemy)
end)

AvengingWrath:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Avenging Wrath - Burst", "Red", 1)
    return spell:Cast(player)
end)

HolyAvenger:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Holy Avenger - Burst", "Blue", 1)
    return spell:Cast(player)
end)

HandOfProtection:Callback("arena", function(spell, friendly)
    if friendly.distance > 30 then return end
    if friendly:HasDeBuff(debuffs.forbearance) then return end
    if friendly.hp > 30 then return end

    -- Protect low health party members
    if friendly.hp < 25 and not friendly:IsMe() then
        Aware:displayMessage("Hand of Protection - Emergency", "Green", 1)
        return spell:Cast(friendly)
    end
end)

HandOfFreedom:Callback("arena", function(spell, friendly)
    if friendly.distance > 30 then return end
    if not friendly.rooted and not friendly.slowed then return end

    -- Free party members from CC
    if friendly.rooted or friendly.slowed then
        Aware:displayMessage("Hand of Freedom - CC Break", "Blue", 1)
        return spell:Cast(friendly)
    end
end)

HandOfSacrifice:Callback("arena", function(spell, friendly)
    if friendly.distance > 30 then return end
    if friendly.hp > 20 then return end

    -- Damage transfer for ally protection
    Aware:displayMessage("Hand of Sacrifice - Protection", "Purple", 1)
    return spell:Cast(friendly)
end)


