-- APL UPDATE MoP Retribution Paladin
-- Mists of Pandaria Retribution Paladin Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Retribution spec (talent tree 3 for <PERSON>lad<PERSON> in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 3 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local ACTION_CONST_PALADIN_RETRIBUTION = 70

local _G, setmetatable = _G, setmetatable

-- MoP Retribution Paladin Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    
    -- MoP Retribution Paladin Core Abilities
    TemplarsVerdict = { ID = 85256, MAKULU_INFO = { damageType = "holy" } },
    DivineStorm = { ID = 53385, MAKULU_INFO = { damageType = "holy" } },
    CrusaderStrike = { ID = 35395, MAKULU_INFO = { damageType = "physical" } },
    HammerOfTheRighteous = { ID = 53595, MAKULU_INFO = { damageType = "physical" } },
    Judgment = { ID = 20271, MAKULU_INFO = { damageType = "holy" } },
    HammerOfWrath = { ID = 24275, MAKULU_INFO = { damageType = "holy" } },
    Exorcism = { ID = 879, MAKULU_INFO = { damageType = "holy" } },
    Consecration = { ID = 26573, MAKULU_INFO = { damageType = "holy" } },
    
    -- Seals
    SealOfTruth = { ID = 31801, MAKULU_INFO = { targeted = false } },
    SealOfRighteousness = { ID = 20154, MAKULU_INFO = { targeted = false } },
    SealOfJustice = { ID = 20164, MAKULU_INFO = { targeted = false } },
    SealOfInsight = { ID = 20165, MAKULU_INFO = { targeted = false } },
    
    -- Buffs and Maintenance
    Inquisition = { ID = 84963, MAKULU_INFO = { targeted = false } },
    
    -- Cooldowns
    AvengingWrath = { ID = 31884, MAKULU_INFO = { targeted = false } },
    GuardianOfAncientKings = { ID = 86698, MAKULU_INFO = { targeted = false } },
    
    -- Defensive Abilities
    DivineShield = { ID = 642, MAKULU_INFO = { targeted = false } },
    DivineProtection = { ID = 498, MAKULU_INFO = { targeted = false } },
    LayOnHands = { ID = 633, MAKULU_INFO = { heal = true, targeted = true } },
    
    -- Utility Abilities
    Rebuke = { ID = 96231, MAKULU_INFO = { damageType = "physical", ignoreCasting = true } },
    HammerOfJustice = { ID = 853, MAKULU_INFO = { damageType = "physical" } },
    Repentance = { ID = 20066, MAKULU_INFO = { targeted = true } },
    TurnEvil = { ID = 10326, MAKULU_INFO = { targeted = true } },
    
    -- Hand Spells
    HandOfProtection = { ID = 1022, MAKULU_INFO = { targeted = true } },
    HandOfFreedom = { ID = 1044, MAKULU_INFO = { targeted = true } },
    HandOfSacrifice = { ID = 6940, MAKULU_INFO = { targeted = true } },
    
    -- Blessings
    BlessingOfKings = { ID = 20217, MAKULU_INFO = { targeted = true } },
    BlessingOfMight = { ID = 19740, MAKULU_INFO = { targeted = true } },
    
    -- Auras
    DevotionAura = { ID = 465, MAKULU_INFO = { targeted = false } },
    RetributionAura = { ID = 7294, MAKULU_INFO = { targeted = false } },
    ConcentrationAura = { ID = 19746, MAKULU_INFO = { targeted = false } },
    
    -- Cleanse
    Cleanse = { ID = 4987, MAKULU_INFO = { targeted = true } },
    
    -- Healing
    FlashOfLight = { ID = 19750, MAKULU_INFO = { heal = true, targeted = true } },
    WordOfGlory = { ID = 85673, MAKULU_INFO = { heal = true, targeted = true } },
    
    -- MoP Talents
    ExecutionSentence = { ID = 114157, MAKULU_INFO = { damageType = "holy" } },
    LightsHammer = { ID = 114158, MAKULU_INFO = { damageType = "holy" } },
    HolyPrism = { ID = 114165, MAKULU_INFO = { damageType = "holy" } },
    HolyAvenger = { ID = 105809, MAKULU_INFO = { targeted = false } },
    SanctifiedWrath = { ID = 53376, MAKULU_INFO = { targeted = false } },
    DivinePurpose = { ID = 86172, MAKULU_INFO = { targeted = false } },
    SpeedOfLight = { ID = 85499, MAKULU_INFO = { targeted = false } },
    LongArmOfTheLaw = { ID = 87172, MAKULU_INFO = { targeted = false } },
    PursuitOfJustice = { ID = 26022, MAKULU_INFO = { targeted = false } },
    
    -- Utility
    AuraMastery = { ID = 31821, MAKULU_INFO = { targeted = false } },
}

local ConstSpells = {
    -- Trinkets
    Trinket1 = { ID = 13 },
    Trinket2 = { ID = 14 },
    
    -- Potions
    HealthPotion = { ID = 431 },
    ManaPotion = { ID = 431 },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP Retribution Paladin Buffs
local buffs = {
    sealOfTruth = 31801,
    sealOfRighteousness = 20154,
    sealOfJustice = 20164,
    sealOfInsight = 20165,
    inquisition = 84963,
    avengingWrath = 31884,
    guardianOfAncientKings = 86698,
    ancientPower = 86700,
    divineShield = 642,
    divineProtection = 498,
    forbearance = 25771,
    handOfProtection = 1022,
    handOfFreedom = 1044,
    handOfSacrifice = 6940,
    blessingOfKings = 20217,
    blessingOfMight = 19740,
    devotionAura = 465,
    retributionAura = 7294,
    concentrationAura = 19746,
    holyAvenger = 105809,
    divinePurpose = 90174,
    speedOfLight = 85499,
    longArmOfTheLaw = 87172,
    pursuitOfJustice = 26022,
    auraMastery = 31821,
}

-- MoP Retribution Paladin Debuffs
local debuffs = {
    censure = 31803,
    hammerOfJustice = 853,
    repentance = 20066,
    turnEvil = 10326,
    executionSentence = 114157,
    consecration = 26573,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    holyPower = 0,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    combatTime = 0,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.holyPower = player.holyPower or 0
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.combatTime = player.combatTime or 0
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function shouldUseAoERotation()
    return gameState.activeEnemies >= 4
end

local function needsInquisition()
    return not player:HasBuff(buffs.inquisition) or player:BuffRemains(buffs.inquisition) < 8000
end

local function hasCorrectSeal()
    if shouldUseAoERotation() then
        return player:HasBuff(buffs.sealOfRighteousness)
    else
        return player:HasBuff(buffs.sealOfTruth)
    end
end

local function shouldPoolHolyPower()
    if player:HasBuff(buffs.avengingWrath) or player:HasBuff(buffs.holyAvenger) then
        return false -- Don't pool during burst
    end
    return gameState.holyPower < 5
end

-- Seal management
SealOfTruth:Callback(function(spell)
    if shouldUseAoERotation() then return end
    if player:HasBuff(buffs.sealOfTruth) then return end

    return spell:Cast(player)
end)

SealOfRighteousness:Callback(function(spell)
    if not shouldUseAoERotation() then return end
    if player:HasBuff(buffs.sealOfRighteousness) then return end

    return spell:Cast(player)
end)

-- Inquisition maintenance
Inquisition:Callback(function(spell)
    if not needsInquisition() then return end
    if gameState.holyPower < 3 then return end

    return spell:Cast(player)
end)

-- Cooldowns
AvengingWrath:Callback(function(spell)
    if not shouldBurst() then return end
    if player:HasBuff(buffs.avengingWrath) then return end

    return spell:Cast(player)
end)

GuardianOfAncientKings:Callback(function(spell)
    if not shouldBurst() then return end
    if player:HasBuff(buffs.guardianOfAncientKings) then return end

    return spell:Cast(player)
end)

-- Defensive abilities
DivineShield:Callback(function(spell)
    if player.hp > 20 then return end
    if player:HasBuff(buffs.divineShield) then return end
    if player:HasDebuff(buffs.forbearance) then return end

    return spell:Cast(player)
end)

DivineProtection:Callback(function(spell)
    if player.hp > 50 then return end
    if player:HasBuff(buffs.divineProtection) then return end

    return spell:Cast(player)
end)

LayOnHands:Callback(function(spell)
    if player.hp > 15 then return end
    if player:HasDebuff(buffs.forbearance) then return end

    return spell:Cast(player)
end)

-- Finisher abilities
TemplarsVerdict:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if shouldUseAoERotation() then return end

    -- During burst, use at 3+ holy power
    if player:HasBuff(buffs.avengingWrath) or player:HasBuff(buffs.holyAvenger) then
        if gameState.holyPower < 3 then return end
    else
        -- Normal rotation, pool to 5 or use at 3+ if nothing else available
        if gameState.holyPower < 5 and shouldPoolHolyPower() then return end
        if gameState.holyPower < 3 then return end
    end

    return spell:Cast(target)
end)

DivineStorm:Callback(function(spell)
    if not shouldAoE() then return end

    -- During burst, use at 3+ holy power
    if player:HasBuff(buffs.avengingWrath) or player:HasBuff(buffs.holyAvenger) then
        if gameState.holyPower < 3 then return end
    else
        -- Normal rotation, pool to 5 or use at 3+ if nothing else available
        if gameState.holyPower < 5 and shouldPoolHolyPower() then return end
        if gameState.holyPower < 3 then return end
    end

    return spell:Cast(player)
end)

-- Holy Power generators
HammerOfWrath:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if gameState.holyPower >= 5 then return end

    -- Available below 20% health or during Avenging Wrath
    if target.hp > 20 and not player:HasBuff(buffs.avengingWrath) then return end

    return spell:Cast(target)
end)

CrusaderStrike:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if gameState.holyPower >= 5 then return end
    if shouldUseAoERotation() then return end

    return spell:Cast(target)
end)

HammerOfTheRighteous:Callback(function(spell)
    if not shouldUseAoERotation() then return end
    if gameState.holyPower >= 5 then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

Judgment:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if gameState.holyPower >= 5 then return end

    return spell:Cast(target)
end)

Exorcism:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if gameState.holyPower >= 5 then return end

    return spell:Cast(target)
end)

-- Utility abilities
Consecration:Callback(function(spell)
    if target.distance > 8 then return end
    if not target:HasDebuff(debuffs.consecration) then
        return spell:Cast(player)
    end
end)

-- Interrupt abilities
Rebuke:Callback(function(spell)
    if not target.exists then return end
    if not target:IsCasting() then return end
    if not target:IsInterruptible() then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

HammerOfJustice:Callback(function(spell)
    if not target.exists then return end
    if target:HasDebuff(debuffs.hammerOfJustice) then return end
    if target.distance > 10 then return end
    if gameState.isPvP and target.hp < 30 then return end -- Don't CC low targets in PvP

    return spell:Cast(target)
end)

-- Hand spells for utility
HandOfProtection:Callback(function(spell)
    if not gameState.isPvP then return end
    if player.hp > 30 then return end
    if player:HasDebuff(buffs.forbearance) then return end

    return spell:Cast(player)
end)

HandOfFreedom:Callback(function(spell)
    if not gameState.isPvP then return end
    if not player.rooted and not player.slowed then return end

    return spell:Cast(player)
end)

-- Healing abilities
FlashOfLight:Callback(function(spell)
    if player.hp > 60 then return end
    if gameState.inCombat and target.exists and target.hp < 20 then return end -- Don't heal in combat if target is low

    return spell:Cast(player)
end)

WordOfGlory:Callback(function(spell)
    if gameState.holyPower < 3 then return end
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

-- Talent abilities
ExecutionSentence:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if not shouldBurst() then return end
    if target:HasDebuff(debuffs.executionSentence) then return end

    return spell:Cast(target)
end)

LightsHammer:Callback(function(spell)
    if not shouldAoE() then return end
    if target.distance > 30 then return end

    return spell:Cast(target)
end)

HolyPrism:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 40 then return end

    -- Use on enemy for damage or friendly for healing
    if shouldAoE() then
        return spell:Cast(target)
    elseif player.hp < 70 then
        return spell:Cast(player)
    end
end)

HolyAvenger:Callback(function(spell)
    if not shouldBurst() then return end
    if player:HasBuff(buffs.holyAvenger) then return end

    return spell:Cast(player)
end)

-- Aura management
DevotionAura:Callback(function(spell)
    if player:HasBuff(buffs.devotionAura) or player:HasBuff(buffs.retributionAura) or player:HasBuff(buffs.concentrationAura) then return end

    return spell:Cast(player)
end)

-- Racial abilities
BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.mana > 80 then return end

    return spell:Cast(player)
end)

GiftOfTheNaaru:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if player.hp > 60 then return end

    return spell:Cast(player)
end)

-- Single target rotation
local function singleTargetRotation()
    updateGameState()

    -- Seal management
    SealOfTruth()

    -- Maintain Inquisition
    if needsInquisition() and gameState.holyPower >= 3 then
        Inquisition()
        return
    end

    -- Use finishers
    if not shouldPoolHolyPower() or gameState.holyPower >= 5 then
        TemplarsVerdict()
    end

    -- Holy Power generators (priority order)
    HammerOfWrath()
    CrusaderStrike()
    Judgment()
    Exorcism()

    -- Filler abilities
    Consecration()
end

-- AoE rotation
local function aoeRotation()
    updateGameState()

    -- Seal management for AoE
    if shouldUseAoERotation() then
        SealOfRighteousness()
    else
        SealOfTruth()
    end

    -- Maintain Inquisition
    if needsInquisition() and gameState.holyPower >= 3 then
        Inquisition()
        return
    end

    -- Use AoE finishers
    if not shouldPoolHolyPower() or gameState.holyPower >= 5 then
        DivineStorm()
    end

    -- AoE Holy Power generators
    HammerOfWrath()
    if shouldUseAoERotation() then
        HammerOfTheRighteous()
    else
        CrusaderStrike()
    end
    Exorcism()
    Judgment()

    -- AoE abilities
    Consecration()
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 20 then
        DivineShield()
        LayOnHands()
    end

    if player.hp <= 40 then
        DivineProtection()
        HandOfProtection()
        WordOfGlory()
    end

    if player.hp <= 60 then
        FlashOfLight()
    end

    -- Utility
    HandOfFreedom()

    -- Interrupt priority
    Rebuke()

    -- CC abilities
    HammerOfJustice()

    if target.exists and target.alive then
        -- Seal management
        SealOfTruth()

        -- Maintain Inquisition
        if needsInquisition() and gameState.holyPower >= 3 then
            Inquisition()
            return
        end

        -- Damage rotation
        if not shouldPoolHolyPower() or gameState.holyPower >= 5 then
            if shouldAoE() then
                DivineStorm()
            else
                TemplarsVerdict()
            end
        end

        -- Holy Power generation
        HammerOfWrath()
        if shouldUseAoERotation() then
            HammerOfTheRighteous()
        else
            CrusaderStrike()
        end
        Judgment()
        Exorcism()

        -- Utility
        Consecration()
    end
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Save holy power for AoE abilities
        if gameState.holyPower < 4 then
            CrusaderStrike()
            Judgment()
            Exorcism()
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                AvengingWrath()
                GuardianOfAncientKings()
                HolyAvenger()
            end
        end

        -- Maintain current target
        if not shouldPoolHolyPower() or gameState.holyPower >= 5 then
            TemplarsVerdict()
        end

        -- Maintain Inquisition
        if needsInquisition() and gameState.holyPower >= 3 then
            Inquisition()
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 15 then
        LayOnHands()
        DivineShield()
    end

    if player.hp <= 30 then
        DivineProtection()
        WordOfGlory()
    end

    if player.hp <= 50 then
        FlashOfLight()
    end

    -- Aura management
    DevotionAura()

    -- Cooldowns during burst
    if shouldBurst() then
        AvengingWrath()
        GuardianOfAncientKings()
        HolyAvenger()
        ExecutionSentence()

        -- Racial abilities during burst
        BloodFury()
        Berserking()
    end

    -- Talent abilities
    LightsHammer()
    HolyPrism()

    -- Resource management
    ArcaneTorrent()
    GiftOfTheNaaru()

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- Enhanced A[3] function for advanced rotation
A[3] = function(icon)
    RegisterIcon(icon)

    updateGameState()

    -- Enhanced defensive priority
    if player.hp <= 15 then
        LayOnHands()
        DivineShield()
    end

    if player.hp <= 30 then
        DivineProtection()
        WordOfGlory()
        HandOfProtection()
    end

    if player.hp <= 50 then
        FlashOfLight()
    end

    -- Enhanced utility
    HandOfFreedom()

    if target.exists and target.alive then
        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                Rebuke()
                HammerOfJustice()
            end
        end

        -- Burst phase
        if shouldBurst() then
            AvengingWrath()
            GuardianOfAncientKings()
            HolyAvenger()
            ExecutionSentence()

            -- Racial abilities during burst
            BloodFury()
            Berserking()
        end

        -- Talent abilities
        LightsHammer()
        HolyPrism()

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        Rebuke()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Retribution Paladin
Rebuke:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

HammerOfJustice:Callback("arena", function(spell, enemy)
    if enemy.distance > 10 then return end
    if enemy:HasDebuff(debuffs.hammerOfJustice) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Hammer of Justice - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Hammer of Justice - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

TemplarsVerdict:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.holyPower < 3 then return end

    -- Priority on low health targets
    if enemy.hp < 40 then
        Aware:displayMessage("Priority Templar's Verdict", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

DivineStorm:Callback("arena", function(spell, enemy)
    if gameState.holyPower < 3 then return end
    if gameState.activeEnemies < 2 then return end

    return spell:Cast(player)
end)

ExecutionSentence:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:HasDebuff(debuffs.executionSentence) then return end
    if enemy.hp < 30 then return end -- Don't waste on low targets

    -- Use on high priority targets
    if enemy.isHealer or enemy.hp > 70 then
        Aware:displayMessage("Execution Sentence - Priority Target", "Purple", 1)
        return spell:Cast(enemy)
    end
end)

HandOfProtection:Callback("arena", function(spell, friendly)
    if friendly.distance > 30 then return end
    if friendly:HasDebuff(buffs.forbearance) then return end
    if friendly.hp > 30 then return end

    -- Protect low health party members
    if friendly.hp < 25 and not friendly:IsMe() then
        Aware:displayMessage("Hand of Protection - Emergency", "Green", 1)
        return spell:Cast(friendly)
    end
end)

HandOfFreedom:Callback("arena", function(spell, friendly)
    if friendly.distance > 30 then return end
    if not friendly.rooted and not friendly.slowed then return end

    -- Free party members from CC
    if friendly.rooted or friendly.slowed then
        Aware:displayMessage("Hand of Freedom - CC Break", "Blue", 1)
        return spell:Cast(friendly)
    end
end)

WordOfGlory:Callback("arena", function(spell, friendly)
    if gameState.holyPower < 3 then return end
    if friendly.distance > 30 then return end
    if friendly.hp > 50 then return end

    -- Emergency healing
    if friendly.hp < 40 then
        Aware:displayMessage("Word of Glory - Emergency Heal", "Green", 1)
        return spell:Cast(friendly)
    end
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    Rebuke("arena", enemy)

    -- CC abilities
    HammerOfJustice("arena", enemy)

    -- Damage abilities
    ExecutionSentence("arena", enemy)
    TemplarsVerdict("arena", enemy)
    DivineStorm("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end

    -- Cleanse for party members
    if friendly:HasDebuff("Disease") or friendly:HasDebuff("Poison") or friendly:HasDebuff("Magic") then
        Cleanse("arena", friendly)
    end

    -- Utility spells
    HandOfProtection("arena", friendly)
    HandOfFreedom("arena", friendly)
    WordOfGlory("arena", friendly)

    -- Emergency healing
    if friendly.hp < 30 then
        FlashOfLight("arena", friendly)
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end
