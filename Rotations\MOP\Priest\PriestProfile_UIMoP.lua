local Action = _G.Action

local A                = Action

local CONST                                                              = Action.Const

local ACTION_CONST_PRIEST_SHADOW                                      = CONST.PRIEST_SHADOW
local ACTION_CONST_PRIEST_DISCIPLINE                                 = CONST.PRIEST_DISCIPLINE
local ACTION_CONST_PRIEST_HOLY                                       = CONST.PRIEST_HOLY

LPH_ENCNUM = function(val) return val end

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "Makulu MoP v1.0.0 (7/29/2025)",
    -- Class settings
    [2] = {
        {
            {
                E = "Header",
                L = {
                    ANY = " ====== Makulu - MoP Priest ====== ",
                },
            },
        },
        {
            { -- AOE
                E = "Checkbox", 
                DB = "AoE",
                DBV = true,
                L = { 
                    enUS = "Use AoE", 
                    ruRU = "Использовать AoE", 
                    frFR = "Utiliser l'AoE",
                }, 
                TT = { 
                    enUS = "Enable multiunits actions", 
                    ruRU = "Включает действия для нескольких целей", 
                    frFR = "Activer les actions multi-unités",
                }, 
                M = {},
            },
            { -- Auto DOT
                E = "Checkbox", 
                DB = "autoDOT",
                DBV = false,
                L = { 
                    ANY = "Auto DOT Spread", 
                }, 
                TT = { 
                    ANY = "Automatically swap targets to spread DoTs when appropriate.", 
                }, 
                M = {},
            },
            { -- Cursor Check
                E = "Checkbox", 
                DB = "cursorCheck",
                DBV = true,
                L = { 
                    ANY = "Cursor Check (Shadow Crash)", 
                }, 
                TT = { 
                    ANY = "Check that the cursor is over an enemy before using Shadow Crash."
                }, 
                M = {},
            },   
        },
        { -- Spacer
            
            {
                E = "LayoutSpace",
            },
        },
        { -- Potions
            { -- useDamagePotion
                E = "Checkbox", 
                DB = "damagePotion",
                DBV = true,
                L = { 
                    ANY = "Damage Potion"
                }, 
                TT = { 
                    ANY = "Use Damage Potion", 
                }, 
                M = {},
            },
            { -- potionBossOnly
                E = "Checkbox", 
                DB = "potionLustOnly",
                DBV = true,
                L = { 
                    ANY = "Damage Potion Bloodlust/TimeWarp Only", 
                }, 
                TT = { 
                    ANY = "Only use Damage Potion when any kind of Bloodlust/Warp active."
                }, 
                M = {},
            },
        },
        {
            { -- potionExhausted
                E = "Checkbox", 
                DB = "potionExhausted",
                DBV = true,
                L = { 
                    ANY = "Damage Potion With Exhaustion", 
                }, 
                TT = { 
                    ANY = "Use Damage Potion while Exhausted (can't use Bloodlust)."
                }, 
                M = {},
            },
            { -- potionExhaustedSlider
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,   
                Precision = 1,                         
                DB = "potionExhaustedSlider",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Exhaustion Time Remaining",
                },
                TT = { 
                    ANY = "Time in minutes left on the Exhaustion Debuff to consider using Damage Potion.", 
                },                     
                M = {},
            },
        },
        { -- LAYOUT SPACE   
            {
                E = "LayoutSpace",                                                                         
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Cooldowns",
                },
            },
        },
        {
            {
                E = "Dropdown",                                                         
                H = 20,
                OT = {
                    { text = "Void Eruption / Dark Ascension", value = 1 }, 
                    { text = "Shadowfiend / Mindbender", value = 2 },     
                    { text = "Void Torrent", value = 3 },
                    { text = "Power Infusion", value = 4 },
                    { text = "Inner Fire", value = 5 },
                    { text = "Guardian Spirit", value = 6 },   
                },
                MULT = true,
                DB = "cooldownSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                    [5] = true,
                    [6] = true,
                },  
                L = { 
                    ANY = "Cooldown Abilities", 
                }, 
                TT = { 
                    ANY = "Select what abilities you want the rotation to obey the burst toggle.\nIf a spell is unchecked, it will be used even when burst is turned off!", 
                }, 
                M = {},                                    
            },  
        }, 
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { 
            {-- Burst Sensitivity
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "burstSens",
                DBV = 18,
                ONOFF = false,
                L = { 
                    ANY = "Burst Mode Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to use cooldowns. A lower number means cooldowns used closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },  
            {-- DOT Refresh TTD
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "dotRefresh",
                DBV = 12,
                ONOFF = false,
                L = { 
                    ANY = "DoT Refresh Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to refresh DoTs. A lower number means DoTs refreshed closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- PRIEST HEADER
            {
                E = "Header",
                L = {
                    ANY = "INTERRUPTS",
                },
            },
        },
        {    
            { -- Automatic Interrupt
                E = "Checkbox", 
                DB = "AutoInterrupt",
                DBV = true,
                L = { 
                    ANY = "Switch Targets Interrupt",
                }, 
                TT = { 
                    ANY = "Automatically switches targets to interrupt.",
                }, 
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- PRIEST HEADER
            {
                E = "Header",
                L = {
                    ANY = "DEFENSIVES",
                },
            },
        },
        {
            { -- Desperate Prayer HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "DesperatePrayerHP",
                DBV = 75,
                ONOFF = false,
                L = { 
                    ANY = "Desperate Prayer HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Desperate Prayer on yourself.", 
                },                     
                M = {},
            },    
            { -- Power Word Shield HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "PWSHP",
                DBV = 75,
                ONOFF = false,
                L = { 
                    ANY = "Power Word: Shield HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Power Word: Shield on yourself.", 
                },                     
                M = {},
            },    
        },
        {
            {-- Dispersion HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "dispersionHP",
                DBV = 20,
                ONOFF = false,
                L = { 
                    ANY = "Dispersion HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Dispersion.", 
                },                     
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Fade", value = 1 },
                    { text = "Vampiric Embrace", value = 2 },
                    { text = "Power Word: Shield", value = 3 },
                    { text = "Psychic Scream", value = 4 },
                },
                MULT = true,
                DB = "defensiveSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Defensive Reactions",
                },
                TT = {
                    ANY = "Select what spells to be used when reacting to incoming damage in dungeons.",
                },
                M = {},
            },
        },
        { -- Spacer

            {
                E = "LayoutSpace",
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Debug/Aware Options",
                },
            },
        },
        {
            { -- Debug
                E = "Checkbox",
                DB = "makDebug",
                DBV = false,
                L = {
                    ANY = "Enable debug options",
                },
                TT = {
                    ANY = "Show a box with various debug data.\nIt takes a couple of seconds to get rid of the box when you disable this.",
                },
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Mana Reminder", value = 1 },
                    { text = "Shadow Crash Ready", value = 2 },
                    { text = "Void Torrent Ready", value = 3 },
                    { text = "DoT Alert", value = 4 },
                },
                MULT = true,
                DB = "makAware",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Aware Text Alert Reminders",
                },
                TT = {
                    ANY = "Select what text alert reminders you would like.\nThese will appear in the center of your screen.",
                },
                M = {},
            },
        },
    },
}
