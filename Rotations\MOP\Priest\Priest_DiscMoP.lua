-- APL UPDATE MoP Discipline Priest
-- Mists of Pandaria Discipline Priest Rotation

-- Check if MakuluValidCheck exists before calling it
if MakuluValidCheck and not MakuluValidCheck() then return true end
if Maku<PERSON>_magic_number and Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Discipline spec (spec ID 256 for Priest in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 1 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Discipline Priest Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },

    -- MoP Discipline Core Healing Abilities
    PowerWordShield = { ID = 17, MAKULU_INFO = { heal = true, ignoreCasting = true } },
    Penance = { ID = 47540, MAKULU_INFO = { heal = true, channeled = true } },
    GreaterHeal = { ID = 2060, MAKULU_INFO = { heal = true, castTime = 2500 } },
    FlashHeal = { ID = 2061, MAKULU_INFO = { heal = true, castTime = 1500 } },
    Heal = { ID = 2050, MAKULU_INFO = { heal = true, castTime = 2500 } },
    Renew = { ID = 139, MAKULU_INFO = { heal = true, ignoreCasting = true } },
    PrayerOfHealing = { ID = 596, MAKULU_INFO = { heal = true, castTime = 3000 } },
    PrayerOfMending = { ID = 33076, MAKULU_INFO = { heal = true, ignoreCasting = true } },

    -- MoP Discipline Damage Abilities
    Smite = { ID = 585, MAKULU_INFO = { damageType = "holy", castTime = 2500 } },
    HolyFire = { ID = 14914, MAKULU_INFO = { damageType = "holy", castTime = 2500 } },
    ShadowWordPain = { ID = 589, MAKULU_INFO = { damageType = "shadow", ignoreCasting = true } },
    ShadowWordDeath = { ID = 32379, MAKULU_INFO = { damageType = "shadow", ignoreCasting = true } },
    MindBlast = { ID = 8092, MAKULU_INFO = { damageType = "shadow", castTime = 1500 } },
    MindFlay = { ID = 15407, MAKULU_INFO = { damageType = "shadow", channeled = true } },

    -- MoP Discipline Cooldowns
    PainSuppression = { ID = 33206, MAKULU_INFO = { heal = true, targeted = true } },
    PowerWordBarrier = { ID = 62618, MAKULU_INFO = { heal = true, targeted = false } },
    Archangel = { ID = 81700, MAKULU_INFO = { targeted = false } },
    InnerFire = { ID = 588, MAKULU_INFO = { targeted = false } },
    InnerWill = { ID = 73413, MAKULU_INFO = { targeted = false } },
    PowerWordFortitude = { ID = 21562, MAKULU_INFO = { targeted = false } },

    -- MoP Discipline Utility
    Dispel = { ID = 527, MAKULU_INFO = { targeted = true, ignoreCasting = true } },
    MassDispel = { ID = 32375, MAKULU_INFO = { castTime = 1500 } },
    PsychicScream = { ID = 8122, MAKULU_INFO = { targeted = false } },
    Fade = { ID = 586, MAKULU_INFO = { targeted = false } },
    Shadowfiend = { ID = 34433, MAKULU_INFO = { targeted = false } },
    Silence = { ID = 15487, MAKULU_INFO = { targeted = true, ignoreCasting = true } },

    -- MoP Discipline Talents
    PowerInfusion = { ID = 10060, MAKULU_INFO = { targeted = false } },
    InnerFocus = { ID = 89485, MAKULU_INFO = { targeted = false } },
    Twist = { ID = 47585, MAKULU_INFO = { targeted = false } },
    SpiritShell = { ID = 109964, MAKULU_INFO = { heal = true, targeted = false } },
    DivineInsight = { ID = 109175, MAKULU_INFO = { targeted = false } },
    VoidShift = { ID = 108968, MAKULU_INFO = { heal = true, targeted = true } },

    -- MoP Movement
    AngelicFeather = { ID = 121536, MAKULU_INFO = { targeted = false } },
    BodyAndSoul = { ID = 64129, MAKULU_INFO = { targeted = false } },

    -- MoP Defensive
    DesperatePrayer = { ID = 19236, MAKULU_INFO = { heal = true, targeted = false } },
    SpectralGuise = { ID = 112833, MAKULU_INFO = { targeted = false } },

    -- Potions and consumables
    TemperedPotion1 = { Type = "Potion", ID = 171263, QueueForbidden = true },
    TemperedPotion2 = { Type = "Potion", ID = 171264, QueueForbidden = true },
    TemperedPotion3 = { Type = "Potion", ID = 171265, QueueForbidden = true },
    PotionofUnwaveringFocus1 = { Type = "Potion", ID = 171266, QueueForbidden = true },
    PotionofUnwaveringFocus2 = { Type = "Potion", ID = 171267, QueueForbidden = true },
    PotionofUnwaveringFocus3 = { Type = "Potion", ID = 171268, QueueForbidden = true },

    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 15487, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 8122, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Discipline Priest Buffs
local buffs = {
    powerWordShield = 17,
    renew = 139,
    penance = 47540,
    archangel = 81700,
    innerFire = 588,
    innerWill = 73413,
    powerWordFortitude = 21562,
    powerInfusion = 10060,
    innerFocus = 89485,
    spiritShell = 109964,
    divineInsight = 109175,
    angelicFeather = 121536,
    bodyAndSoul = 64129,
    spectralGuise = 112833,
    evangelism = 81661,
    darkEvangelism = 87118,
    borrowedTime = 59889,
    grace = 77613,
    serendipity = 63734,
}

-- MoP Discipline Priest Debuffs
local debuffs = {
    shadowWordPain = 589,
    holyFire = 14914,
    weakenedSoul = 6788,
    silence = 15487,
    psychicScream = 8122,
    mindFlay = 15407,
    shadowWordDeath = 32379,
}

-- Game state tracking (enhanced with new functionality)
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
    shouldBurst = false,
    fightRemains = 999,
    lowestPartyMember = nil,
    lowestPartyHp = 100,
    shouldAoE = false,
    shouldCleave = false,
    evangelismStacks = 0,
    darkEvangelismStacks = 0,
    imCasting = nil,
    needsDispel = false,
    tankHealth = 100,
}

local function updategs()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.channeling = player:IsCasting() or player:IsChanneling()
    gameState.shouldBurst = A.GetToggle(2, "BurstMode")

    -- Enhanced tracking
    gameState.fightRemains = target.exists and target.timeToDie or 999
    gameState.shouldAoE = gameState.activeEnemies > 2
    gameState.shouldCleave = gameState.activeEnemies > 1
    gameState.evangelismStacks = player:BuffStacks(buffs.evangelism) or 0
    gameState.darkEvangelismStacks = player:BuffStacks(buffs.darkEvangelism) or 0
    gameState.imCasting = player:IsCasting() and player:GetCastingSpell() or nil

    -- Party health tracking
    local lowestHp = 100
    local lowestMember = nil
    for i = 1, 4 do
        local member = MakUnit:new("party" .. i)
        if member.exists and member.hp < lowestHp then
            lowestHp = member.hp
            lowestMember = member
        end
    end
    if player.hp < lowestHp then
        lowestHp = player.hp
        lowestMember = player
    end
    gameState.lowestPartyMember = lowestMember
    gameState.lowestPartyHp = lowestHp

    -- Tank health tracking
    local tank = MakUnit:new("party1") -- Assuming party1 is tank
    gameState.tankHealth = tank.exists and tank.hp or 100

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Alias for compatibility
local updateGameState = updategs
local gs = gameState

-- Utility functions (enhanced)
local function shouldBurst()
    return gameState.shouldBurst
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsHeal(unit, threshold)
    return unit.exists and unit.hp < threshold
end

local function needsDispel(unit)
    return unit.exists and unit:HasDebuffType("Magic")
end

local function getLowestPartyMember()
    return gameState.lowestPartyMember
end

local function isSpellInFlight(spell, range)
    return spell:IsSpellInFlight() or false
end

local function makInterrupt(interrupts)
    if not interrupts then return end
    for _, interrupt in pairs(interrupts) do
        if interrupt and interrupt:IsReady() then
            interrupt()
        end
    end
end

-- PvP utility functions
local function shouldSilence(enemy)
    if not enemy or not enemy.exists then return false end
    return enemy:IsCasting() and enemy:IsInterruptible()
end

local function shouldFear(enemy)
    if not enemy or not enemy.exists then return false end
    return not enemy:HasDebuff(debuffs.psychicScream) and enemy.hp > 30
end

-- Core healing callbacks
PowerWordShield:Callback(function(spell)
    local target = getLowestPartyMember()
    if not target or not target.exists then return end
    if target:HasDebuff(debuffs.weakenedSoul) then return end
    if target:HasBuff(buffs.powerWordShield) then return end
    if target.hp > 80 then return end

    return spell:Cast(target)
end)

Penance:Callback(function(spell)
    local target = getLowestPartyMember()
    if not target or not target.exists then return end
    if target.hp > 60 then return end
    if gameState.channeling then return end

    return spell:Cast(target)
end)

FlashHeal:Callback(function(spell)
    local target = getLowestPartyMember()
    if not target or not target.exists then return end
    if target.hp > 40 then return end
    if gameState.channeling then return end

    return spell:Cast(target)
end)

GreaterHeal:Callback(function(spell)
    local target = getLowestPartyMember()
    if not target or not target.exists then return end
    if target.hp > 50 then return end
    if gameState.channeling then return end

    return spell:Cast(target)
end)

Renew:Callback(function(spell)
    local target = getLowestPartyMember()
    if not target or not target.exists then return end
    if target:HasBuff(buffs.renew) then return end
    if target.hp > 70 then return end

    return spell:Cast(target)
end)

PrayerOfMending:Callback(function(spell)
    local target = getLowestPartyMember()
    if not target or not target.exists then return end
    if target.hp > 80 then return end

    return spell:Cast(target)
end)

-- Damage abilities
Smite:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gameState.channeling then return end
    if gameState.lowestPartyHp < 70 then return end -- Prioritize healing

    return spell:Cast(target)
end)

HolyFire:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDebuff(debuffs.holyFire) then return end
    if gameState.channeling then return end
    if gameState.lowestPartyHp < 70 then return end

    return spell:Cast(target)
end)

ShadowWordPain:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDebuff(debuffs.shadowWordPain) then return end
    if gameState.lowestPartyHp < 60 then return end

    return spell:Cast(target)
end)

MindBlast:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gameState.channeling then return end
    if gameState.lowestPartyHp < 70 then return end

    return spell:Cast(target)
end)

ShadowWordDeath:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target.hp > 25 then return end
    if gameState.lowestPartyHp < 50 then return end

    return spell:Cast(target)
end)

-- Cooldowns
PainSuppression:Callback(function(spell)
    local tank = MakUnit:new("party1")
    if not tank.exists then return end
    if tank.hp > 30 then return end

    return spell:Cast(tank)
end)

PowerWordBarrier:Callback(function(spell)
    if gameState.lowestPartyHp > 50 then return end
    if not shouldAoE() then return end

    return spell:Cast(player)
end)

Archangel:Callback(function(spell)
    if gameState.evangelismStacks < 5 then return end
    if gameState.lowestPartyHp > 60 then return end

    return spell:Cast(player)
end)

-- Utility abilities
Dispel:Callback(function(spell)
    for i = 1, 4 do
        local member = MakUnit:new("party" .. i)
        if member.exists and needsDispel(member) then
            return spell:Cast(member)
        end
    end
    if needsDispel(player) then
        return spell:Cast(player)
    end
end)

Silence:Callback(function(spell)
    if not target.exists or not target:IsCasting() then return end
    if not target:IsInterruptible() then return end

    return spell:Cast(target)
end)

PsychicScream:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDebuff(debuffs.psychicScream) then return end
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

Fade:Callback(function(spell)
    if player.hp > 40 then return end
    if not gameState.inCombat then return end

    return spell:Cast(player)
end)

Shadowfiend:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gameState.mana > 50 then return end

    return spell:Cast(target)
end)

-- Enhanced PvP callbacks
Silence:Callback("arena", function(spell, enemy)
    if not enemy.pvpKick then return end

    return spell:Cast(enemy)
end)

PsychicScream:Callback("arena", function(spell, enemy)
    if enemy.distance > 8 then return end
    if enemy:HasDebuff(debuffs.psychicScream) then return end
    if enemy.hp < 30 then return end

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Fear - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on DPS targeting low health allies
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Fear - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

ShadowWordPain:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if enemy:DebuffRemains(debuffs.shadowWordPain, true) > 5000 then return end

    return spell:Cast(enemy)
end)

HolyFire:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if enemy:DebuffRemains(debuffs.holyFire, true) > 8000 then return end
    if gs.imCasting and gs.imCasting == spell.id then return end

    return spell:Cast(enemy)
end)

-- Enhanced rotation functions
local function st()
    -- Single target healing rotation
    updategs()

    -- Emergency healing
    if gs.lowestPartyHp < 30 then
        FlashHeal()
        return
    end

    -- Shield priority targets
    PowerWordShield()

    -- Maintain renews
    Renew()

    -- Penance for efficient healing
    if gs.lowestPartyHp < 70 then
        Penance()
    end

    -- Prayer of Mending
    PrayerOfMending()

    -- Damage when safe
    if gs.lowestPartyHp > 80 then
        ShadowWordPain()
        HolyFire()
        Smite()
    end
end

local function aoe()
    -- AoE healing rotation
    updategs()

    -- Emergency barrier
    if gs.lowestPartyHp < 40 then
        PowerWordBarrier()
    end

    -- Prayer of Healing for group damage
    if gs.activeEnemies > 2 and gs.lowestPartyHp < 70 then
        PrayerOfHealing()
    end

    -- Individual shields
    PowerWordShield()

    -- Maintain healing
    st()
end

local function cleave()
    -- Cleave healing (2-3 enemies)
    updategs()

    -- Focus on tank and lowest member
    PowerWordShield()
    Penance()
    FlashHeal()

    -- Damage when safe
    if gs.lowestPartyHp > 70 then
        ShadowWordPain()
        Smite()
    end
end

local function ogcd()
    -- Off-global cooldown abilities
    if shouldBurst() then
        Archangel()
        PowerInfusion()
    end

    -- Emergency cooldowns
    if gs.tankHealth < 30 then
        PainSuppression()
    end

    -- Racial abilities
    racials()
end

local function eof()
    -- End of fight logic
    if gs.fightRemains < 30000 then
        -- Focus on damage
        if gs.lowestPartyHp > 60 then
            ShadowWordPain()
            HolyFire()
            Smite()
        end
    end
end

local function pvpenis()
    -- PvP specific logic placeholder
    -- This would contain PvP-specific rotation logic
end

-- Alias for compatibility
local function singleTargetRotation()
    st()
end

-- AoE rotation
local function aoeRotation()
    updateGameState()
    aoe()
end

-- PvP specific rotation
local function pvpRotation()
    updateGameState()

    -- Interrupt priority
    Silence()

    -- Fear for CC
    PsychicScream()

    -- Dispel priority
    Dispel()

    -- Healing priority in PvP
    if gs.lowestPartyHp < 50 then
        FlashHeal()
        PowerWordShield()
    end

    -- Damage pressure
    ShadowWordPain()
    HolyFire()

    -- Mana management
    if gs.mana < 30 then
        Shadowfiend()
    end

    -- Filler
    Smite()
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Pre-shield before adds spawn
    if gs.timeToAdds < 8000 and gs.timeToAdds > 0 then
        -- Prepare shields on party
        PowerWordShield()

        -- Prepare cooldowns
        if gs.timeToAdds < 3000 then
            PowerWordBarrier()
            Archangel()
        end
    end

    -- During adds phase
    if gs.activeEnemies >= 3 then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency healing always takes priority
    if gs.lowestPartyHp < 20 then
        FlashHeal()
        return
    end

    -- Emergency defensives
    if player.hp <= 30 then
        DesperatePrayer()
        Fade()
    end

    -- Dispel priority
    Dispel()

    -- Cooldowns during burst or emergency
    if shouldBurst() or gs.lowestPartyHp < 40 then
        Archangel()
        PowerInfusion()
    end

    -- TimeToAdds logic
    if gs.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gs.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- Enhanced A[3] function with new functionality
A[3] = function(icon)
    -- Safety check for framework initialization
    if not FrameworkStart then return end

    FrameworkStart(icon)
    updategs()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Mana: ", gs.mana)
        MakPrint(2, "Lowest Party HP: ", gs.lowestPartyHp)
        MakPrint(3, "Evangelism Stacks: ", gs.evangelismStacks)
        MakPrint(4, "Dark Evangelism Stacks: ", gs.darkEvangelismStacks)
        MakPrint(5, "Fight Remains: ", gs.fightRemains)
        MakPrint(6, "Should AoE: ", gs.shouldAoE)
        MakPrint(7, "Should Cleave: ", gs.shouldCleave)
        MakPrint(8, "Tank Health: ", gs.tankHealth)
        MakPrint(9, "Time to Adds: ", gs.timeToAdds)
        MakPrint(10, "Silence Learned: ", Silence:IsKnown())
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then -- Archangel ready
        if Archangel:IsReady() and gs.evangelismStacks >= 5 and player.inCombat then
            Aware:displayMessage("ARCHANGEL READY", "Purple", 1)
        end
    end

    -- Interrupt handling
    local interrupts = {Silence}
    makInterrupt(interrupts)

    -- Emergency healing and utility management
    PowerWordShield()
    Dispel()
    DesperatePrayer()

    if target.exists and target.canAttack and (Smite:InRange(target) or HolyFire:InRange(target)) then
        if A.IsInPvP then
            Silence("bg")
            PsychicScream("bg")
            pvpenis()
        end

        if player.channeling and gs.imCasting and gs.imCasting == Penance.id then return end

        local damagePotion = Action.GetToggle(2, "damagePotion")
        local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
        local potionExhausted = Action.GetToggle(2, "potionExhausted")
        local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")
        local damagePotionObject = Action.DetermineUsableObject("player", nil, nil, true, nil, A.TemperedPotion1, A.TemperedPotion2, A.TemperedPotion3, A.PotionofUnwaveringFocus1, A.PotionofUnwaveringFocus2, A.PotionofUnwaveringFocus3)

        if damagePotionObject and damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
            local shouldPot = gs.evangelismStacks >= 5 and gs.lowestPartyHp > 70
            if shouldPot then
                return damagePotionObject:Show(icon)
            end
        end

        if gs.shouldAoE then
            aoe()
        end

        ogcd()
        eof()

        if gs.shouldCleave then
            cleave()
        end

        st()

    end

    return FrameworkEnd()
end

-- Enhanced enemy and party rotation functions
local enemyRotation = function(enemy)
    if not enemy.exists then return end
    if A.Zone ~= "arena" then return end
    Silence("arena", enemy)
    PsychicScream("arena", enemy)
    ShadowWordPain("arena", enemy)
    HolyFire("arena", enemy)
end

local partyRotation = function(friendly)
    if not friendly.exists then return end
    if friendly.hp < 80 then
        PowerWordShield("arena", friendly)
        FlashHeal("arena", friendly)
        Renew("arena", friendly)
    end
    if needsDispel(friendly) then
        Dispel("arena", friendly)
    end
end

-- Arena functions
A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        Silence()
    end
    if Action.Zone == "arena" then
        enemyRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and gameState.mana < 50 then
        return spell:Cast(player)
    end
end)

Berserking:Callback(function(spell)
    if shouldBurst() and gs.lowestPartyHp < 60 then
        return spell:Cast(player)
    end
end)

BloodFury:Callback(function(spell)
    if shouldBurst() and gs.lowestPartyHp < 60 then
        return spell:Cast(player)
    end
end)

WillOfTheForsaken:Callback(function(spell)
    if player:HasDebuffType("Fear") or player:HasDebuffType("Charm") then
        return spell:Cast(player)
    end
end)

QuakingPalm:Callback(function(spell)
    if target.exists and target.canAttack and target:IsCasting() then
        return spell:Cast(target)
    end
end)

GiftOfTheNaaru:Callback(function(spell)
    if player.hp < 60 then
        return spell:Cast(player)
    end
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    WillOfTheForsaken()
    QuakingPalm()
    GiftOfTheNaaru()
end

local function mopTalents()
    PowerInfusion()
    InnerFocus()
    SpiritShell()
    VoidShift()
end

local function baseStuff()
    DesperatePrayer()
    Fade()
    SpectralGuise()
end

-- Enhanced utility for MoP
local function mopUtility()
    Silence()
    PsychicScream()
    Dispel()
    MassDispel()
    Shadowfiend()
end


-- Set up commonly used units
local player = ConstUnit.player
local target = ConstUnit.target

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP Discipline Priest Buffs
local buffs = {
    powerWordShield = 17,
    renew = 139,
    innerFire = 588,
    innerWill = 73413,
    powerWordFortitude = 21562,
    archangel = 81700,
    evangelism = 81661,
    borrowedTime = 59889,
    grace = 77613,
    painSuppression = 33206,
    powerWordBarrier = 81782,
    spiritShell = 114908,
    angelicFeather = 121557,
    spectralGuise = 112833,
    divineInsight = 123266,
    innerFocus = 89485,
    powerInfusion = 10060,
    twist = 47585,
    atonement = 81749,
}

-- MoP Discipline Priest Debuffs
local debuffs = {
    shadowWordPain = 589,
    holyFire = 14914,
    weakenedSoul = 6788,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    evangelismStacks = 0,
    atonementCount = 0,
}

local function updateGameState()
    gameState.inCombat = player.inCombat
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.evangelismStacks = player:BuffStacks(buffs.evangelism) or 0
    
    -- Count atonement buffs on party members
    gameState.atonementCount = 0
    if player:Buff(buffs.atonement) then
        gameState.atonementCount = gameState.atonementCount + 1
    end
    
    for i = 1, 4 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember:Buff(buffs.atonement) then
            gameState.atonementCount = gameState.atonementCount + 1
        end
    end

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function getLowestHealthPartyMember()
    local lowestUnit = player
    local lowestHP = player.hp
    
    for i = 1, 4 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember.hp < lowestHP then
            lowestUnit = partyMember
            lowestHP = partyMember.hp
        end
    end
    
    return lowestUnit
end

local function getInjuredPartyCount(threshold)
    local count = 0
    if player.hp < threshold then count = count + 1 end

    for i = 1, 4 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists and partyMember.hp < threshold then
            count = count + 1
        end
    end

    return count
end

local function needsShield(unit)
    return not unit:Buff(buffs.powerWordShield) and not unit:DeBuff(debuffs.weakenedSoul)
end

local function shouldUseArchangel()
    return gameState.evangelismStacks >= 5 and gameState.mana < 80
end

-- Buff management
InnerFire:Callback(function(spell)
    if not player:Buff(buffs.innerFire) and not player:Buff(buffs.innerWill) then
        return spell:Cast()
    end
end)

PowerWordFortitude:Callback(function(spell)
    if not player:Buff(buffs.powerWordFortitude) then
        return spell:Cast()
    end
end)

-- Core healing callbacks
PowerWordShield:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 90 and needsShield(healTarget) then
        return spell:Cast(healTarget)
    end
end)

Penance:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 70 then
        return spell:Cast(healTarget)
    end
    
    -- Use on enemy for atonement healing in PvP
    if gameState.isPvP and target.exists and target.canAttack and gameState.atonementCount > 0 then
        return spell:Cast(target)
    end
end)

FlashHeal:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 50 then
        return spell:Cast(healTarget)
    end
end)

GreaterHeal:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 60 and not player.moving then
        return spell:Cast(healTarget)
    end
end)

Renew:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 80 and not healTarget:Buff(buffs.renew) then
        return spell:Cast(healTarget)
    end
end)

PrayerOfHealing:Callback(function(spell)
    local injuredCount = getInjuredPartyCount(70)
    if injuredCount >= 3 and not player.moving then
        return spell:Cast()
    end
end)

PrayerOfMending:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 85 and not healTarget:Buff(33076) then
        return spell:Cast(healTarget)
    end
end)

-- Damage abilities for atonement healing
Smite:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not target.canAttack then return end

    -- Use for atonement healing when party has shields
    if gameState.atonementCount > 0 then
        return spell:Cast(target)
    end

    -- Use for evangelism stacks
    if gameState.evangelismStacks < 5 then
        return spell:Cast(target)
    end
end)

HolyFire:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not target.canAttack then return end
    if player.moving then return end
    if target:DeBuff(debuffs.holyFire) then return end

    return spell:Cast(target)
end)

ShadowWordPain:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not target.canAttack then return end
    if target:DeBuff(debuffs.shadowWordPain) then return end

    return spell:Cast(target)
end)

MindBlast:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not target.canAttack then return end
    if player.moving then return end

    return spell:Cast(target)
end)

ShadowWordDeath:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not target.canAttack then return end
    if target.hp > 25 then return end

    return spell:Cast(target)
end)

-- Cooldowns
PainSuppression:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 30 then
        return spell:Cast(healTarget)
    end
end)

PowerWordBarrier:Callback(function(spell)
    local injuredCount = getInjuredPartyCount(50)
    if injuredCount >= 3 then
        return spell:Cast()
    end
end)

Archangel:Callback(function(spell)
    if shouldUseArchangel() then
        return spell:Cast()
    end
end)

Shadowfiend:Callback(function(spell)
    if gameState.mana <= 60 and target.exists and target.canAttack then
        return spell:Cast()
    end
end)

-- Utility abilities
Dispel:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget:HasDispellableDebuff() then
        return spell:Cast(healTarget)
    end
end)

PsychicScream:Callback(function(spell)
    if gameState.isPvP and player.hp <= 40 then
        return spell:Cast()
    end
end)

Fade:Callback(function(spell)
    if gameState.isPvP and player.hp <= 50 then
        return spell:Cast()
    end
end)

DesperatePrayer:Callback(function(spell)
    if player.hp <= 35 then
        return spell:Cast()
    end
end)

-- Talent abilities
PowerInfusion:Callback(function(spell)
    if shouldBurst() then
        return spell:Cast()
    end
end)

InnerFocus:Callback(function(spell)
    local healTarget = getLowestHealthPartyMember()
    if healTarget and healTarget.hp <= 40 then
        return spell:Cast()
    end
end)

SpiritShell:Callback(function(spell)
    -- Use for TimeToAdds ramping
    if gameState.timeToAdds < 15000 and gameState.timeToAdds > 8000 then
        Aware:displayMessage("Spirit Shell - TimeToAdds Ramp", "Blue", 1)
        return spell:Cast()
    end

    -- Use for emergency situations
    local injuredCount = getInjuredPartyCount(60)
    if injuredCount >= 2 then
        return spell:Cast()
    end

    -- Use during burst phases
    if shouldBurst() then
        local injuredCount = getInjuredPartyCount(80)
        if injuredCount >= 1 then
            return spell:Cast()
        end
    end
end)

-- Spirit Shell ramping callbacks for enhanced control
PowerWordShield:Callback("spiritshell", function(spell, unit)
    if not player:Buff(buffs.spiritShell) then return end
    if unit:Buff(buffs.powerWordShield) then return end
    if unit:DeBuff(debuffs.weakenedSoul) then return end

    -- During Spirit Shell, shield everyone for absorbs
    return spell:Cast(unit)
end)

Penance:Callback("spiritshell", function(spell, unit)
    if not player:Buff(buffs.spiritShell) then return end
    if unit.hp > 95 then return end -- Higher threshold during ramp

    return spell:Cast(unit)
end)

FlashHeal:Callback("spiritshell", function(spell, unit)
    if not player:Buff(buffs.spiritShell) then return end
    if unit.hp > 90 then return end -- Higher threshold during ramp

    return spell:Cast(unit)
end)

GreaterHeal:Callback("spiritshell", function(spell, unit)
    if not player:Buff(buffs.spiritShell) then return end
    if player.moving then return end
    if unit.hp > 85 then return end -- Higher threshold during ramp

    return spell:Cast(unit)
end)

PrayerOfHealing:Callback("spiritshell", function(spell)
    if not player:Buff(buffs.spiritShell) then return end
    if player.moving then return end

    local injuredCount = getInjuredPartyCount(95) -- Very high threshold during ramp
    if injuredCount >= 2 then
        return spell:Cast()
    end
end)

AngelicFeather:Callback(function(spell)
    if player.moving and not player:Buff(buffs.angelicFeather) then
        return spell:Cast()
    end
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast()
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.mana <= 70 then
        return spell:Cast()
    end
end)

GiftOfTheNaaru:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if player.hp <= 60 then
        return spell:Cast(player)
    end
end)

-- PvE Single Target Healing Rotation
local function singleTargetHealing()
    updateGameState()

    -- Maintain buffs
    if InnerFire() then return true end
    if PowerWordFortitude() then return true end

    -- Emergency healing
    if DesperatePrayer() then return true end
    if PainSuppression() then return true end

    -- Dispel priority
    if Dispel() then return true end

    -- Shield priority targets
    if PowerWordShield() then return true end

    -- Direct healing
    if FlashHeal() then return true end
    if Penance() then return true end
    if GreaterHeal() then return true end

    -- HoTs and maintenance
    if Renew() then return true end
    if PrayerOfMending() then return true end

    -- Mana management
    if Archangel() then return true end
    if Shadowfiend() then return true end

    return false
end

-- PvE AoE Healing Rotation
local function aoeHealing()
    updateGameState()

    -- Maintain buffs
    if InnerFire() then return true end
    if PowerWordFortitude() then return true end

    -- Emergency cooldowns
    if PowerWordBarrier() then return true end
    if SpiritShell() then return true end

    -- AoE healing priority
    if PrayerOfHealing() then return true end

    -- Shield multiple targets
    if PowerWordShield() then return true end

    -- Direct healing on lowest
    if Penance() then return true end
    if FlashHeal() then return true end

    -- Maintenance healing
    if Renew() then return true end
    if PrayerOfMending() then return true end

    return false
end

-- PvP Rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 30 then
        if DesperatePrayer() then return true end
        if PainSuppression() then return true end
    end

    if player.hp <= 50 then
        if Fade() then return true end
        if PsychicScream() then return true end
    end

    -- Maintain buffs
    if InnerFire() then return true end
    if PowerWordFortitude() then return true end

    -- Dispel priority
    if Dispel() then return true end

    -- Shield priority
    if PowerWordShield() then return true end

    -- Healing priority
    if FlashHeal() then return true end
    if Penance() then return true end
    if GreaterHeal() then return true end

    -- Offensive abilities for atonement
    if target.exists and target.canAttack then
        if ShadowWordPain() then return true end
        if HolyFire() then return true end
        if MindBlast() then return true end
        if Smite() then return true end
        if ShadowWordDeath() then return true end
    end

    -- Maintenance
    if Renew() then return true end
    if PrayerOfMending() then return true end

    return false
end

-- Spirit Shell ramping logic for TimeToAdds
local function spiritShellRamp()
    updateGameState()

    -- Activate Spirit Shell if available
    if SpiritShell() then return true end

    -- Check if Spirit Shell is active
    if not player:Buff(buffs.spiritShell) then return false end

    Aware:displayMessage("Spirit Shell Active - Ramping Absorbs", "Blue", 2)

    -- Use Spirit Shell specific callbacks for optimized ramping
    local partyMembers = {}
    table.insert(partyMembers, player)
    for i = 1, 4 do
        local partyMember = MakUnit:new("party" .. i)
        if partyMember.exists then
            table.insert(partyMembers, partyMember)
        end
    end

    -- Priority 1: Shield all party members for maximum absorbs
    for _, member in ipairs(partyMembers) do
        if PowerWordShield("spiritshell", member) then
            Aware:displayMessage("Spirit Shell Ramp - Shield", "Blue", 1)
            return true
        end
    end

    -- Priority 2: Prayer of Healing for group absorbs
    if PrayerOfHealing("spiritshell") then
        Aware:displayMessage("Spirit Shell Ramp - PoH", "Blue", 1)
        return true
    end

    -- Priority 3: Single target heals for absorbs
    local lowestTarget = getLowestHealthPartyMember()
    if lowestTarget then
        -- Use Greater Heal for largest absorbs (if not moving)
        if GreaterHeal("spiritshell", lowestTarget) then
            Aware:displayMessage("Spirit Shell Ramp - Greater", "Blue", 1)
            return true
        end

        -- Use Penance for channeled absorbs
        if Penance("spiritshell", lowestTarget) then
            Aware:displayMessage("Spirit Shell Ramp - Penance", "Blue", 1)
            return true
        end

        -- Use Flash Heal for quick absorbs
        if FlashHeal("spiritshell", lowestTarget) then
            Aware:displayMessage("Spirit Shell Ramp - Flash", "Blue", 1)
            return true
        end
    end

    return false
end

-- Enhanced TimeToAdds specific logic with Spirit Shell ramping
local function timeToAddsRotation()
    updateGameState()

    -- Spirit Shell ramping phase (15-8 seconds before adds)
    if gameState.timeToAdds < 15000 and gameState.timeToAdds > 8000 then
        Aware:displayMessage("Spirit Shell Ramp Phase", "Yellow", 1)

        -- Start Spirit Shell ramping
        if spiritShellRamp() then return true end

        -- Prepare evangelism stacks for Archangel
        if gameState.evangelismStacks < 5 and target.exists and target.canAttack then
            if Smite() then return true end
            if HolyFire() then return true end
        end

        -- Maintain basic healing
        if PowerWordShield() then return true end
        if Penance() then return true end
    end

    -- Pre-adds preparation phase (8-3 seconds)
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 3000 then
        Aware:displayMessage("Pre-Adds Preparation", "Orange", 1)

        -- Continue Spirit Shell if still active
        if player:Buff(buffs.spiritShell) then
            if spiritShellRamp() then return true end
        end

        -- Pre-shield all party members
        if PowerWordShield() then return true end

        -- Use Archangel for mana if we have stacks
        if gameState.evangelismStacks >= 3 then
            if Archangel() then return true end
        end

        -- Maintain healing
        if Penance() then return true end
        if FlashHeal() then return true end
    end

    -- Immediate adds preparation (3-0 seconds)
    if gameState.timeToAdds < 3000 and gameState.timeToAdds > 0 then
        Aware:displayMessage("Adds Incoming!", "Red", 1)

        -- Final cooldown preparation
        if shouldBurst() then
            if PowerWordBarrier() then return true end
            if PowerInfusion() then return true end
            if InnerFocus() then return true end
        end

        -- Final shields
        if PowerWordShield() then return true end

        -- Position Prayer of Healing
        local injuredCount = getInjuredPartyCount(90)
        if injuredCount >= 2 then
            if PrayerOfHealing() then return true end
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        Aware:displayMessage("Adds Phase - AoE Healing", "Green", 1)
        return aoeHealing()
    else
        return singleTargetHealing()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 25 then
        if DesperatePrayer() then return true end
        if PainSuppression() then return true end
    end

    if player.hp <= 40 then
        if Fade() then return true end
        if PsychicScream() then return true end
    end

    -- Buff maintenance
    if InnerFire() then return true end
    if PowerWordFortitude() then return true end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on injured count
    local injuredCount = getInjuredPartyCount(80)
    if injuredCount >= 3 then
        return aoeHealing()
    else
        return singleTargetHealing()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    if not MakuluFramework.start() then
        enhancedMainRotation()
    end
    return MakuluFramework.endFunc()
end

-- Enhanced A[3] function for advanced rotation with burst and cooldowns
A[3] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    updateGameState()

    -- Enhanced defensive priority
    if player.hp <= 25 then
        if DesperatePrayer() then return MakuluFramework.endFunc() end
        if PainSuppression() then return MakuluFramework.endFunc() end
    end

    if player.hp <= 40 then
        if Fade() then return MakuluFramework.endFunc() end
        if PsychicScream() then return MakuluFramework.endFunc() end
    end

    -- Buff maintenance
    if InnerFire() then return MakuluFramework.endFunc() end
    if PowerWordFortitude() then return MakuluFramework.endFunc() end

    -- Enhanced healing priority
    local healTarget = getLowestHealthPartyMember()
    if healTarget then
        -- Emergency healing
        if healTarget.hp <= 30 then
            if PainSuppression() then return MakuluFramework.endFunc() end
            if InnerFocus() then return MakuluFramework.endFunc() end
            if FlashHeal() then return MakuluFramework.endFunc() end
        end

        -- Burst healing phase
        if shouldBurst() then
            if PowerInfusion() then return MakuluFramework.endFunc() end
            if SpiritShell() then return MakuluFramework.endFunc() end
            if PowerWordBarrier() then return MakuluFramework.endFunc() end

            -- Racial abilities during burst
            if QuakingPalm() then return MakuluFramework.endFunc() end
            if BloodFury() then return MakuluFramework.endFunc() end
            if Berserking() then return MakuluFramework.endFunc() end
            if GiftOfTheNaaru() then return MakuluFramework.endFunc() end
        end

        -- Dispel priority
        if Dispel() then return MakuluFramework.endFunc() end

        -- Shield priority
        if PowerWordShield() then return MakuluFramework.endFunc() end

        -- Direct healing
        if Penance() then return MakuluFramework.endFunc() end
        if FlashHeal() then return MakuluFramework.endFunc() end
        if GreaterHeal() then return MakuluFramework.endFunc() end

        -- AoE healing
        local injuredCount = getInjuredPartyCount(70)
        if injuredCount >= 3 then
            if PrayerOfHealing() then return MakuluFramework.endFunc() end
        end

        -- Maintenance healing
        if Renew() then return MakuluFramework.endFunc() end
        if PrayerOfMending() then return MakuluFramework.endFunc() end
    end

    -- Offensive abilities for atonement healing
    if target.exists and target.canAttack then
        -- Apply DoTs
        if ShadowWordPain() then return MakuluFramework.endFunc() end
        if HolyFire() then return MakuluFramework.endFunc() end

        -- Damage abilities
        if MindBlast() then return MakuluFramework.endFunc() end
        if Smite() then return MakuluFramework.endFunc() end
        if ShadowWordDeath() then return MakuluFramework.endFunc() end
    end

    -- Mana management
    if Archangel() then return MakuluFramework.endFunc() end
    if Shadowfiend() then return MakuluFramework.endFunc() end
    if ArcaneTorrent() then return MakuluFramework.endFunc() end

    -- TimeToAdds preparation
    if gameState.timeToAdds < 10000 then
        timeToAddsRotation()
    else
        -- Enhanced rotation selection
        local injuredCount = getInjuredPartyCount(80)
        if injuredCount >= 3 then
            aoeHealing()
        else
            singleTargetHealing()
        end
    end

    return MakuluFramework.endFunc()
end

-- Arena functions
A[6] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if A.GetToggle(2, "AutoInterrupt") and target.casting then
        if PsychicScream() then return MakuluFramework.endFunc() end
    end
    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena1)
        partyRotation(ConstUnit.party1)
    end

    return MakuluFramework.endFunc()
end

A[7] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena2)
        partyRotation(ConstUnit.party2)
    end

    return MakuluFramework.endFunc()
end

A[8] = function(icon)
    if MakuluFramework.start() then return MakuluFramework.endFunc() end

    if Action.Zone == "arena" then
        arenaRotation(ConstUnit.arena3)
        partyRotation(ConstUnit.party3)
    end

    return MakuluFramework.endFunc()
end

-- Arena-specific callback functions for MoP Discipline Priest
PsychicScream:Callback("arena", function(spell, enemy)
    if enemy.distance > 8 then return end
    if enemy.hp < 30 then return end -- Don't fear low targets

    return spell:Cast()
end)

Penance:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end

    -- Use offensively for atonement healing
    if gameState.atonementCount > 0 then
        Aware:displayMessage("Penance - Atonement Healing", "Green", 1)
        return spell:Cast(enemy)
    end
end)

PowerWordShield:Callback("arena", function(spell, friendly)
    if friendly:Buff(buffs.powerWordShield) then return end
    if friendly:DeBuff(debuffs.weakenedSoul) then return end
    if friendly.hp > 90 then return end

    -- Priority shielding when low
    if friendly.hp < 50 then
        Aware:displayMessage("Priority Shield", "Red", 1)
        return spell:Cast(friendly)
    end

    return spell:Cast(friendly)
end)

FlashHeal:Callback("arena", function(spell, friendly)
    if friendly.distance > 40 then return end
    if friendly.hp > 70 then return end

    -- Priority healing when low
    if friendly.hp < 40 then
        Aware:displayMessage("Priority Flash Heal", "Red", 1)
        return spell:Cast(friendly)
    end

    return spell:Cast(friendly)
end)

Dispel:Callback("arena", function(spell, friendly)
    if friendly.distance > 40 then return end
    if not friendly:HasDispellableDebuff() then return end

    return spell:Cast(friendly)
end)

MassDispel:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if not enemy:HasDispellableBuff() then return end

    return spell:Cast(enemy)
end)

local enhancedArenaRotation = function(enemy)
    if not enemy.exists then return end

    -- Offensive dispel
    MassDispel("arena", enemy)

    -- Damage for atonement
    Penance("arena", enemy)

    -- Fear for control
    PsychicScream("arena", enemy)
end

local enhancedPartyRotation = function(friendly)
    if not friendly.exists then return end

    -- Dispel priority
    Dispel("arena", friendly)

    -- Shield priority
    PowerWordShield("arena", friendly)

    -- Healing priority
    FlashHeal("arena", friendly)
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end
