-- APL UPDATE MoP Holy Priest
-- Mists of Pandaria Holy Priest Rotation

if not <PERSON><PERSON>luValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Holy spec (talent tree 2 for Priest in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 2 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Holy Priest Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Holy Core Healing Abilities
    Heal = { ID = 2050, MAKULU_INFO = { heal = true, castTime = 2500 } },
    GreaterHeal = { ID = 2060, MAKULU_INFO = { heal = true, castTime = 2500 } },
    FlashHeal = { ID = 2061, MAKULU_INFO = { heal = true, castTime = 1500 } },
    Renew = { ID = 139, MAKULU_INFO = { heal = true } },
    PrayerOfHealing = { ID = 596, MAKULU_INFO = { heal = true, castTime = 3000 } },
    PrayerOfMending = { ID = 33076, MAKULU_INFO = { heal = true } },
    CircleOfHealing = { ID = 34861, MAKULU_INFO = { heal = true } },
    BindingHeal = { ID = 32546, MAKULU_INFO = { heal = true, castTime = 1500 } },
    
    -- MoP Holy Specific Abilities
    Chakra = { ID = 14751, MAKULU_INFO = { targeted = false } },
    ChakraSerenity = { ID = 81208, MAKULU_INFO = { targeted = false } },
    ChakraSanctuary = { ID = 81206, MAKULU_INFO = { targeted = false } },
    ChakraChastise = { ID = 81209, MAKULU_INFO = { targeted = false } },
    Serendipity = { ID = 63730, MAKULU_INFO = { targeted = false } },
    SpiritOfRedemption = { ID = 27827, MAKULU_INFO = { targeted = false } },
    
    -- MoP Cooldowns
    GuardianSpirit = { ID = 47788, MAKULU_INFO = { heal = true } },
    DivineHymn = { ID = 64843, MAKULU_INFO = { heal = true, channeled = true } },
    HymnOfHope = { ID = 64901, MAKULU_INFO = { targeted = false, channeled = true } },
    
    -- MoP Damage Abilities
    HolyFire = { ID = 14914, MAKULU_INFO = { damageType = "holy", castTime = 2500 } },
    Smite = { ID = 585, MAKULU_INFO = { damageType = "holy", castTime = 2500 } },
    HolyNova = { ID = 15237, MAKULU_INFO = { damageType = "holy" } },
    
    -- MoP Utility
    PowerWordShield = { ID = 17, MAKULU_INFO = { heal = true } },
    PowerWordFortitude = { ID = 21562, MAKULU_INFO = { targeted = false } },
    InnerFire = { ID = 588, MAKULU_INFO = { targeted = false } },
    InnerWill = { ID = 73413, MAKULU_INFO = { targeted = false } },
    Fade = { ID = 586, MAKULU_INFO = { targeted = false } },
    PsychicScream = { ID = 8122, MAKULU_INFO = { targeted = false } },
    
    -- MoP Dispel and Utility
    Dispel = { ID = 527, MAKULU_INFO = { targeted = true } },
    MassDispel = { ID = 32375, MAKULU_INFO = { targeted = false, castTime = 1500 } },
    Purify = { ID = 527, MAKULU_INFO = { targeted = true } },
    
    -- MoP Shadow Abilities (for damage)
    ShadowWordPain = { ID = 589, MAKULU_INFO = { damageType = "shadow" } },
    ShadowWordDeath = { ID = 32379, MAKULU_INFO = { damageType = "shadow" } },
    MindBlast = { ID = 8092, MAKULU_INFO = { damageType = "shadow", castTime = 1500 } },
    MindFlay = { ID = 15407, MAKULU_INFO = { damageType = "shadow", channeled = true } },
    
    -- MoP Talents
    VoidShift = { ID = 108968, MAKULU_INFO = { heal = true } },
    PowerInfusion = { ID = 10060, MAKULU_INFO = { targeted = false } },
    TwistOfFate = { ID = 109142, MAKULU_INFO = { targeted = false } },
    PowerWordBarrier = { ID = 62618, MAKULU_INFO = { heal = true } },
    
    -- MoP Defensive Abilities
    DesperatePrayer = { ID = 19236, MAKULU_INFO = { heal = true, targeted = false } },
    
    -- MoP Resurrection
    Resurrection = { ID = 2006, MAKULU_INFO = { castTime = 10000 } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 8122, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 8122, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Holy Priest Buffs
local buffs = {
    chakra = 14751,
    chakraSerenity = 81208,
    chakraSanctuary = 81206,
    chakraChastise = 81209,
    serendipity = 63730,
    spiritOfRedemption = 27827,
    guardianSpirit = 47788,
    powerWordShield = 17,
    powerWordFortitude = 21562,
    innerFire = 588,
    innerWill = 73413,
    renew = 139,
    fade = 586,
    powerInfusion = 10060,
    twistOfFate = 109142,
    desperatePrayer = 19236,
    shadowmeld = 58984,
    divineHymn = 64843,
    hymnOfHope = 64901,
}

-- MoP Holy Priest Debuffs
local debuffs = {
    shadowWordPain = 589,
    holyFire = 14914,
    weakenedSoul = 6788,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    healingTarget = nil,
    chakraState = "none",
    serendipityStacks = 0,
    groupHealth = 100,
    needsAoEHealing = false,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.serendipityStacks = player:GetBuffStacks(buffs.serendipity) or 0
    
    -- Determine chakra state
    if player:HasBuff(buffs.chakraSerenity) then
        gameState.chakraState = "serenity"
    elseif player:HasBuff(buffs.chakraSanctuary) then
        gameState.chakraState = "sanctuary"
    elseif player:HasBuff(buffs.chakraChastise) then
        gameState.chakraState = "chastise"
    else
        gameState.chakraState = "none"
    end
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
    
    -- Check group health for AoE healing decisions
    local lowHealthCount = 0
    local totalHealth = 0
    local groupSize = 0
    
    -- Check party members
    for i = 1, 4 do
        local unit = MakUnit:new("party" .. i)
        if unit.exists then
            groupSize = groupSize + 1
            totalHealth = totalHealth + unit.hp
            if unit.hp < 80 then
                lowHealthCount = lowHealthCount + 1
            end
        end
    end
    
    -- Include player
    groupSize = groupSize + 1
    totalHealth = totalHealth + player.hp
    if player.hp < 80 then
        lowHealthCount = lowHealthCount + 1
    end
    
    gameState.groupHealth = groupSize > 0 and (totalHealth / groupSize) or 100
    gameState.needsAoEHealing = lowHealthCount >= 3
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3 or gameState.needsAoEHealing
end

local function needsChakra()
    return gameState.chakraState == "none"
end

local function shouldUseSerenity()
    return not gameState.needsAoEHealing
end

local function shouldUseSanctuary()
    return gameState.needsAoEHealing
end

local function getHealingTarget()
    -- Priority: player > party members by health
    if player.hp < 50 then
        return player
    end
    
    local lowestUnit = nil
    local lowestHealth = 100
    
    for i = 1, 4 do
        local unit = MakUnit:new("party" .. i)
        if unit.exists and unit.hp < lowestHealth and unit.hp < 80 then
            lowestUnit = unit
            lowestHealth = unit.hp
        end
    end
    
    return lowestUnit or player
end

local function shouldUseEmergencyHealing()
    return player.hp < 30 or (gameState.healingTarget and gameState.healingTarget.hp < 30)
end

-- Buff management
PowerWordFortitude:Callback(function(spell)
    if not player:HasBuff(buffs.powerWordFortitude) then
        return spell:Cast(player)
    end
end)

InnerFire:Callback(function(spell)
    if not player:HasBuff(buffs.innerFire) and not player:HasBuff(buffs.innerWill) then
        return spell:Cast(player)
    end
end)

InnerWill:Callback(function(spell)
    if player.moving and not player:HasBuff(buffs.innerWill) then
        return spell:Cast(player)
    end
end)

-- Chakra management
Chakra:Callback(function(spell)
    if needsChakra() then
        return spell:Cast(player)
    end
end)

ChakraSerenity:Callback(function(spell)
    if gameState.chakraState ~= "serenity" and shouldUseSerenity() then
        return spell:Cast(player)
    end
end)

ChakraSanctuary:Callback(function(spell)
    if gameState.chakraState ~= "sanctuary" and shouldUseSanctuary() then
        return spell:Cast(player)
    end
end)

-- Core healing abilities
Heal:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 80 then return end
    if player.moving then return end
    if gameState.mana < 20 then return end

    return spell:Cast(healTarget)
end)

GreaterHeal:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 60 then return end
    if player.moving then return end
    if gameState.mana < 30 then return end
    if gameState.serendipityStacks < 2 then return end

    return spell:Cast(healTarget)
end)

FlashHeal:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 70 then return end
    if gameState.mana < 25 then return end

    return spell:Cast(healTarget)
end)

Renew:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget:HasBuff(buffs.renew) then return end
    if healTarget.hp > 85 then return end

    return spell:Cast(healTarget)
end)

PrayerOfHealing:Callback(function(spell)
    if not gameState.needsAoEHealing then return end
    if player.moving then return end
    if gameState.mana < 40 then return end

    return spell:Cast(player)
end)

PrayerOfMending:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 90 then return end

    return spell:Cast(healTarget)
end)

CircleOfHealing:Callback(function(spell)
    if not gameState.needsAoEHealing then return end
    if gameState.mana < 30 then return end

    return spell:Cast(player)
end)

BindingHeal:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 70 and player.hp > 70 then return end
    if gameState.mana < 35 then return end

    return spell:Cast(healTarget)
end)

PowerWordShield:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget:HasBuff(buffs.powerWordShield) then return end
    if healTarget:HasDeBuff(debuffs.weakenedSoul) then return end
    if healTarget.hp > 80 then return end

    return spell:Cast(healTarget)
end)

-- Emergency abilities
GuardianSpirit:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 30 then return end

    return spell:Cast(healTarget)
end)

DesperatePrayer:Callback(function(spell)
    if player.hp > 40 then return end

    return spell:Cast(player)
end)

VoidShift:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 20 then return end
    if player.hp < 50 then return end

    return spell:Cast(healTarget)
end)

-- Cooldowns
DivineHymn:Callback(function(spell)
    if not shouldBurst() then return end
    if not gameState.needsAoEHealing then return end
    if player.moving then return end

    return spell:Cast(player)
end)

HymnOfHope:Callback(function(spell)
    if gameState.mana > 30 then return end
    if player.moving then return end

    return spell:Cast(player)
end)

PowerInfusion:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

PowerWordBarrier:Callback(function(spell)
    if not gameState.needsAoEHealing then return end
    if not shouldBurst() then return end

    return spell:Cast(target)
end)

-- Damage abilities
HolyFire:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if player.moving then return end
    if target:HasDeBuff(debuffs.holyFire) then return end

    return spell:Cast(target)
end)

Smite:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if player.moving then return end
    if gameState.groupHealth < 90 then return end

    return spell:Cast(target)
end)

ShadowWordPain:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if target:HasDeBuff(debuffs.shadowWordPain) then return end

    return spell:Cast(target)
end)

ShadowWordDeath:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if target.hp > 25 then return end

    return spell:Cast(target)
end)

MindBlast:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if player.moving then return end
    if gameState.groupHealth < 90 then return end

    return spell:Cast(target)
end)

HolyNova:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end

    return spell:Cast(player)
end)

-- Utility abilities
Fade:Callback(function(spell)
    if not gameState.inCombat then return end
    if player.hp > 70 then return end

    return spell:Cast(player)
end)

PsychicScream:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 8 then return end
    if not gameState.isPvP then return end

    return spell:Cast(player)
end)

Dispel:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if not healTarget:HasDispellableDebuff() then return end

    return spell:Cast(healTarget)
end)

MassDispel:Callback(function(spell)
    if not gameState.needsAoEHealing then return end
    if player.moving then return end

    return spell:Cast(target)
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.mana > 80 then return end

    return spell:Cast(player)
end)

-- PvE Single Target Healing Rotation
local function singleTargetHealing()
    updateGameState()

    -- Maintain buffs
    if PowerWordFortitude() then return true end
    if InnerFire() then return true end
    if player.moving and InnerWill() then return true end

    -- Chakra management
    if needsChakra() then
        if Chakra() then return true end
    end

    if shouldUseSerenity() then
        if ChakraSerenity() then return true end
    end

    -- Emergency healing
    if shouldUseEmergencyHealing() then
        if GuardianSpirit() then return true end
        if VoidShift() then return true end
        if DesperatePrayer() then return true end
    end

    -- Dispel priority
    if Dispel() then return true end

    -- Shield priority target
    if PowerWordShield() then return true end

    -- Renew maintenance
    if Renew() then return true end

    -- Prayer of Mending
    if PrayerOfMending() then return true end

    -- Main healing spells
    local healTarget = getHealingTarget()
    if healTarget then
        if healTarget.hp < 40 then
            if FlashHeal() then return true end
        elseif healTarget.hp < 70 and gameState.serendipityStacks >= 2 then
            if GreaterHeal() then return true end
        elseif healTarget.hp < 80 then
            if Heal() then return true end
        end
    end

    -- Damage when group is healthy
    if gameState.groupHealth > 90 and target.exists then
        if not target:HasDeBuff(debuffs.shadowWordPain) then
            if ShadowWordPain() then return true end
        end
        if not target:HasDeBuff(debuffs.holyFire) then
            if HolyFire() then return true end
        end
        if Smite() then return true end
    end

    return false
end

-- PvE AoE Healing Rotation
local function aoeHealing()
    updateGameState()

    -- Maintain buffs
    if PowerWordFortitude() then return true end
    if InnerFire() then return true end

    -- Chakra management
    if needsChakra() then
        if Chakra() then return true end
    end

    if shouldUseSanctuary() then
        if ChakraSanctuary() then return true end
    end

    -- Emergency healing
    if shouldUseEmergencyHealing() then
        if GuardianSpirit() then return true end
        if VoidShift() then return true end
        if DesperatePrayer() then return true end
    end

    -- Major AoE cooldowns
    if shouldBurst() then
        if DivineHymn() then return true end
        if PowerWordBarrier() then return true end
    end

    -- AoE healing priority
    if CircleOfHealing() then return true end
    if PrayerOfHealing() then return true end

    -- Individual healing for critical targets
    local healTarget = getHealingTarget()
    if healTarget and healTarget.hp < 50 then
        if PowerWordShield() then return true end
        if FlashHeal() then return true end
    end

    -- Renew on multiple targets
    if Renew() then return true end

    -- Prayer of Mending
    if PrayerOfMending() then return true end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 30 then
        if DesperatePrayer() then return true end
        if Fade() then return true end
    end

    -- Maintain buffs
    if PowerWordFortitude() then return true end
    if InnerWill() then return true end -- Prefer Inner Will in PvP for movement

    -- Chakra management (prefer Serenity for PvP)
    if needsChakra() then
        if Chakra() then return true end
    end

    if ChakraSerenity() then return true end

    -- Dispel priority
    if Dispel() then return true end

    -- Emergency healing
    if shouldUseEmergencyHealing() then
        if GuardianSpirit() then return true end
        if VoidShift() then return true end
    end

    -- Shield priority
    if PowerWordShield() then return true end

    -- Healing priority
    local healTarget = getHealingTarget()
    if healTarget then
        if healTarget.hp < 40 then
            if FlashHeal() then return true end
        elseif healTarget.hp < 70 then
            if Heal() then return true end
        end
    end

    -- Renew for mobility
    if Renew() then return true end

    -- Prayer of Mending
    if PrayerOfMending() then return true end

    -- Offensive abilities
    if target.exists and target.alive then
        if PsychicScream() then return true end

        if not target:HasDeBuff(debuffs.shadowWordPain) then
            if ShadowWordPain() then return true end
        end

        if target.hp < 25 then
            if ShadowWordDeath() then return true end
        end

        if not player.moving then
            if HolyFire() then return true end
            if Smite() then return true end
        end
    end

    return false
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Prepare mana
        if gameState.mana < 80 then
            if HymnOfHope() then return true end
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                if PowerInfusion() then return true end
                if DivineHymn() then return true end
                if PowerWordBarrier() then return true end
            end
        end

        -- Switch to Sanctuary for AoE healing
        if ChakraSanctuary() then return true end

        -- Maintain current healing
        local healTarget = getHealingTarget()
        if healTarget and healTarget.hp < 80 then
            if PowerWordShield() then return true end
            if Renew() then return true end
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 or gameState.needsAoEHealing then
        return aoeHealing()
    else
        return singleTargetHealing()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 30 then
        if DesperatePrayer() then return true end
        if Fade() then return true end
    end

    -- Mana management
    if gameState.mana < 20 then
        if HymnOfHope() then return true end
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on healing needs
    if shouldAoE() then
        return aoeHealing()
    else
        return singleTargetHealing()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Mana: ", gameState.mana)
        MakPrint(3, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(4, "In Combat: ", gameState.inCombat)
        MakPrint(5, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(6, "Is PvP: ", gameState.isPvP)
        MakPrint(7, "Chakra State: ", gameState.chakraState)
        MakPrint(8, "Serendipity Stacks: ", gameState.serendipityStacks)
        MakPrint(9, "Group Health: ", gameState.groupHealth)
        MakPrint(10, "Needs AoE Healing: ", gameState.needsAoEHealing)
        MakPrint(11, "PWS Active: ", player:HasBuff(buffs.powerWordShield))
        MakPrint(12, "Renew Active: ", player:HasBuff(buffs.renew))
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if GuardianSpirit:IsReady() and shouldUseEmergencyHealing() then
            Aware:displayMessage("GUARDIAN SPIRIT READY", "Red", 1)
        end
        if DivineHymn:IsReady() and shouldBurst() and gameState.needsAoEHealing then
            Aware:displayMessage("DIVINE HYMN READY", "Blue", 1)
        end
        if needsChakra() then
            Aware:displayMessage("CHAKRA NEEDED", "Yellow", 1)
        end
        if gameState.chakraState ~= "serenity" and shouldUseSerenity() then
            Aware:displayMessage("SWITCH TO SERENITY", "Orange", 1)
        end
        if gameState.chakraState ~= "sanctuary" and shouldUseSanctuary() then
            Aware:displayMessage("SWITCH TO SANCTUARY", "Orange", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
        if not player:HasBuff(buffs.powerWordFortitude) then
            Aware:displayMessage("NO FORTITUDE", "White", 1)
        end
        if gameState.needsAoEHealing then
            Aware:displayMessage("AOE HEALING NEEDED", "Green", 1)
        end
        if gameState.mana < 30 then
            Aware:displayMessage("LOW MANA", "Red", 1)
        end
        if gameState.serendipityStacks >= 2 then
            Aware:displayMessage("SERENDIPITY STACKS: " .. gameState.serendipityStacks, "Blue", 1)
        end
        local healTarget = getHealingTarget()
        if healTarget and healTarget.hp < 40 then
            Aware:displayMessage("CRITICAL HEALING NEEDED", "Red", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 15 then
        if DesperatePrayer:IsReady() then return FrameworkEnd() end
        if Fade:IsReady() then return FrameworkEnd() end
    end

    if player.hp <= 30 then
        if GuardianSpirit:IsReady() then return FrameworkEnd() end
        if VoidShift:IsReady() then return FrameworkEnd() end
    end

    -- Emergency healing priority
    local healTarget = getHealingTarget()
    if healTarget and healTarget.hp < 20 then
        if GuardianSpirit() then return FrameworkEnd() end
        if VoidShift() then return FrameworkEnd() end
        if FlashHeal() then return FrameworkEnd() end
    end

    -- Mana management
    if gameState.mana < 20 then
        if HymnOfHope() then return FrameworkEnd() end
        if ArcaneTorrent() then return FrameworkEnd() end
    end

    -- Burst phase
    if shouldBurst() then
        if PowerInfusion() then return FrameworkEnd() end
        if DivineHymn() then return FrameworkEnd() end
        if PowerWordBarrier() then return FrameworkEnd() end

        -- Racial abilities during burst
        if BloodFury() then return FrameworkEnd() end
        if Berserking() then return FrameworkEnd() end
    end

    -- TimeToAdds preparation
    if gameState.timeToAdds < 10000 then
        timeToAddsRotation()
    else
        -- Enhanced rotation selection
        if shouldAoE() then
            aoeHealing()
        else
            singleTargetHealing()
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- Offensive dispel
    MassDispel("arena", enemy)

    -- CC abilities
    PsychicScream("arena", enemy)

    -- Damage abilities
    if not enemy:HasDeBuff(debuffs.shadowWordPain) then
        ShadowWordPain("arena", enemy)
    end

    if enemy.hp < 25 then
        ShadowWordDeath("arena", enemy)
    end

    if not player.moving then
        HolyFire("arena", enemy)
        Smite("arena", enemy)
        MindBlast("arena", enemy)
    end
end

local function enhancedPartyRotation(friendly)
    if not friendly.exists then return end

    -- Healing priority for party members
    if friendly.hp < 30 then
        GuardianSpirit("arena", friendly)
        FlashHeal("arena", friendly)
    elseif friendly.hp < 60 then
        PowerWordShield("arena", friendly)
        Heal("arena", friendly)
    elseif friendly.hp < 80 then
        Renew("arena", friendly)
    end

    -- Dispel party members
    Dispel("arena", friendly)
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Holy Priest
FlashHeal:Callback("arena", function(spell, friendly)
    if friendly.hp > 70 then return end
    if gameState.mana < 25 then return end

    -- Priority healing in arena
    if friendly.hp < 40 then
        Aware:displayMessage("Emergency Flash Heal", "Red", 1)
        return spell:Cast(friendly)
    end

    return spell:Cast(friendly)
end)

Heal:Callback("arena", function(spell, friendly)
    if friendly.hp > 80 then return end
    if player.moving then return end
    if gameState.mana < 20 then return end

    return spell:Cast(friendly)
end)

PowerWordShield:Callback("arena", function(spell, friendly)
    if friendly:HasBuff(buffs.powerWordShield) then return end
    if friendly:HasDeBuff(debuffs.weakenedSoul) then return end
    if friendly.hp > 80 then return end

    -- Priority shielding in arena
    Aware:displayMessage("Shield - Protection", "Blue", 1)
    return spell:Cast(friendly)
end)

Renew:Callback("arena", function(spell, friendly)
    if friendly:HasBuff(buffs.renew) then return end
    if friendly.hp > 85 then return end

    return spell:Cast(friendly)
end)

GuardianSpirit:Callback("arena", function(spell, friendly)
    if friendly.hp > 30 then return end

    -- Emergency save in arena
    Aware:displayMessage("Guardian Spirit - Emergency", "Red", 1)
    return spell:Cast(friendly)
end)

Dispel:Callback("arena", function(spell, friendly)
    if not friendly:HasDispellableDebuff() then return end

    -- Dispel priority in arena
    Aware:displayMessage("Dispel - Cleanse", "Green", 1)
    return spell:Cast(friendly)
end)

ShadowWordPain:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.shadowWordPain) then return end

    return spell:Cast(enemy)
end)

ShadowWordDeath:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy.hp > 25 then return end

    -- Execute in arena
    Aware:displayMessage("Shadow Word: Death - Execute", "Purple", 1)
    return spell:Cast(enemy)
end)

HolyFire:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if player.moving then return end
    if enemy:HasDeBuff(debuffs.holyFire) then return end

    return spell:Cast(enemy)
end)

Smite:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if player.moving then return end

    return spell:Cast(enemy)
end)

MindBlast:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if player.moving then return end

    return spell:Cast(enemy)
end)

PsychicScream:Callback("arena", function(spell, enemy)
    if enemy.distance > 8 then return end

    -- Fear in arena
    Aware:displayMessage("Psychic Scream - Fear", "Yellow", 1)
    return spell:Cast(player)
end)

MassDispel:Callback("arena", function(spell, enemy)
    if player.moving then return end

    -- Offensive dispel in arena
    Aware:displayMessage("Mass Dispel - Offensive", "Orange", 1)
    return spell:Cast(enemy)
end)


