local Action = _G.Action

local A                = Action

local CONST                                                              = Action.Const

local ACTION_CONST_ROGUE_ASSASSINATION                              = CONST.ROGUE_ASSASSINATION
local ACTION_CONST_ROGUE_OUTLAW                                     = CONST.ROGUE_OUTLAW
local ACTION_CONST_ROGUE_SUBTLETY                                   = CONST.ROGUE_SUBTLETY

LPH_ENCNUM = function(val) return val end

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "Makulu MoP v1.0.0 (7/29/2025)",
    -- Class settings
    [2] = {
        {
            {
                E = "Header",
                L = {
                    ANY = " ====== Makulu - MoP Rogue ====== ",
                },
            },
        },
        {
            { -- AOE
                E = "Checkbox", 
                DB = "AoE",
                DBV = true,
                L = { 
                    enUS = "Use AoE", 
                    ruRU = "Использовать AoE", 
                    frFR = "Utiliser l'AoE",
                }, 
                TT = { 
                    enUS = "Enable multiunits actions", 
                    ruRU = "Включает действия для нескольких целей", 
                    frFR = "Activer les actions multi-unités",
                }, 
                M = {},
            },
            { -- Auto Poison
                E = "Checkbox", 
                DB = "autoPoison",
                DBV = true,
                L = { 
                    ANY = "Auto Poison Management", 
                }, 
                TT = { 
                    ANY = "Automatically maintain appropriate poisons on weapons.", 
                }, 
                M = {},
            },
            { -- Combo Point Management
                E = "Checkbox", 
                DB = "comboManagement",
                DBV = true,
                L = { 
                    ANY = "Smart Combo Point Management", 
                }, 
                TT = { 
                    ANY = "Optimize combo point usage and prevent combo point capping."
                }, 
                M = {},
            },   
        },
        { -- Spacer
            
            {
                E = "LayoutSpace",
            },
        },
        { -- Potions
            { -- useDamagePotion
                E = "Checkbox", 
                DB = "damagePotion",
                DBV = true,
                L = { 
                    ANY = "Damage Potion"
                }, 
                TT = { 
                    ANY = "Use Damage Potion", 
                }, 
                M = {},
            },
            { -- potionBossOnly
                E = "Checkbox", 
                DB = "potionLustOnly",
                DBV = true,
                L = { 
                    ANY = "Damage Potion Bloodlust/TimeWarp Only", 
                }, 
                TT = { 
                    ANY = "Only use Damage Potion when any kind of Bloodlust/Warp active."
                }, 
                M = {},
            },
        },
        {
            { -- potionExhausted
                E = "Checkbox", 
                DB = "potionExhausted",
                DBV = true,
                L = { 
                    ANY = "Damage Potion With Exhaustion", 
                }, 
                TT = { 
                    ANY = "Use Damage Potion while Exhausted (can't use Bloodlust)."
                }, 
                M = {},
            },
            { -- potionExhaustedSlider
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,   
                Precision = 1,                         
                DB = "potionExhaustedSlider",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Exhaustion Time Remaining",
                },
                TT = { 
                    ANY = "Time in minutes left on the Exhaustion Debuff to consider using Damage Potion.", 
                },                     
                M = {},
            },
        },
        { -- LAYOUT SPACE   
            {
                E = "LayoutSpace",                                                                         
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Cooldowns",
                },
            },
        },
        {
            {
                E = "Dropdown",                                                         
                H = 20,
                OT = {
                    { text = "Shadow Clone", value = 1 }, 
                    { text = "Vendetta", value = 2 },     
                    { text = "Cold Blood", value = 3 },
                    { text = "Preparation", value = 4 },
                    { text = "Adrenaline Rush", value = 5 },
                    { text = "Killing Spree", value = 6 },   
                },
                MULT = true,
                DB = "cooldownSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                    [5] = true,
                    [6] = true,
                },  
                L = { 
                    ANY = "Cooldown Abilities", 
                }, 
                TT = { 
                    ANY = "Select what abilities you want the rotation to obey the burst toggle.\nIf a spell is unchecked, it will be used even when burst is turned off!", 
                }, 
                M = {},                                    
            },  
        }, 
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { 
            {-- Burst Sensitivity
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "burstSens",
                DBV = 18,
                ONOFF = false,
                L = { 
                    ANY = "Burst Mode Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to use cooldowns. A lower number means cooldowns used closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },  
            {-- Combo Point Threshold
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,                            
                DB = "comboThreshold",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Combo Point Conservation Threshold",
                },
                TT = { 
                    ANY = "Combo Point amount to start conserving and use finishers.", 
                },                     
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- ROGUE HEADER
            {
                E = "Header",
                L = {
                    ANY = "INTERRUPTS",
                },
            },
        },
        {    
            { -- Automatic Interrupt
                E = "Checkbox", 
                DB = "AutoInterrupt",
                DBV = true,
                L = { 
                    ANY = "Switch Targets Interrupt",
                }, 
                TT = { 
                    ANY = "Automatically switches targets to interrupt.",
                }, 
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- ROGUE HEADER
            {
                E = "Header",
                L = {
                    ANY = "DEFENSIVES",
                },
            },
        },
        {
            { -- Evasion HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "EvasionHP",
                DBV = 50,
                ONOFF = false,
                L = { 
                    ANY = "Evasion HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Evasion on yourself.", 
                },                     
                M = {},
            },    
            { -- Cloak of Shadows HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "CloakShadowsHP",
                DBV = 60,
                ONOFF = false,
                L = { 
                    ANY = "Cloak of Shadows HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Cloak of Shadows on yourself.", 
                },                     
                M = {},
            },    
        },
        {
            {-- Recuperate HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "recuperateHP",
                DBV = 70,
                ONOFF = false,
                L = { 
                    ANY = "Recuperate HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Recuperate for healing over time.", 
                },                     
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Evasion", value = 1 },
                    { text = "Cloak of Shadows", value = 2 },
                    { text = "Recuperate", value = 3 },
                    { text = "Vanish", value = 4 },
                },
                MULT = true,
                DB = "defensiveSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Defensive Reactions",
                },
                TT = {
                    ANY = "Select what spells to be used when reacting to incoming damage in dungeons.",
                },
                M = {},
            },
        },
        { -- Spacer

            {
                E = "LayoutSpace",
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Debug/Aware Options",
                },
            },
        },
        {
            { -- Debug
                E = "Checkbox",
                DB = "makDebug",
                DBV = false,
                L = {
                    ANY = "Enable debug options",
                },
                TT = {
                    ANY = "Show a box with various debug data.\nIt takes a couple of seconds to get rid of the box when you disable this.",
                },
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Combo Point Reminder", value = 1 },
                    { text = "Vendetta Ready", value = 2 },
                    { text = "Shadow Clone Ready", value = 3 },
                    { text = "Energy Alert", value = 4 },
                },
                MULT = true,
                DB = "makAware",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Aware Text Alert Reminders",
                },
                TT = {
                    ANY = "Select what text alert reminders you would like.\nThese will appear in the center of your screen.",
                },
                M = {},
            },
        },
    },
}
