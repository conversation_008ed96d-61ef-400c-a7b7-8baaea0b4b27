-- APL UPDATE MoP Assassination Rogue
-- Mists of Pandaria Assassination Rogue Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Assassination spec (talent tree 1 for Rogue in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 1 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Assassination Rogue Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Assassination Core Abilities
    Mutilate = { ID = 1329, MAKULU_INFO = { damageType = "physical" } },
    Envenom = { ID = 32645, MAKULU_INFO = { damageType = "nature" } },
    Rupture = { ID = 1943, MAKULU_INFO = { damageType = "physical" } },
    Garrote = { ID = 703, MAKULU_INFO = { damageType = "physical" } },
    SliceAndDice = { ID = 5171, MAKULU_INFO = { targeted = false } },
    Backstab = { ID = 53, MAKULU_INFO = { damageType = "physical" } },
    Ambush = { ID = 8676, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP Assassination Finishers
    Eviscerate = { ID = 2098, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP Assassination AoE
    FanOfKnives = { ID = 51723, MAKULU_INFO = { damageType = "physical" } },
    CrimsonTempest = { ID = 121411, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP Cooldowns
    Vendetta = { ID = 79140, MAKULU_INFO = { targeted = false } },
    ColdBlood = { ID = 14177, MAKULU_INFO = { targeted = false } },
    
    -- MoP Utility
    Kick = { ID = 1766, MAKULU_INFO = { damageType = "physical", ignoreCasting = true } },
    Blind = { ID = 2094, MAKULU_INFO = { targeted = true } },
    CheapShot = { ID = 1833, MAKULU_INFO = { damageType = "physical" } },
    KidneyShot = { ID = 408, MAKULU_INFO = { damageType = "physical" } },
    Sap = { ID = 6770, MAKULU_INFO = { targeted = true } },
    Stealth = { ID = 1784, MAKULU_INFO = { targeted = false } },
    Vanish = { ID = 1856, MAKULU_INFO = { targeted = false } },
    Sprint = { ID = 2983, MAKULU_INFO = { targeted = false } },
    
    -- MoP Defensive Abilities
    Evasion = { ID = 5277, MAKULU_INFO = { targeted = false } },
    CloakOfShadows = { ID = 31224, MAKULU_INFO = { targeted = false } },
    Feint = { ID = 1966, MAKULU_INFO = { targeted = false } },
    CrimsonVial = { ID = 185311, MAKULU_INFO = { heal = true, targeted = false } },
    
    -- MoP Poisons
    DeadlyPoison = { ID = 2823, MAKULU_INFO = { targeted = false } },
    InstantPoison = { ID = 8679, MAKULU_INFO = { targeted = false } },
    WoundPoison = { ID = 8680, MAKULU_INFO = { targeted = false } },
    CripplingPoison = { ID = 3408, MAKULU_INFO = { targeted = false } },
    
    -- MoP Talents
    ShadowClone = { ID = 114018, MAKULU_INFO = { targeted = false } },
    MarkedForDeath = { ID = 137619, MAKULU_INFO = { targeted = true } },
    Anticipation = { ID = 114015, MAKULU_INFO = { targeted = false } },
    Dispatch = { ID = 111240, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP Preparation and Utility
    Preparation = { ID = 14185, MAKULU_INFO = { targeted = false } },
    Distract = { ID = 1725, MAKULU_INFO = { targeted = true } },
    PickLock = { ID = 1804, MAKULU_INFO = { targeted = true } },
    PickPocket = { ID = 921, MAKULU_INFO = { targeted = true } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 1766, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 2094, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Assassination Rogue Buffs
local buffs = {
    stealth = 1784,
    vanish = 11327,
    sliceAndDice = 5171,
    envenom = 32645,
    coldBlood = 14177,
    vendetta = 79140,
    shadowClone = 114018,
    anticipation = 114015,
    evasion = 5277,
    cloakOfShadows = 31224,
    feint = 1966,
    sprint = 2983,
    shadowmeld = 58984,
    preparation = 14185,
    deadlyPoison = 2823,
    instantPoison = 8679,
    woundPoison = 8680,
    cripplingPoison = 3408,
}

-- MoP Assassination Rogue Debuffs
local debuffs = {
    rupture = 1943,
    garrote = 703,
    vendetta = 79140,
    crimsonTempest = 121411,
    blind = 2094,
    sap = 6770,
    cheapShot = 1833,
    kidneyShot = 408,
    deadlyPoison = 2823,
    woundPoison = 8680,
    cripplingPoison = 3408,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    energy = 0,
    comboPoints = 0,
    timeToAdds = 999,
    isPvP = false,
    executeRange = false,
    stealthed = false,
    behindTarget = false,
    maxComboPoints = 5,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.energy = player.energy or 0
    gameState.comboPoints = player.cp or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.executeRange = target.exists and target.hp <= 35 -- Dispatch range in MoP
    gameState.stealthed = player:HasBuff(buffs.stealth) or player:HasBuff(buffs.vanish)
    gameState.behindTarget = target.exists and target.facing ~= "player"
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function needsSliceAndDice()
    return not player:HasBuff(buffs.sliceAndDice) or player:BuffRemains(buffs.sliceAndDice) < 6000
end

local function shouldUseFinisher()
    return gameState.comboPoints >= 4 or (gameState.comboPoints >= 3 and gameState.executeRange)
end

local function shouldUseEnvenom()
    return shouldUseFinisher() and not needsSliceAndDice()
end

local function shouldUseRupture()
    return not target:HasDeBuff(debuffs.rupture) or target:DeBuffRemains(debuffs.rupture) < 6000
end

local function canUseFromStealth()
    return gameState.stealthed
end

-- Poison management
DeadlyPoison:Callback(function(spell)
    if not player:HasBuff(buffs.deadlyPoison) then
        return spell:Cast(player)
    end
end)

InstantPoison:Callback(function(spell)
    if not player:HasBuff(buffs.instantPoison) then
        return spell:Cast(player)
    end
end)

-- Stealth abilities
Stealth:Callback(function(spell)
    if gameState.inCombat then return end
    if gameState.stealthed then return end

    return spell:Cast(player)
end)

Vanish:Callback(function(spell)
    if not gameState.inCombat then return end
    if gameState.stealthed then return end
    if player.hp > 30 then return end

    return spell:Cast(player)
end)

-- Core abilities
Mutilate:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if gameState.energy < 60 then return end
    if gameState.comboPoints >= gameState.maxComboPoints then return end

    return spell:Cast(target)
end)

Backstab:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if not gameState.behindTarget then return end
    if gameState.energy < 60 then return end
    if gameState.comboPoints >= gameState.maxComboPoints then return end

    return spell:Cast(target)
end)

Ambush:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if not canUseFromStealth() then return end
    if gameState.energy < 60 then return end

    return spell:Cast(target)
end)

-- Finishers
Envenom:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if not shouldUseEnvenom() then return end
    if gameState.energy < 35 then return end

    return spell:Cast(target)
end)

SliceAndDice:Callback(function(spell)
    if not needsSliceAndDice() then return end
    if gameState.comboPoints < 1 then return end
    if gameState.energy < 25 then return end

    return spell:Cast(player)
end)

Rupture:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if not shouldUseRupture() then return end
    if not shouldUseFinisher() then return end
    if gameState.energy < 25 then return end

    return spell:Cast(target)
end)

Eviscerate:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if not shouldUseFinisher() then return end
    if gameState.energy < 35 then return end
    if needsSliceAndDice() then return end
    if shouldUseRupture() then return end

    return spell:Cast(target)
end)

Dispatch:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if not gameState.executeRange then return end
    if gameState.energy < 30 then return end

    return spell:Cast(target)
end)

-- AoE abilities
FanOfKnives:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if gameState.energy < 50 then return end

    return spell:Cast(target)
end)

CrimsonTempest:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if not shouldUseFinisher() then return end
    if gameState.energy < 35 then return end

    return spell:Cast(target)
end)

-- DoT abilities
Garrote:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if not canUseFromStealth() then return end
    if target:HasDeBuff(debuffs.garrote) then return end
    if gameState.energy < 45 then return end

    return spell:Cast(target)
end)

-- Cooldowns
Vendetta:Callback(function(spell)
    if not target.exists then return end
    if not shouldBurst() then return end

    return spell:Cast(target)
end)

ColdBlood:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Preparation:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

-- Utility abilities
Kick:Callback(function(spell)
    if not target.exists then return end
    if not target:IsCasting() then return end
    if not target:IsInterruptible() then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

Blind:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 10 then return end
    if not gameState.isPvP then return end

    return spell:Cast(target)
end)

CheapShot:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if not canUseFromStealth() then return end
    if gameState.energy < 40 then return end

    return spell:Cast(target)
end)

KidneyShot:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if not shouldUseFinisher() then return end
    if not gameState.isPvP then return end
    if gameState.energy < 25 then return end

    return spell:Cast(target)
end)

Sap:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 10 then return end
    if not canUseFromStealth() then return end
    if gameState.inCombat then return end

    return spell:Cast(target)
end)

-- Defensive abilities
Evasion:Callback(function(spell)
    if player.hp > 50 then return end
    if player:HasBuff(buffs.evasion) then return end

    return spell:Cast(player)
end)

CloakOfShadows:Callback(function(spell)
    if player.hp > 40 then return end
    if player:HasBuff(buffs.cloakOfShadows) then return end

    return spell:Cast(player)
end)

Feint:Callback(function(spell)
    if player.hp > 60 then return end
    if player:HasBuff(buffs.feint) then return end
    if gameState.energy < 20 then return end

    return spell:Cast(player)
end)

CrimsonVial:Callback(function(spell)
    if player.hp > 50 then return end
    if gameState.energy < 30 then return end

    return spell:Cast(player)
end)

Sprint:Callback(function(spell)
    if not player.moving then return end
    if player:HasBuff(buffs.sprint) then return end

    return spell:Cast(player)
end)

-- Talent abilities
ShadowClone:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

MarkedForDeath:Callback(function(spell)
    if not target.exists then return end
    if gameState.comboPoints >= 3 then return end

    return spell:Cast(target)
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.energy > 80 then return end

    return spell:Cast(player)
end)

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain poisons
    if DeadlyPoison() then return true end
    if InstantPoison() then return true end

    -- Stealth opener
    if canUseFromStealth() then
        if Garrote() then return true end
        if Ambush() then return true end
    end

    -- Maintain Slice and Dice
    if needsSliceAndDice() and gameState.comboPoints >= 1 then
        if SliceAndDice() then return true end
    end

    -- Execute range priority
    if gameState.executeRange then
        if Dispatch() then return true end
    end

    -- Maintain Rupture
    if shouldUseRupture() and shouldUseFinisher() then
        if Rupture() then return true end
    end

    -- Envenom at 4+ combo points
    if shouldUseEnvenom() then
        if Envenom() then return true end
    end

    -- Eviscerate as fallback finisher
    if shouldUseFinisher() and not needsSliceAndDice() and not shouldUseRupture() then
        if Eviscerate() then return true end
    end

    -- Combo point generation
    if gameState.comboPoints < gameState.maxComboPoints then
        if gameState.behindTarget then
            if Backstab() then return true end
        else
            if Mutilate() then return true end
        end
    end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain poisons
    if DeadlyPoison() then return true end
    if InstantPoison() then return true end

    -- Stealth opener
    if canUseFromStealth() then
        if Garrote() then return true end
        if Ambush() then return true end
    end

    -- Maintain Slice and Dice
    if needsSliceAndDice() and gameState.comboPoints >= 1 then
        if SliceAndDice() then return true end
    end

    -- AoE finisher
    if shouldUseFinisher() and not needsSliceAndDice() then
        if CrimsonTempest() then return true end
    end

    -- Maintain Rupture on primary target
    if shouldUseRupture() and shouldUseFinisher() then
        if Rupture() then return true end
    end

    -- Fan of Knives for AoE combo point generation
    if gameState.comboPoints < gameState.maxComboPoints then
        if FanOfKnives() then return true end
    end

    -- Fallback to single target abilities
    if gameState.comboPoints < gameState.maxComboPoints then
        if Mutilate() then return true end
    end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 30 then
        if CloakOfShadows() then return true end
        if Evasion() then return true end
        if Vanish() then return true end
    end

    if player.hp <= 50 then
        if CrimsonVial() then return true end
        if Feint() then return true end
    end

    -- Interrupt priority
    if Kick() then return true end

    -- Maintain poisons
    if DeadlyPoison() then return true end
    if InstantPoison() then return true end

    if target.exists and target.alive then
        -- Stealth openers
        if canUseFromStealth() then
            if CheapShot() then return true end
            if Garrote() then return true end
            if Ambush() then return true end
        end

        -- CC abilities
        if not gameState.inCombat then
            if Sap() then return true end
        end

        if Blind() then return true end

        -- Maintain Slice and Dice
        if needsSliceAndDice() and gameState.comboPoints >= 1 then
            if SliceAndDice() then return true end
        end

        -- Execute range priority
        if gameState.executeRange then
            if Dispatch() then return true end
        end

        -- Control finishers
        if shouldUseFinisher() and not needsSliceAndDice() then
            if KidneyShot() then return true end
        end

        -- Maintain Rupture
        if shouldUseRupture() and shouldUseFinisher() then
            if Rupture() then return true end
        end

        -- Envenom for damage
        if shouldUseEnvenom() then
            if Envenom() then return true end
        end

        -- Combo point generation
        if gameState.comboPoints < gameState.maxComboPoints then
            if gameState.behindTarget then
                if Backstab() then return true end
            else
                if Mutilate() then return true end
            end
        end
    end

    return false
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Save energy for AoE abilities
        if gameState.energy < 80 then
            -- Use basic abilities to build energy
            if gameState.comboPoints < gameState.maxComboPoints then
                if Mutilate() then return true end
            end
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                if Vendetta() then return true end
                if ColdBlood() then return true end
                if ShadowClone() then return true end
            end
        end

        -- Maintain current target
        if shouldUseRupture() and shouldUseFinisher() then
            if Rupture() then return true end
        end

        if needsSliceAndDice() and gameState.comboPoints >= 1 then
            if SliceAndDice() then return true end
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 30 then
        if CloakOfShadows() then return true end
        if Evasion() then return true end
        if Vanish() then return true end
    end

    if player.hp <= 50 then
        if CrimsonVial() then return true end
        if Feint() then return true end
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Energy: ", gameState.energy)
        MakPrint(3, "Combo Points: ", gameState.comboPoints)
        MakPrint(4, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(5, "In Combat: ", gameState.inCombat)
        MakPrint(6, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(7, "Is PvP: ", gameState.isPvP)
        MakPrint(8, "Execute Range: ", gameState.executeRange)
        MakPrint(9, "Stealthed: ", gameState.stealthed)
        MakPrint(10, "Behind Target: ", gameState.behindTarget)
        MakPrint(11, "Slice and Dice Active: ", player:HasBuff(buffs.sliceAndDice))
        MakPrint(12, "Rupture on Target: ", target:HasDeBuff(debuffs.rupture))
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if Vendetta:IsReady() and shouldBurst() then
            Aware:displayMessage("VENDETTA READY", "Red", 1)
        end
        if ColdBlood:IsReady() and shouldBurst() then
            Aware:displayMessage("COLD BLOOD READY", "Blue", 1)
        end
        if needsSliceAndDice() then
            Aware:displayMessage("SLICE AND DICE NEEDED", "Yellow", 1)
        end
        if shouldUseRupture() and shouldUseFinisher() then
            Aware:displayMessage("RUPTURE NEEDED", "Orange", 1)
        end
        if gameState.executeRange then
            Aware:displayMessage("EXECUTE RANGE", "Purple", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
        if not player:HasBuff(buffs.deadlyPoison) then
            Aware:displayMessage("NO DEADLY POISON", "White", 1)
        end
        if shouldUseFinisher() and shouldUseEnvenom() then
            Aware:displayMessage("USE ENVENOM", "Green", 1)
        end
        if gameState.comboPoints >= gameState.maxComboPoints then
            Aware:displayMessage("COMBO POINTS CAPPED", "Red", 1)
        end
        if gameState.energy < 30 then
            Aware:displayMessage("LOW ENERGY", "Orange", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 15 then
        if CloakOfShadows:IsReady() then return FrameworkEnd() end
        if Vanish:IsReady() then return FrameworkEnd() end
    end

    if player.hp <= 30 then
        if Evasion:IsReady() then return FrameworkEnd() end
        if CrimsonVial:IsReady() then return FrameworkEnd() end
    end

    if target.exists and target.alive then
        -- Enhanced interrupt priority
        if target:IsCasting() and target:IsInterruptible() then
            if Kick() then return FrameworkEnd() end
        end

        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if Blind() then return FrameworkEnd() end
                if Sap() then return FrameworkEnd() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if Vendetta() then return FrameworkEnd() end
            if ColdBlood() then return FrameworkEnd() end
            if ShadowClone() then return FrameworkEnd() end

            -- Racial abilities during burst
            if BloodFury() then return FrameworkEnd() end
            if Berserking() then return FrameworkEnd() end
        end

        -- Talent abilities
        if MarkedForDeath() then return FrameworkEnd() end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    Kick("arena", enemy)

    -- CC abilities
    Blind("arena", enemy)
    CheapShot("arena", enemy)
    KidneyShot("arena", enemy)

    -- Burst damage
    if shouldBurst() then
        Vendetta("arena")
        ColdBlood("arena")
        ShadowClone("arena")
    end

    -- Execute priority
    if enemy.hp <= 35 then
        Dispatch("arena", enemy)
    end

    -- Core rotation
    if canUseFromStealth() then
        Garrote("arena", enemy)
        Ambush("arena", enemy)
    end

    if shouldUseEnvenom() then
        Envenom("arena", enemy)
    end

    if shouldUseRupture() and shouldUseFinisher() then
        Rupture("arena", enemy)
    end

    if gameState.comboPoints < gameState.maxComboPoints then
        Mutilate("arena", enemy)
        Backstab("arena", enemy)
    end
end

local function enhancedPartyRotation(friendly)
    if not friendly.exists then return end

    -- Support abilities for party members
    -- Rogues have limited party support in MoP
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        Kick()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Assassination Rogue
Kick:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Blind:Callback("arena", function(spell, enemy)
    if enemy.distance > 10 then return end
    if enemy:HasDeBuff(debuffs.blind) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets

    -- Use on healers or high priority targets
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Blind - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

CheapShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if not canUseFromStealth() then return end
    if gameState.energy < 40 then return end

    return spell:Cast(enemy)
end)

KidneyShot:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if not shouldUseFinisher() then return end
    if gameState.energy < 25 then return end

    -- Priority on casters
    if enemy.isCaster then
        Aware:displayMessage("Kidney Shot - Caster", "Yellow", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

Mutilate:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if gameState.energy < 60 then return end
    if gameState.comboPoints >= gameState.maxComboPoints then return end

    return spell:Cast(enemy)
end)

Backstab:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if not gameState.behindTarget then return end
    if gameState.energy < 60 then return end
    if gameState.comboPoints >= gameState.maxComboPoints then return end

    return spell:Cast(enemy)
end)

Envenom:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if not shouldUseEnvenom() then return end
    if gameState.energy < 35 then return end

    -- Priority on low health targets
    if enemy.hp < 40 then
        Aware:displayMessage("Priority Envenom", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)

Rupture:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if not shouldUseRupture() then return end
    if not shouldUseFinisher() then return end
    if gameState.energy < 25 then return end

    return spell:Cast(enemy)
end)

Garrote:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if not canUseFromStealth() then return end
    if enemy:HasDeBuff(debuffs.garrote) then return end
    if gameState.energy < 45 then return end

    return spell:Cast(enemy)
end)

Ambush:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if not canUseFromStealth() then return end
    if gameState.energy < 60 then return end

    return spell:Cast(enemy)
end)

Dispatch:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy.hp > 35 then return end
    if gameState.energy < 30 then return end

    -- Execute range priority
    Aware:displayMessage("Dispatch - Execute", "Purple", 1)
    return spell:Cast(enemy)
end)

Vendetta:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Vendetta - Burst", "Red", 1)
    return spell:Cast(target)
end)

ColdBlood:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Cold Blood - Burst", "Blue", 1)
    return spell:Cast(player)
end)

ShadowClone:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Burst cooldown
    Aware:displayMessage("Shadow Clone - Burst", "Orange", 1)
    return spell:Cast(player)
end)


