local Action = _G.Action

local A                = Action

local CONST                                                              = Action.Const

local ACTION_CONST_SHAMAN_ELEMENTAL                                  = CONST.SHAMAN_ELEMENTAL
local ACTION_CONST_SHAMAN_ENHANCEMENT                               = CONST.SHAMAN_ENHANCEMENT
local ACTION_CONST_SHAMAN_RESTORATION                               = CONST.SHAMAN_RESTORATION

LPH_ENCNUM = function(val) return val end

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "Makulu MoP v1.0.0 (7/29/2025)",
    -- Class settings
    [2] = {
        {
            {
                E = "Header",
                L = {
                    ANY = " ====== Makulu - MoP Shaman ====== ",
                },
            },
        },
        {
            { -- AOE
                E = "Checkbox", 
                DB = "AoE",
                DBV = true,
                L = { 
                    enUS = "Use AoE", 
                    ruRU = "Использовать AoE", 
                    frFR = "Utiliser l'AoE",
                }, 
                TT = { 
                    enUS = "Enable multiunits actions", 
                    ruRU = "Включает действия для нескольких целей", 
                    frFR = "Activer les actions multi-unités",
                }, 
                M = {},
            },
            { -- Auto Totem
                E = "Checkbox", 
                DB = "autoTotem",
                DBV = true,
                L = { 
                    ANY = "Auto Totem Management", 
                }, 
                TT = { 
                    ANY = "Automatically maintain appropriate totems based on situation.", 
                }, 
                M = {},
            },
            { -- Mana Management
                E = "Checkbox", 
                DB = "manaManagement",
                DBV = true,
                L = { 
                    ANY = "Smart Mana Management", 
                }, 
                TT = { 
                    ANY = "Optimize mana usage and prevent mana starvation."
                }, 
                M = {},
            },   
        },
        { -- Spacer
            
            {
                E = "LayoutSpace",
            },
        },
        { -- Potions
            { -- useDamagePotion
                E = "Checkbox", 
                DB = "damagePotion",
                DBV = true,
                L = { 
                    ANY = "Damage Potion"
                }, 
                TT = { 
                    ANY = "Use Damage Potion", 
                }, 
                M = {},
            },
            { -- potionBossOnly
                E = "Checkbox", 
                DB = "potionLustOnly",
                DBV = true,
                L = { 
                    ANY = "Damage Potion Bloodlust/TimeWarp Only", 
                }, 
                TT = { 
                    ANY = "Only use Damage Potion when any kind of Bloodlust/Warp active."
                }, 
                M = {},
            },
        },
        {
            { -- potionExhausted
                E = "Checkbox", 
                DB = "potionExhausted",
                DBV = true,
                L = { 
                    ANY = "Damage Potion With Exhaustion", 
                }, 
                TT = { 
                    ANY = "Use Damage Potion while Exhausted (can't use Bloodlust)."
                }, 
                M = {},
            },
            { -- potionExhaustedSlider
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 5,   
                Precision = 1,                         
                DB = "potionExhaustedSlider",
                DBV = 4,
                ONOFF = false,
                L = { 
                    ANY = "Exhaustion Time Remaining",
                },
                TT = { 
                    ANY = "Time in minutes left on the Exhaustion Debuff to consider using Damage Potion.", 
                },                     
                M = {},
            },
        },
        { -- LAYOUT SPACE   
            {
                E = "LayoutSpace",                                                                         
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Cooldowns",
                },
            },
        },
        {
            {
                E = "Dropdown",                                                         
                H = 20,
                OT = {
                    { text = "Elemental Mastery", value = 1 }, 
                    { text = "Fire Elemental Totem", value = 2 },     
                    { text = "Earth Elemental Totem", value = 3 },
                    { text = "Feral Spirit", value = 4 },
                    { text = "Shamanistic Rage", value = 5 },
                    { text = "Bloodlust/Heroism", value = 6 },   
                },
                MULT = true,
                DB = "cooldownSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                    [5] = true,
                    [6] = true,
                },  
                L = { 
                    ANY = "Cooldown Abilities", 
                }, 
                TT = { 
                    ANY = "Select what abilities you want the rotation to obey the burst toggle.\nIf a spell is unchecked, it will be used even when burst is turned off!", 
                }, 
                M = {},                                    
            },  
        }, 
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { 
            {-- Burst Sensitivity
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 40,                            
                DB = "burstSens",
                DBV = 18,
                ONOFF = false,
                L = { 
                    ANY = "Burst Mode Time To Die",
                },
                TT = { 
                    ANY = "Measures the TTD of enemies to determine when to use cooldowns. A lower number means cooldowns used closer to death.\nIgnored on bosses.", 
                },                     
                M = {},
            },  
            {-- Mana Threshold
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "manaThreshold",
                DBV = 25,
                ONOFF = false,
                L = { 
                    ANY = "Mana Conservation Threshold (%)",
                },
                TT = { 
                    ANY = "Mana percentage to start conserving mana and use more efficient spells.", 
                },                     
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- SHAMAN HEADER
            {
                E = "Header",
                L = {
                    ANY = "INTERRUPTS",
                },
            },
        },
        {    
            { -- Automatic Interrupt
                E = "Checkbox", 
                DB = "AutoInterrupt",
                DBV = true,
                L = { 
                    ANY = "Switch Targets Interrupt",
                }, 
                TT = { 
                    ANY = "Automatically switches targets to interrupt.",
                }, 
                M = {},
            },     
        },
        { -- Spacer
            {
                E = "LayoutSpace",
            },
        },
        { -- SHAMAN HEADER
            {
                E = "Header",
                L = {
                    ANY = "DEFENSIVES",
                },
            },
        },
        {
            { -- Shamanistic Rage HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "ShamanisticRageHP",
                DBV = 60,
                ONOFF = false,
                L = { 
                    ANY = "Shamanistic Rage HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Shamanistic Rage on yourself.", 
                },                     
                M = {},
            },    
            { -- Healing Wave HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "HealingWaveHP",
                DBV = 70,
                ONOFF = false,
                L = { 
                    ANY = "Healing Wave HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Healing Wave on yourself.", 
                },                     
                M = {},
            },    
        },
        {
            {-- Healing Stream Totem HP
                E = "Slider",                                                     
                MIN = 0, 
                MAX = 100,                            
                DB = "healingStreamHP",
                DBV = 80,
                ONOFF = false,
                L = { 
                    ANY = "Healing Stream Totem HP (%)",
                },
                TT = { 
                    ANY = "HP (%) to use Healing Stream Totem for healing over time.", 
                },                     
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Shamanistic Rage", value = 1 },
                    { text = "Healing Wave", value = 2 },
                    { text = "Healing Stream Totem", value = 3 },
                    { text = "Ghost Wolf", value = 4 },
                },
                MULT = true,
                DB = "defensiveSelect",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Defensive Reactions",
                },
                TT = {
                    ANY = "Select what spells to be used when reacting to incoming damage in dungeons.",
                },
                M = {},
            },
        },
        { -- Spacer

            {
                E = "LayoutSpace",
            },
        },
        { -- General -- Header
            {
                E = "Header",
                L = {
                    ANY = "Debug/Aware Options",
                },
            },
        },
        {
            { -- Debug
                E = "Checkbox",
                DB = "makDebug",
                DBV = false,
                L = {
                    ANY = "Enable debug options",
                },
                TT = {
                    ANY = "Show a box with various debug data.\nIt takes a couple of seconds to get rid of the box when you disable this.",
                },
                M = {},
            },
        },
        {
            {
                E = "Dropdown",
                H = 20,
                OT = {
                    { text = "Mana Reminder", value = 1 },
                    { text = "Elemental Mastery Ready", value = 2 },
                    { text = "Fire Elemental Ready", value = 3 },
                    { text = "Totem Alert", value = 4 },
                },
                MULT = true,
                DB = "makAware",
                DBV = {
                    [1] = true,
                    [2] = true,
                    [3] = true,
                    [4] = true,
                },
                L = {
                    ANY = "Aware Text Alert Reminders",
                },
                TT = {
                    ANY = "Select what text alert reminders you would like.\nThese will appear in the center of your screen.",
                },
                M = {},
            },
        },
    },
}
