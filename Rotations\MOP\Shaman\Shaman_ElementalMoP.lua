-- APL UPDATE MoP Elemental Shaman
-- Mists of Pandaria Elemental Shaman Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Elemental spec (talent tree 1 for <PERSON>haman in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 1 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Elemental Shaman Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Elemental Core Abilities
    LightningBolt = { ID = 403, MAKULU_INFO = { damageType = "nature", castTime = 2000 } },
    ChainLightning = { ID = 421, MAKULU_INFO = { damageType = "nature", castTime = 2000 } },
    LavaBurst = { ID = 51505, MAKULU_INFO = { damageType = "fire", castTime = 2000 } },
    FlameShock = { ID = 8050, MAKULU_INFO = { damageType = "fire" } },
    EarthShock = { ID = 8042, MAKULU_INFO = { damageType = "nature" } },
    FrostShock = { ID = 8056, MAKULU_INFO = { damageType = "frost" } },
    
    -- MoP Elemental Specific
    ElementalMastery = { ID = 16166, MAKULU_INFO = { targeted = false } },
    LightningShield = { ID = 324, MAKULU_INFO = { targeted = false } },
    Thunderstorm = { ID = 51490, MAKULU_INFO = { damageType = "nature" } },
    Earthquake = { ID = 61882, MAKULU_INFO = { damageType = "nature", castTime = 2000 } },
    
    -- MoP Totems
    SearingTotem = { ID = 3599, MAKULU_INFO = { targeted = false } },
    MagmaTotem = { ID = 8190, MAKULU_INFO = { targeted = false } },
    FireElementalTotem = { ID = 2894, MAKULU_INFO = { targeted = false } },
    EarthElementalTotem = { ID = 2062, MAKULU_INFO = { targeted = false } },
    StoneclawTotem = { ID = 5730, MAKULU_INFO = { targeted = false } },
    EarthbindTotem = { ID = 2484, MAKULU_INFO = { targeted = false } },
    TremorTotem = { ID = 8143, MAKULU_INFO = { targeted = false } },
    GroundingTotem = { ID = 8177, MAKULU_INFO = { targeted = false } },
    WindfuryTotem = { ID = 8512, MAKULU_INFO = { targeted = false } },
    WrathOfAirTotem = { ID = 3738, MAKULU_INFO = { targeted = false } },
    ManaSpringTotem = { ID = 5675, MAKULU_INFO = { targeted = false } },
    HealingStreamTotem = { ID = 5394, MAKULU_INFO = { targeted = false } },
    TotemicRecall = { ID = 36936, MAKULU_INFO = { targeted = false } },
    
    -- MoP Weapon Imbues
    FlametongueWeapon = { ID = 8024, MAKULU_INFO = { targeted = false } },
    FrostbrandWeapon = { ID = 8033, MAKULU_INFO = { targeted = false } },
    EarthlivingWeapon = { ID = 51730, MAKULU_INFO = { targeted = false } },
    
    -- MoP Utility
    WindShear = { ID = 57994, MAKULU_INFO = { damageType = "nature", ignoreCasting = true } },
    Hex = { ID = 51514, MAKULU_INFO = { castTime = 1500 } },
    Bind = { ID = 76780, MAKULU_INFO = { targeted = true } },
    Purge = { ID = 370, MAKULU_INFO = { targeted = true } },
    CleanseSpirit = { ID = 51886, MAKULU_INFO = { targeted = true } },
    
    -- MoP Healing (limited for Elemental)
    HealingSurge = { ID = 8004, MAKULU_INFO = { heal = true, castTime = 1500 } },
    GreaterHealingWave = { ID = 77472, MAKULU_INFO = { heal = true, castTime = 2500 } },
    HealingWave = { ID = 331, MAKULU_INFO = { heal = true, castTime = 1500 } },
    
    -- MoP Movement and Utility
    GhostWolf = { ID = 2645, MAKULU_INFO = { targeted = false } },
    AstralRecall = { ID = 556, MAKULU_INFO = { castTime = 10000 } },
    FarSight = { ID = 6196, MAKULU_INFO = { castTime = 2000 } },
    WaterWalking = { ID = 546, MAKULU_INFO = { targeted = true } },
    
    -- MoP Talents
    ElementalBlast = { ID = 117014, MAKULU_INFO = { damageType = "elemental", castTime = 2000 } },
    UnleashedFury = { ID = 117012, MAKULU_INFO = { targeted = false } },
    PrimalElementalist = { ID = 117013, MAKULU_INFO = { targeted = false } },
    
    -- MoP Cooldowns
    Heroism = { ID = 32182, MAKULU_INFO = { targeted = false } },
    Bloodlust = { ID = 2825, MAKULU_INFO = { targeted = false } },
    
    -- MoP Defensive Abilities
    ShamanisticRage = { ID = 30823, MAKULU_INFO = { targeted = false } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 57994, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 51514, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Elemental Shaman Buffs
local buffs = {
    lightningShield = 324,
    elementalMastery = 16166,
    flametongueWeapon = 8024,
    frostbrandWeapon = 8033,
    earthlivingWeapon = 51730,
    ghostWolf = 2645,
    shamanisticRage = 30823,
    heroism = 32182,
    bloodlust = 2825,
    unleashedFury = 117012,
    primalElementalist = 117013,
    lavaSurge = 77762,
    clearcasting = 16246,
    shadowmeld = 58984,
    waterWalking = 546,
}

-- MoP Elemental Shaman Debuffs
local debuffs = {
    flameShock = 8050,
    hex = 51514,
    bind = 76780,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    lightningShieldCharges = 0,
    lavaSurgeProc = false,
    clearcasting = false,
    totemActive = false,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.lightningShieldCharges = player:GetBuffStacks(buffs.lightningShield) or 0
    gameState.lavaSurgeProc = player:HasBuff(buffs.lavaSurge)
    gameState.clearcasting = player:HasBuff(buffs.clearcasting)
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function needsLightningShield()
    return not player:HasBuff(buffs.lightningShield) or gameState.lightningShieldCharges < 3
end

local function shouldUseEarthShock()
    return gameState.lightningShieldCharges >= 7
end

local function needsFlameShock()
    return not target:HasDeBuff(debuffs.flameShock) or target:DeBuffRemains(debuffs.flameShock) < 6000
end

local function shouldUseLavaBurst()
    return gameState.lavaSurgeProc or not player.moving
end

local function getHealingTarget()
    -- Priority: player > party members by health
    if player.hp < 50 then
        return player
    end
    
    local lowestUnit = nil
    local lowestHealth = 100
    
    for i = 1, 4 do
        local unit = MakUnit:new("party" .. i)
        if unit.exists and unit.hp < lowestHealth and unit.hp < 80 then
            lowestUnit = unit
            lowestHealth = unit.hp
        end
    end
    
    return lowestUnit
end

-- Buff management
LightningShield:Callback(function(spell)
    if needsLightningShield() then
        return spell:Cast(player)
    end
end)

FlametongueWeapon:Callback(function(spell)
    if not player:HasBuff(buffs.flametongueWeapon) then
        return spell:Cast(player)
    end
end)

FrostbrandWeapon:Callback(function(spell)
    if not player:HasBuff(buffs.frostbrandWeapon) and gameState.isPvP then
        return spell:Cast(player)
    end
end)

-- Core damage abilities
LightningBolt:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if player.moving then return end

    return spell:Cast(target)
end)

ChainLightning:Callback(function(spell)
    if gameState.activeEnemies < 2 then return end
    if target.distance > 30 then return end
    if player.moving then return end

    return spell:Cast(target)
end)

LavaBurst:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not shouldUseLavaBurst() then return end

    return spell:Cast(target)
end)

FlameShock:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 25 then return end
    if target:HasDeBuff(debuffs.flameShock) then return end

    return spell:Cast(target)
end)

EarthShock:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 25 then return end
    if not shouldUseEarthShock() then return end

    return spell:Cast(target)
end)

FrostShock:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 25 then return end
    if not gameState.isPvP then return end

    return spell:Cast(target)
end)

Earthquake:Callback(function(spell)
    if gameState.activeEnemies < 4 then return end
    if player.moving then return end

    return spell:Cast(target)
end)

Thunderstorm:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if target.distance > 10 then return end

    return spell:Cast(player)
end)

-- Cooldowns
ElementalMastery:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

FireElementalTotem:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

EarthElementalTotem:Callback(function(spell)
    if not shouldBurst() then return end
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

ShamanisticRage:Callback(function(spell)
    if gameState.mana > 50 then return end
    if player.hp > 70 then return end

    return spell:Cast(player)
end)

Heroism:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Bloodlust:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

-- Utility abilities
WindShear:Callback(function(spell)
    if not target.exists then return end
    if not target:IsCasting() then return end
    if not target:IsInterruptible() then return end
    if target.distance > 25 then return end

    return spell:Cast(target)
end)

Hex:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if not gameState.isPvP then return end
    if player.moving then return end

    return spell:Cast(target)
end)

Bind:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if not gameState.isPvP then return end

    return spell:Cast(target)
end)

Purge:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if not target:HasDispellableBuff() then return end

    return spell:Cast(target)
end)

CleanseSpirit:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if not healTarget:HasDispellableDebuff() then return end

    return spell:Cast(healTarget)
end)

-- Totem abilities
SearingTotem:Callback(function(spell)
    if gameState.totemActive then return end

    return spell:Cast(player)
end)

MagmaTotem:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if gameState.totemActive then return end

    return spell:Cast(player)
end)

GroundingTotem:Callback(function(spell)
    if not gameState.isPvP then return end
    if gameState.totemActive then return end

    return spell:Cast(player)
end)

TremorTotem:Callback(function(spell)
    if not gameState.isPvP then return end
    if gameState.totemActive then return end

    return spell:Cast(player)
end)

EarthbindTotem:Callback(function(spell)
    if not gameState.isPvP then return end
    if gameState.totemActive then return end

    return spell:Cast(player)
end)

WindfuryTotem:Callback(function(spell)
    if gameState.totemActive then return end

    return spell:Cast(player)
end)

ManaSpringTotem:Callback(function(spell)
    if gameState.mana > 70 then return end
    if gameState.totemActive then return end

    return spell:Cast(player)
end)

HealingStreamTotem:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 80 then return end
    if gameState.totemActive then return end

    return spell:Cast(player)
end)

TotemicRecall:Callback(function(spell)
    if not gameState.totemActive then return end

    return spell:Cast(player)
end)

-- Limited healing for Elemental
HealingSurge:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 40 then return end
    if gameState.mana < 25 then return end

    return spell:Cast(healTarget)
end)

HealingWave:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 60 then return end
    if player.moving then return end
    if gameState.mana < 20 then return end

    return spell:Cast(healTarget)
end)

GreaterHealingWave:Callback(function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 30 then return end
    if player.moving then return end
    if gameState.mana < 35 then return end

    return spell:Cast(healTarget)
end)

-- Movement
GhostWolf:Callback(function(spell)
    if not player.moving then return end
    if gameState.inCombat then return end
    if player:HasBuff(buffs.ghostWolf) then return end

    return spell:Cast(player)
end)

-- Talent abilities
ElementalBlast:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if player.moving then return end

    return spell:Cast(target)
end)

UnleashedFury:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.mana > 80 then return end

    return spell:Cast(player)
end)

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain buffs
    if LightningShield() then return true end
    if FlametongueWeapon() then return true end

    -- Maintain Flame Shock
    if needsFlameShock() then
        if FlameShock() then return true end
    end

    -- Use Lava Burst on cooldown or with Lava Surge proc
    if shouldUseLavaBurst() then
        if LavaBurst() then return true end
    end

    -- Earth Shock at high Lightning Shield charges
    if shouldUseEarthShock() then
        if EarthShock() then return true end
    end

    -- Elemental Blast if talented
    if ElementalBlast() then return true end

    -- Lightning Bolt as filler
    if LightningBolt() then return true end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain buffs
    if LightningShield() then return true end
    if FlametongueWeapon() then return true end

    -- Place AoE totem
    if MagmaTotem() then return true end

    -- Maintain Flame Shock on primary target
    if needsFlameShock() then
        if FlameShock() then return true end
    end

    -- Earthquake for heavy AoE
    if Earthquake() then return true end

    -- Chain Lightning for AoE
    if ChainLightning() then return true end

    -- Thunderstorm for close AoE
    if Thunderstorm() then return true end

    -- Earth Shock at high Lightning Shield charges
    if shouldUseEarthShock() then
        if EarthShock() then return true end
    end

    -- Lava Burst with procs
    if gameState.lavaSurgeProc then
        if LavaBurst() then return true end
    end

    -- Chain Lightning as filler
    if ChainLightning() then return true end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 30 then
        if ShamanisticRage() then return true end
        if EarthElementalTotem() then return true end
    end

    -- Interrupt priority
    if WindShear() then return true end

    -- Maintain buffs
    if LightningShield() then return true end
    if FrostbrandWeapon() then return true end

    -- Utility totems
    if GroundingTotem() then return true end
    if TremorTotem() then return true end
    if EarthbindTotem() then return true end

    if target.exists and target.alive then
        -- CC abilities
        if Hex() then return true end
        if Bind() then return true end

        -- Purge enemy buffs
        if Purge() then return true end

        -- Maintain Flame Shock
        if needsFlameShock() then
            if FlameShock() then return true end
        end

        -- Frost Shock for slowing
        if FrostShock() then return true end

        -- Use Lava Burst
        if shouldUseLavaBurst() then
            if LavaBurst() then return true end
        end

        -- Earth Shock at high charges
        if shouldUseEarthShock() then
            if EarthShock() then return true end
        end

        -- Thunderstorm for knockback
        if Thunderstorm() then return true end

        -- Lightning Bolt as filler
        if LightningBolt() then return true end
    end

    return false
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Prepare mana
        if gameState.mana < 80 then
            if ManaSpringTotem() then return true end
            if ShamanisticRage() then return true end
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                if ElementalMastery() then return true end
                if FireElementalTotem() then return true end
                if Heroism() then return true end
                if Bloodlust() then return true end
            end
        end

        -- Maintain current target
        if needsFlameShock() then
            if FlameShock() then return true end
        end

        -- Build Lightning Shield charges
        if gameState.lightningShieldCharges < 7 then
            if LightningBolt() then return true end
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency healing
    local healTarget = getHealingTarget()
    if healTarget and healTarget.hp < 30 then
        if HealingStreamTotem() then return true end
        if GreaterHealingWave() then return true end
        if HealingSurge() then return true end
    end

    -- Mana management
    if gameState.mana < 20 then
        if ManaSpringTotem() then return true end
        if ShamanisticRage() then return true end
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Mana: ", gameState.mana)
        MakPrint(3, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(4, "In Combat: ", gameState.inCombat)
        MakPrint(5, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(6, "Is PvP: ", gameState.isPvP)
        MakPrint(7, "Lightning Shield Charges: ", gameState.lightningShieldCharges)
        MakPrint(8, "Lava Surge Proc: ", gameState.lavaSurgeProc)
        MakPrint(9, "Clearcasting: ", gameState.clearcasting)
        MakPrint(10, "Flame Shock on Target: ", target:HasDeBuff(debuffs.flameShock))
        MakPrint(11, "Lightning Shield Active: ", player:HasBuff(buffs.lightningShield))
        MakPrint(12, "Totem Active: ", gameState.totemActive)
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if ElementalMastery:IsReady() and shouldBurst() then
            Aware:displayMessage("ELEMENTAL MASTERY READY", "Red", 1)
        end
        if FireElementalTotem:IsReady() and shouldBurst() then
            Aware:displayMessage("FIRE ELEMENTAL READY", "Blue", 1)
        end
        if needsLightningShield() then
            Aware:displayMessage("LIGHTNING SHIELD NEEDED", "Yellow", 1)
        end
        if needsFlameShock() then
            Aware:displayMessage("FLAME SHOCK NEEDED", "Orange", 1)
        end
        if shouldUseEarthShock() then
            Aware:displayMessage("USE EARTH SHOCK", "Green", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
        if not player:HasBuff(buffs.flametongueWeapon) then
            Aware:displayMessage("NO WEAPON IMBUE", "White", 1)
        end
        if gameState.lavaSurgeProc then
            Aware:displayMessage("LAVA SURGE PROC", "Red", 1)
        end
        if gameState.mana < 30 then
            Aware:displayMessage("LOW MANA", "Red", 1)
        end
        if gameState.lightningShieldCharges >= 7 then
            Aware:displayMessage("HIGH LS CHARGES: " .. gameState.lightningShieldCharges, "Blue", 1)
        end
        local healTarget = getHealingTarget()
        if healTarget and healTarget.hp < 40 then
            Aware:displayMessage("HEALING NEEDED", "Red", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 30 then
        if ShamanisticRage:IsReady() then return FrameworkEnd() end
        if EarthElementalTotem:IsReady() then return FrameworkEnd() end
    end

    -- Emergency healing priority
    local healTarget = getHealingTarget()
    if healTarget and healTarget.hp < 20 then
        if HealingStreamTotem() then return FrameworkEnd() end
        if GreaterHealingWave() then return FrameworkEnd() end
        if HealingSurge() then return FrameworkEnd() end
    end

    -- Mana management
    if gameState.mana < 20 then
        if ManaSpringTotem() then return FrameworkEnd() end
        if ShamanisticRage() then return FrameworkEnd() end
        if ArcaneTorrent() then return FrameworkEnd() end
    end

    if target.exists and target.alive then
        -- Enhanced interrupt priority
        if target:IsCasting() and target:IsInterruptible() then
            if WindShear() then return FrameworkEnd() end
        end

        -- PvP specific abilities
        if gameState.isPvP then
            if Hex() then return FrameworkEnd() end
            if Bind() then return FrameworkEnd() end
            if Purge() then return FrameworkEnd() end
            if Thunderstorm() then return FrameworkEnd() end
        end

        -- Burst phase
        if shouldBurst() then
            if ElementalMastery() then return FrameworkEnd() end
            if FireElementalTotem() then return FrameworkEnd() end
            if Heroism() then return FrameworkEnd() end
            if Bloodlust() then return FrameworkEnd() end
            if UnleashedFury() then return FrameworkEnd() end

            -- Racial abilities during burst
            if BloodFury() then return FrameworkEnd() end
            if Berserking() then return FrameworkEnd() end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    WindShear("arena", enemy)

    -- CC abilities
    Hex("arena", enemy)
    Bind("arena", enemy)

    -- Offensive dispel
    Purge("arena", enemy)

    -- Burst damage
    if shouldBurst() then
        ElementalMastery("arena")
        FireElementalTotem("arena")
        UnleashedFury("arena")
    end

    -- Core rotation
    if not enemy:HasDeBuff(debuffs.flameShock) then
        FlameShock("arena", enemy)
    end

    if shouldUseLavaBurst() then
        LavaBurst("arena", enemy)
    end

    if shouldUseEarthShock() then
        EarthShock("arena", enemy)
    end

    FrostShock("arena", enemy)
    ElementalBlast("arena", enemy)
    LightningBolt("arena", enemy)
end

local function enhancedPartyRotation(friendly)
    if not friendly.exists then return end

    -- Healing priority for party members
    if friendly.hp < 30 then
        GreaterHealingWave("arena", friendly)
        HealingSurge("arena", friendly)
    elseif friendly.hp < 60 then
        HealingWave("arena", friendly)
    elseif friendly.hp < 80 then
        HealingStreamTotem("arena")
    end

    -- Dispel party members
    CleanseSpirit("arena", friendly)
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        WindShear()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Elemental Shaman
WindShear:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 25 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Hex:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:HasDeBuff(debuffs.hex) then return end
    if player.moving then return end

    -- Use on healers or high priority targets
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Hex - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

Bind:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:HasDeBuff(debuffs.bind) then return end

    return spell:Cast(enemy)
end)

Purge:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if not enemy:HasDispellableBuff() then return end

    -- Offensive dispel in arena
    Aware:displayMessage("Purge - Dispel", "Orange", 1)
    return spell:Cast(enemy)
end)

FlameShock:Callback("arena", function(spell, enemy)
    if enemy.distance > 25 then return end
    if enemy:HasDeBuff(debuffs.flameShock) then return end

    return spell:Cast(enemy)
end)

LavaBurst:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if not shouldUseLavaBurst() then return end

    -- Priority Lava Burst
    if gameState.lavaSurgeProc then
        Aware:displayMessage("Lava Burst - Proc", "Red", 1)
    end
    return spell:Cast(enemy)
end)

EarthShock:Callback("arena", function(spell, enemy)
    if enemy.distance > 25 then return end
    if not shouldUseEarthShock() then return end

    -- High damage shock
    Aware:displayMessage("Earth Shock - Burst", "Yellow", 1)
    return spell:Cast(enemy)
end)

FrostShock:Callback("arena", function(spell, enemy)
    if enemy.distance > 25 then return end

    -- Slowing effect in arena
    Aware:displayMessage("Frost Shock - Slow", "Blue", 1)
    return spell:Cast(enemy)
end)

LightningBolt:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if player.moving then return end

    return spell:Cast(enemy)
end)

ElementalBlast:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if player.moving then return end

    return spell:Cast(enemy)
end)

Thunderstorm:Callback("arena", function(spell, enemy)
    if enemy.distance > 10 then return end

    -- Knockback in arena
    Aware:displayMessage("Thunderstorm - Knockback", "Purple", 1)
    return spell:Cast(player)
end)

ElementalMastery:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Elemental Mastery - Burst", "Red", 1)
    return spell:Cast(player)
end)

FireElementalTotem:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Fire Elemental - Burst", "Blue", 1)
    return spell:Cast(player)
end)

UnleashedFury:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Burst cooldown
    Aware:displayMessage("Unleashed Fury - Burst", "Orange", 1)
    return spell:Cast(player)
end)

-- Arena healing callbacks
HealingSurge:Callback("arena", function(spell, friendly)
    if friendly.hp > 40 then return end
    if gameState.mana < 25 then return end

    -- Emergency healing in arena
    Aware:displayMessage("Emergency Healing Surge", "Red", 1)
    return spell:Cast(friendly)
end)

HealingWave:Callback("arena", function(spell, friendly)
    if friendly.hp > 60 then return end
    if player.moving then return end
    if gameState.mana < 20 then return end

    return spell:Cast(friendly)
end)

GreaterHealingWave:Callback("arena", function(spell, friendly)
    if friendly.hp > 30 then return end
    if player.moving then return end
    if gameState.mana < 35 then return end

    -- Big heal in arena
    Aware:displayMessage("Greater Healing Wave - Emergency", "Red", 1)
    return spell:Cast(friendly)
end)

CleanseSpirit:Callback("arena", function(spell, friendly)
    if not friendly:HasDispellableDebuff() then return end

    -- Dispel priority in arena
    Aware:displayMessage("Cleanse Spirit - Dispel", "Green", 1)
    return spell:Cast(friendly)
end)

HealingStreamTotem:Callback("arena", function(spell)
    local healTarget = getHealingTarget()
    if not healTarget then return end
    if healTarget.hp > 80 then return end
    if gameState.totemActive then return end

    -- Healing totem in arena
    Aware:displayMessage("Healing Stream Totem", "Blue", 1)
    return spell:Cast(player)
end)


