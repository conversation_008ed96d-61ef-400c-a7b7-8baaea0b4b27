-- APL UPDATE MoP Restoration Shaman
-- Mists of Pandaria Restoration Shaman Rotation

-- Check if Ma<PERSON>luValidCheck exists before calling it
if MakuluValidCheck and not Ma<PERSON>luValidCheck() then return true end
if Ma<PERSON><PERSON>_magic_number and Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Restoration spec (talent tree 3 for Shaman in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 3 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Restoration Shaman Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Restoration Core Healing Abilities
    HealingWave = { ID = 331, MAKULU_INFO = { heal = true, castTime = 2500 } },
    GreaterHealingWave = { ID = 77472, MAKULU_INFO = { heal = true, castTime = 2500 } },
    HealingSurge = { ID = 8004, MAKULU_INFO = { heal = true, castTime = 1500 } },
    ChainHeal = { ID = 1064, MAKULU_INFO = { heal = true, castTime = 2500 } },
    Riptide = { ID = 61295, MAKULU_INFO = { heal = true, ignoreCasting = true } },
    EarthShield = { ID = 974, MAKULU_INFO = { heal = true, ignoreCasting = true } },
    HealingRain = { ID = 73920, MAKULU_INFO = { heal = true, castTime = 2000 } },
    
    -- MoP Restoration Totems
    HealingStreamTotem = { ID = 5394, MAKULU_INFO = { heal = true, targeted = false } },
    ManaTideTotem = { ID = 16190, MAKULU_INFO = { targeted = false } },
    SpiritLinkTotem = { ID = 98008, MAKULU_INFO = { heal = true, targeted = false } },
    HealingTideTotem = { ID = 108280, MAKULU_INFO = { heal = true, targeted = false } },
    AncestralGuidanceTotem = { ID = 108281, MAKULU_INFO = { heal = true, targeted = false } },
    
    -- MoP Restoration Damage Abilities
    LightningBolt = { ID = 403, MAKULU_INFO = { damageType = "nature", castTime = 2000 } },
    ChainLightning = { ID = 421, MAKULU_INFO = { damageType = "nature", castTime = 2000 } },
    LavaLash = { ID = 60103, MAKULU_INFO = { damageType = "fire" } },
    FlameShock = { ID = 8050, MAKULU_INFO = { damageType = "fire", ignoreCasting = true } },
    EarthShock = { ID = 8042, MAKULU_INFO = { damageType = "nature", ignoreCasting = true } },
    
    -- MoP Restoration Cooldowns
    NaturesSwiftness = { ID = 16188, MAKULU_INFO = { targeted = false } },
    AncestralSwiftness = { ID = 16188, MAKULU_INFO = { targeted = false } },
    Bloodlust = { ID = 2825, MAKULU_INFO = { targeted = false } },
    Heroism = { ID = 32182, MAKULU_INFO = { targeted = false } },
    SpiritWalk = { ID = 58875, MAKULU_INFO = { targeted = false } },
    
    -- MoP Restoration Utility
    Purify = { ID = 77130, MAKULU_INFO = { heal = true, targeted = true } },
    WindShear = { ID = 57994, MAKULU_INFO = { targeted = true, ignoreCasting = true } },
    Hex = { ID = 51514, MAKULU_INFO = { castTime = 1700, targeted = true } },
    Thunderstorm = { ID = 51490, MAKULU_INFO = { damageType = "nature", targeted = false } },
    GhostWolf = { ID = 2645, MAKULU_INFO = { targeted = false } },
    
    -- MoP Restoration Talents
    AncestralGuidance = { ID = 108281, MAKULU_INFO = { heal = true, targeted = false } },
    Conductivity = { ID = 108282, MAKULU_INFO = { heal = true, targeted = false } },
    UnleashedFury = { ID = 117012, MAKULU_INFO = { targeted = false } },
    ElementalBlast = { ID = 117014, MAKULU_INFO = { damageType = "elemental", castTime = 2000 } },
    
    -- MoP Defensive
    ShamanisticRage = { ID = 30823, MAKULU_INFO = { targeted = false } },
    AstralShift = { ID = 108271, MAKULU_INFO = { targeted = false } },
    
    -- Potions and consumables
    TemperedPotion1 = { Type = "Potion", ID = 171263, QueueForbidden = true },
    TemperedPotion2 = { Type = "Potion", ID = 171264, QueueForbidden = true },
    TemperedPotion3 = { Type = "Potion", ID = 171265, QueueForbidden = true },
    PotionofUnwaveringFocus1 = { Type = "Potion", ID = 171266, QueueForbidden = true },
    PotionofUnwaveringFocus2 = { Type = "Potion", ID = 171267, QueueForbidden = true },
    PotionofUnwaveringFocus3 = { Type = "Potion", ID = 171268, QueueForbidden = true },

    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 57994, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 51514, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Restoration Shaman Buffs
local buffs = {
    riptide = 61295,
    earthShield = 974,
    naturesSwiftness = 16188,
    ancestralSwiftness = 16188,
    bloodlust = 2825,
    heroism = 32182,
    spiritWalk = 58875,
    shamanisticRage = 30823,
    astralShift = 108271,
    ancestralGuidance = 108281,
    conductivity = 108282,
    unleashedFury = 117012,
    tidalWaves = 53390,
    improvedWaterShield = 16236,
    waterShield = 52127,
    lightningShield = 324,
}

-- MoP Restoration Shaman Debuffs
local debuffs = {
    flameShock = 8050,
    earthShock = 8042,
    hex = 51514,
    windShear = 57994,
    thunderstorm = 51490,
}

-- Game state tracking (enhanced with healer-specific functionality)
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
    shouldBurst = false,
    fightRemains = 999,
    lowestPartyMember = nil,
    lowestPartyHp = 100,
    shouldAoE = false,
    shouldCleave = false,
    riptideCount = 0,
    earthShieldStacks = 0,
    imCasting = nil,
    needsDispel = false,
    tankHealth = 100,
    tidalWavesStacks = 0,
    manaPercent = 100,
}

local function updategs()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.mana = player.mana or 0
    gameState.manaPercent = player.maxMana > 0 and (gameState.mana / player.maxMana * 100) or 100
    gameState.isPvP = Action.IsInPvP or false
    gameState.channeling = player:IsCasting() or player:IsChanneling()
    gameState.shouldBurst = A.GetToggle(2, "BurstMode")

    -- Enhanced tracking
    gameState.fightRemains = target.exists and target.timeToDie or 999
    gameState.shouldAoE = gameState.activeEnemies > 2
    gameState.shouldCleave = gameState.activeEnemies > 1
    gameState.riptideCount = getRiptideCount()
    gameState.earthShieldStacks = player:BuffStacks(buffs.earthShield) or 0
    gameState.tidalWavesStacks = player:BuffStacks(buffs.tidalWaves) or 0
    gameState.imCasting = player:IsCasting() and player:GetCastingSpell() or nil

    -- Party health tracking
    local lowestHp = 100
    local lowestMember = nil
    for i = 1, 4 do
        local member = MakUnit:new("party" .. i)
        if member.exists and member.hp < lowestHp then
            lowestHp = member.hp
            lowestMember = member
        end
    end
    if player.hp < lowestHp then
        lowestHp = player.hp
        lowestMember = player
    end
    gameState.lowestPartyMember = lowestMember
    gameState.lowestPartyHp = lowestHp

    -- Tank health tracking
    local tank = MakUnit:new("party1") -- Assuming party1 is tank
    gameState.tankHealth = tank.exists and tank.hp or 100

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Alias for compatibility
local updateGameState = updategs
local gs = gameState

-- Utility functions (enhanced for healing)
local function shouldBurst()
    return gameState.shouldBurst
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function needsHeal(unit, threshold)
    return unit.exists and unit.hp < threshold
end

local function needsDispel(unit)
    return unit.exists and unit:HasDebuffType("Magic")
end

local function getLowestPartyMember()
    return gameState.lowestPartyMember
end

local function getRiptideCount()
    local count = 0
    for i = 1, 4 do
        local member = MakUnit:new("party" .. i)
        if member.exists and member:HasBuff(buffs.riptide) then
            count = count + 1
        end
    end
    if player:HasBuff(buffs.riptide) then
        count = count + 1
    end
    return count
end

local function isSpellInFlight(spell, range)
    return spell:IsSpellInFlight() or false
end

local function makInterrupt(interrupts)
    if not interrupts then return end
    for _, interrupt in pairs(interrupts) do
        if interrupt and interrupt:IsReady() then
            interrupt()
        end
    end
end

-- PvP utility functions
local function shouldInterrupt(enemy)
    if not enemy or not enemy.exists then return false end
    return enemy:IsCasting() and enemy:IsInterruptible()
end

local function shouldHex(enemy)
    if not enemy or not enemy.exists then return false end
    return not enemy:HasDebuff(debuffs.hex) and enemy.hp > 30
end

-- Core healing callbacks
Riptide:Callback(function(spell)
    local target = getLowestPartyMember()
    if not target or not target.exists then return end
    if target:HasBuff(buffs.riptide) then return end
    if target.hp > 85 then return end

    return spell:Cast(target)
end)

HealingSurge:Callback(function(spell)
    local target = getLowestPartyMember()
    if not target or not target.exists then return end
    if target.hp > 40 then return end
    if gameState.channeling then return end

    return spell:Cast(target)
end)

HealingWave:Callback(function(spell)
    local target = getLowestPartyMember()
    if not target or not target.exists then return end
    if target.hp > 60 then return end
    if gameState.channeling then return end
    if gs.tidalWavesStacks > 0 then
        return spell:Cast(target)
    end
end)

GreaterHealingWave:Callback(function(spell)
    local target = getLowestPartyMember()
    if not target or not target.exists then return end
    if target.hp > 50 then return end
    if gameState.channeling then return end

    return spell:Cast(target)
end)

ChainHeal:Callback(function(spell)
    if gs.lowestPartyHp > 70 then return end
    if gameState.channeling then return end
    local target = getLowestPartyMember()
    if not target or not target.exists then return end

    return spell:Cast(target)
end)

EarthShield:Callback(function(spell)
    local tank = MakUnit:new("party1")
    if not tank.exists then tank = player end
    if tank:HasBuff(buffs.earthShield) and gs.earthShieldStacks > 3 then return end

    return spell:Cast(tank)
end)

HealingRain:Callback(function(spell)
    if gs.lowestPartyHp > 60 then return end
    if not shouldAoE() then return end
    if gameState.channeling then return end

    return spell:Cast(player)
end)

-- Totem abilities
HealingStreamTotem:Callback(function(spell)
    if gs.lowestPartyHp > 80 then return end

    return spell:Cast(player)
end)

SpiritLinkTotem:Callback(function(spell)
    if gs.lowestPartyHp > 40 then return end

    return spell:Cast(player)
end)

HealingTideTotem:Callback(function(spell)
    if gs.lowestPartyHp > 30 then return end

    return spell:Cast(player)
end)

ManaTideTotem:Callback(function(spell)
    if gs.manaPercent > 40 then return end

    return spell:Cast(player)
end)

-- Damage abilities
LightningBolt:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gameState.channeling then return end
    if gs.lowestPartyHp < 80 then return end -- Prioritize healing

    return spell:Cast(target)
end)

FlameShock:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDebuff(debuffs.flameShock) then return end
    if gs.lowestPartyHp < 70 then return end

    return spell:Cast(target)
end)

EarthShock:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gs.lowestPartyHp < 70 then return end

    return spell:Cast(target)
end)

ChainLightning:Callback(function(spell)
    if not shouldAoE() then return end
    if not target.exists or not target.canAttack then return end
    if gameState.channeling then return end
    if gs.lowestPartyHp < 80 then return end

    return spell:Cast(target)
end)

-- Cooldowns
NaturesSwiftness:Callback(function(spell)
    if gs.lowestPartyHp > 30 then return end

    return spell:Cast(player)
end)

Bloodlust:Callback(function(spell)
    if not shouldBurst() then return end
    if not gameState.inCombat then return end

    return spell:Cast(player)
end)

-- Utility abilities
Purify:Callback(function(spell)
    for i = 1, 4 do
        local member = MakUnit:new("party" .. i)
        if member.exists and needsDispel(member) then
            return spell:Cast(member)
        end
    end
    if needsDispel(player) then
        return spell:Cast(player)
    end
end)

WindShear:Callback(function(spell)
    if not target.exists or not target:IsCasting() then return end
    if not target:IsInterruptible() then return end

    return spell:Cast(target)
end)

Hex:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDebuff(debuffs.hex) then return end
    if gameState.channeling then return end

    return spell:Cast(target)
end)

Thunderstorm:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target.distance > 10 then return end
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

-- Defensive abilities
AstralShift:Callback(function(spell)
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

ShamanisticRage:Callback(function(spell)
    if gs.manaPercent > 60 then return end

    return spell:Cast(player)
end)

-- Enhanced PvP callbacks
WindShear:Callback("arena", function(spell, enemy)
    if not enemy.pvpKick then return end

    return spell:Cast(enemy)
end)

Hex:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDebuff(debuffs.hex) then return end
    if enemy.hp < 30 then return end
    if gs.imCasting and gs.imCasting == spell.id then return end

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Hex - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on DPS targeting low health allies
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Hex - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

FlameShock:Callback("arena", function(spell, enemy)
    if enemy.distance > 25 then return end
    if enemy:DebuffRemains(debuffs.flameShock, true) > 5000 then return end

    return spell:Cast(enemy)
end)

EarthShock:Callback("arena", function(spell, enemy)
    if enemy.distance > 25 then return end
    if gs.lowestPartyHp < 60 then return end

    return spell:Cast(enemy)
end)

-- Enhanced rotation functions
local function st()
    -- Single target healing rotation
    updategs()

    -- Emergency healing
    if gs.lowestPartyHp < 30 then
        if player:HasBuff(buffs.naturesSwiftness) then
            GreaterHealingWave()
        else
            HealingSurge()
        end
        return
    end

    -- Maintain Earth Shield
    EarthShield()

    -- Maintain Riptides
    Riptide()

    -- Efficient healing with Tidal Waves
    if gs.tidalWavesStacks > 0 and gs.lowestPartyHp < 70 then
        HealingWave()
    end

    -- Chain Heal for group damage
    if gs.lowestPartyHp < 80 then
        ChainHeal()
    end

    -- Damage when safe
    if gs.lowestPartyHp > 85 then
        FlameShock()
        LightningBolt()
    end
end

local function aoe()
    -- AoE healing rotation
    updategs()

    -- Emergency group healing
    if gs.lowestPartyHp < 40 then
        HealingTideTotem()
        SpiritLinkTotem()
    end

    -- Healing Rain for sustained group healing
    if gs.lowestPartyHp < 70 then
        HealingRain()
    end

    -- Chain Heal for group damage
    ChainHeal()

    -- Maintain Riptides
    Riptide()

    -- Earth Shield maintenance
    EarthShield()
end

local function cleave()
    -- Cleave healing (2-3 enemies)
    updategs()

    -- Focus on tank and lowest member
    EarthShield()
    Riptide()
    HealingWave()

    -- Chain Heal for efficiency
    if gs.lowestPartyHp < 75 then
        ChainHeal()
    end

    -- Damage when safe
    if gs.lowestPartyHp > 80 then
        FlameShock()
        LightningBolt()
    end
end

local function ogcd()
    -- Off-global cooldown abilities
    if shouldBurst() then
        Bloodlust()
        NaturesSwiftness()
    end

    -- Emergency cooldowns
    if gs.lowestPartyHp < 30 then
        HealingTideTotem()
        SpiritLinkTotem()
    end

    -- Mana management
    if gs.manaPercent < 40 then
        ManaTideTotem()
        ShamanisticRage()
    end

    -- Racial abilities
    racials()
end

local function eof()
    -- End of fight logic
    if gs.fightRemains < 30000 then
        -- Focus on damage
        if gs.lowestPartyHp > 70 then
            FlameShock()
            LightningBolt()
            EarthShock()
        end
    end
end

local function pvpenis()
    -- PvP specific logic placeholder
    -- This would contain PvP-specific rotation logic
end

-- Alias for compatibility
local function singleTargetRotation()
    st()
end

-- AoE rotation
local function aoeRotation()
    updateGameState()
    aoe()
end

-- PvP specific rotation
local function pvpRotation()
    updateGameState()

    -- Interrupt priority
    WindShear()

    -- CC for control
    Hex()

    -- Dispel priority
    Purify()

    -- Healing priority in PvP
    if gs.lowestPartyHp < 60 then
        HealingSurge()
        Riptide()
    end

    -- Damage pressure
    FlameShock()
    EarthShock()

    -- Mana management
    if gs.manaPercent < 40 then
        ManaTideTotem()
    end

    -- Filler
    LightningBolt()
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Pre-heal before adds spawn
    if gs.timeToAdds < 8000 and gs.timeToAdds > 0 then
        -- Prepare healing
        Riptide()
        EarthShield()

        -- Prepare cooldowns
        if gs.timeToAdds < 3000 then
            HealingTideTotem()
            NaturesSwiftness()
        end
    end

    -- During adds phase
    if gs.activeEnemies >= 3 then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency healing always takes priority
    if gs.lowestPartyHp < 20 then
        HealingSurge()
        return
    end

    -- Emergency defensives
    if player.hp <= 30 then
        AstralShift()
    end

    -- Dispel priority
    Purify()

    -- Cooldowns during burst or emergency
    if shouldBurst() or gs.lowestPartyHp < 40 then
        NaturesSwiftness()
        Bloodlust()
    end

    -- TimeToAdds logic
    if gs.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gs.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- Enhanced A[3] function with new functionality
A[3] = function(icon)
    -- Safety check for framework initialization
    if not FrameworkStart then return end

    FrameworkStart(icon)
    updategs()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Mana: ", gs.mana)
        MakPrint(2, "Mana Percent: ", gs.manaPercent)
        MakPrint(3, "Lowest Party HP: ", gs.lowestPartyHp)
        MakPrint(4, "Riptide Count: ", gs.riptideCount)
        MakPrint(5, "Earth Shield Stacks: ", gs.earthShieldStacks)
        MakPrint(6, "Tidal Waves Stacks: ", gs.tidalWavesStacks)
        MakPrint(7, "Fight Remains: ", gs.fightRemains)
        MakPrint(8, "Should AoE: ", gs.shouldAoE)
        MakPrint(9, "Time to Adds: ", gs.timeToAdds)
        MakPrint(10, "Wind Shear Learned: ", WindShear:IsKnown())
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then -- Nature's Swiftness ready
        if NaturesSwiftness:IsReady() and gs.lowestPartyHp < 40 and player.inCombat then
            Aware:displayMessage("NATURE'S SWIFTNESS READY", "Purple", 1)
        end
    end

    -- Interrupt handling
    local interrupts = {WindShear}
    makInterrupt(interrupts)

    -- Emergency healing and utility management
    EarthShield()
    Purify()
    AstralShift()

    if target.exists and target.canAttack and (LightningBolt:InRange(target) or FlameShock:InRange(target)) then
        if A.IsInPvP then
            WindShear("bg")
            Hex("bg")
            Thunderstorm("bg")
            pvpenis()
        end

        if player.channeling and gs.imCasting and gs.imCasting == HealingRain.id then return end

        local damagePotion = Action.GetToggle(2, "damagePotion")
        local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
        local potionExhausted = Action.GetToggle(2, "potionExhausted")
        local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")
        local damagePotionObject = Action.DetermineUsableObject("player", nil, nil, true, nil, A.TemperedPotion1, A.TemperedPotion2, A.TemperedPotion3, A.PotionofUnwaveringFocus1, A.PotionofUnwaveringFocus2, A.PotionofUnwaveringFocus3)

        if damagePotionObject and damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
            local shouldPot = gs.riptideCount >= 3 and gs.lowestPartyHp > 80
            if shouldPot then
                return damagePotionObject:Show(icon)
            end
        end

        if gs.shouldAoE then
            aoe()
        end

        ogcd()
        eof()

        if gs.shouldCleave then
            cleave()
        end

        st()

    end

    return FrameworkEnd()
end

-- Enhanced enemy and party rotation functions
local enemyRotation = function(enemy)
    if not enemy.exists then return end
    if A.Zone ~= "arena" then return end
    WindShear("arena", enemy)
    Hex("arena", enemy)
    FlameShock("arena", enemy)
    EarthShock("arena", enemy)
end

local partyRotation = function(friendly)
    if not friendly.exists then return end
    if friendly.hp < 80 then
        Riptide("arena", friendly)
        HealingSurge("arena", friendly)
        EarthShield("arena", friendly)
    end
    if needsDispel(friendly) then
        Purify("arena", friendly)
    end
end

-- Arena functions
A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        WindShear()
    end
    if Action.Zone == "arena" then
        enemyRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and gameState.manaPercent < 50 then
        return spell:Cast(player)
    end
end)

Berserking:Callback(function(spell)
    if shouldBurst() and gs.lowestPartyHp < 60 then
        return spell:Cast(player)
    end
end)

BloodFury:Callback(function(spell)
    if shouldBurst() and gs.lowestPartyHp < 60 then
        return spell:Cast(player)
    end
end)

WillOfTheForsaken:Callback(function(spell)
    if player:HasDebuffType("Fear") or player:HasDebuffType("Charm") then
        return spell:Cast(player)
    end
end)

QuakingPalm:Callback(function(spell)
    if target.exists and target.canAttack and target:IsCasting() then
        return spell:Cast(target)
    end
end)

GiftOfTheNaaru:Callback(function(spell)
    if player.hp < 60 then
        return spell:Cast(player)
    end
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    WillOfTheForsaken()
    QuakingPalm()
    GiftOfTheNaaru()
end

local function mopTalents()
    AncestralGuidance()
    Conductivity()
    UnleashedFury()
    ElementalBlast()
end

local function baseStuff()
    AstralShift()
    ShamanisticRage()
    GhostWolf()
end

-- Enhanced utility for MoP
local function mopUtility()
    WindShear()
    Hex()
    Purify()
    Thunderstorm()
    SpiritWalk()
end
