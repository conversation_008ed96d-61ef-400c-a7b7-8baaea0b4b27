-- APL UPDATE MoP Affliction Warlock
-- Mists of Pandaria Affliction Warlock Rotation

-- Check if MakuluValidCheck exists before calling it
if MakuluValidCheck and not MakuluValidCheck() then return true end
if Maku<PERSON>_magic_number and Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Affliction spec (talent tree 1 for Warlock in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 1 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Affliction Warlock Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Affliction Core DoT Abilities
    Agony = { ID = 980, MAKULU_INFO = { damageType = "shadow", ignoreCasting = true } },
    Corruption = { ID = 172, MAKULU_INFO = { damageType = "shadow", ignoreCasting = true } },
    UnstableAffliction = { ID = 30108, MAKULU_INFO = { damageType = "shadow", castTime = 1500 } },
    SeedOfCorruption = { ID = 27243, MAKULU_INFO = { damageType = "shadow", castTime = 2500 } },
    
    -- MoP Drain/Channel Abilities
    DrainSoul = { ID = 1120, MAKULU_INFO = { damageType = "shadow", channeled = true } },
    DrainLife = { ID = 689, MAKULU_INFO = { damageType = "shadow", channeled = true, heal = true } },
    MaleficGrasp = { ID = 103103, MAKULU_INFO = { damageType = "shadow", channeled = true, castTime = 4000 } },
    
    -- MoP Instant Cast Abilities
    ShadowBolt = { ID = 686, MAKULU_INFO = { damageType = "shadow", castTime = 2500 } },
    Haunt = { ID = 48181, MAKULU_INFO = { damageType = "shadow", castTime = 1500 } },
    SoulBurn = { ID = 74434, MAKULU_INFO = { targeted = false } },
    
    -- MoP Curses
    CurseOfElements = { ID = 1490, MAKULU_INFO = { damageType = "shadow", ignoreCasting = true } },
    CurseOfWeakness = { ID = 702, MAKULU_INFO = { damageType = "shadow", ignoreCasting = true } },
    CurseOfTongues = { ID = 1714, MAKULU_INFO = { damageType = "shadow", ignoreCasting = true } },
    
    -- MoP Cooldowns
    DarkSoul = { ID = 113860, MAKULU_INFO = { targeted = false } },
    SoulSwap = { ID = 86121, MAKULU_INFO = { targeted = false } },
    SoulSwapExhale = { ID = 86213, MAKULU_INFO = { targeted = false } },
    
    -- MoP Defensive Abilities
    UnendingResolve = { ID = 104773, MAKULU_INFO = { targeted = false } },
    DarkRegeneration = { ID = 108359, MAKULU_INFO = { targeted = false, heal = true } },
    DarkPact = { ID = 108416, MAKULU_INFO = { targeted = false } },
    
    -- MoP Utility
    Fear = { ID = 5782, MAKULU_INFO = { castTime = 1500 } },
    Banish = { ID = 710, MAKULU_INFO = { castTime = 1500 } },
    SpellLock = { ID = 19647, MAKULU_INFO = { ignoreCasting = true } },
    
    -- MoP Pet Abilities
    SummonImp = { ID = 688, MAKULU_INFO = { castTime = 6000 } },
    SummonVoidwalker = { ID = 697, MAKULU_INFO = { castTime = 6000 } },
    SummonSuccubus = { ID = 712, MAKULU_INFO = { castTime = 6000 } },
    SummonFelhunter = { ID = 691, MAKULU_INFO = { castTime = 6000 } },
    SummonDoomguard = { ID = 18540, MAKULU_INFO = { castTime = 10000, targeted = false } },
    SummonInfernal = { ID = 1122, MAKULU_INFO = { castTime = 10000, targeted = true } },
    
    -- MoP Talents
    SoulLeech = { ID = 108370, MAKULU_INFO = { targeted = false } },
    HarvestLife = { ID = 108371, MAKULU_INFO = { targeted = false } },
    DarkRegeneration = { ID = 108359, MAKULU_INFO = { targeted = false } },
    HowlOfTerror = { ID = 5484, MAKULU_INFO = { targeted = false } },
    MortalCoil = { ID = 6789, MAKULU_INFO = { castTime = 1000, heal = true } },
    ShadowFury = { ID = 30283, MAKULU_INFO = { castTime = 1500 } },

    -- Additional utility spells
    HealthFunnel = { ID = 755, MAKULU_INFO = { channeled = true, heal = true } },
    CreateSoulwell = { ID = 29893, MAKULU_INFO = { castTime = 3000 } },
    SummonSayaad = { ID = 366222, MAKULU_INFO = { castTime = 6000 } },
    GrimoireOfSacrifice = { ID = 108503, MAKULU_INFO = { targeted = false } },
    FelDomination = { ID = 18708, MAKULU_INFO = { targeted = false } },
    LifeTap = { ID = 1454, MAKULU_INFO = { targeted = false } },
    
    -- Potions and consumables
    TemperedPotion1 = { Type = "Potion", ID = 171263, QueueForbidden = true },
    TemperedPotion2 = { Type = "Potion", ID = 171264, QueueForbidden = true },
    TemperedPotion3 = { Type = "Potion", ID = 171265, QueueForbidden = true },
    PotionofUnwaveringFocus1 = { Type = "Potion", ID = 171266, QueueForbidden = true },
    PotionofUnwaveringFocus2 = { Type = "Potion", ID = 171267, QueueForbidden = true },
    PotionofUnwaveringFocus3 = { Type = "Potion", ID = 171268, QueueForbidden = true },

    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 19647, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 5782, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")
local pet = MakUnit:new("pet")

-- MoP Affliction Warlock Buffs
local buffs = {
    darkSoul = 113860,
    soulBurn = 74434,
    soulSwap = 86121,
    unendingResolve = 104773,
    darkRegeneration = 108359,
    darkPact = 108416,
    soulLeech = 108370,
    harvestLife = 108371,
    nightfall = 17941,
    shadowTrance = 17941,
    moltenCore = 71165,
    decimation = 63165,
    backdraft = 54274,
    empoweredImp = 47283,
    felSynergy = 54181,
    demonicEmpowerment = 54508,
}

-- MoP Affliction Warlock Debuffs
local debuffs = {
    agony = 980,
    corruption = 146739, -- MoP Corruption debuff ID
    unstableAffliction = 30108,
    seedOfCorruption = 27243,
    haunt = 48181,
    curseOfElements = 1490,
    curseOfWeakness = 702,
    curseOfTongues = 1714,
    fear = 5782,
    banish = 710,
    howlOfTerror = 5484,
    mortalCoil = 6789,
    shadowFury = 30283,
}

-- Game state tracking (enhanced with new functionality)
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    soulShards = 0,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
    hauntUp = false,
    darkSoulUp = false,
    shouldBurst = false,
    uaCount = 0,
    uaRemains = 0,
    fightRemains = 999,
    minAgony = 0,
    minAgonyTarget = nil,
    shouldAoE = false,
    shouldCleave = false,
    canSeed = false,
    cdDotsUp = false,
    imCasting = nil,
}

-- Utility functions that need to be defined before updategs()
local function getDoTCount(debuffID)
    local count = 0
    if target.exists and target:HasDeBuff(debuffID) then count = count + 1 end

    -- Check nearby enemies for DoT count
    for i = 1, gameState.activeEnemies do
        local enemy = MakUnit:new("nameplate" .. i)
        if enemy.exists and enemy:HasDeBuff(debuffID) then
            count = count + 1
        end
    end

    return count
end

local function getLowestDoTTarget(debuffID)
    local lowestTarget = target
    local lowestRemaining = target.exists and target:DebuffRemains(debuffID, true) or 0

    for i = 1, gameState.activeEnemies do
        local enemy = MakUnit:new("nameplate" .. i)
        if enemy.exists then
            local remaining = enemy:DebuffRemains(debuffID, true)
            if remaining < lowestRemaining then
                lowestTarget = enemy
                lowestRemaining = remaining
            end
        end
    end

    return lowestTarget
end

local function updategs()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetEnemies():Size()
    gameState.soulShards = player.soulShards or 0
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.channeling = player:IsCasting() or player:IsChanneling()
    gameState.hauntUp = target.exists and target:HasDeBuff(debuffs.haunt) or false
    gameState.darkSoulUp = player:HasBuff(buffs.darkSoul)
    gameState.shouldBurst = A.GetToggle(2, "BurstMode")

    -- Enhanced tracking
    gameState.uaCount = getDoTCount(debuffs.unstableAffliction)
    gameState.uaRemains = target.exists and target:DebuffRemains(debuffs.unstableAffliction, true) or 0
    --gameState.fightRemains = target.exists and target.timeToDie or 999
    gameState.minAgony = target.exists and target:DebuffRemains(debuffs.agony, true) or 0
    gameState.minAgonyTarget = getLowestDoTTarget(debuffs.agony)
    gameState.shouldAoE = gameState.activeEnemies > 2
    gameState.shouldCleave = gameState.activeEnemies > 1
    gameState.canSeed = gameState.soulShards >= 1 and gameState.shouldAoE
    gameState.cdDotsUp = gameState.hauntUp and (gameState.uaCount > 0)
    local castInfo = player:CastInfo()
    gameState.imCasting = castInfo and castInfo.spellId or nil

    -- TimeToAdds calculation using boss mod timers
    -- Default to high value for open world, only use boss mod timers in instances
    if Action.IsInInstance then
        gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
    else
        gameState.timeToAdds = 999  -- Open world - no adds timing needed
    end
end

-- Alias for compatibility
local updateGameState = updategs
local gs = gameState

-- Utility functions (enhanced)
local function isOpenWorld()
    return not Action.IsInInstance and not gameState.isPvP
end

-- DoT snapshotting and proc detection functions
local function hasStrongProcs()
    -- Check for Dark Soul: Misery
    if gameState.darkSoulUp then return true end

    -- Check for Bloodlust/Heroism
    if player:HasBuff(2825) or player:HasBuff(32182) then return true end

    -- Check for trinket procs (common intellect/haste proc IDs for MoP)
    -- These would need to be adjusted based on actual trinkets used
    if player:HasBuff(105702) or player:HasBuff(126554) or player:HasBuff(138786) then return true end

    -- Check for racial procs (Berserking for Trolls)
    if player:HasBuff(26297) then return true end

    return false
end

local function shouldBurst()
    -- Always burst if toggle is enabled
    if gameState.shouldBurst then return true end

    -- Auto-burst conditions for better gameplay
    if not gameState.inCombat then return false end

    -- MoP Priority: Use Dark Soul when procs are active or about to align
    if hasStrongProcs() then return true end

    -- Use on pull for long fights
    if gameState.fightRemains > 990 and target.exists and target.hp > 90 then return true end

    -- Open world optimizations - more aggressive bursting
    if isOpenWorld() then
        -- Burst more frequently in open world since mobs die faster
        if target.exists and target.hp > 70 and gameState.soulShards >= 1 then return true end
        if gameState.activeEnemies >= 2 then return true end  -- Burst on any multi-target
    end

    -- Burst during execute phase for maximum damage
    if target.exists and target.hp <= 20 then return true end

    -- Burst when facing multiple enemies
    if gameState.activeEnemies >= 3 then return true end

    return false
end

local function shouldAoE()
    return gameState.activeEnemies >= 6
end

local function shouldCleave()
    return gameState.activeEnemies >= 2 and gameState.activeEnemies <= 5
end

local function needsDoTRefresh(unit, debuffID, threshold)
    return unit.exists and unit:DebuffRemains(debuffID, true) < threshold
end

local function shouldSnapshot(unit, debuffID)
    -- Always snapshot if no DoT is active
    if not unit.exists or unit:DebuffRemains(debuffID, true) <= 0 then return true end

    -- Snapshot if we have strong procs and current DoT is significantly weaker
    if hasStrongProcs() then
        -- Simplified logic: if we have strong procs, refresh DoTs that have >50% duration remaining
        -- This represents the "≥15% stronger" condition from the guide
        local remaining = unit:DebuffRemains(debuffID, true)
        local maxDuration = 18000 -- Most DoTs are 18 seconds in MoP
        return remaining > (maxDuration * 0.5)
    end

    return false
end

-- Soulburn + Soul Swap logic
local function shouldSoulburnSoulSwap()
    -- Use if we have strong procs (~≥50% DoT bonus)
    if hasStrongProcs() then return true end

    -- Use if no DoTs are active on target
    if target.exists then
        local hasAnyDoT = target:HasDeBuff(debuffs.corruption) or
                         target:HasDeBuff(debuffs.agony) or
                         target:HasDeBuff(debuffs.unstableAffliction)
        if not hasAnyDoT then return true end
    end

    -- Use during movement (if player is moving and DoTs are about to expire)
    if player:IsMoving() and target.exists then
        local corruptionRemains = target:DebuffRemains(debuffs.corruption, true)
        local agonyRemains = target:DebuffRemains(debuffs.agony, true)
        local uaRemains = target:DebuffRemains(debuffs.unstableAffliction, true)

        -- If multiple DoTs are expiring soon (within 3 seconds)
        local expiringCount = 0
        if corruptionRemains > 0 and corruptionRemains < 3000 then expiringCount = expiringCount + 1 end
        if agonyRemains > 0 and agonyRemains < 3000 then expiringCount = expiringCount + 1 end
        if uaRemains > 0 and uaRemains < 3000 then expiringCount = expiringCount + 1 end

        if expiringCount >= 2 then return true end
    end

    return false
end

-- Mana management
local function needsLifeTap()
    -- Use Life Tap if mana ≤15%
    local maxMana = player:GetManaMax()
    if maxMana == 0 then return false end -- Avoid division by zero
    local manaPercent = (gameState.mana / maxMana) * 100
    return manaPercent <= 15
end

-- Cleave and AoE helper functions
local function getTargetWithLowestDoT(debuffID)
    local lowestTarget = nil
    local lowestRemaining = 999999

    -- Check current target first
    if target.exists then
        local remaining = target:DebuffRemains(debuffID, true)
        if remaining < lowestRemaining then
            lowestTarget = target
            lowestRemaining = remaining
        end
    end

    -- Check other enemies
    for i = 1, math.min(gameState.activeEnemies, 8) do
        local enemy = MakUnit:new("nameplate" .. i)
        if enemy.exists and enemy.canAttack then
            local remaining = enemy:DebuffRemains(debuffID, true)
            if remaining < lowestRemaining then
                lowestTarget = enemy
                lowestRemaining = remaining
            end
        end
    end

    return lowestTarget, lowestRemaining
end

local function countTargetsWithDoT(debuffID)
    local count = 0

    if target.exists and target:HasDeBuff(debuffID) then
        count = count + 1
    end

    for i = 1, math.min(gameState.activeEnemies, 8) do
        local enemy = MakUnit:new("nameplate" .. i)
        if enemy.exists and enemy.canAttack and enemy:HasDeBuff(debuffID) then
            count = count + 1
        end
    end

    return count
end

local function hasFullDoTs(unit)
    if not unit or not unit.exists then return false end
    return unit:HasDeBuff(debuffs.corruption) and
           unit:HasDeBuff(debuffs.agony) and
           unit:HasDeBuff(debuffs.unstableAffliction)
end

local function needsSoulSwapSpread()
    -- Check if we have a target with full DoTs and other targets without them
    local hasSourceTarget = false
    local hasTargetNeedingDoTs = false

    if target.exists and hasFullDoTs(target) then
        hasSourceTarget = true
    end

    for i = 1, math.min(gameState.activeEnemies, 8) do
        local enemy = MakUnit:new("nameplate" .. i)
        if enemy.exists and enemy.canAttack then
            if hasFullDoTs(enemy) and not hasSourceTarget then
                hasSourceTarget = true
            elseif not hasFullDoTs(enemy) then
                hasTargetNeedingDoTs = true
            end
        end
    end

    return hasSourceTarget and hasTargetNeedingDoTs
end



-- Enhanced utility functions for new functionality
local function isSpellInFlight(spell, range)
    return spell:IsSpellInFlight() or false
end

local function makInterrupt(interrupts)
    if not interrupts then return end
    for _, interrupt in pairs(interrupts) do
        if interrupt and interrupt:ReadyToUse() then
            interrupt()
        end
    end
end

-- PvP utility functions
local function shouldExhaustion(enemy)
    if not enemy or not enemy.exists then return false end
    return enemy:IsMoving() and not enemy:HasDeBuff(debuffs.curseOfWeakness)
end

local function shouldTongues(enemy)
    if not enemy or not enemy.exists then return false end
    return enemy:IsCasting() and enemy:IsInterruptible()
end

local function shouldWeakness(enemy)
    if not enemy or not enemy.exists then return false end
    return not enemy:HasDeBuff(debuffs.curseOfWeakness) and not enemy:HasDeBuff(debuffs.curseOfTongues)
end

-- Core DoT callbacks
Agony:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:DebuffRemains(debuffs.agony, true) > 5000 then return end

    return spell:Cast(target)
end)

Corruption:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:DebuffRemains(debuffs.corruption, true) > 3000 then return end

    return spell:Cast(target)
end)

UnstableAffliction:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gameState.soulShards < 1 then return end

    -- Don't refresh if it has more than 3 seconds remaining
    if target:DebuffRemains(debuffs.unstableAffliction, true) > 3000 then return end

    -- Don't cast if we're already casting it
    if gameState.channeling and gameState.imCasting == spell.id then return end

    -- Priority casting - UA is very important for Affliction
    return spell:Cast(target)
end)

-- Pre-combat Unstable Affliction callback
UnstableAffliction:Callback("pre", function(spell)
    if player.inCombat then return end
    if gameState.channeling and gameState.imCasting == spell.id then return end
    if gameState.soulShards < 1 then return end

    -- Pre-cast UA before combat starts
    return spell:Cast(target)
end)

Haunt:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:DebuffRemains(debuffs.haunt, true) > 3000 then return end
    if gameState.soulShards < 1 then return end

    return spell:Cast(target)
end)

-- Pre-combat Haunt callback
Haunt:Callback("pre", function(spell)
    if player.inCombat then return end
    if gameState.channeling and gameState.imCasting == spell.id then return end
    if gameState.soulShards < 1 then return end

    return spell:Cast(target)
end)

SeedOfCorruption:Callback(function(spell)
    if not shouldAoE() then return end
    if not target.exists or not target.canAttack then return end
    if target:DebuffRemains(debuffs.seedOfCorruption, true) > 3000 then return end
    if gameState.soulShards < 1 then return end

    return spell:Cast(target)
end)

-- Pre-combat SeedOfCorruption callback
SeedOfCorruption:Callback("pre", function(spell)
    if gameState.channeling then return end
    if player.inCombat then return end
    if not shouldAoE() then return end
    if gameState.soulShards < 1 then return end

    if gameState.activeEnemies > 2 then
        return spell:Cast(target)
    end
end)

-- Drain abilities
DrainSoul:Callback(function(spell)
    if not target.exists or not target.canAttack then return end

    -- Use for soul shard management (when ≤1 shards) or during execute phase (target <20% HP)
    if target.hp > 20 and gameState.soulShards > 1 then return end
    if gameState.channeling then return end

    -- Interrupt after 2 ticks if Shards are capped (4 shards)
    if gameState.soulShards >= 4 and player:IsChanneling() and player:GetChannelTimeLeft() < 2000 then
        player:StopCasting()
        return
    end

    return spell:Cast(target)
end)

-- Malefic Grasp - Primary filler for MoP Affliction
MaleficGrasp:Callback(function(spell)
    if not target.exists or not target.canAttack then return end

    -- Don't use if we need to refresh DoTs urgently
    if needsDoTRefresh(target, debuffs.agony, 2000) then return end
    if needsDoTRefresh(target, debuffs.corruption, 2000) then return end
    if needsDoTRefresh(target, debuffs.unstableAffliction, 2000) and gameState.soulShards >= 1 then return end

    -- Prefer Malefic Grasp when DoTs are up (it extends their duration)
    if target:HasDeBuff(debuffs.agony) or target:HasDeBuff(debuffs.corruption) or target:HasDeBuff(debuffs.unstableAffliction) then
        return spell:Cast(target)
    end

    -- Use as filler when no DoTs are up
    return spell:Cast(target)
end)

DrainLife:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if player.hp > 80 then return end
    if gameState.channeling then return end

    return spell:Cast(target)
end)

-- Filler spell
ShadowBolt:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gameState.channeling then return end

    return spell:Cast(target)
end)

-- Cooldowns
DarkSoul:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if not gameState.inCombat then return end

    -- Don't use if already active
    if gameState.darkSoulUp then return end

    -- Use during burst conditions
    if shouldBurst() then
        return spell:Cast(player)
    end

    -- Auto-use when DoTs are up and we have good conditions
    if gameState.hauntUp and gameState.uaCount >= 1 and target.hp > 50 then
        return spell:Cast(player)
    end

    return false
end)

-- Summon Doomguard - Use before other cooldowns
SummonDoomguard:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if not gameState.inCombat then return end

    -- Don't use if Doomguard is already active (check for Doomguard specifically)
    if pet.exists and pet.name == "Doomguard" then return end

    -- Use before activating other cooldowns during burst
    if shouldBurst() and hasStrongProcs() then
        return spell:Cast(player)
    end

    -- Use on pull if we expect a long fight
    if gameState.fightRemains > 990 and target.hp > 80 then
        return spell:Cast(player)
    end

    return false
end)

-- Life Tap for mana management
LifeTap:Callback(function(spell)
    if not needsLifeTap() then return end

    -- Prefer to use during movement or downtime
    if player:IsMoving() or not gameState.inCombat then
        return spell:Cast(player)
    end

    -- Use if we're about to run out of mana
    if gameState.mana < 1000 then
        return spell:Cast(player)
    end

    return false
end)

-- Defensive abilities
UnendingResolve:Callback(function(spell)
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

DarkRegeneration:Callback(function(spell)
    if player.hp > 60 then return end

    return spell:Cast(player)
end)

DarkPact:Callback(function(spell)
    if gameState.mana > 50 then return end
    if not pet.exists then return end

    return spell:Cast(player)
end)

-- Soulburn + Soul Swap combo
SoulBurn:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if not gameState.inCombat then return end

    -- Don't use if already active
    if player:HasBuff(buffs.soulBurn) then return end

    -- Use when we should do Soulburn + Soul Swap combo
    if shouldSoulburnSoulSwap() and gameState.soulShards >= 1 then
        return spell:Cast(player)
    end

    return false
end)

SoulSwap:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if not gameState.inCombat then return end

    -- Only use if Soulburn is active
    if not player:HasBuff(buffs.soulBurn) then return end

    -- Use to inhale DoTs from current target
    if target:HasDeBuff(debuffs.corruption) or target:HasDeBuff(debuffs.agony) or target:HasDeBuff(debuffs.unstableAffliction) then
        return spell:Cast(target)
    end

    return false
end)

SoulSwapExhale:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if not gameState.inCombat then return end

    -- Only use if we have Soul Swap buff (after inhaling DoTs)
    if not player:HasBuff(buffs.soulSwap) then return end

    -- Exhale DoTs onto target
    return spell:Cast(target)
end)

-- Utility abilities
Fear:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDeBuff(debuffs.fear) then return end
    if gameState.isPvP and target.hp < 30 then return end

    return spell:Cast(target)
end)

SpellLock:Callback(function(spell)
    if not target.exists or not target:IsCasting() then return end
    if not target:IsInterruptible() then return end

    return spell:Cast(target)
end)

-- BG SpellLock callback
SpellLock:Callback("bg", function(spell)
    if not target.exists or not target:IsCasting() then return end
    if not target:IsInterruptible() then return end

    return spell:Cast(target)
end)

-- Curse abilities
CurseOfElements:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDeBuff(debuffs.curseOfElements) then return end

    return spell:Cast(target)
end)

-- Pet summoning and utility
SummonFelhunter:Callback(function(spell)
    if pet.exists then return end
    if gameState.inCombat then return end

    return spell:Cast(player)
end)

SummonImp:Callback(function(spell)
    if pet.exists then return end
    if gameState.inCombat then return end

    return spell:Cast(player)
end)

SummonVoidwalker:Callback(function(spell)
    if pet.exists then return end
    if gameState.inCombat then return end

    return spell:Cast(player)
end)

SummonSayaad:Callback(function(spell)
    if pet.exists then return end
    if gameState.inCombat then return end

    return spell:Cast(player)
end)

FelDomination:Callback(function(spell)
    if pet.exists then return end
    if gameState.inCombat and player.hp < 50 then
        return spell:Cast(player)
    end
end)

GrimoireOfSacrifice:Callback(function(spell)
    if not pet.exists then return end
    if gameState.inCombat and shouldBurst() then
        return spell:Cast(player)
    end
end)

-- Summon Infernal for AoE (5+ targets)
SummonInfernal:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if not gameState.inCombat then return end

    -- Use for 5+ targets to add burst AoE and stun on impact
    if gameState.activeEnemies >= 5 then
        return spell:Cast(target)
    end

    -- Use during burst with multiple enemies
    if shouldBurst() and gameState.activeEnemies >= 3 then
        return spell:Cast(target)
    end

    return false
end)

CreateSoulwell:Callback(function(spell)
    if gameState.inCombat then return end

    return spell:Cast(player)
end)

HealthFunnel:Callback(function(spell)
    if not pet.exists then return end
    if pet.hp > 80 then return end
    if player.hp < 60 then return end

    return spell:Cast(pet)
end)

-- Enhanced PvP callbacks
CurseOfWeakness:Callback("arena", function(spell, enemy)
    if not shouldWeakness(enemy) then return end

    return spell:Cast(enemy)
end)

-- BG CurseOfWeakness callback
CurseOfWeakness:Callback("bg", function(spell)
    if not shouldWeakness(target) then return end

    return spell:Cast(target)
end)

CurseOfTongues:Callback("arena", function(spell, enemy)
    if not shouldTongues(enemy) then return end

    return spell:Cast(enemy)
end)

-- BG CurseOfTongues callback
CurseOfTongues:Callback("bg", function(spell)
    if not shouldTongues(target) then return end

    return spell:Cast(target)
end)

local function fearDuration()
    for i = 1, 3 do
        local enemy = "arena" .. i
        if ActionUnit(enemy):IsExists() and ActionUnit(enemy):HasDeBuffs(A.Fear.ID, true) > 0 then
            return ActionUnit(enemy):HasDeBuffs(A.Fear.ID) * 1000
        end
    end
    return 0
end

Fear:Callback("arena", function(spell, enemy)
    local ccRemains = 0
    if enemy.cc then
        ccRemains = enemy:CCRemains()
    end

    if gs.imCasting and gs.imCasting == spell.id then return end
    if enemy:IsTarget() then return end
    if enemy.disorientDr <= 0.25 then return end
    if ccRemains > Fear:CastTime() + MakGcd() then return end
    if fearDuration() > Fear:CastTime() then return end

    local fearCastTime = spell:CastTime()
    if arena1:HasDeBuffRemain(spell.Id, fearCastTime) then return end
    if arena2:HasDeBuffRemain(spell.Id, fearCastTime) then return end
    if arena3:HasDeBuffRemain(spell.Id, fearCastTime) then return end

    if enemy.cc then return end

    if gs.uaCount < 1 then return end

    if enemy:Debuff(debuffs.fear) then
        Aware:displayMessage("Chain Fearing", "Purple", 1)
        return spell:Cast(enemy)
    end

    if enemy.isHealer then
        Aware:displayMessage("Fearing Healer", "Green", 1)
        return spell:Cast(enemy)
    end

    local peelParty = (party1.exists and party1.hp > 0 and party1.hp < 50) or (party2.exists and party1.hp > 0 and party2.hp < 50)
    if peelParty and not enemy.isHealer and enemy.hp > 40 then
        Aware:displayMessage("Fearing To Peel", "Red", 1)
        return spell:Cast(enemy)
    end

    if enemy:Debuff(debuffs.mortalCoil) then
        return spell:Cast(enemy)
    end
end)

SpellLock:Callback("arena", function(spell, enemy)
    if not enemy.pvpKick then return end

    return spell:Cast(enemy)
end)

MortalCoil:Callback("arena", function(spell, enemy)
    if enemy.pvpKick or (enemy:Debuff(debuffs.fear) and enemy:DebuffRemains(debuffs.fear) < 1000) then
        return spell:Cast(enemy)
    end
end)

UnstableAffliction:Callback("arena", function(spell, enemy)
    if gs.uaCount >= 3 then return end
    if enemy:DebuffRemains(debuffs.unstableAffliction) > 5000 then return end
    if gs.imCasting and gs.imCasting == spell.id then return end

    return spell:Cast(enemy)
end)

Agony:Callback("arena", function(spell, enemy)
    if enemy:DebuffRemains(debuffs.agony) > 4000 then return end

    return spell:Cast(enemy)
end)

Corruption:Callback("arena", function(spell, enemy)
    if enemy:DebuffRemains(debuffs.corruption, true) > 4000 then return end

    return spell:Cast(enemy)
end)

-- Enhanced rotation functions
local function st()
    -- MoP Affliction Single Target Rotation following priority list
    updategs()

    -- Mana management - Life Tap if mana ≤15%
    if needsLifeTap() then
        LifeTap()
        return
    end

    -- Curse of the Elements (if no one else applies spell damage debuff)
    CurseOfElements()

    -- Dark Soul: Misery during procs or Bloodlust
    if shouldBurst() and hasStrongProcs() then
        DarkSoul()
    end

    -- Soulburn + Soul Swap for DoT application with strong procs
    if shouldSoulburnSoulSwap() then
        if not player:HasBuff(buffs.soulBurn) and gameState.soulShards >= 1 then
            SoulBurn()
            return
        elseif player:HasBuff(buffs.soulBurn) and not player:HasBuff(buffs.soulSwap) then
            SoulSwap()
            return
        elseif player:HasBuff(buffs.soulSwap) then
            SoulSwapExhale()
            return
        end
    end

    -- DoT Maintenance with snapshotting logic
    -- Corruption - Refresh if ≤1/2 duration or ≥15% stronger
    local corruptionRemains = target.exists and target:DebuffRemains(debuffs.corruption, true) or 0
    local corruptionMaxDuration = 18000
    if target.exists and (corruptionRemains <= (corruptionMaxDuration / 2) or shouldSnapshot(target, debuffs.corruption)) then
        Corruption()
        return
    end

    -- Agony - Refresh if ≤1/2 duration or ≥15% stronger
    local agonyRemains = target.exists and target:DebuffRemains(debuffs.agony, true) or 0
    local agonyMaxDuration = 24000 -- Agony has longer duration and stacks
    if target.exists and (agonyRemains <= (agonyMaxDuration / 2) or shouldSnapshot(target, debuffs.agony)) then
        Agony()
        return
    end

    -- Unstable Affliction - Refresh if ≤1/2 duration or ≥15% stronger
    local uaRemains = target.exists and target:DebuffRemains(debuffs.unstableAffliction, true) or 0
    local uaMaxDuration = 14000
    if target.exists and gameState.soulShards >= 1 and (uaRemains <= (uaMaxDuration / 2) or shouldSnapshot(target, debuffs.unstableAffliction)) then
        UnstableAffliction()
        return
    end

    -- Haunt - Keep active when Shards ≥1 and buffs are aligned, don't overcap
    local hauntRemains = target.exists and target:DebuffRemains(debuffs.haunt, true) or 0
    if target.exists and gameState.soulShards >= 1 and gameState.soulShards < 4 then
        if hauntRemains <= 3000 and (hasStrongProcs() or gameState.darkSoulUp) then
            Haunt()
            return
        end
    end

    -- Soul Shard Management - Use Drain Soul when low on shards (≤1) until we reach 4
    if target.exists and gameState.soulShards <= 1 then
        DrainSoul()
        return
    end

    -- Execute phase (target <20% HP) - Switch to Drain Soul
    if target.exists and target.hp < 20 then
        DrainSoul()
        return
    end

    -- Filler - Malefic Grasp when DoTs are up
    if target.exists and (target:HasDeBuff(debuffs.corruption) or target:HasDeBuff(debuffs.agony) or target:HasDeBuff(debuffs.unstableAffliction)) then
        MaleficGrasp()
        return
    end

    -- Fallback filler
    ShadowBolt()
end

local function aoe()
    -- MoP AoE Rotation (6+ targets)
    -- Focus: Spread Corruption via Soulburn: Seed of Corruption, layer empowered DoTs with cooldowns
    updategs()

    -- Mana management
    if needsLifeTap() then
        LifeTap()
        return
    end

    -- Step 1: Soulburn + Seed of Corruption for Corruption spread
    if gameState.soulShards >= 1 then
        -- Check if we need to reapply Corruption via Seed
        local corruptionCount = countTargetsWithDoT(debuffs.corruption)
        local needsSeed = corruptionCount < math.min(gameState.activeEnemies, 8) * 0.7 -- If less than 70% have Corruption

        if needsSeed then
            if not player:HasBuff(buffs.soulBurn) then
                SoulBurn()
                return
            elseif player:HasBuff(buffs.soulBurn) then
                SeedOfCorruption()
                return
            end
        end
    end

    -- Step 2: Summon Infernal for 5+ targets
    SummonInfernal()

    -- Step 3: Agony and Unstable Affliction on 2-3 key targets (long-lived or dangerous)
    local priorityTargets = math.min(3, gameState.activeEnemies)
    local agonyCount = countTargetsWithDoT(debuffs.agony)
    local uaCount = countTargetsWithDoT(debuffs.unstableAffliction)

    -- Apply Agony to priority targets
    if agonyCount < priorityTargets then
        local agonyTarget, agonyRemains = getTargetWithLowestDoT(debuffs.agony)
        if agonyTarget and agonyRemains <= 6000 then
            Agony()
            return
        end
    end

    -- Apply Unstable Affliction to priority targets
    if uaCount < priorityTargets and gameState.soulShards >= 1 then
        local uaTarget, uaRemains = getTargetWithLowestDoT(debuffs.unstableAffliction)
        if uaTarget and uaRemains <= 4000 then
            UnstableAffliction()
            return
        end
    end

    -- Step 4: Soul Swap + Soul Swap Exhale for 3-5 enemies (less ideal for very large packs)
    if gameState.activeEnemies <= 8 and needsSoulSwapSpread() then
        if not player:HasBuff(buffs.soulSwap) then
            -- Find target with full DoTs to inhale from
            local sourceTarget = nil
            if target.exists and hasFullDoTs(target) then
                sourceTarget = target
            else
                for i = 1, math.min(gameState.activeEnemies, 8) do
                    local enemy = MakUnit:new("nameplate" .. i)
                    if enemy.exists and enemy.canAttack and hasFullDoTs(enemy) then
                        sourceTarget = enemy
                        break
                    end
                end
            end

            if sourceTarget then
                SoulSwap()
                return
            end
        else
            -- Exhale to spread DoTs
            for i = 1, math.min(gameState.activeEnemies, 8) do
                local enemy = MakUnit:new("nameplate" .. i)
                if enemy.exists and enemy.canAttack and not hasFullDoTs(enemy) then
                    SoulSwapExhale()
                    return
                end
            end
        end
    end

    -- Step 5: Reapply Soulburn + Seed of Corruption as needed
    if gameState.soulShards >= 1 then
        local corruptionCount = countTargetsWithDoT(debuffs.corruption)
        if corruptionCount < gameState.activeEnemies * 0.8 then -- If less than 80% have Corruption
            if not player:HasBuff(buffs.soulBurn) then
                SoulBurn()
                return
            else
                SeedOfCorruption()
                return
            end
        end
    end

    -- Step 6: Drain Soul if many targets are dying rapidly (execute phase)
    local executingTargets = 0
    if target.exists and target.hp < 20 then executingTargets = executingTargets + 1 end
    for i = 1, math.min(gameState.activeEnemies, 8) do
        local enemy = MakUnit:new("nameplate" .. i)
        if enemy.exists and enemy.canAttack and enemy.hp < 20 then
            executingTargets = executingTargets + 1
        end
    end

    if executingTargets >= 2 then
        DrainSoul()
        return
    end

    -- Step 7: Filler - Malefic Grasp extends DoTs
    MaleficGrasp()
end

local function cleave()
    -- MoP Cleave Rotation (2-5 targets)
    -- Priority: Apply full DoTs to all targets using Soulburn-enhanced spells, then maintain with Soul Swap
    updategs()

    -- Mana management
    if needsLifeTap() then
        LifeTap()
        return
    end

    -- Step 1: Soulburn + Soul Swap on primary target (if we have strong procs or no DoTs)
    if shouldSoulburnSoulSwap() and target.exists then
        if not player:HasBuff(buffs.soulBurn) and gameState.soulShards >= 1 then
            SoulBurn()
            return
        elseif player:HasBuff(buffs.soulBurn) and not player:HasBuff(buffs.soulSwap) then
            -- Apply DoTs to primary target first if needed
            if not hasFullDoTs(target) then
                if not target:HasDeBuff(debuffs.agony) then
                    Agony()
                    return
                elseif not target:HasDeBuff(debuffs.corruption) then
                    Corruption()
                    return
                elseif not target:HasDeBuff(debuffs.unstableAffliction) and gameState.soulShards >= 1 then
                    UnstableAffliction()
                    return
                end
            end
            -- Inhale DoTs from primary target
            SoulSwap()
            return
        elseif player:HasBuff(buffs.soulSwap) then
            -- Find target that needs DoTs and exhale
            for i = 1, math.min(gameState.activeEnemies, 8) do
                local enemy = MakUnit:new("nameplate" .. i)
                if enemy.exists and enemy.canAttack and not hasFullDoTs(enemy) then
                    SoulSwapExhale()
                    return
                end
            end
        end
    end

    -- Step 2: Soul Swap Extension Cycle for DoT maintenance
    if needsSoulSwapSpread() and not player:HasBuff(buffs.soulBurn) then
        if not player:HasBuff(buffs.soulSwap) then
            -- Find target with full DoTs to inhale from
            local sourceTarget = nil
            if target.exists and hasFullDoTs(target) then
                sourceTarget = target
            else
                for i = 1, math.min(gameState.activeEnemies, 8) do
                    local enemy = MakUnit:new("nameplate" .. i)
                    if enemy.exists and enemy.canAttack and hasFullDoTs(enemy) then
                        sourceTarget = enemy
                        break
                    end
                end
            end

            if sourceTarget then
                SoulSwap()
                return
            end
        else
            -- We have Soul Swap buff, do 1 filler GCD then exhale for 3x duration
            -- Find target with lowest DoT duration to exhale on
            local bestTarget, lowestDuration = getTargetWithLowestDoT(debuffs.corruption)
            if bestTarget and lowestDuration < 10000 then -- If DoTs are getting low
                SoulSwapExhale()
                return
            else
                -- Do 1 filler GCD for duration extension
                MaleficGrasp()
                return
            end
        end
    end

    -- Step 3: Dark Soul when all DoTs are applied and we have procs
    if shouldBurst() and hasStrongProcs() then
        local dotsApplied = countTargetsWithDoT(debuffs.corruption)
        if dotsApplied >= math.min(gameState.activeEnemies, 3) then
            DarkSoul()
        end
    end

    -- Step 4: Haunt for DoT amplification on priority targets
    if gameState.soulShards >= 1 and gameState.soulShards < 4 then
        local hauntRemains = target.exists and target:DebuffRemains(debuffs.haunt, true) or 0
        if target.exists and hauntRemains <= 3000 and hasFullDoTs(target) then
            Haunt()
            return
        end
    end

    -- Step 5: Manual DoT application if Soul Swap isn't available/efficient
    -- Priority: Agony > Corruption > Unstable Affliction
    local agonyTarget, agonyRemains = getTargetWithLowestDoT(debuffs.agony)
    if agonyTarget and agonyRemains <= 6000 then
        Agony()
        return
    end

    local corruptionTarget, corruptionRemains = getTargetWithLowestDoT(debuffs.corruption)
    if corruptionTarget and corruptionRemains <= 4000 then
        Corruption()
        return
    end

    local uaTarget, uaRemains = getTargetWithLowestDoT(debuffs.unstableAffliction)
    if uaTarget and uaRemains <= 4000 and gameState.soulShards >= 1 then
        UnstableAffliction()
        return
    end

    -- Step 6: Filler - Malefic Grasp extends DoT durations
    MaleficGrasp()
end

-- Racial abilities function
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    WillOfTheForsaken()
    QuakingPalm()
end

local function ogcd()
    -- Off-global cooldown abilities
    DarkSoul()  -- Let the spell's own logic decide when to cast

    -- Racial abilities (they have their own conditions)
    racials()
end

local function eof()
    -- End of fight logic
    if gs.fightRemains < 30000 then
        -- Burn resources
        if gs.soulShards >= 1 then
            UnstableAffliction()
            Haunt()
        end
    end
end

local function pvpenis()
    -- PvP specific logic placeholder
    -- This would contain PvP-specific rotation logic
end

-- Alias for compatibility
local function singleTargetRotation()
    st()
end

-- AoE rotation
local function aoeRotation()
    updateGameState()

    -- Seed of Corruption for AoE
    if gameState.soulShards >= 1 then
        SeedOfCorruption()
    end

    -- Maintain DoTs on multiple targets
    local agonyTarget = getLowestDoTTarget(debuffs.agony)
    if agonyTarget and needsDoTRefresh(agonyTarget, debuffs.agony, 5000) then
        Agony()
    end

    local corruptionTarget = getLowestDoTTarget(debuffs.corruption)
    if corruptionTarget and needsDoTRefresh(corruptionTarget, debuffs.corruption, 3000) then
        Corruption()
    end

    -- Filler - Malefic Grasp extends DoTs
    MaleficGrasp()
end

-- Cleave rotation (2 enemies)
local function cleaveRotation()
    cleave()  -- Use the existing cleave() function
end

-- PvP specific rotation
local function pvpRotation()
    updateGameState()

    -- Interrupt priority
    SpellLock()

    -- Fear for CC
    Fear()

    -- DoT pressure
    if needsDoTRefresh(target, debuffs.agony, 5000) then
        Agony()
    end

    if needsDoTRefresh(target, debuffs.corruption, 3000) then
        Corruption()
    end

    if needsDoTRefresh(target, debuffs.unstableAffliction, 3000) and gameState.soulShards >= 1 then
        UnstableAffliction()
    end

    -- Curse application
    CurseOfElements()

    -- Drain for sustain
    if player.hp <= 70 then
        DrainLife()
    end

    -- Execute
    if target.hp <= 25 then
        DrainSoul()
    end

    -- Filler
    ShadowBolt()
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Pre-DoT before adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Prepare DoTs on current target
        if needsDoTRefresh(target, debuffs.agony, 8000) then
            Agony()
        end

        if needsDoTRefresh(target, debuffs.corruption, 6000) then
            Corruption()
        end

        if needsDoTRefresh(target, debuffs.unstableAffliction, 6000) and gameState.soulShards >= 1 then
            UnstableAffliction()
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            DarkSoul()
        end
    end

    -- During adds phase
    if shouldAoE() then
        aoeRotation()  -- 6+ enemies
    elseif shouldCleave() then
        cleaveRotation()  -- 2-5 enemies
    else
        singleTargetRotation()  -- 1 enemy
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 30 then
        UnendingResolve()
        DarkRegeneration()
    end

    -- Resource management
    if gameState.mana <= 30 and pet.exists then
        DarkPact()
    end

    -- Cooldowns during burst
    if shouldBurst() then
        DarkSoul()
    end

    -- TimeToAdds logic (only relevant in instances with boss mods)
    if Action.IsInInstance and gameState.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Standard rotation selection (works for all content types)
    -- Open world, dungeons, raids - all use the same enemy count logic
    if shouldAoE() then
        aoeRotation()  -- 6+ enemies
    elseif shouldCleave() then
        cleaveRotation()  -- 2-5 enemies
    else
        singleTargetRotation()  -- 1 enemy
    end
end

-- Main function - Works in all content types (Open World, Dungeons, Raids, PvP)
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- Enhanced A[3] function with new functionality
A[3] = function(icon)
    FrameworkStart(icon)
    updategs()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Soul Shards: ", gs.soulShards)
        MakPrint(2, "Unstable Affliction Count: ", gs.uaCount)
        MakPrint(3, "Unstable Affliction Remains: ", gs.uaRemains)
        MakPrint(4, "Seed in flight: ", isSpellInFlight(SeedOfCorruption, 20))
        MakPrint(5, "Dark Soul Active: ", gs.darkSoulUp)
        MakPrint(6, "Fight Remains: ", gs.fightRemains)
        MakPrint(7, "Min Agony: ", gs.minAgony)
        MakPrint(8, "Min Agony Target: ", gs.minAgonyTarget and gs.minAgonyTarget.name or "None")
        MakPrint(9, "Should AoE: ", gs.shouldAoE)
        MakPrint(10, "Should Cleave: ", gs.shouldCleave)
        MakPrint(11, "Can Seed: ", gs.canSeed)
        MakPrint(12, "Spell Lock Learned: ", SpellLock:IsKnown())
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then -- Dark Soul ready
        if DarkSoul:ReadyToUse() and shouldBurst() and player.inCombat then
            Aware:displayMessage("DARK SOUL READY", "Purple", 1)
        end
    end

    -- Interrupt handling
    local interrupts = {SpellLock}
    makInterrupt(interrupts)

    -- Utility management
    UnendingResolve()
    DarkPact()

    if target.exists and target.canAttack and (ShadowBolt:InRange(target) or DrainSoul:InRange(target)) then
        MortalCoil()
        DrainLife()

        if A.IsInPvP then
            SpellLock("bg")
            CurseOfWeakness("bg")
            CurseOfTongues("bg")
            pvpenis()
        end

        SeedOfCorruption("pre")
        Haunt("pre")
        UnstableAffliction("pre")

        if player.channeling and gs.imCasting and gs.imCasting == ShadowBolt.id then return end

        local damagePotion = Action.GetToggle(2, "damagePotion")
        local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
        local potionExhausted = Action.GetToggle(2, "potionExhausted")
        local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")
        local damagePotionObject = Action.DetermineUsableObject("player", nil, nil, true, nil, A.TemperedPotion1, A.TemperedPotion2, A.TemperedPotion3, A.PotionofUnwaveringFocus1, A.PotionofUnwaveringFocus2, A.PotionofUnwaveringFocus3)

        if damagePotionObject and damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
            local shouldPot = gs.cdDotsUp
            if shouldPot then
                return damagePotionObject:Show(icon)
            end
        end

        if gs.shouldAoE then
            aoe()
        end

        ogcd()
        eof()

        if gs.shouldCleave then
            cleave()
        end

        st()

    end

    return FrameworkEnd()
end

-- Enhanced enemy and party rotation functions
local enemyRotation = function(enemy)
    if not enemy.exists then return end
    if A.Zone ~= "arena" then return end
    SpellLock("arena", enemy)
    MortalCoil("arena", enemy)
    CurseOfWeakness("arena", enemy)
    CurseOfTongues("arena", enemy)
    Fear("arena", enemy)
    UnstableAffliction("arena", enemy)
    Agony("arena", enemy)
    Corruption("arena", enemy)
end

local partyRotation = function(friendly)
    if not friendly.exists then return end
    -- No specific party healing for Warlock in MoP
    -- Focus on defensive abilities for self
end

-- Arena functions
A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        SpellLock()
    end
    if Action.Zone == "arena" then
        enemyRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Affliction Warlock
SpellLock:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 30 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Fear:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.fear) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Fear - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Fear - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

Agony:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if enemy:DebuffRemains(debuffs.agony, true) > 5000 then return end

    return spell:Cast(enemy)
end)

Corruption:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if enemy:DebuffRemains(debuffs.corruption, true) > 3000 then return end

    return spell:Cast(enemy)
end)

UnstableAffliction:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if enemy:DebuffRemains(debuffs.unstableAffliction, true) > 3000 then return end
    if gameState.soulShards < 1 then return end

    -- Priority on low health targets
    if enemy.hp < 60 then
        Aware:displayMessage("Priority UA", "Red", 1)
        return spell:Cast(enemy)
    end

    return spell:Cast(enemy)
end)



-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and gameState.mana < 50 then
        return spell:Cast(player)
    end
end)

Berserking:Callback(function(spell)
    if shouldBurst() and (target.hp > 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

BloodFury:Callback(function(spell)
    if shouldBurst() and (target.hp > 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

WillOfTheForsaken:Callback(function(spell)
    if player:DebuffFrom(MakLists.feared) or player:DebuffFrom(MakLists.CC) then
        return spell:Cast(player)
    end
end)

QuakingPalm:Callback(function(spell)
    if target.exists and target.canAttack and target:IsCasting() then
        return spell:Cast(target)
    end
end)

-- Utility functions


local function mopTalents()
    HowlOfTerror()
    MortalCoil()
    ShadowFury()
end

local function baseStuff()
    UnendingResolve()
    DarkRegeneration()
    DarkPact()
end

-- Enhanced utility for MoP
local function mopUtility()
    SpellLock()
    Fear()
    Banish()
    SoulSwap()
    SoulSwapExhale()
end
