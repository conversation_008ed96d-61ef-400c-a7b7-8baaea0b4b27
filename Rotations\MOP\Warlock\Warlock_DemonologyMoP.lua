-- APL UPDATE MoP Demonology Warlock
-- Mists of Pandaria Demonology Warlock Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Demonology spec (talent tree 2 for Warlock in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 2 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Demonology Warlock Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Demonology Core Abilities
    ShadowBolt = { ID = 686, MAKULU_INFO = { damageType = "shadow", castTime = 2500 } },
    SoulFire = { ID = 6353, MAKULU_INFO = { damageType = "fire", castTime = 4000 } },
    HandOfGuldan = { ID = 105174, MAKULU_INFO = { damageType = "shadow", castTime = 2000 } },
    Corruption = { ID = 172, MAKULU_INFO = { damageType = "shadow" } },
    Doom = { ID = 603, MAKULU_INFO = { damageType = "shadow" } },
    
    -- MoP Demonology Specific
    Metamorphosis = { ID = 103958, MAKULU_INFO = { targeted = false } },
    DemonicFury = { ID = 104315, MAKULU_INFO = { targeted = false } },
    DarkSoul = { ID = 113858, MAKULU_INFO = { targeted = false } },
    DemonicLeap = { ID = 109151, MAKULU_INFO = { targeted = false } },
    Carrion = { ID = 104316, MAKULU_INFO = { damageType = "shadow" } },
    
    -- MoP Pet Summons
    SummonImp = { ID = 688, MAKULU_INFO = { castTime = 6000 } },
    SummonVoidwalker = { ID = 697, MAKULU_INFO = { castTime = 6000 } },
    SummonSuccubus = { ID = 712, MAKULU_INFO = { castTime = 6000 } },
    SummonFelhunter = { ID = 691, MAKULU_INFO = { castTime = 6000 } },
    SummonFelguard = { ID = 30146, MAKULU_INFO = { castTime = 6000 } },
    SummonDoomguard = { ID = 18540, MAKULU_INFO = { castTime = 10000 } },
    SummonInfernal = { ID = 1122, MAKULU_INFO = { castTime = 6000 } },
    
    -- MoP Utility Spells
    FelFlame = { ID = 77799, MAKULU_INFO = { damageType = "fire" } },
    DrainLife = { ID = 689, MAKULU_INFO = { damageType = "shadow", channeled = true } },
    DrainSoul = { ID = 1120, MAKULU_INFO = { damageType = "shadow", channeled = true } },
    LifeTap = { ID = 1454, MAKULU_INFO = { targeted = false } },
    
    -- MoP Curses
    CurseOfTheElements = { ID = 1490, MAKULU_INFO = { targeted = true } },
    CurseOfWeakness = { ID = 702, MAKULU_INFO = { targeted = true } },
    CurseOfTongues = { ID = 1714, MAKULU_INFO = { targeted = true } },
    
    -- MoP Fear and CC
    Fear = { ID = 5782, MAKULU_INFO = { castTime = 1500 } },
    Banish = { ID = 710, MAKULU_INFO = { castTime = 1500 } },
    Enslave = { ID = 1098, MAKULU_INFO = { castTime = 3000 } },
    
    -- MoP Defensive Abilities
    DemonSkin = { ID = 687, MAKULU_INFO = { targeted = false } },
    DemonArmor = { ID = 706, MAKULU_INFO = { targeted = false } },
    UnendingResolve = { ID = 104773, MAKULU_INFO = { targeted = false } },
    DarkRegeneration = { ID = 108359, MAKULU_INFO = { targeted = false } },
    ShadowWard = { ID = 6229, MAKULU_INFO = { targeted = false } },
    
    -- MoP Utility
    SpellLock = { ID = 19647, MAKULU_INFO = { ignoreCasting = true } },
    DevourMagic = { ID = 19505, MAKULU_INFO = { targeted = true } },
    CreateHealthstone = { ID = 6201, MAKULU_INFO = { castTime = 3000 } },
    CreateSoulstone = { ID = 693, MAKULU_INFO = { castTime = 3000 } },
    
    -- MoP Movement
    BurningRush = { ID = 111400, MAKULU_INFO = { targeted = false } },
    
    -- MoP Talents
    SoulBurn = { ID = 74434, MAKULU_INFO = { targeted = false } },
    DemonicBreath = { ID = 119905, MAKULU_INFO = { damageType = "fire" } },
    Hellfire = { ID = 1949, MAKULU_INFO = { damageType = "fire", channeled = true } },
    RainOfFire = { ID = 5740, MAKULU_INFO = { damageType = "fire", channeled = true } },
    
    -- MoP Metamorphosis Abilities
    ImmolationAura = { ID = 129476, MAKULU_INFO = { damageType = "fire" } },
    ShadowCleave = { ID = 50581, MAKULU_INFO = { damageType = "shadow" } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 19647, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 5782, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Demonology Warlock Buffs
local buffs = {
    demonSkin = 687,
    demonArmor = 706,
    metamorphosis = 103958,
    demonicFury = 104315,
    darkSoul = 113858,
    shadowWard = 6229,
    unendingResolve = 104773,
    darkRegeneration = 108359,
    burningRush = 111400,
    soulBurn = 74434,
    immolationAura = 129476,
    shadowmeld = 58984,
    moltenCore = 122355,
    decimation = 63165,
}

-- MoP Demonology Warlock Debuffs
local debuffs = {
    corruption = 172,
    doom = 603,
    curseOfTheElements = 1490,
    curseOfWeakness = 702,
    curseOfTongues = 1714,
    fear = 5782,
    banish = 710,
    enslave = 1098,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    mana = 0,
    timeToAdds = 999,
    isPvP = false,
    demonicFury = 0,
    inMetamorphosis = false,
    hasPet = false,
    moltenCoreStacks = 0,
    decimationActive = false,
    inBurstPhase = false,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.mana = player.mana or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.demonicFury = player:GetBuffStacks(buffs.demonicFury) or 0
    gameState.inMetamorphosis = player:HasBuff(buffs.metamorphosis)
    gameState.hasPet = player.pet and player.pet.exists
    gameState.moltenCoreStacks = player:GetBuffStacks(buffs.moltenCore) or 0
    gameState.decimationActive = player:HasBuff(buffs.decimation)
    gameState.inBurstPhase = gameState.inMetamorphosis or player:HasBuff(buffs.darkSoul)
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function needsArmor()
    return not player:HasBuff(buffs.demonSkin) and not player:HasBuff(buffs.demonArmor)
end

local function shouldUseMetamorphosis()
    return gameState.demonicFury >= 500 and (shouldBurst() or gameState.activeEnemies >= 3)
end

local function shouldUseSoulFire()
    return gameState.decimationActive or gameState.moltenCoreStacks > 0
end

local function needsCorruption()
    if gameState.inMetamorphosis then
        return not target:HasDeBuff(debuffs.doom) or target:DeBuffRemains(debuffs.doom) < 6000
    else
        return not target:HasDeBuff(debuffs.corruption) or target:DeBuffRemains(debuffs.corruption) < 6000
    end
end

local function getHealingTarget()
    -- Priority: player > party members by health
    if player.hp < 50 then
        return player
    end
    
    local lowestUnit = nil
    local lowestHealth = 100
    
    for i = 1, 4 do
        local unit = MakUnit:new("party" .. i)
        if unit.exists and unit.hp < lowestHealth and unit.hp < 80 then
            lowestUnit = unit
            lowestHealth = unit.hp
        end
    end
    
    return lowestUnit
end

-- Buff management
DemonArmor:Callback(function(spell)
    if needsArmor() then
        return spell:Cast(player)
    end
end)

DemonSkin:Callback(function(spell)
    if needsArmor() and gameState.isPvP then
        return spell:Cast(player)
    end
end)

-- Pet management
SummonFelguard:Callback(function(spell)
    if not gameState.hasPet then
        return spell:Cast(player)
    end
end)

SummonImp:Callback(function(spell)
    if not gameState.hasPet and gameState.isPvP then
        return spell:Cast(player)
    end
end)

-- Core damage abilities
ShadowBolt:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if player.moving then return end
    if gameState.inMetamorphosis then return end -- Use different spells in Meta

    return spell:Cast(target)
end)

SoulFire:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not shouldUseSoulFire() then return end
    if player.moving and not gameState.decimationActive then return end

    return spell:Cast(target)
end)

HandOfGuldan:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if player.moving then return end
    if gameState.inMetamorphosis then return end -- Can't use in Meta

    return spell:Cast(target)
end)

Corruption:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if gameState.inMetamorphosis then return end -- Use Doom instead
    if target:HasDeBuff(debuffs.corruption) then return end

    return spell:Cast(target)
end)

Doom:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not gameState.inMetamorphosis then return end -- Only in Meta
    if target:HasDeBuff(debuffs.doom) then return end

    return spell:Cast(target)
end)

FelFlame:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if not player.moving then return end

    return spell:Cast(target)
end)

-- Metamorphosis abilities
Carrion:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not gameState.inMetamorphosis then return end

    return spell:Cast(target)
end)

ImmolationAura:Callback(function(spell)
    if not gameState.inMetamorphosis then return end
    if gameState.activeEnemies < 2 then return end
    if player:HasBuff(buffs.immolationAura) then return end

    return spell:Cast(player)
end)

ShadowCleave:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 5 then return end
    if not gameState.inMetamorphosis then return end
    if gameState.activeEnemies < 2 then return end

    return spell:Cast(target)
end)

DemonicLeap:Callback(function(spell)
    if not gameState.inMetamorphosis then return end
    if not gameState.isPvP then return end
    if target.distance < 10 then return end

    return spell:Cast(target)
end)

-- Cooldowns
Metamorphosis:Callback(function(spell)
    if not shouldUseMetamorphosis() then return end

    return spell:Cast(player)
end)

DarkSoul:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

SummonDoomguard:Callback(function(spell)
    if not shouldBurst() then return end
    if player.moving then return end

    return spell:Cast(player)
end)

SummonInfernal:Callback(function(spell)
    if not shouldBurst() then return end
    if gameState.activeEnemies < 3 then return end
    if player.moving then return end

    return spell:Cast(target)
end)

-- Utility abilities
SpellLock:Callback(function(spell)
    if not target.exists then return end
    if not target:IsCasting() then return end
    if not target:IsInterruptible() then return end
    if target.distance > 24 then return end

    return spell:Cast(target)
end)

Fear:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if not gameState.isPvP then return end
    if player.moving then return end

    return spell:Cast(target)
end)

Banish:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not gameState.isPvP then return end
    if player.moving then return end

    return spell:Cast(target)
end)

DevourMagic:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not target:HasDispellableBuff() then return end

    return spell:Cast(target)
end)

-- Curses
CurseOfTheElements:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if target:HasDeBuff(debuffs.curseOfTheElements) then return end

    return spell:Cast(target)
end)

CurseOfWeakness:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not gameState.isPvP then return end
    if target:HasDeBuff(debuffs.curseOfWeakness) then return end

    return spell:Cast(target)
end)

CurseOfTongues:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 30 then return end
    if not gameState.isPvP then return end
    if target:HasDeBuff(debuffs.curseOfTongues) then return end

    return spell:Cast(target)
end)

-- Defensive abilities
UnendingResolve:Callback(function(spell)
    if player.hp > 40 then return end

    return spell:Cast(player)
end)

DarkRegeneration:Callback(function(spell)
    if player.hp > 50 then return end

    return spell:Cast(player)
end)

ShadowWard:Callback(function(spell)
    if player:HasBuff(buffs.shadowWard) then return end
    if player.hp > 70 then return end

    return spell:Cast(player)
end)

BurningRush:Callback(function(spell)
    if player:HasBuff(buffs.burningRush) then return end
    if not player.moving then return end
    if player.hp < 50 then return end

    return spell:Cast(player)
end)

-- Channeled abilities
DrainLife:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if player.hp > 70 then return end
    if player.moving then return end

    return spell:Cast(target)
end)

DrainSoul:Callback(function(spell)
    if not target.exists then return end
    if target.distance > 20 then return end
    if target.hp > 25 then return end
    if player.moving then return end

    return spell:Cast(target)
end)

Hellfire:Callback(function(spell)
    if gameState.activeEnemies < 4 then return end
    if player.moving then return end
    if player.hp < 50 then return end

    return spell:Cast(player)
end)

RainOfFire:Callback(function(spell)
    if gameState.activeEnemies < 3 then return end
    if player.moving then return end

    return spell:Cast(target)
end)

-- Utility
LifeTap:Callback(function(spell)
    if gameState.mana > 50 then return end
    if player.hp < 30 then return end

    return spell:Cast(player)
end)

SoulBurn:Callback(function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

-- Racial abilities
QuakingPalm:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not target.exists then return end
    if target.distance > 5 then return end

    return spell:Cast(target)
end)

BloodFury:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

ArcaneTorrent:Callback(function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if gameState.mana > 80 then return end

    return spell:Cast(player)
end)

-- PvE Single Target Rotation
local function singleTargetRotation()
    updateGameState()

    -- Maintain buffs and pet
    if DemonArmor() then return true end
    if SummonFelguard() then return true end

    -- Metamorphosis management
    if shouldUseMetamorphosis() then
        if Metamorphosis() then return true end
    end

    -- Apply DoTs based on form
    if needsCorruption() then
        if gameState.inMetamorphosis then
            if Doom() then return true end
        else
            if Corruption() then return true end
        end
    end

    -- Apply curse
    if not target:HasDeBuff(debuffs.curseOfTheElements) then
        if CurseOfTheElements() then return true end
    end

    -- Metamorphosis rotation
    if gameState.inMetamorphosis then
        -- Carrion Swarm priority
        if Carrion() then return true end

        -- Shadow Cleave for AoE
        if gameState.activeEnemies >= 2 then
            if ShadowCleave() then return true end
        end

        -- Immolation Aura for AoE
        if gameState.activeEnemies >= 2 then
            if ImmolationAura() then return true end
        end
    else
        -- Normal form rotation
        -- Soul Fire with procs
        if shouldUseSoulFire() then
            if SoulFire() then return true end
        end

        -- Hand of Gul'dan
        if HandOfGuldan() then return true end

        -- Shadow Bolt as filler
        if ShadowBolt() then return true end
    end

    -- Movement spell
    if player.moving then
        if FelFlame() then return true end
    end

    return false
end

-- PvE AoE Rotation
local function aoeRotation()
    updateGameState()

    -- Maintain buffs and pet
    if DemonArmor() then return true end
    if SummonFelguard() then return true end

    -- Metamorphosis for AoE
    if shouldUseMetamorphosis() then
        if Metamorphosis() then return true end
    end

    -- Apply DoTs to primary target
    if needsCorruption() then
        if gameState.inMetamorphosis then
            if Doom() then return true end
        else
            if Corruption() then return true end
        end
    end

    -- Metamorphosis AoE rotation
    if gameState.inMetamorphosis then
        -- Immolation Aura
        if ImmolationAura() then return true end

        -- Shadow Cleave
        if ShadowCleave() then return true end

        -- Carrion Swarm
        if Carrion() then return true end
    else
        -- Normal form AoE
        -- Rain of Fire
        if RainOfFire() then return true end

        -- Hellfire for heavy AoE
        if Hellfire() then return true end

        -- Hand of Gul'dan
        if HandOfGuldan() then return true end

        -- Soul Fire with procs
        if shouldUseSoulFire() then
            if SoulFire() then return true end
        end
    end

    -- Movement spell
    if player.moving then
        if FelFlame() then return true end
    end

    return false
end

-- PvP rotation
local function pvpRotation()
    updateGameState()

    -- Defensive priority in PvP
    if player.hp <= 30 then
        if UnendingResolve() then return true end
        if DarkRegeneration() then return true end
        if ShadowWard() then return true end
    end

    -- Interrupt priority
    if SpellLock() then return true end

    -- Maintain buffs and pet
    if DemonSkin() then return true end
    if SummonImp() then return true end

    if target.exists and target.alive then
        -- CC abilities
        if Fear() then return true end
        if Banish() then return true end

        -- Dispel enemy buffs
        if DevourMagic() then return true end

        -- Apply curses
        if not target:HasDeBuff(debuffs.curseOfTongues) then
            if CurseOfTongues() then return true end
        end

        if not target:HasDeBuff(debuffs.curseOfWeakness) then
            if CurseOfWeakness() then return true end
        end

        -- Metamorphosis for burst
        if shouldUseMetamorphosis() then
            if Metamorphosis() then return true end
        end

        -- Apply DoTs
        if needsCorruption() then
            if gameState.inMetamorphosis then
                if Doom() then return true end
            else
                if Corruption() then return true end
            end
        end

        -- Metamorphosis abilities
        if gameState.inMetamorphosis then
            if DemonicLeap() then return true end
            if Carrion() then return true end
            if ShadowCleave() then return true end
        else
            -- Soul Fire with procs
            if shouldUseSoulFire() then
                if SoulFire() then return true end
            end

            -- Hand of Gul'dan
            if HandOfGuldan() then return true end

            -- Shadow Bolt
            if ShadowBolt() then return true end
        end

        -- Movement spell
        if player.moving then
            if FelFlame() then return true end
        end
    end

    return false
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Prepare for adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Prepare mana
        if gameState.mana < 80 then
            if LifeTap() then return true end
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            if shouldBurst() then
                if DarkSoul() then return true end
                if SummonDoomguard() then return true end
                if SummonInfernal() then return true end
                if SoulBurn() then return true end
            end
        end

        -- Build Demonic Fury for Metamorphosis
        if gameState.demonicFury < 500 then
            if HandOfGuldan() then return true end
            if ShadowBolt() then return true end
        end

        -- Maintain current target
        if needsCorruption() then
            if Corruption() then return true end
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency healing
    if player.hp <= 30 then
        if UnendingResolve() then return true end
        if DarkRegeneration() then return true end
        if DrainLife() then return true end
    end

    -- Mana management
    if gameState.mana < 20 then
        if LifeTap() then return true end
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        return timeToAddsRotation()
    end

    -- PvP specific logic
    if gameState.isPvP then
        return pvpRotation()
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        return aoeRotation()
    else
        return singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Mana: ", gameState.mana)
        MakPrint(3, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(4, "In Combat: ", gameState.inCombat)
        MakPrint(5, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(6, "Is PvP: ", gameState.isPvP)
        MakPrint(7, "Demonic Fury: ", gameState.demonicFury)
        MakPrint(8, "In Metamorphosis: ", gameState.inMetamorphosis)
        MakPrint(9, "Has Pet: ", gameState.hasPet)
        MakPrint(10, "Molten Core Stacks: ", gameState.moltenCoreStacks)
        MakPrint(11, "Decimation Active: ", gameState.decimationActive)
        MakPrint(12, "In Burst Phase: ", gameState.inBurstPhase)
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if Metamorphosis:IsReady() and shouldBurst() then
            Aware:displayMessage("METAMORPHOSIS READY", "Red", 1)
        end
        if DarkSoul:IsReady() and shouldBurst() then
            Aware:displayMessage("DARK SOUL READY", "Blue", 1)
        end
        if needsArmor() then
            Aware:displayMessage("ARMOR NEEDED", "Yellow", 1)
        end
        if not gameState.hasPet then
            Aware:displayMessage("NO PET", "Orange", 1)
        end
        if shouldUseMetamorphosis() then
            Aware:displayMessage("USE METAMORPHOSIS", "Green", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
        if gameState.inMetamorphosis then
            Aware:displayMessage("METAMORPHOSIS ACTIVE", "Red", 1)
        end
        if gameState.decimationActive then
            Aware:displayMessage("DECIMATION ACTIVE", "Orange", 1)
        end
        if gameState.mana < 30 then
            Aware:displayMessage("LOW MANA", "Red", 1)
        end
        if gameState.demonicFury >= 400 then
            Aware:displayMessage("DEMONIC FURY: " .. gameState.demonicFury, "Blue", 1)
        end
        if gameState.moltenCoreStacks > 0 then
            Aware:displayMessage("MOLTEN CORE: " .. gameState.moltenCoreStacks, "Orange", 1)
        end
        if needsCorruption() then
            Aware:displayMessage("CORRUPTION/DOOM NEEDED", "Purple", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 15 then
        if UnendingResolve:IsReady() then return FrameworkEnd() end
        if DarkRegeneration:IsReady() then return FrameworkEnd() end
    end

    if player.hp <= 30 then
        if ShadowWard:IsReady() then return FrameworkEnd() end
        if DrainLife:IsReady() then return FrameworkEnd() end
    end

    -- Mana management
    if gameState.mana < 20 then
        if LifeTap() then return FrameworkEnd() end
        if ArcaneTorrent() then return FrameworkEnd() end
    end

    if target.exists and target.alive then
        -- Enhanced interrupt priority
        if target:IsCasting() and target:IsInterruptible() then
            if SpellLock() then return FrameworkEnd() end
        end

        -- PvP specific abilities
        if gameState.isPvP then
            if Fear() then return FrameworkEnd() end
            if Banish() then return FrameworkEnd() end
            if DevourMagic() then return FrameworkEnd() end
            if CurseOfTongues() then return FrameworkEnd() end
        end

        -- Burst phase
        if shouldBurst() then
            if Metamorphosis() then return FrameworkEnd() end
            if DarkSoul() then return FrameworkEnd() end
            if SummonDoomguard() then return FrameworkEnd() end
            if SummonInfernal() then return FrameworkEnd() end
            if SoulBurn() then return FrameworkEnd() end

            -- Racial abilities during burst
            if BloodFury() then return FrameworkEnd() end
            if Berserking() then return FrameworkEnd() end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    SpellLock("arena", enemy)

    -- CC abilities
    Fear("arena", enemy)
    Banish("arena", enemy)

    -- Offensive dispel
    DevourMagic("arena", enemy)

    -- Burst damage
    if shouldBurst() then
        Metamorphosis("arena")
        DarkSoul("arena")
        SummonDoomguard("arena")
        SoulBurn("arena")
    end

    -- Core rotation
    if needsCorruption() then
        if gameState.inMetamorphosis then
            Doom("arena", enemy)
        else
            Corruption("arena", enemy)
        end
    end

    if not enemy:HasDeBuff(debuffs.curseOfTongues) then
        CurseOfTongues("arena", enemy)
    end

    if gameState.inMetamorphosis then
        Carrion("arena", enemy)
        ShadowCleave("arena", enemy)
        DemonicLeap("arena", enemy)
    else
        if shouldUseSoulFire() then
            SoulFire("arena", enemy)
        end
        HandOfGuldan("arena", enemy)
        ShadowBolt("arena", enemy)
    end

    -- Movement spell
    if player.moving then
        FelFlame("arena", enemy)
    end
end

local function enhancedPartyRotation(friendly)
    if not friendly.exists then return end

    -- No direct healing for Demonology, focus on utility
    -- Could add utility spells here if needed
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        SpellLock()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Demonology Warlock
SpellLock:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 24 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

Fear:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:HasDeBuff(debuffs.fear) then return end
    if player.moving then return end

    -- Use on healers or high priority targets
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Fear - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

Banish:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.banish) then return end
    if player.moving then return end

    -- Banish in arena
    Aware:displayMessage("Banish - CC", "Purple", 1)
    return spell:Cast(enemy)
end)

DevourMagic:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if not enemy:HasDispellableBuff() then return end

    -- Offensive dispel in arena
    Aware:displayMessage("Devour Magic - Dispel", "Orange", 1)
    return spell:Cast(enemy)
end)

ShadowBolt:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if player.moving then return end
    if gameState.inMetamorphosis then return end

    return spell:Cast(enemy)
end)

SoulFire:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if not shouldUseSoulFire() then return end

    -- Priority with procs
    if gameState.decimationActive then
        Aware:displayMessage("Soul Fire - Decimation", "Red", 1)
    elseif gameState.moltenCoreStacks > 0 then
        Aware:displayMessage("Soul Fire - Molten Core", "Orange", 1)
    end
    return spell:Cast(enemy)
end)

HandOfGuldan:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if player.moving then return end
    if gameState.inMetamorphosis then return end

    return spell:Cast(enemy)
end)

Corruption:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if gameState.inMetamorphosis then return end
    if enemy:HasDeBuff(debuffs.corruption) then return end

    return spell:Cast(enemy)
end)

Doom:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if not gameState.inMetamorphosis then return end
    if enemy:HasDeBuff(debuffs.doom) then return end

    -- High damage DoT in Meta
    Aware:displayMessage("Doom - Meta DoT", "Purple", 1)
    return spell:Cast(enemy)
end)

Carrion:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if not gameState.inMetamorphosis then return end

    -- Meta ability
    Aware:displayMessage("Carrion Swarm - Meta", "Red", 1)
    return spell:Cast(enemy)
end)

ShadowCleave:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if not gameState.inMetamorphosis then return end

    -- Melee Meta ability
    Aware:displayMessage("Shadow Cleave - Meta Melee", "Purple", 1)
    return spell:Cast(enemy)
end)

DemonicLeap:Callback("arena", function(spell, enemy)
    if not gameState.inMetamorphosis then return end
    if enemy.distance < 10 then return end

    -- Gap closer in Meta
    Aware:displayMessage("Demonic Leap - Gap Closer", "Blue", 1)
    return spell:Cast(enemy)
end)

FelFlame:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end

    -- Movement spell
    Aware:displayMessage("Fel Flame - Movement", "Green", 1)
    return spell:Cast(enemy)
end)

CurseOfTongues:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.curseOfTongues) then return end

    -- Slow casting in arena
    Aware:displayMessage("Curse of Tongues - Slow Cast", "Yellow", 1)
    return spell:Cast(enemy)
end)

CurseOfWeakness:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.curseOfWeakness) then return end

    return spell:Cast(enemy)
end)

Metamorphosis:Callback("arena", function(spell)
    if not shouldUseMetamorphosis() then return end

    -- Major transformation
    Aware:displayMessage("Metamorphosis - Transform", "Red", 1)
    return spell:Cast(player)
end)

DarkSoul:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Dark Soul - Burst", "Purple", 1)
    return spell:Cast(player)
end)

SummonDoomguard:Callback("arena", function(spell)
    if not shouldBurst() then return end
    if player.moving then return end

    -- Major summon
    Aware:displayMessage("Doomguard - Major Pet", "Red", 1)
    return spell:Cast(player)
end)

SoulBurn:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Burst enhancement
    Aware:displayMessage("Soul Burn - Enhancement", "Orange", 1)
    return spell:Cast(player)
end)

UnendingResolve:Callback("arena", function(spell)
    if player.hp > 40 then return end

    -- Defensive cooldown
    Aware:displayMessage("Unending Resolve - Defensive", "Blue", 1)
    return spell:Cast(player)
end)


