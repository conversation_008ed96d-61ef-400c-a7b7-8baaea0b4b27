-- APL UPDATE MoP Destruction Warlock
-- Mists of Pandaria Destruction Warlock Rotation

-- Check if MakuluValidCheck exists before calling it
if MakuluValidCheck and not MakuluValidCheck() then return true end
if Maku<PERSON>_magic_number and Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Destruction spec (talent tree 3 for Warlock in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 3 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Destruction Warlock Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752, MAKULU_INFO = {ignoreCasting = true } },
    Stoneform = { ID = 20594, MAKULU_INFO = {ignoreCasting = true } },
    Shadowmeld = { ID = 58984, MAKULU_INFO = {ignoreCasting = true } },
    EscapeArtist = { ID = 20589, MAKULU_INFO = {ignoreCasting = true } },
    GiftOfTheNaaru = { ID = 59544, MAKULU_INFO = {ignoreCasting = true } },
    Darkflight = { ID = 68992, MAKULU_INFO = {ignoreCasting = true } },
    BloodFury = { ID = 20572, MAKULU_INFO = {ignoreCasting = true } },
    WillOfTheForsaken = { ID = 7744, MAKULU_INFO = {ignoreCasting = true } },
    WarStomp = { ID = 20549, MAKULU_INFO = {ignoreCasting = true } },
    Berserking = { ID = 26297, MAKULU_INFO = {ignoreCasting = true } },
    ArcaneTorrent = { ID = 50613, MAKULU_INFO = {ignoreCasting = true } },
    
    -- MoP Destruction Warlock Core Abilities
    Incinerate = { ID = 29722, MAKULU_INFO = { damageType = "fire", castTime = 2500 } },
    ChaosBolt = { ID = 116858, MAKULU_INFO = { damageType = "chaos", castTime = 3000 } },
    Conflagrate = { ID = 17962, MAKULU_INFO = { damageType = "fire" } },
    Immolate = { ID = 348, MAKULU_INFO = { damageType = "fire", castTime = 2000 } },
    SoulFire = { ID = 6353, MAKULU_INFO = { damageType = "fire", castTime = 4000 } },
    Shadowburn = { ID = 17877, MAKULU_INFO = { damageType = "shadow" } },
    
    -- AoE Abilities
    RainOfFire = { ID = 5740, MAKULU_INFO = { damageType = "fire", castTime = 3000 } },
    Hellfire = { ID = 1949, MAKULU_INFO = { damageType = "fire", channeled = true } },
    
    -- Curses
    CurseOfTheElements = { ID = 1490, MAKULU_INFO = { damageType = "shadow" } },
    CurseOfDoom = { ID = 603, MAKULU_INFO = { damageType = "shadow" } },
    CurseOfAgony = { ID = 980, MAKULU_INFO = { damageType = "shadow" } },
    CurseOfExhaustion = { ID = 18223, MAKULU_INFO = { targeted = false } },
    CurseOfTongues = { ID = 1714, MAKULU_INFO = { targeted = false } },
    CurseOfWeakness = { ID = 702, MAKULU_INFO = { targeted = false } },
    
    -- Cooldowns and Utilities
    DarkSoul = { ID = 113858, MAKULU_INFO = { targeted = false } },
    Metamorphosis = { ID = 103958, MAKULU_INFO = { targeted = false } },
    SummonDoomguard = { ID = 18540, MAKULU_INFO = { targeted = false, castTime = 10000 } },
    SummonInfernal = { ID = 1122, MAKULU_INFO = { targeted = false, castTime = 6000 } },
    
    -- Defensive Abilities
    DemonSkin = { ID = 687, MAKULU_INFO = { targeted = false } },
    DemonArmor = { ID = 706, MAKULU_INFO = { targeted = false } },
    UnendingResolve = { ID = 104773, MAKULU_INFO = { targeted = false } },
    DarkRegeneration = { ID = 108359, MAKULU_INFO = { targeted = false } },
    
    -- Utility Spells
    Fear = { ID = 5782, MAKULU_INFO = { castTime = 1500 } },
    Banish = { ID = 710, MAKULU_INFO = { castTime = 1500 } },
    SpellLock = { ID = 19647, MAKULU_INFO = { targeted = false } },
    ShadowWard = { ID = 6229, MAKULU_INFO = { targeted = false } },
    
    -- Pet Abilities
    SummonImp = { ID = 688, MAKULU_INFO = { castTime = 6000 } },
    SummonVoidwalker = { ID = 697, MAKULU_INFO = { castTime = 6000 } },
    SummonSuccubus = { ID = 712, MAKULU_INFO = { castTime = 6000 } },
    SummonFelhunter = { ID = 691, MAKULU_INFO = { castTime = 6000 } },
    
    -- Movement
    BurningRush = { ID = 111400, MAKULU_INFO = { targeted = false } },
    
    -- MoP Specific Abilities
    HavocMoP = { ID = 80240, MAKULU_INFO = { targeted = false } },
    KilJaedensKunning = { ID = 137587, MAKULU_INFO = { targeted = false } },
    
    -- Metamorphosis abilities (when transformed)
    Doom = { ID = 603, MAKULU_INFO = { damageType = "shadow" } },
    Carrion = { ID = 104316, MAKULU_INFO = { damageType = "shadow" } },

    -- Potions and consumables
    TemperedPotion1 = { Type = "Potion", ID = 171263, QueueForbidden = true },
    TemperedPotion2 = { Type = "Potion", ID = 171264, QueueForbidden = true },
    TemperedPotion3 = { Type = "Potion", ID = 171265, QueueForbidden = true },
    PotionofUnwaveringFocus1 = { Type = "Potion", ID = 171266, QueueForbidden = true },
    PotionofUnwaveringFocus2 = { Type = "Potion", ID = 171267, QueueForbidden = true },
    PotionofUnwaveringFocus3 = { Type = "Potion", ID = 171268, QueueForbidden = true },

    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 19647, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 5782, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local function createAction(actionData)
    return Action.Create(actionData)
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
    local result = {}
    for k, v in pairs(A) do
        result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
    end
    return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_WARLOCK_DESTRUCTION] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")

-- MoP Destruction Warlock Buffs
local buffs = {
    demonSkin = 687,
    demonArmor = 706,
    darkSoul = 113858,
    metamorphosis = 103958,
    backdraft = 117828,
    moltenCore = 122355,
    decimation = 63165,
    shadowWard = 6229,
    unendingResolve = 104773,
    darkRegeneration = 108359,
    burningRush = 111400,
    kilJaedensKunning = 137587,
}

-- MoP Destruction Warlock Debuffs
local debuffs = {
    immolate = 348,
    curseOfTheElements = 1490,
    curseOfDoom = 603,
    curseOfAgony = 980,
    curseOfExhaustion = 18223,
    curseOfTongues = 1714,
    curseOfWeakness = 702,
    fear = 5782,
    banish = 710,
    havoc = 80240,
    doom = 603,
}

-- Game state tracking (enhanced with new functionality)
local gameState = {
    activeEnemies = 1,
    executePhase = false,
    inCombat = false,
    burningEmbers = 0,
    metamorphosisActive = false,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
    shouldBurst = false,
    fightRemains = 999,
    shouldAoE = false,
    shouldCleave = false,
    backdraftStacks = 0,
    moltenCoreStacks = 0,
    imCasting = nil,
    darkSoulUp = false,
    immolateRemains = 0,
    havocTarget = nil,
}

local function updategs()
    gameState.inCombat = player:InCombat()
    gameState.executePhase = target.hp <= 25 -- MoP execute threshold
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.burningEmbers = UnitPower(player:CallerId(), 14) -- Burning Embers power type
    gameState.metamorphosisActive = player:HasBuff(buffs.metamorphosis)
    gameState.isPvP = Action.IsInPvP or false
    gameState.channeling = player:IsCasting() or player:IsChanneling()
    gameState.shouldBurst = A.GetToggle(2, "BurstMode")

    -- Enhanced tracking
    gameState.fightRemains = target.exists and target.timeToDie or 999
    gameState.shouldAoE = gameState.activeEnemies > 2
    gameState.shouldCleave = gameState.activeEnemies > 1
    gameState.backdraftStacks = player:BuffStacks(buffs.backdraft) or 0
    gameState.moltenCoreStacks = player:BuffStacks(buffs.moltenCore) or 0
    gameState.imCasting = player:IsCasting() and player:GetCastingSpell() or nil
    gameState.darkSoulUp = player:HasBuff(buffs.darkSoul)
    gameState.immolateRemains = target.exists and target:DebuffRemains(debuffs.immolate) or 0

    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Alias for compatibility
local updateGameState = updategs
local gs = gameState

-- Utility functions (enhanced)
local function shouldBurst()
    return gameState.shouldBurst
end

local function shouldAoE()
    return gameState.activeEnemies > 2
end

local function hasBurningEmbers(amount)
    return gameState.burningEmbers >= (amount or 10)
end

local function isSpellInFlight(spell, range)
    return spell:IsSpellInFlight() or false
end

local function makInterrupt(interrupts)
    if not interrupts then return end
    for _, interrupt in pairs(interrupts) do
        if interrupt and interrupt:IsReady() then
            interrupt()
        end
    end
end

-- PvP utility functions
local function shouldInterrupt(enemy)
    if not enemy or not enemy.exists then return false end
    return enemy:IsCasting() and enemy:IsInterruptible()
end

local function shouldFear(enemy)
    if not enemy or not enemy.exists then return false end
    return not enemy:HasDebuff(debuffs.fear) and enemy.hp > 30
end

-- Armor management
DemonArmor:Callback(function(spell)
    if not player:HasBuff(buffs.demonArmor) and not player:HasBuff(buffs.demonSkin) then
        return spell:Cast(player)
    end
end)

-- Pet summoning
SummonImp:Callback(function(spell)
    if not player:HasPet() and not player:InCombat() then
        return spell:Cast(player)
    end
end)

-- Curse management
CurseOfTheElements:Callback(function(spell)
    if not target:HasDeBuff(debuffs.curseOfTheElements) and target.exists then
        return spell:Cast(target)
    end
end)

-- Immolate DoT management (enhanced)
Immolate:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if not target:HasDebuff(debuffs.immolate) or gs.immolateRemains <= 3000 then
        return spell:Cast(target)
    end
end)

-- Conflagrate - High priority (enhanced)
Conflagrate:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if target:HasDebuff(debuffs.immolate) then
        return spell:Cast(target)
    end
end)

-- Chaos Bolt - Main nuke (enhanced)
ChaosBolt:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if hasBurningEmbers(10) and (gs.darkSoulUp or gs.backdraftStacks > 0) then
        return spell:Cast(target)
    end
end)

-- Soul Fire with Decimation proc (enhanced)
SoulFire:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if player:HasBuff(buffs.decimation) or gs.executePhase then
        return spell:Cast(target)
    end
end)

-- Shadowburn in execute phase (enhanced)
Shadowburn:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gs.executePhase and hasBurningEmbers(5) then
        return spell:Cast(target)
    end
end)

-- Incinerate as filler (enhanced)
Incinerate:Callback(function(spell)
    if not target.exists or not target.canAttack then return end
    if gs.channeling then return end
    return spell:Cast(target)
end)

-- Enhanced PvP callbacks
SpellLock:Callback("arena", function(spell, enemy)
    if not enemy.pvpKick then return end

    return spell:Cast(enemy)
end)

Fear:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDebuff(debuffs.fear) then return end
    if enemy.hp < 30 then return end
    if gs.imCasting and gs.imCasting == spell.id then return end

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Fear - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on DPS targeting low health allies
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Fear - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

Immolate:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if enemy:DebuffRemains(debuffs.immolate, true) > 5000 then return end

    return spell:Cast(enemy)
end)

Conflagrate:Callback("arena", function(spell, enemy)
    if enemy.distance > 40 then return end
    if not enemy:HasDebuff(debuffs.immolate) then return end

    return spell:Cast(enemy)
end)

-- Cooldowns
DarkSoul:Callback(function(spell)
    if shouldBurst() and target:HasDeBuff(debuffs.immolate) then
        return spell:Cast(player)
    end
end)

Metamorphosis:Callback(function(spell)
    if shouldBurst() and hasBurningEmbers(40) then
        return spell:Cast(player)
    end
end)

-- AoE abilities
RainOfFire:Callback(function(spell)
    if shouldAoE() and gameState.activeEnemies >= 3 and hasBurningEmbers(30) then
        return spell:Cast(target)
    end
end)

Hellfire:Callback(function(spell)
    if shouldAoE() and gameState.activeEnemies >= 4 and not player:IsMoving() then
        return spell:Cast(player)
    end
end)

-- Interrupt
SpellLock:Callback(function(spell)
    if target:IsCasting() and target:IsInterruptible() then
        return spell:Cast(target)
    end
end)

-- Fear for CC
Fear:Callback(function(spell)
    if not target:HasDeBuff(debuffs.fear) and target.hp > 50 then
        return spell:Cast(target)
    end
end)

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and gameState.burningEmbers < 20 then
        return spell:Cast(player)
    end
end)

Berserking:Callback(function(spell)
    if shouldBurst() and target:HasDeBuff(debuffs.immolate) then
        return spell:Cast(player)
    end
end)

BloodFury:Callback(function(spell)
    if shouldBurst() and target:HasDeBuff(debuffs.immolate) then
        return spell:Cast(player)
    end
end)

-- Defensive abilities
UnendingResolve:Callback(function(spell)
    if player.hp <= 40 then
        return spell:Cast(player)
    end
end)

DarkRegeneration:Callback(function(spell)
    if player.hp <= 50 then
        return spell:Cast(player)
    end
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
end

local function defensives()
    UnendingResolve()
    DarkRegeneration()
end

local function baseStuff()
    DemonArmor()
    SummonImp()
    CurseOfTheElements()
end

-- Single target rotation
local function singleTargetRotation()
    -- Priority order for single target MoP Destruction
    Conflagrate()
    ChaosBolt()
    SoulFire()
    Shadowburn()
    Immolate()
    Incinerate()
end

-- AoE rotation
local function aoeRotation()
    RainOfFire()
    Hellfire()
    Immolate()
    Conflagrate()
    Incinerate()
end

-- Metamorphosis rotation (when transformed)
local function metamorphosisRotation()
    if gameState.metamorphosisActive then
        -- Use Metamorphosis-specific abilities
        Doom()
        Carrion()
        -- Fall back to normal rotation
        singleTargetRotation()
    end
end

-- Arena specific functions
local function arenaRotation(enemy)
    if not enemy.exists then return end

    -- Interrupt priority in arena
    if enemy:IsCasting() and enemy:IsInterruptible() then
        SpellLock()
    end

    -- CC abilities
    if enemy.hp > 70 and not enemy:HasDeBuff(debuffs.fear) then
        Fear()
    end

    -- Curse management in arena
    if not enemy:HasDeBuff(debuffs.curseOfExhaustion) then
        CurseOfExhaustion()
    end

    -- DoT management
    if not enemy:HasDeBuff(debuffs.immolate) then
        Immolate()
    end
end

local function partyRotation(friendly)
    if not friendly.exists then return end

    -- No specific party abilities for MoP Destruction
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Base maintenance
    baseStuff()

    -- Defensive abilities
    defensives()

    if target.exists and target.alive then
        -- Interrupt priority
        SpellLock()

        -- Racial abilities
        racials()

        -- Cooldowns
        if shouldBurst() then
            DarkSoul()
            Metamorphosis()
        end

        -- Choose rotation based on state and enemy count
        if gameState.metamorphosisActive then
            metamorphosisRotation()
        elseif shouldAoE() then
            aoeRotation()
        else
            singleTargetRotation()
        end
    end
end

-- Main function
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Burning Embers: ", gameState.burningEmbers)
        MakPrint(2, "Chaos Bolt Ready: ", ChaosBolt:IsReady())
        MakPrint(3, "Soul Fire Ready: ", SoulFire:IsReady())
        MakPrint(4, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(5, "Should AoE: ", shouldAoE())
        MakPrint(6, "Metamorphosis Active: ", gameState.metamorphosisActive)
        MakPrint(7, "Spell Lock Ready: ", SpellLock:IsReady())
        MakPrint(8, "Execute Phase: ", gameState.executePhase)
        MakPrint(9, "In Combat: ", gameState.inCombat)
        MakPrint(10, "Target HP: ", target.hp)
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if DarkSoul:IsReady() and shouldBurst() and player:InCombat() then
            Aware:displayMessage("DARK SOUL READY", "Red", 1)
        end
        if Metamorphosis:IsReady() and shouldBurst() and hasBurningEmbers(40) then
            Aware:displayMessage("METAMORPHOSIS READY", "Purple", 1)
        end
    end

    BurningRush()

    if player:IsChanneling() then return end

    -- MoP specific interrupts
    if target:IsCasting() and target:IsInterruptible() then
        SpellLock()
    end

    if player:IsCasting() and player:CastRemains() > 500 then return end

    -- Base maintenance
    baseStuff()
    defensives()

    if target.exists and target.canAttack and ChaosBolt:InRange(target) then

        -- PvP specific abilities
        if A.IsInPvP then
            if A.Zone ~= "arena" then
                SpellLock()
                CurseOfExhaustion()
                CurseOfTongues()
                CurseOfWeakness()
            end
        end

        -- Pre-combat casting
        if not player:InCombat() then
            SoulFire()
            Immolate()
            Incinerate()
        end

        -- Burst phase
        if shouldBurst() then
            DarkSoul()
            Metamorphosis()

            -- Trinket usage
            local damagePotion = Action.GetToggle(2, "damagePotion")
            if damagePotion and player:HasBuff(buffs.darkSoul) then
                -- Use damage potions during Dark Soul
            end
        end

        -- Rotation selection
        if gameState.metamorphosisActive then
            metamorphosisRotation()
        elseif shouldAoE() then
            aoeRotation()
        else
            singleTargetRotation()
        end
    end

    return FrameworkEnd()
end

-- Arena functions
A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        SpellLock()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Enhanced rotation functions
local function st()
    -- Single target rotation
    updategs()

    -- Maintain Immolate
    if gs.immolateRemains < 3000 then
        Immolate()
    end

    -- Conflagrate with Immolate up
    if target:HasDebuff(debuffs.immolate) then
        Conflagrate()
    end

    -- Chaos Bolt with procs or burst
    if hasBurningEmbers(10) and (gs.darkSoulUp or gs.backdraftStacks > 0) then
        ChaosBolt()
    end

    -- Soul Fire with Decimation
    if player:HasBuff(buffs.decimation) or gs.executePhase then
        SoulFire()
    end

    -- Shadowburn in execute
    if gs.executePhase and hasBurningEmbers(5) then
        Shadowburn()
    end

    -- Incinerate filler
    Incinerate()
end

local function aoe()
    -- AoE rotation
    updategs()

    -- Rain of Fire for AoE
    if gs.shouldAoE then
        RainOfFire()
    end

    -- Maintain Immolate on primary target
    if gs.immolateRemains < 3000 then
        Immolate()
    end

    -- Conflagrate for Backdraft
    Conflagrate()

    -- Chaos Bolt with procs
    if hasBurningEmbers(10) and gs.backdraftStacks > 0 then
        ChaosBolt()
    end

    -- Incinerate filler
    Incinerate()
end

local function cleave()
    -- Cleave rotation (2-3 enemies)
    updategs()

    -- Havoc for cleave damage
    HavocMoP()

    -- Maintain Immolate
    if gs.immolateRemains < 3000 then
        Immolate()
    end

    -- Conflagrate
    Conflagrate()

    -- Chaos Bolt with Havoc
    if hasBurningEmbers(10) and target:HasDebuff(debuffs.havoc) then
        ChaosBolt()
    end

    -- Incinerate filler
    Incinerate()
end

local function ogcd()
    -- Off-global cooldown abilities
    if shouldBurst() then
        DarkSoul()
        Metamorphosis()
    end

    -- Racial abilities
    racials()
end

local function eof()
    -- End of fight logic
    if gs.fightRemains < 30000 then
        -- Burn Burning Embers
        if hasBurningEmbers(10) then
            ChaosBolt()
        end
        if gs.executePhase then
            Shadowburn()
        end
    end
end

local function pvpenis()
    -- PvP specific logic placeholder
    -- This would contain PvP-specific rotation logic
end

-- Enhanced A[3] function with new functionality
A[3] = function(icon)
    -- Safety check for framework initialization
    if not FrameworkStart then return end

    FrameworkStart(icon)
    updategs()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Burning Embers: ", gs.burningEmbers)
        MakPrint(2, "Backdraft Stacks: ", gs.backdraftStacks)
        MakPrint(3, "Molten Core Stacks: ", gs.moltenCoreStacks)
        MakPrint(4, "Immolate Remains: ", gs.immolateRemains)
        MakPrint(5, "Dark Soul Active: ", gs.darkSoulUp)
        MakPrint(6, "Fight Remains: ", gs.fightRemains)
        MakPrint(7, "Should AoE: ", gs.shouldAoE)
        MakPrint(8, "Should Cleave: ", gs.shouldCleave)
        MakPrint(9, "Time to Adds: ", gs.timeToAdds)
        MakPrint(10, "Spell Lock Learned: ", SpellLock:IsKnown())
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then -- Dark Soul ready
        if DarkSoul:IsReady() and hasBurningEmbers(20) and player.inCombat then
            Aware:displayMessage("DARK SOUL READY", "Purple", 1)
        end
    end

    -- Interrupt handling
    local interrupts = {SpellLock}
    makInterrupt(interrupts)

    -- Emergency utility management
    DemonArmor()
    SummonImp()
    UnendingResolve()

    if target.exists and target.canAttack and (Incinerate:InRange(target) or Immolate:InRange(target)) then
        if A.IsInPvP then
            SpellLock("bg")
            Fear("bg")
            pvpenis()
        end

        if player.channeling and gs.imCasting and gs.imCasting == RainOfFire.id then return end

        local damagePotion = Action.GetToggle(2, "damagePotion")
        local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
        local potionExhausted = Action.GetToggle(2, "potionExhausted")
        local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")
        local damagePotionObject = Action.DetermineUsableObject("player", nil, nil, true, nil, A.TemperedPotion1, A.TemperedPotion2, A.TemperedPotion3, A.PotionofUnwaveringFocus1, A.PotionofUnwaveringFocus2, A.PotionofUnwaveringFocus3)

        if damagePotionObject and damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
            local shouldPot = hasBurningEmbers(20) and gs.darkSoulUp
            if shouldPot then
                return damagePotionObject:Show(icon)
            end
        end

        if gs.shouldAoE then
            aoe()
        end

        ogcd()
        eof()

        if gs.shouldCleave then
            cleave()
        end

        st()

    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP
CurseOfExhaustion:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.curseOfExhaustion) then return end
    if enemy.hp < 20 then return end

    return spell:Cast(enemy)
end)

CurseOfTongues:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.curseOfTongues) then return end
    if not enemy:IsCasting() then return end

    return spell:Cast(enemy)
end)

CurseOfWeakness:Callback("arena", function(spell, enemy)
    if enemy.distance > 30 then return end
    if enemy:HasDeBuff(debuffs.curseOfWeakness) then return end
    if enemy.hp < 30 then return end

    return spell:Cast(enemy)
end)

local function fearDuration()
    for i = 1, 3 do
        local enemy = "arena" .. i
        local arenaUnit = MakUnit:new(enemy)
        if arenaUnit.exists and arenaUnit:HasDeBuff(debuffs.fear) then
            return arenaUnit:DebuffRemains(debuffs.fear)
        end
    end
    return 0
end

Fear:Callback("arena", function(spell, enemy)
    if enemy:IsTarget() then return end
    if enemy.distance > 30 then return end
    if fearDuration() > spell:CastTime() then return end
    if enemy:HasDeBuff(debuffs.fear) then return end

    -- Fear healers priority
    if enemy.isHealer then
        Aware:displayMessage("Fearing Healer", "Green", 1)
        return spell:Cast(enemy)
    end

    -- Peel for low health party members
    local peelParty = (party1.exists and party1.hp < 50) or (party2.exists and party2.hp < 50)
    if peelParty and enemy.hp > 40 then
        Aware:displayMessage("Fearing To Peel", "Red", 1)
        return spell:Cast(enemy)
    end
end)

SpellLock:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 30 then return end

    return spell:Cast(enemy)
end)

HavocMoP:Callback("arena", function(spell, enemy)
    if enemy:IsTarget() then return end
    if enemy:HasDeBuff(debuffs.havoc) then return end
    if arena3.exists and arena3.hp > 0 and enemy.isHealer then return end

    -- Use Havoc for cleave damage
    if target:HasDeBuff(debuffs.immolate) and hasBurningEmbers(10) then
        return spell:Cast(enemy)
    end
end)

Immolate:Callback("arena", function(spell, enemy)
    if enemy:DebuffRemains(debuffs.immolate) > 4000 then return end
    if enemy.distance > 30 then return end

    return spell:Cast(enemy)
end)

local enemyRotation = function(enemy)
    if not enemy.exists then return end
    SpellLock("arena", enemy)
    Fear("arena", enemy)
    HavocMoP("arena", enemy)
    Immolate("arena", enemy)
    CurseOfExhaustion("arena", enemy)
    CurseOfTongues("arena", enemy)
    CurseOfWeakness("arena", enemy)
end

local partyRotation = function(friendly)
    if not friendly.exists then return end
    -- No specific party abilities for MoP Destruction
end

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and hasBurningEmbers(30) then
        return spell:Cast(player)
    end
end)

Berserking:Callback(function(spell)
    if shouldBurst() and (target.hp > 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

BloodFury:Callback(function(spell)
    if shouldBurst() and (target.hp > 50 or gameState.isPvP) then
        return spell:Cast(player)
    end
end)

WillOfTheForsaken:Callback(function(spell)
    if player:HasDebuffType("Fear") or player:HasDebuffType("Charm") then
        return spell:Cast(player)
    end
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    WillOfTheForsaken()
end

local function mopTalents()
    DarkSoul()
    Metamorphosis()
    HavocMoP()
    KilJaedensKunning()
end

local function baseStuff()
    DemonArmor()
    SummonImp()
    UnendingResolve()
    DarkRegeneration()
end

-- Enhanced utility for MoP
local function mopUtility()
    SpellLock()
    Fear()
    Banish()
    CurseOfTheElements()
    ShadowWard()
end
