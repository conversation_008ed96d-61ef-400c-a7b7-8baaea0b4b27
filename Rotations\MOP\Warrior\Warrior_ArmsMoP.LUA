-- APL UPDATE MoP Arms Warrior
-- Mists of Pandaria Arms Warrior Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

if WOW_PROJECT_ID ~= WOW_PROJECT_MISTS_CLASSIC then return end

-- Check if player is Arms spec (talent tree 1 for Warrior in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 1 and talentTree ~= 5 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Arms Warrior Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    
    -- MoP Arms Warrior Core Abilities
    MortalStrike = { ID = 12294, MAKULU_INFO = { damageType = "physical" } },
    ColossusSmash = { ID = 86346, MAKULU_INFO = { damageType = "physical" } },
    Overpower = { ID = 7384, MAKULU_INFO = { damageType = "physical" } },
    Execute = { ID = 5308, MAKULU_INFO = { damageType = "physical" } },
    Slam = { ID = 1464, MAKULU_INFO = { damageType = "physical" } },
    Rend = { ID = 772, MAKULU_INFO = { damageType = "physical" } },
    HeroicStrike = { ID = 78, MAKULU_INFO = { damageType = "physical" } },
    Cleave = { ID = 845, MAKULU_INFO = { damageType = "physical" } },
    
    -- Stances and Shouts
    BattleStance = { ID = 2457, MAKULU_INFO = { targeted = false } },
    DefensiveStance = { ID = 71, MAKULU_INFO = { targeted = false } },
    BerserkerStance = { ID = 2458, MAKULU_INFO = { targeted = false } },
    BattleShout = { ID = 6673, MAKULU_INFO = { targeted = false } },
    CommandingShout = { ID = 469, MAKULU_INFO = { targeted = false } },
    DemoralizingShout = { ID = 1160, MAKULU_INFO = { targeted = false } },
    
    -- Cooldowns and Utilities
    Recklessness = { ID = 1719, MAKULU_INFO = { targeted = false } },
    BerserkerRage = { ID = 18499, MAKULU_INFO = { targeted = false } },
    ShatteringThrow = { ID = 64382, MAKULU_INFO = { damageType = "physical" } },
    Bladestorm = { ID = 46924, MAKULU_INFO = { targeted = false } },
    Avatar = { ID = 107574, MAKULU_INFO = { targeted = false } },
    Sweepingstrikes = { ID = 12328, MAKULU_INFO = { targeted = false } },
    SkullBanner = { ID = 114207, MAKULU_INFO = { targeted = false } },
    
    -- Movement and Utility
    Charge = { ID = 100, MAKULU_INFO = { damageType = "physical" } },
    Intercept = { ID = 20252, MAKULU_INFO = { damageType = "physical" } },
    HeroicLeap = { ID = 6544, MAKULU_INFO = { targeted = false } },
    HeroicThrow = { ID = 57755, MAKULU_INFO = { damageType = "physical" } },
    
    -- Defensive Abilities
    LastStand = { ID = 12975, MAKULU_INFO = { targeted = false } },
    EnragedRegeneration = { ID = 55694, MAKULU_INFO = { targeted = false } },
    
    -- Interrupts and CC
    Pummel = { ID = 6552, MAKULU_INFO = { damageType = "physical" } },
    IntimidatingShout = { ID = 5246, MAKULU_INFO = { targeted = false } },
    Disarm = { ID = 676, MAKULU_INFO = { targeted = false } },
    
    -- Hamstring and other utilities
    Hamstring = { ID = 1715, MAKULU_INFO = { damageType = "physical" } },
    ThunderClap = { ID = 6343, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP Specific Abilities
    StormBolt = { ID = 107570, MAKULU_INFO = { damageType = "physical" } },
    DragonRoar = { ID = 118000, MAKULU_INFO = { damageType = "physical" } },
    Shockwave = { ID = 46968, MAKULU_INFO = { damageType = "physical" } },
    VictoryRush = { ID = 34428, MAKULU_INFO = { damageType = "physical" } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 6552, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 107570, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")
local pet = MakUnit:new("pet")

-- MoP Arms Warrior Buffs
local buffs = {
    battleStance = 2457,
    defensiveStance = 71,
    berserkerStance = 2458,
    battleShout = 6673,
    commandingShout = 469,
    recklessness = 1719,
    berserkerRage = 18499,
    enrage = 12880,
    sweepingStrikes = 12328,
    suddenDeath = 52437,
    overpower = 60503,
    avatar = 107574,
    bladestorm = 46924,
    enragedRegeneration = 55694,
    lastStand = 12975,
    skullBanner = 114207,
    victorious = 32216,
}

-- MoP Arms Warrior Debuffs
local debuffs = {
    colossusSmash = 86346,
    rend = 772,
    hamstring = 1715,
    thunderClap = 6343,
    demoralizingShout = 1160,
    intimidatingShout = 5246,
    disarm = 676,
}

-- Game state tracking (enhanced with new functionality)
local gameState = {
    activeEnemies = 1,
    executePhase = false,
    inCombat = false,
    rage = 0,
    timeToAdds = 999,
    isPvP = false,
    channeling = false,
    shouldBurst = false,
    fightRemains = 999,
    shouldAoE = false,
    shouldCleave = false,
    imCasting = nil,
    lastStanceCheck = 0,  -- Track last stance check to prevent spam
}

-- Utility functions that need to be defined before updategs()
local function getDoTCount(debuffID)
    local count = 0
    if target.exists and target:HasDeBuff(debuffID) then count = count + 1 end

    -- Check nearby enemies for DoT count
    for i = 1, gameState.activeEnemies do
        local enemy = MakUnit:new("nameplate" .. i)
        if enemy.exists and enemy:HasDeBuff(debuffID) then
            count = count + 1
        end
    end

    return count
end

local function getLowestDoTTarget(debuffID)
    local lowestTarget = target
    local lowestRemaining = target.exists and target:DebuffRemains(debuffID, true) or 0

    for i = 1, gameState.activeEnemies do
        local enemy = MakUnit:new("nameplate" .. i)
        if enemy.exists then
            local remaining = enemy:DebuffRemains(debuffID, true)
            if remaining < lowestRemaining then
                lowestTarget = enemy
                lowestRemaining = remaining
            end
        end
    end

    return lowestTarget
end

local function updategs()
    gameState.inCombat = player:InCombat()
    gameState.executePhase = target.hp <= 20
    gameState.activeEnemies = MakMulti:GetEnemies():Size()
    gameState.rage = player.rage or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.channeling = player:IsCasting() or player:IsChanneling()
    gameState.shouldBurst = A.GetToggle(2, "BurstMode")

    -- Enhanced tracking
    --gameState.fightRemains = target.exists and target.timeToDie or 999
    gameState.shouldAoE = gameState.activeEnemies > 2
    gameState.shouldCleave = gameState.activeEnemies > 1
    local castInfo = player:CastInfo()
    gameState.imCasting = castInfo and castInfo.spellId or nil

    -- TimeToAdds calculation using boss mod timers
    -- Default to high value for open world, only use boss mod timers in instances
    if Action.IsInInstance then
        gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
    else
        gameState.timeToAdds = 999  -- Open world - no adds timing needed
    end
end

-- Alias for compatibility
local updateGameState = updategs
local gs = gameState

-- Forward declare functions that are used before they're defined
local isOpenWorld

-- Utility functions (enhanced)
local function shouldBurst()
    -- Always burst if toggle is enabled
    if gameState.shouldBurst then return true end

    -- Auto-burst conditions for better gameplay
    if not gameState.inCombat then return false end

    -- Open world optimizations - more aggressive bursting
    if isOpenWorld() then
        -- Burst more frequently in open world since mobs die faster
        if target.exists and target.hp > 70 and gameState.rage >= 50 then return true end
        if gameState.activeEnemies >= 2 then return true end  -- Burst on any multi-target
    end

    -- Burst at combat start (first 10 seconds)
    if gameState.fightRemains > 990 then return true end

    -- Burst when target has high health and we have resources
    if target.exists and target.hp > 80 and gameState.rage >= 80 then return true end

    -- Burst during execute phase for maximum damage
    if target.exists and target.hp <= 25 then return true end

    -- Burst when facing multiple enemies
    if gameState.activeEnemies >= 3 then return true end

    return false
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function shouldCleave()
    return gameState.activeEnemies == 2
end

isOpenWorld = function()
    return not Action.IsInInstance and not gameState.isPvP
end

local function needsDoTRefresh(unit, debuffID, threshold)
    return unit.exists and unit:DebuffRemains(debuffID, true) < threshold
end

-- Enhanced utility functions for new functionality
local function isSpellInFlight(spell, range)
    return spell:IsSpellInFlight() or false
end

local function makInterrupt(interrupts)
    if not interrupts then return end
    for _, interrupt in pairs(interrupts) do
        if interrupt and interrupt:ReadyToUse() then
            interrupt()
        end
    end
end

-- PvP utility functions
local function shouldExhaustion(enemy)
    if not enemy or not enemy.exists then return false end
    return enemy.isMoving and not enemy:HasDeBuff(debuffs.hamstring)
end

local function shouldDisarm(enemy)
    if not enemy or not enemy.exists then return false end
    return not enemy:HasDeBuff(debuffs.disarm) and enemy.distance <= 5
end

local function shouldIntimidatingShout(enemy)
    if not enemy or not enemy.exists then return false end
    return not enemy:HasDeBuff(debuffs.intimidatingShout) and enemy.distance <= 8
end

-- Stance utility function to prevent rapid switching
local function shouldSwitchStance(targetStanceBuff, spell)
    local currentTime = GetTime() * 1000  -- Convert to milliseconds
    return not player:HasBuff(targetStanceBuff) and
           spell:ReadyToUse() and
           (currentTime - gameState.lastStanceCheck) > 1000  -- 1 second debounce
end

-- Stance management for MoP Arms Warrior
-- Battle Stance is the primary stance for Arms Warriors in MoP - provides optimal damage
-- We stay in Battle Stance for most situations and avoid unnecessary stance switching
-- Defensive/Berserker stances are not used in this rotation for optimal DPS
BattleStance:Callback(function(spell)
    -- Use utility function to prevent rapid stance switching attempts
    -- Only switch if we're not already in Battle Stance and enough time has passed
    if shouldSwitchStance(buffs.battleStance, spell) then
        gameState.lastStanceCheck = GetTime() * 1000
        return spell:Cast(player)
    end
end)

-- Shout management
BattleShout:Callback(function(spell)
    if not player:HasBuff(buffs.battleShout) and not player:HasBuff(buffs.commandingShout) and spell:ReadyToUse() then
        return spell:Cast(player)
    end
end)

-- Charge ability (works in and out of combat in MoP)
Charge:Callback(function(spell)
    if not target.exists then return end

    -- Use Charge when target is out of melee range but within Charge range
    if target.distance > 8 and target.distance <= 25 then
        -- Don't charge if we're already moving toward the target or if target is moving away rapidly
        if not player:IsMoving() or target.distance > 15 then
            return spell:Cast(target)
        end
    end
end)

-- Intercept ability (alternative gap closer in MoP)
Intercept:Callback(function(spell)
    if not target.exists then return end

    -- Use Intercept when Charge is on cooldown and target is out of range
    if target.distance > 8 and target.distance <= 25 and not Charge:ReadyToUse() then
        return spell:Cast(target)
    end
end)

-- Heroic Leap (utility movement)
HeroicLeap:Callback(function(spell)
    if not target.exists then return end

    -- Use Heroic Leap when both Charge and Intercept are on cooldown and target is far
    if target.distance > 15 and target.distance <= 40 and not Charge:ReadyToUse() and not Intercept:ReadyToUse() then
        return spell:Cast(target)
    end
end)

-- Rend DoT management
Rend:Callback(function(spell)
    if not target:HasDeBuff(debuffs.rend) or target:DebuffRemains(debuffs.rend) <= 3000 then
        return spell:Cast(target)
    end
end)

-- Colossus Smash - Priority ability
ColossusSmash:Callback(function(spell)
    if not target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(target)
    end
end)

-- Victory Rush - Highest priority when Victorious buff is active
VictoryRush:Callback(function(spell)
    -- Use Victory Rush immediately when Victorious buff is active (highest priority)
    if player:HasBuff(buffs.victorious) and spell:ReadyToUse() then
        return spell:Cast(target)
    end
end)

-- Mortal Strike - Main ability, use on cooldown
MortalStrike:Callback(function(spell)
    -- Mortal Strike should be used on cooldown as it's our primary ability
    if target.exists and target:CanAttack() and spell:ReadyToUse() then
        return spell:Cast(target)
    end
end)

-- Overpower - Proc based (high priority in MoP)
Overpower:Callback(function(spell)
    -- Cast Overpower when proc is available or when it's off cooldown and we have rage
    if player:HasBuff(buffs.overpower) or (spell:ReadyToUse() and player.rage >= 15) then
        return spell:Cast(target)
    end
end)

-- Execute in execute phase
Execute:Callback(function(spell)
    if (target.exists and target.hp <= 20) or player:HasBuff(buffs.suddenDeath) then
        return spell:Cast(target)
    end
end)

-- Slam as filler
Slam:Callback(function(spell)
    if player.rage >= 20 and not MortalStrike:ReadyToUse() then
        return spell:Cast(target)
    end
end)

-- Cleave for AoE
Cleave:Callback(function(spell)
    if gameState.activeEnemies >= 2 and player.rage >= 20 then
        return spell:Cast(target)
    end
end)

-- Cooldowns
Recklessness:Callback(function(spell)
    if shouldBurst() and target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(player)
    end
end)

-- Berserker Rage for Enrage uptime
BerserkerRage:Callback(function(spell)
    if not player:HasBuff(buffs.enrage) and spell:ReadyToUse() then
        return spell:Cast(player)
    end
end)

Avatar:Callback(function(spell)
    if shouldBurst() and target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(player)
    end
end)

SkullBanner:Callback(function(spell)
    if shouldBurst() and target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(player)
    end
end)

-- Shattering Throw for armor reduction
ShatteringThrow:Callback(function(spell)
    if shouldBurst() and target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(target)
    end
end)



-- AoE abilities
Sweepingstrikes:Callback(function(spell)
    if shouldAoE() and not player:HasBuff(buffs.sweepingStrikes) then
        return spell:Cast(player)
    end
end)

Bladestorm:Callback(function(spell)
    -- Only use Bladestorm during burst with major cooldowns active
    if shouldAoE() and gameState.activeEnemies >= 3 then
        -- Check if we have major cooldowns active (burst phase)
        local hasMajorCooldowns = player:HasBuff(buffs.recklessness) or
                                 player:HasBuff(buffs.avatar) or
                                 player:HasBuff(buffs.skullBanner)
        if hasMajorCooldowns then
            return spell:Cast(player)
        end
    end
end)

ThunderClap:Callback(function(spell)
    if shouldAoE() and gameState.activeEnemies >= 2 then
        return spell:Cast(player)
    end
end)

-- Defensive abilities
EnragedRegeneration:Callback(function(spell)
    if player.hp <= 30 and not player:HasBuff(buffs.enragedRegeneration) then
        return spell:Cast(player)
    end
end)

LastStand:Callback(function(spell)
    if player.hp <= 30 and not player:HasBuff(buffs.lastStand) then
        return spell:Cast(player)
    end
end)

-- Utility abilities - Intimidating Shout should be used strategically, not automatically
IntimidatingShout:Callback(function(spell)
    -- Only use Intimidating Shout in specific strategic situations
    if not target.exists or target.distance > 8 or target:HasDeBuff(debuffs.intimidatingShout) then return end

    -- Use for emergency peeling when low health
    if player.hp < 30 then
        return spell:Cast(target)
    end

    -- Use to interrupt dangerous casts when Pummel is on cooldown
    if target:IsCasting() and not Pummel:ReadyToUse() and not StormBolt:ReadyToUse() then
        return spell:Cast(target)
    end

    -- Don't use automatically - let manual control or specific PvP logic handle it
end)

Disarm:Callback(function(spell)
    if target.exists and target.distance <= 5 and not target:HasDeBuff(debuffs.disarm) then
        return spell:Cast(target)
    end
end)

Hamstring:Callback(function(spell)
    if target.exists and target.distance <= 5 and not target:HasDeBuff(debuffs.hamstring) then
        return spell:Cast(target)
    end
end)

-- Interrupt
Pummel:Callback(function(spell)
    if target.exists and target:IsCasting() and target:IsSafeToKick() then
        return spell:Cast(target)
    end
end)

-- Main rotation function
local function mainRotation()
    updateGameState()
    
    -- Defensive abilities
    if player.hp <= 30 then
        EnragedRegeneration()
        LastStand()
    end
    
    -- Stance and shout management
    BattleStance()
    BattleShout()
    
    -- Combat rotation
    if target.exists and target:CanAttack() then
        -- Charge if out of range
        Charge()
        
        -- Interrupt priority
        Pummel()
        
        -- DoT maintenance
        Rend()
        
        -- Cooldowns
        if shouldBurst() then
            Recklessness()
            Avatar()
            SkullBanner()
        end
        
        -- AoE setup
        if shouldAoE() then
            Sweepingstrikes()
            if gameState.activeEnemies >= 3 then
                -- Only use Bladestorm during burst with major cooldowns
                local hasMajorCooldowns = player:HasBuff(buffs.recklessness) or
                                         player:HasBuff(buffs.avatar) or
                                         player:HasBuff(buffs.skullBanner)
                if hasMajorCooldowns then
                    Bladestorm()
                end
            end
            ThunderClap()
        end
        
        -- Main rotation priority
        ColossusSmash()
        Execute()
        MortalStrike()
        Slam()
        Overpower()
    end
end

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and MortalStrike:Cooldown() > 1500 and player.rage < 50 then
        return spell:Cast(player)
    end
end)

Berserking:Callback(function(spell)
    if shouldBurst() and target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(player)
    end
end)

BloodFury:Callback(function(spell)
    if shouldBurst() and target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(player)
    end
end)

-- MoP specific abilities
StormBolt:Callback(function(spell)
    if target.exists and target:IsCasting() and target:IsSafeToKick() then
        return spell:Cast(target)
    end
end)

DragonRoar:Callback(function(spell)
    if shouldBurst() and target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(player)
    end
end)

Shockwave:Callback(function(spell)
    if shouldAoE() and gameState.activeEnemies >= 3 then
        return spell:Cast(player)
    end
end)

-- Enhanced rotation functions
local function st()
    -- Single target rotation
    updategs()

    -- Victory Rush has highest priority when Victorious buff is active
    if player:HasBuff(buffs.victorious) then
        VictoryRush()
        return
    end

    -- Cooldowns first (including auto-burst logic)
    if shouldBurst() then
        Recklessness()
        Avatar()
        SkullBanner()
    end

    -- Execute phase rotation - save Rage almost exclusively for Execute
    if gs.executePhase or player:HasBuff(buffs.suddenDeath) then
        -- 1. Colossus Smash if debuff is not up already
        if not target:HasDeBuff(debuffs.colossusSmash) and ColossusSmash:ReadyToUse() then
            ColossusSmash()
            return
        end

        -- 2. Mortal Strike
        if MortalStrike:ReadyToUse() then
            MortalStrike()
            return
        end

        -- 3. Execute
        if Execute:ReadyToUse() then
            Execute()
            return
        end

        -- 4. Overpower (costs 0 Rage thanks to Sudden Death)
        if player:HasBuff(buffs.overpower) or player:HasBuff(buffs.suddenDeath) then
            Overpower()
            return
        end

        -- 5. Dragon Roar if you took this
        if DragonRoar:ReadyToUse() then
            DragonRoar()
            return
        end

        -- 6. Battle Shout or Commanding Shout
        if not player:HasBuff(buffs.battleShout) and not player:HasBuff(buffs.commandingShout) then
            BattleShout()
            return
        end
    end

    -- Check if Colossus Smash debuff is active on target
    if not target:HasDeBuff(debuffs.colossusSmash) then
        -- Outside of Colossus Smash rotation
        -- 1. Colossus Smash - use as soon as available
        if ColossusSmash:ReadyToUse() then
            ColossusSmash()
            return
        end

        -- 2. Mortal Strike - prioritize our main ability
        if MortalStrike:ReadyToUse() then
            MortalStrike()
            return
        end

        -- 3. Overpower - high priority when proc is available
        if (gameState.isPvP and Overpower:ReadyToUse()) or (not gameState.isPvP and player:HasBuff(buffs.overpower)) then
            Overpower()
            return
        end

        -- 4. Dragon Roar - use after core abilities
        if DragonRoar:ReadyToUse() then
            DragonRoar()
            return
        end

        -- 5. Bladestorm - only during burst with major cooldowns
        if Bladestorm:ReadyToUse() then
            local hasMajorCooldowns = player:HasBuff(buffs.recklessness) or
                                     player:HasBuff(buffs.avatar) or
                                     player:HasBuff(buffs.skullBanner)
            if hasMajorCooldowns then
                Bladestorm()
                return
            end
        end

        -- 6. Slam if you have over 80 Rage
        if gs.rage >= 80 and Slam:ReadyToUse() then
            Slam()
            return
        end

        -- 7. Battle Shout or Commanding Shout
        if not player:HasBuff(buffs.battleShout) and not player:HasBuff(buffs.commandingShout) then
            BattleShout()
            return
        end

        -- 8. Heroic Strike if you are Rage capped
        if gs.rage >= 100 and HeroicStrike:ReadyToUse() then
            HeroicStrike()
            return
        end
    else
        -- During Colossus Smash - higher-burst rotation
        -- Never use Colossus Smash while debuff is still active

        -- 1. Mortal Strike
        if MortalStrike:ReadyToUse() then
            MortalStrike()
            return
        end

        -- 2. Overpower - high priority during Colossus Smash
        if player:HasBuff(buffs.overpower) then
            Overpower()
            return
        end

        -- 3. Slam
        if Slam:ReadyToUse() then
            Slam()
            return
        end

        -- 4. Heroic Throw
        if HeroicThrow:ReadyToUse() and target.distance > 8 then
            HeroicThrow()
            return
        end

        -- 5. Heroic Leap
        if HeroicLeap:ReadyToUse() and target.distance > 8 then
            HeroicLeap()
            return
        end
    end
end

local function aoe()
    -- AoE rotation for 3+ targets
    updategs()

    -- 1. Sweeping Strikes
    if not player:HasBuff(buffs.sweepingStrikes) then
        Sweepingstrikes()
        return
    end

    -- 2. Thunder Clap - use to refresh Deep Wounds on all targets
    if ThunderClap:ReadyToUse() then
        ThunderClap()
        return
    end

    -- 3. Bladestorm if you took this talent - only during burst
    if Bladestorm:ReadyToUse() then
        local hasMajorCooldowns = player:HasBuff(buffs.recklessness) or
                                 player:HasBuff(buffs.avatar) or
                                 player:HasBuff(buffs.skullBanner)
        if hasMajorCooldowns then
            Bladestorm()
            return
        end
    end

    -- 4. Colossus Smash if debuff is not up already on high health target
    if not target:HasDeBuff(debuffs.colossusSmash) and ColossusSmash:ReadyToUse() then
        ColossusSmash()
        return
    end

    -- 5. Mortal Strike
    if MortalStrike:ReadyToUse() then
        MortalStrike()
        return
    end

    -- 6. Slam
    if Slam:ReadyToUse() then
        Slam()
        return
    end

    -- 7. Dragon Roar if you took this talent
    if DragonRoar:ReadyToUse() then
        DragonRoar()
        return
    end

    -- 8. Heroic Leap
    if HeroicLeap:ReadyToUse() and target.distance > 8 then
        HeroicLeap()
        return
    end

    -- 9. Battle Shout or Commanding Shout
    if not player:HasBuff(buffs.battleShout) and not player:HasBuff(buffs.commandingShout) then
        BattleShout()
        return
    end

    -- 10. Overpower
    if player:HasBuff(buffs.overpower) then
        Overpower()
        return
    end
end

local function cleave()
    -- Cleave rotation for 2 targets - use Sweeping Strikes and follow single-target rotation
    updategs()

    -- Sweeping Strikes for cleave
    if not player:HasBuff(buffs.sweepingStrikes) then
        Sweepingstrikes()
        return
    end

    -- Follow single-target rotation with Sweeping Strikes active
    st()
end

-- Racial abilities function
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
end

local function ogcd()
    -- Off-global cooldown abilities
    BerserkerRage()  -- For Enrage uptime
    Recklessness()  -- Let the spell's own logic decide when to cast
    ShatteringThrow()  -- Armor reduction cooldown
    Avatar()
    SkullBanner()

    -- Racial abilities (they have their own conditions)
    racials()
end

local function eof()
    -- End of fight logic
    if gs.fightRemains < 30000 then
        -- Burn resources
        if gs.rage >= 50 then
            Execute()
            MortalStrike()
        end
    end
end

local function pvpenis()
    -- PvP specific logic placeholder
    -- This would contain PvP-specific rotation logic
end

-- Alias for compatibility
local function singleTargetRotation()
    st()
end

-- AoE rotation
local function aoeRotation()
    updateGameState()

    -- Sweeping Strikes for cleave
    if not player:HasBuff(buffs.sweepingStrikes) then
        Sweepingstrikes()
    end

    -- Bladestorm for heavy AoE - only during burst
    if gameState.activeEnemies >= 3 then
        local hasMajorCooldowns = player:HasBuff(buffs.recklessness) or
                                 player:HasBuff(buffs.avatar) or
                                 player:HasBuff(buffs.skullBanner)
        if hasMajorCooldowns then
            Bladestorm()
        end
    end

    -- Thunder Clap for AoE damage
    ThunderClap()

    -- Maintain Rend on multiple targets
    local rendTarget = getLowestDoTTarget(debuffs.rend)
    if rendTarget and needsDoTRefresh(rendTarget, debuffs.rend, 3000) then
        Rend()
    end

    -- Cleave as filler
    Cleave()
end

-- Cleave rotation (2 enemies)
local function cleaveRotation()
    cleave()  -- Use the existing cleave() function
end

-- PvP specific rotation
local function pvpRotation()
    updateGameState()

    -- Interrupt priority
    Pummel()

    -- DoT pressure
    if needsDoTRefresh(target, debuffs.rend, 3000) then
        Rend()
    end

    -- Hamstring for kiting
    if not target:HasDeBuff(debuffs.hamstring) then
        Hamstring()
    end

    -- Execute phase
    if target.hp <= 25 or player:HasBuff(buffs.suddenDeath) then
        Execute()
    end

    -- Standard rotation
    st()
end

-- TimeToAdds specific logic
local function timeToAddsRotation()
    updateGameState()

    -- Pre-DoT before adds spawn
    if gameState.timeToAdds < 8000 and gameState.timeToAdds > 0 then
        -- Prepare DoTs on current target
        if needsDoTRefresh(target, debuffs.rend, 8000) then
            Rend()
        end

        -- Prepare cooldowns
        if gameState.timeToAdds < 3000 then
            Recklessness()
            Avatar()
        end
    end

    -- During adds phase
    if gameState.activeEnemies >= 3 then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency defensives
    if player.hp <= 30 then
        EnragedRegeneration()
        LastStand()
    end

    -- Stance and shout management
    BattleStance()
    BattleShout()
    -- Cooldowns during burst
    if shouldBurst() then
        Recklessness()
        Avatar()
    end

    -- TimeToAdds logic (only relevant in instances with boss mods)
    if Action.IsInInstance and gameState.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Standard rotation selection (works for all content types)
    -- Open world, dungeons, raids - all use the same enemy count logic
    if shouldAoE() then
        aoeRotation()  -- 3+ enemies
    elseif shouldCleave() then
        cleaveRotation()  -- 2 enemies
    else
        singleTargetRotation()  -- 1 enemy
    end
end

-- Utility functions
local function mopAbilities()
    StormBolt()
    DragonRoar()
    Shockwave()
end

local function baseStuff()
    BattleStance()
    BattleShout()

    -- Defensive abilities based on health
    if player.hp <= 30 then
        EnragedRegeneration()
        LastStand()
    end
end







-- Main function - Works in all content types (Open World, Dungeons, Raids, PvP)
A[1] = function(icon)
    FrameworkStart(icon)
    updategs()

    -- Base maintenance
    baseStuff()

    if target.exists and target:CanAttack() then
        -- Victory Rush has absolute highest priority when Victorious buff is active
        if player:HasBuff(buffs.victorious) then
            VictoryRush()
        end

        -- Movement abilities - Gap closers when out of range
        if target.distance > 8 then
            Charge()
            Intercept()
            HeroicLeap()
        end

        -- Interrupt priority
        Pummel()

        -- DoT maintenance
        Rend()

        -- Racial abilities
        racials()

        -- MoP specific abilities
        mopAbilities()

        -- Cooldowns
        if shouldBurst() then
            Recklessness()
            Avatar()
            SkullBanner()
        end

        -- Choose rotation based on enemy count
        if shouldAoE() then
            aoeRotation()
        else
            singleTargetRotation()
        end

        -- PvP Overpower procs after main rotation (after Colossus Smash priority)
        if gameState.isPvP and player:HasBuff(buffs.overpower) then
            Overpower()
        end
    end

    return FrameworkEnd()
end

-- Enhanced A[3] function with new functionality
A[3] = function(icon)
    FrameworkStart(icon)
    updategs()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player Rage: ", gs.rage)
        MakPrint(2, "Mortal Strike Ready: ", MortalStrike:ReadyToUse())
        MakPrint(3, "Colossus Smash Ready: ", ColossusSmash:ReadyToUse())
        MakPrint(4, "Active Enemies: ", gs.activeEnemies)
        MakPrint(5, "Execute Phase: ", gs.executePhase)
        MakPrint(6, "In Combat: ", gs.inCombat)
        MakPrint(7, "Overpower Proc: ", player:HasBuff(buffs.overpower))
        MakPrint(8, "Sudden Death Proc: ", player:HasBuff(buffs.suddenDeath))
        MakPrint(9, "Colossus Smash Debuff: ", target:HasDeBuff(debuffs.colossusSmash))
        MakPrint(10, "Target HP: ", target.hp)
        MakPrint(11, "Should AoE: ", gs.shouldAoE)
        MakPrint(12, "Should Cleave: ", gs.shouldCleave)
        MakPrint(13, "Time To Adds: ", gs.timeToAdds)
        MakPrint(14, "Is PvP: ", gs.isPvP)
        MakPrint(15, "Fight Remains: ", gs.fightRemains)
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert and awareAlert[1] then -- Recklessness ready
        if Recklessness:ReadyToUse() and shouldBurst() and player.inCombat then
            Aware:displayMessage("RECKLESSNESS READY", "Red", 1)
        end
        if Avatar:ReadyToUse() and shouldBurst() and player.inCombat then
            Aware:displayMessage("AVATAR READY", "Purple", 1)
        end
        if player:HasBuff(buffs.suddenDeath) then
            Aware:displayMessage("SUDDEN DEATH PROC", "Yellow", 1)
        end
        if player:HasBuff(buffs.overpower) then
            Aware:displayMessage("OVERPOWER PROC", "Green", 1)
        end
    end

    -- Interrupt handling
    local interrupts = {Pummel, StormBolt}
    makInterrupt(interrupts)

    -- Base maintenance
    baseStuff()

    if target.exists and target:CanAttack() then
        -- Movement abilities - Gap closers when out of range
        if target.distance > 8 then
            Charge()
            Intercept()
            HeroicLeap()
        end

        -- Only proceed with combat abilities if in range
        if MortalStrike:InRange(target) or Execute:InRange(target) then
            if A.IsInPvP then
                Pummel()
                Disarm()
                pvpRotation()
            end

            if player.channeling and gs.imCasting and gs.imCasting == Slam.id then return end

        local damagePotion = Action.GetToggle(2, "damagePotion")
        local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
        local potionExhausted = Action.GetToggle(2, "potionExhausted")
        local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")

        if damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
            local shouldPot = target:HasDeBuff(debuffs.colossusSmash) and (player:HasBuff(buffs.recklessness) or player:HasBuff(buffs.avatar))
            if shouldPot then
                -- Damage potion logic would go here
            end
        end

        if gs.shouldAoE then
            aoeRotation()
        end

        ogcd()
        eof()

            if gs.shouldCleave then
                cleave()
            end

            st()

            -- PvP Overpower procs after main rotation (after Colossus Smash priority)
            if gameState.isPvP and player:HasBuff(buffs.overpower) then
                Overpower()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        Pummel()
    end
    if Action.Zone == "arena" then
        enemyRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Arms Warrior
Pummel:Callback("arena", function(spell, enemy)
    if not enemy.exists then return end
    if not enemy:IsCasting() then return end
    if not enemy:IsSafeToKick() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

StormBolt:Callback("arena", function(spell, enemy)
    if not enemy.exists then return end
    if not enemy:IsCasting() then return end
    if not enemy:IsSafeToKick() then return end
    if enemy.distance > 20 then return end
    if Pummel:ReadyToUse() and enemy.distance <= 5 then return end -- Prefer Pummel if in range

    return spell:Cast(enemy)
end)

IntimidatingShout:Callback("arena", function(spell, enemy)
    if enemy.distance > 8 then return end
    if enemy:HasDeBuff(debuffs.intimidatingShout) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Intimidating Shout - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Intimidating Shout - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

Disarm:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy:HasDeBuff(debuffs.disarm) then return end
    if enemy.hp < 40 then return end

    -- Disarm melee DPS when they're bursting
    if not enemy.isHealer and enemy.hp > 70 then
        Aware:displayMessage("Disarming Melee", "Red", 1)
        return spell:Cast(enemy)
    end
end)

Charge:Callback("arena", function(spell, enemy)
    if enemy.distance <= 8 then return end
    if enemy.distance > 25 then return end
    if player:InCombat() then return end

    return spell:Cast(enemy)
end)

Rend:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy:DebuffRemains(debuffs.rend) > 6000 then return end
    if enemy.hp < 20 then return end -- Don't waste time on low targets

    return spell:Cast(enemy)
end)

Hamstring:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy:HasDeBuff(debuffs.hamstring) then return end
    if enemy.hp < 30 then return end

    -- Hamstring kiters and runners
    if enemy.distance > 3 and enemy.hp < 60 then
        Aware:displayMessage("Hamstring - Kiter", "Blue", 1)
        return spell:Cast(enemy)
    end
end)

-- Enhanced enemy and party rotation functions
local enemyRotation = function(enemy)
    if not enemy.exists then return end
    if A.Zone ~= "arena" then return end
    Pummel("arena", enemy)
    StormBolt("arena", enemy)
    IntimidatingShout("arena", enemy)
    Disarm("arena", enemy)
    Charge("arena", enemy)
    Rend("arena", enemy)
    Hamstring("arena", enemy)
end

local partyRotation = function(friendly)
    if not friendly.exists then return end
    -- No specific party healing for Warrior in MoP
    -- Focus on defensive abilities for self
end

-- Enhanced utility for MoP
local function mopUtility()
    Pummel()
    Disarm()
    StormBolt()
    Shockwave()
end

local function mopTalents()
    StormBolt()
    DragonRoar()
    Shockwave()
end
