-- APL UPDATE MoP Fury Warrior
-- Mists of Pandaria Fury Warrior Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is <PERSON> spec (talent tree 2 for Warrior in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 2 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Fury Warrior Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- MoP Fury Warrior Core Abilities
    Bloodthirst = { ID = 23881, MAKULU_INFO = { damageType = "physical" } },
    RagingBlow = { ID = 85288, MAKULU_INFO = { damageType = "physical" } },
    ColossusSmash = { ID = 86346, MAKULU_INFO = { damageType = "physical" } },
    WildStrike = { ID = 100130, MAKULU_INFO = { damageType = "physical" } },
    HeroicStrike = { ID = 78, MAKULU_INFO = { damageType = "physical", offGcd = true } },
    Execute = { ID = 5308, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP AoE Abilities
    Whirlwind = { ID = 1680, MAKULU_INFO = { damageType = "physical" } },
    Cleave = { ID = 845, MAKULU_INFO = { damageType = "physical", offGcd = true } },
    
    -- Stances and Shouts
    BattleStance = { ID = 2457, MAKULU_INFO = { targeted = false } },
    DefensiveStance = { ID = 71, MAKULU_INFO = { targeted = false } },
    BerserkerStance = { ID = 2458, MAKULU_INFO = { targeted = false } },
    BattleShout = { ID = 6673, MAKULU_INFO = { targeted = false } },
    CommandingShout = { ID = 469, MAKULU_INFO = { targeted = false } },
    DemoralizingShout = { ID = 1160, MAKULU_INFO = { targeted = false } },
    
    -- Cooldowns and Utilities
    Recklessness = { ID = 1719, MAKULU_INFO = { targeted = false } },
    BerserkerRage = { ID = 18499, MAKULU_INFO = { targeted = false } },
    EnragedRegeneration = { ID = 55694, MAKULU_INFO = { targeted = false } },
    
    -- MoP Specific Abilities
    Avatar = { ID = 107574, MAKULU_INFO = { targeted = false } },
    Bladestorm = { ID = 46924, MAKULU_INFO = { targeted = false } },
    DragonRoar = { ID = 118000, MAKULU_INFO = { damageType = "physical" } },
    StormBolt = { ID = 107570, MAKULU_INFO = { damageType = "physical" } },
    Shockwave = { ID = 46968, MAKULU_INFO = { damageType = "physical" } },
    
    -- Utility Abilities
    Charge = { ID = 100, MAKULU_INFO = { targeted = true } },
    HeroicLeap = { ID = 6544, MAKULU_INFO = { targeted = false } },
    Pummel = { ID = 6552, MAKULU_INFO = { damageType = "physical", ignoreCasting = true } },
    HeroicThrow = { ID = 57755, MAKULU_INFO = { damageType = "physical" } },
    IntimidatingShout = { ID = 5246, MAKULU_INFO = { targeted = false } },
    
    -- Defensive Abilities
    LastStand = { ID = 12975, MAKULU_INFO = { targeted = false } },
    ShieldWall = { ID = 871, MAKULU_INFO = { targeted = false } },
    SpellReflection = { ID = 23920, MAKULU_INFO = { targeted = false } },
    
    -- MoP Banners
    SkullBanner = { ID = 114207, MAKULU_INFO = { targeted = false } },
    DemoralizingBanner = { ID = 114203, MAKULU_INFO = { targeted = false } },
    MockingBanner = { ID = 114192, MAKULU_INFO = { targeted = false } },
    
    -- Utility
    ShatteringThrow = { ID = 64382, MAKULU_INFO = { castTime = 1500 } },
    VictoryRush = { ID = 34428, MAKULU_INFO = { damageType = "physical" } },
    ImpendingVictory = { ID = 103840, MAKULU_INFO = { damageType = "physical" } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 6552, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 5246, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Fury Warrior Buffs
local buffs = {
    battleStance = 2457,
    defensiveStance = 71,
    berserkerStance = 2458,
    battleShout = 6673,
    commandingShout = 469,
    recklessness = 1719,
    berserkerRage = 18499,
    enrage = 12880,
    bloodsurge = 46915,
    meatCleaver = 12950,
    avatar = 107574,
    bladestorm = 46924,
    enragedRegeneration = 55694,
    lastStand = 12975,
    shieldWall = 871,
    skullBanner = 114207,
    demoralizingBanner = 114203,
    mockingBanner = 114192,
    victoryRush = 32216,
    suddenDeath = 52437,
    bloodbath = 12292,
}

-- MoP Fury Warrior Debuffs
local debuffs = {
    colossusSmash = 86346,
    demoralizing = 1160,
    sunderArmor = 7386,
    hamstring = 1715,
    rend = 94009,
    deepWounds = 115768,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    rage = 0,
    timeToAdds = 999,
    isPvP = false,
    enraged = false,
    ragingBlowCharges = 0,
    bloodsurgeProc = false,
    executeRange = false,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.rage = player.rage or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.enraged = player:HasBuff(buffs.enrage) or player:HasBuff(buffs.berserkerRage)
    gameState.bloodsurgeProc = player:HasBuff(buffs.bloodsurge)
    gameState.executeRange = target.exists and target.hp <= 20
    
    -- Calculate Raging Blow charges (simplified)
    gameState.ragingBlowCharges = 0
    if gameState.enraged then
        gameState.ragingBlowCharges = 2 -- Assume max charges when enraged
    end
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function needsRage()
    return gameState.rage < 30
end

local function hasHighRage()
    return gameState.rage >= 80
end

local function canUseColossusSmash()
    return hasHighRage() and gameState.ragingBlowCharges >= 1 and gameState.enraged
end

-- Core ability callbacks
Bloodthirst:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    
    -- Always use on cooldown - core rage generator and enrage source
    return spell:Cast(target)
end)

RagingBlow:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if not gameState.enraged then return end
    
    -- Use when we have charges and during Colossus Smash
    if gameState.ragingBlowCharges >= 2 or target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(target)
    end
end)

ColossusSmash:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    
    -- Use when we have proper setup (high rage, enraged, raging blow charges)
    if canUseColossusSmash() then
        return spell:Cast(target)
    end
end)

WildStrike:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    
    -- Prioritize when we have Bloodsurge proc or need to dump rage
    if gameState.bloodsurgeProc or (hasHighRage() and not target:HasDeBuff(debuffs.colossusSmash)) then
        return spell:Cast(target)
    end
end)

HeroicStrike:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    
    -- Use to prevent rage capping and during Colossus Smash
    if hasHighRage() or target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(target)
    end
end)

Execute:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if not gameState.executeRange then return end
    
    -- High priority in execute range
    return spell:Cast(target)
end)

-- AoE abilities
Whirlwind:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if not shouldAoE() then return end
    
    return spell:Cast(target)
end)

Cleave:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if gameState.activeEnemies < 2 then return end
    
    -- Use instead of Heroic Strike for 2-3 enemies
    if gameState.activeEnemies <= 3 and hasHighRage() then
        return spell:Cast(target)
    end
end)

-- Stance management
BattleStance:Callback(function(spell)
    if player:HasBuff(buffs.battleStance) then return end
    if gameState.inCombat and not needsRage() then return end
    
    -- Default stance for rage generation
    return spell:Cast(player)
end)

BerserkerStance:Callback(function(spell)
    if player:HasBuff(buffs.berserkerStance) then return end
    if not gameState.inCombat then return end
    
    -- Use when taking damage for extra rage generation
    if player.hp < 90 then
        return spell:Cast(player)
    end
end)

-- Shouts
BattleShout:Callback(function(spell)
    if player:HasBuff(buffs.battleShout) then return end
    
    return spell:Cast(player)
end)

CommandingShout:Callback(function(spell)
    if player:HasBuff(buffs.commandingShout) then return end
    if player:HasBuff(buffs.battleShout) then return end
    
    -- Alternative to Battle Shout for survivability
    if gameState.isPvP or player.hp < 80 then
        return spell:Cast(player)
    end
end)

-- Cooldowns
Recklessness:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    
    -- Major DPS cooldown
    return spell:Cast(player)
end)

BerserkerRage:Callback(function(spell)
    if gameState.enraged then return end
    
    -- Use to force enrage when Bloodthirst doesn't crit
    return spell:Cast(player)
end)

Avatar:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    
    -- Major DPS cooldown
    return spell:Cast(player)
end)

-- MoP Talents
Bladestorm:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if not shouldBurst() and not shouldAoE() then return end
    
    -- Use for burst or AoE
    return spell:Cast(player)
end)

DragonRoar:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target:HasDeBuff(debuffs.colossusSmash) then return end -- Don't use during CS
    
    -- Use outside of Colossus Smash windows
    return spell:Cast(player)
end)

StormBolt:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 20 then return end
    
    -- High damage ability, prioritize during Colossus Smash
    if target:HasDeBuff(debuffs.colossusSmash) or shouldBurst() then
        return spell:Cast(target)
    end
end)

Shockwave:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 10 then return end
    if not shouldAoE() then return end
    
    -- AoE stun and damage
    return spell:Cast(target)
end)

-- Utility
Charge:Callback(function(spell)
    if target.distance < 8 or target.distance > 25 then return end
    if not target.canAttack then return end
    
    return spell:Cast(target)
end)

HeroicLeap:Callback(function(spell)
    if target.distance < 8 then return end
    if target:HasDeBuff(debuffs.colossusSmash) then
        -- Use for damage during Colossus Smash
        return spell:Cast(target)
    end
end)

Pummel:Callback(function(spell)
    if target.distance > 5 then return end
    if not target:IsCasting() then return end
    if not target:IsInterruptible() then return end
    
    return spell:Cast(target)
end)

-- Defensive abilities
EnragedRegeneration:Callback(function(spell)
    if player.hp > 60 then return end
    
    return spell:Cast(player)
end)

LastStand:Callback(function(spell)
    if player.hp > 30 then return end
    
    return spell:Cast(player)
end)

ShieldWall:Callback(function(spell)
    if player.hp > 25 then return end
    
    return spell:Cast(player)
end)

VictoryRush:Callback(function(spell)
    if not player:HasBuff(buffs.victoryRush) then return end
    if player.hp > 80 then return end
    
    return spell:Cast(target)
end)

-- Banners
SkullBanner:Callback(function(spell)
    if not shouldBurst() then return end

    -- Major raid DPS cooldown
    return spell:Cast(player)
end)

DemoralizingBanner:Callback(function(spell)
    if not gameState.inCombat then return end
    if gameState.activeEnemies < 3 then return end

    -- Defensive raid cooldown for heavy damage phases
    return spell:Cast(player)
end)

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if not shouldBurst() then return end
    if gameState.rage > 70 then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(player)
end)

BloodFury:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(player)
end)

QuakingPalm:Callback(function(spell)
    if target.distance > 5 then return end
    if not target:IsCasting() then return end

    return spell:Cast(target)
end)

-- Enhanced rotation functions
local function singleTargetRotation()
    -- Priority order for single target based on MoP Fury rotation

    -- Interrupt first
    Pummel()

    -- Execute range priority
    if gameState.executeRange then
        Execute()
        if hasHighRage() then
            HeroicStrike()
        end
        return
    end

    -- Colossus Smash setup and usage
    if canUseColossusSmash() then
        ColossusSmash()
    end

    -- During Colossus Smash - spam abilities
    if target:HasDeBuff(debuffs.colossusSmash) then
        HeroicStrike() -- Spam this during CS
        if gameState.ragingBlowCharges >= 2 then
            RagingBlow()
        end
        Bloodthirst()
        if gameState.ragingBlowCharges >= 1 then
            RagingBlow()
        end
        WildStrike()
        StormBolt()
        HeroicLeap()
        return
    end

    -- Outside Colossus Smash - prepare and maintain
    if hasHighRage() and not canUseColossusSmash() then
        HeroicStrike() -- Prevent rage capping
    end

    -- Core rotation
    Bloodthirst() -- Always on cooldown

    -- Talent abilities
    DragonRoar() -- Use outside CS
    Bladestorm()

    -- Raging Blow with charges
    if gameState.ragingBlowCharges >= 2 then
        RagingBlow()
    end

    -- Wild Strike with Bloodsurge proc
    if gameState.bloodsurgeProc then
        WildStrike()
    end

    -- Filler abilities
    if gameState.ragingBlowCharges >= 1 and ColossusSmash:Cooldown() > 3000 then
        RagingBlow()
    end

    if hasHighRage() and ColossusSmash:Cooldown() > 3000 then
        WildStrike()
    end

    -- Low priority fillers
    HeroicThrow()
    BattleShout()
end

local function aoeRotation()
    -- Priority order for AoE (4+ enemies)

    -- Interrupt priority target
    Pummel()

    -- AoE abilities
    if gameState.activeEnemies >= 8 then
        -- 8+ enemies: spam Whirlwind
        Bloodthirst() -- For enrage
        Whirlwind()
        if target:HasDeBuff(debuffs.colossusSmash) then
            ColossusSmash() -- For extra rage from 2pc
        end
        if gameState.ragingBlowCharges >= 2 and player:HasBuff(buffs.meatCleaver) then
            RagingBlow()
        end
        return
    end

    if gameState.activeEnemies >= 4 then
        -- 4-7 enemies: Bloodthirst + Whirlwind rotation
        Bloodthirst()
        Whirlwind()
        Whirlwind()
        Whirlwind()
        RagingBlow()
        return
    end

    if gameState.activeEnemies >= 2 then
        -- 2-3 enemies: modified single target
        Bloodthirst()
        if gameState.ragingBlowCharges >= 2 then
            RagingBlow()
        end
        Whirlwind() -- Instead of Wild Strike without Bloodsurge
        Cleave() -- Instead of Heroic Strike
        ColossusSmash()
        if gameState.bloodsurgeProc then
            WildStrike()
        end
        return
    end
end

local function pvpRotation()
    -- PvP specific rotation with enhanced burst and control

    -- Defensive priority
    if player.hp <= 25 then
        LastStand()
        ShieldWall()
    end

    if player.hp <= 50 then
        EnragedRegeneration()
        VictoryRush()
    end

    -- Interrupt and control
    Pummel()
    IntimidatingShout()
    StormBolt()

    -- Burst damage
    if shouldBurst() then
        Recklessness()
        Avatar()
        SkullBanner()
        Bladestorm()
    end

    -- Mobility
    Charge()
    HeroicLeap()

    -- Core rotation with PvP priorities
    if gameState.executeRange then
        Execute()
        HeroicStrike()
    else
        -- Standard rotation but more aggressive
        ColossusSmash()
        if target:HasDeBuff(debuffs.colossusSmash) then
            HeroicStrike()
            RagingBlow()
            Bloodthirst()
            StormBolt()
        else
            Bloodthirst()
            RagingBlow()
            WildStrike()
        end
    end
end

local function timeToAddsRotation()
    -- Prepare for incoming adds

    -- Save cooldowns for adds
    if gameState.timeToAdds < 5000 then
        -- Use cooldowns just before adds spawn
        Recklessness()
        Avatar()
        SkullBanner()
        Bladestorm()
    end

    -- Pool rage for burst
    if gameState.rage < 60 then
        Bloodthirst() -- Generate rage
        BerserkerRage() -- Force enrage
    else
        -- Continue normal rotation but conserve resources
        ColossusSmash()
        RagingBlow()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency responses
    if player.hp <= 20 then
        LastStand()
        ShieldWall()
    end

    if player.hp <= 40 then
        EnragedRegeneration()
        VictoryRush()
    end

    -- Stance management
    BattleStance()
    BerserkerStance()

    -- Shout maintenance
    BattleShout()
    CommandingShout()

    -- Force enrage if needed
    if not gameState.enraged and BerserkerRage:IsReady() then
        BerserkerRage()
    end

    -- Racial abilities during burst
    if shouldBurst() then
        BloodFury()
        Berserking()
        ArcaneTorrent()
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Rage: ", gameState.rage)
        MakPrint(3, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(4, "In Combat: ", gameState.inCombat)
        MakPrint(5, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(6, "Is PvP: ", gameState.isPvP)
        MakPrint(7, "Enraged: ", gameState.enraged)
        MakPrint(8, "Raging Blow Charges: ", gameState.ragingBlowCharges)
        MakPrint(9, "Bloodsurge Proc: ", gameState.bloodsurgeProc)
        MakPrint(10, "Execute Range: ", gameState.executeRange)
        MakPrint(11, "Colossus Smash Debuff: ", target:HasDeBuff(debuffs.colossusSmash))
        MakPrint(12, "Can Use Colossus Smash: ", canUseColossusSmash())
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if Recklessness:IsReady() and shouldBurst() then
            Aware:displayMessage("RECKLESSNESS READY", "Red", 1)
        end
        if Avatar:IsReady() and shouldBurst() then
            Aware:displayMessage("AVATAR READY", "Blue", 1)
        end
        if ColossusSmash:IsReady() and canUseColossusSmash() then
            Aware:displayMessage("COLOSSUS SMASH READY", "Yellow", 1)
        end
        if gameState.rage >= 100 then
            Aware:displayMessage("RAGE CAPPED", "Orange", 1)
        end
        if gameState.bloodsurgeProc then
            Aware:displayMessage("BLOODSURGE PROC", "Green", 1)
        end
        if gameState.executeRange then
            Aware:displayMessage("EXECUTE RANGE", "Purple", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
        if target:HasDeBuff(debuffs.colossusSmash) then
            Aware:displayMessage("COLOSSUS SMASH ACTIVE", "White", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 20 then
        if LastStand:IsReady() then return FrameworkEnd() end
        if ShieldWall:IsReady() then return FrameworkEnd() end
    end

    if player.hp <= 40 then
        if EnragedRegeneration:IsReady() then return FrameworkEnd() end
        if VictoryRush:IsReady() then return FrameworkEnd() end
    end

    -- Enhanced resource management
    if not gameState.enraged and BerserkerRage:IsReady() then
        if BerserkerRage() then return FrameworkEnd() end
    end

    if target.exists and target.alive then
        -- Enhanced interrupt priority
        if target:IsCasting() and target:IsInterruptible() then
            if Pummel() then return FrameworkEnd() end
            if StormBolt() then return FrameworkEnd() end
        end

        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if IntimidatingShout() then return FrameworkEnd() end
                if Pummel() then return FrameworkEnd() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if Recklessness() then return FrameworkEnd() end
            if Avatar() then return FrameworkEnd() end
            if SkullBanner() then return FrameworkEnd() end
            if Bladestorm() then return FrameworkEnd() end

            -- Racial abilities during burst
            if BloodFury() then return FrameworkEnd() end
            if Berserking() then return FrameworkEnd() end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    Pummel("arena", enemy)
    StormBolt("arena", enemy)

    -- CC abilities
    IntimidatingShout("arena", enemy)

    -- Burst damage
    if shouldBurst() then
        Recklessness("arena")
        Avatar("arena")
        Bladestorm("arena")
    end

    -- Execute priority
    if enemy.hp <= 20 then
        Execute("arena", enemy)
        HeroicStrike("arena", enemy)
    else
        -- Standard rotation
        ColossusSmash("arena", enemy)
        if enemy:HasDeBuff(debuffs.colossusSmash) then
            HeroicStrike("arena", enemy)
            RagingBlow("arena", enemy)
            Bloodthirst("arena", enemy)
        else
            Bloodthirst("arena", enemy)
            RagingBlow("arena", enemy)
            WildStrike("arena", enemy)
        end
    end
end

local function enhancedPartyRotation(friendly)
    if not friendly.exists then return end

    -- Limited support abilities for Fury Warrior
    -- Mainly defensive cooldowns that can help the team
    if friendly.hp < 30 then
        DemoralizingBanner("arena")
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        Pummel()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Fury Warrior
Pummel:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

StormBolt:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:IsKickImmune() then return end

    -- Use for interrupt or high damage
    if enemy:IsCasting() and enemy:IsInterruptible() then
        Aware:displayMessage("Storm Bolt - Interrupt", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use for damage during burst
    if shouldBurst() and enemy.hp > 30 then
        Aware:displayMessage("Storm Bolt - Damage", "Red", 1)
        return spell:Cast(enemy)
    end
end)

IntimidatingShout:Callback("arena", function(spell, enemy)
    if enemy.distance > 8 then return end
    if enemy:HasDeBuff(5246) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Intimidating Shout - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Intimidating Shout - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

Execute:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if enemy.hp > 20 then return end

    -- High priority execute
    Aware:displayMessage("Execute - Finish", "Purple", 1)
    return spell:Cast(enemy)
end)

ColossusSmash:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end
    if not canUseColossusSmash() then return end

    -- Setup burst window
    Aware:displayMessage("Colossus Smash - Burst Window", "Red", 1)
    return spell:Cast(enemy)
end)

Recklessness:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Recklessness - Burst", "Red", 1)
    return spell:Cast(player)
end)

Avatar:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Avatar - Burst", "Blue", 1)
    return spell:Cast(player)
end)

Bladestorm:Callback("arena", function(spell)
    if not shouldBurst() and gameState.activeEnemies < 2 then return end

    -- Use for burst or when multiple enemies
    if shouldBurst() then
        Aware:displayMessage("Bladestorm - Burst", "Orange", 1)
    else
        Aware:displayMessage("Bladestorm - AoE", "Yellow", 1)
    end
    return spell:Cast(player)
end)

DemoralizingBanner:Callback("arena", function(spell)
    if gameState.activeEnemies < 2 then return end

    -- Defensive team cooldown
    Aware:displayMessage("Demoralizing Banner - Defense", "Blue", 1)
    return spell:Cast(player)
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    QuakingPalm()
end

local function mopTalents()
    DragonRoar()
    Bladestorm()
    StormBolt()
    Shockwave()
    Avatar()
end

local function baseStuff()
    BattleStance()
    BerserkerStance()
    BattleShout()
    CommandingShout()
    EnragedRegeneration()
    LastStand()
    ShieldWall()
    BerserkerRage()
end

local function baseStuffCombat()
    Pummel()
    Charge()
    VictoryRush()
    HeroicLeap()
end

-- Enhanced utility for MoP Fury Warrior
local function mopUtility()
    Pummel()
    IntimidatingShout()
    StormBolt()
    Charge()
    HeroicLeap()
    HeroicThrow()
    ShatteringThrow()
end

-- Combat tracking
local function combatStart()
    gameState.ragingBlowCharges = 0
    gameState.bloodsurgeProc = false
end

local function combatEnd()
    gameState.ragingBlowCharges = 0
    gameState.bloodsurgeProc = false
end

-- Event handling
local eventFrame = CreateFrame("Frame")
eventFrame:RegisterEvent("PLAYER_REGEN_DISABLED")
eventFrame:RegisterEvent("PLAYER_REGEN_ENABLED")
eventFrame:SetScript("OnEvent", function(self, event)
    if event == "PLAYER_REGEN_DISABLED" then
        combatStart()
    elseif event == "PLAYER_REGEN_ENABLED" then
        combatEnd()
    end
end)

-- Initialize
Action[ACTION_CONST_WARRIOR_FURY] = A
