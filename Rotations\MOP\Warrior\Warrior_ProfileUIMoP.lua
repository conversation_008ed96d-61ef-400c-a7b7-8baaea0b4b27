local Action = _G.Action

local A                = Action

LPH_ENCNUM = function(val) return val end

if WOW_PROJECT_ID ~= WOW_PROJECT_MISTS_CLASSIC then return end

A.Data.ProfileEnabled[Action.CurrentProfile] = true
A.Data.ProfileUI = {
    DateTime = "Makulu MoP v1.0.0 (01 August 2025)",
    -- Class settings
    [2] = {
        {
            {
                E = "Header",
                L = {
                    ANY = " ====== Makulu - MoP Warrior ====== ",
                },
            },
        },
        {
            { -- AOE
                E = "Checkbox",
                DB = "AoE",
                DBV = true,
                L = {
                    enUS = "Use AoE",
                    ruRU = "Использовать AoE",
                    frFR = "Utiliser l'AoE",
                },
                TT = {
                    enUS = "Enable multiunits actions",
                    ruRU = "Включает действия для нескольких целей",
                    frFR = "Activer les actions multi-unités",
                },
                M = {},
            },
            { -- Auto Stance
                E = "Checkbox",
                DB = "autoStance",
                DBV = true,
                L = {
                    ANY = "Auto Stance Management",
                },
                TT = {
                    ANY = "Automatically manage stances based on situation",
                },
                M = {},
            },
        },
        {
            { -- Shield Wall HP
                E = "Slider",
                MIN = 0,
                MAX = 100,
                DB = "ShieldWallHP",
                DBV = 40,
                ONOFF = false,
                L = {
                    ANY = "Shield Wall HP (%)",
                },
                TT = {
                    ANY = "HP percentage to use Shield Wall",
                },
                M = {},
            },
            { -- Last Stand HP
                E = "Slider",
                MIN = 0,
                MAX = 100,
                DB = "LastStandHP",
                DBV = 50,
                ONOFF = false,
                L = {
                    ANY = "Last Stand HP (%)",
                },
                TT = {
                    ANY = "HP percentage to use Last Stand",
                },
                M = {},
            },
        },
        {
            { -- Victory Rush HP
                E = "Slider",
                MIN = 0,
                MAX = 100,
                DB = "VictoryRushHP",
                DBV = 70,
                ONOFF = false,
                L = {
                    ANY = "Victory Rush HP (%)",
                },
                TT = {
                    ANY = "HP percentage to use Victory Rush",
                },
                M = {},
            },
            { -- Enraged Regeneration HP
                E = "Slider",
                MIN = 0,
                MAX = 100,
                DB = "EnragedRegenerationHP",
                DBV = 60,
                ONOFF = false,
                L = {
                    ANY = "Enraged Regeneration HP (%)",
                },
                TT = {
                    ANY = "HP percentage to use Enraged Regeneration",
                },
                M = {},
            },
        },
        {
            { -- Shield Barrier HP
                E = "Slider",
                MIN = 0,
                MAX = 100,
                DB = "ShieldBarrierHP",
                DBV = 65,
                ONOFF = false,
                L = {
                    ANY = "Shield Barrier HP (%)",
                },
                TT = {
                    ANY = "HP percentage to use Shield Barrier",
                },
                M = {},
            },
            { -- Rallying Cry HP
                E = "Slider",
                MIN = 0,
                MAX = 100,
                DB = "RallyingCryHP",
                DBV = 40,
                ONOFF = false,
                L = {
                    ANY = "Rallying Cry HP (%)",
                },
                TT = {
                    ANY = "HP percentage to use Rallying Cry",
                },
                M = {},
            },
        },
    },
}
