-- APL UPDATE MoP Protection Warrior
-- Mists of Pandaria Protection Warrior Rotation

if not MakuluValidCheck() then return true end
if Makulu_magic_number ~= 2347956243324 then return true end

-- Check if player is Protection spec (talent tree 3 for Warrior in MoP)
local talentTree = GetPrimaryTalentTree()
if talentTree ~= 3 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

-- MoP Protection Warrior Abilities
local ActionID = {
    -- Racial Abilities
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    QuakingPalm = { ID = 107079 },
    
    -- Core Abilities - Threat Generation
    ShieldSlam = { ID = 23922, MAKULU_INFO = { damageType = "physical" } },
    Revenge = { ID = 6572, MAKULU_INFO = { damageType = "physical" } },
    Devastate = { ID = 20243, MAKULU_INFO = { damageType = "physical" } },
    ThunderClap = { ID = 6343, MAKULU_INFO = { damageType = "physical" } },
    Execute = { ID = 5308, MAKULU_INFO = { damageType = "physical" } },
    
    -- Active Mitigation
    ShieldBlock = { ID = 2565, MAKULU_INFO = { targeted = false } },
    ShieldBarrier = { ID = 112048, MAKULU_INFO = { targeted = false } },
    
    -- Rage Spenders
    HeroicStrike = { ID = 78, MAKULU_INFO = { damageType = "physical", offGcd = true } },
    Cleave = { ID = 845, MAKULU_INFO = { damageType = "physical", offGcd = true } },
    
    -- Stances and Shouts
    DefensiveStance = { ID = 71, MAKULU_INFO = { targeted = false } },
    BattleStance = { ID = 2457, MAKULU_INFO = { targeted = false } },
    BerserkerStance = { ID = 2458, MAKULU_INFO = { targeted = false } },
    BattleShout = { ID = 6673, MAKULU_INFO = { targeted = false } },
    CommandingShout = { ID = 469, MAKULU_INFO = { targeted = false } },
    DemoralizingShout = { ID = 1160, MAKULU_INFO = { targeted = false } },
    ChallengingShout = { ID = 1161, MAKULU_INFO = { targeted = false } },
    
    -- Defensive Cooldowns
    ShieldWall = { ID = 871, MAKULU_INFO = { targeted = false } },
    LastStand = { ID = 12975, MAKULU_INFO = { targeted = false } },
    EnragedRegeneration = { ID = 55694, MAKULU_INFO = { targeted = false } },
    RallyingCry = { ID = 97462, MAKULU_INFO = { targeted = false } },
    Vigilance = { ID = 114030, MAKULU_INFO = { targeted = true } },
    
    -- Utility Abilities
    Taunt = { ID = 355, MAKULU_INFO = { targeted = true } },
    Charge = { ID = 100, MAKULU_INFO = { targeted = true } },
    HeroicLeap = { ID = 6544, MAKULU_INFO = { targeted = false } },
    Pummel = { ID = 6552, MAKULU_INFO = { damageType = "physical", ignoreCasting = true } },
    HeroicThrow = { ID = 57755, MAKULU_INFO = { damageType = "physical" } },
    IntimidatingShout = { ID = 5246, MAKULU_INFO = { targeted = false } },
    SpellReflection = { ID = 23920, MAKULU_INFO = { targeted = false } },
    
    -- MoP Specific Abilities
    Avatar = { ID = 107574, MAKULU_INFO = { targeted = false } },
    Bladestorm = { ID = 46924, MAKULU_INFO = { targeted = false } },
    DragonRoar = { ID = 118000, MAKULU_INFO = { damageType = "physical" } },
    StormBolt = { ID = 107570, MAKULU_INFO = { damageType = "physical" } },
    Shockwave = { ID = 46968, MAKULU_INFO = { damageType = "physical" } },
    
    -- MoP Banners
    SkullBanner = { ID = 114207, MAKULU_INFO = { targeted = false } },
    DemoralizingBanner = { ID = 114203, MAKULU_INFO = { targeted = false } },
    MockingBanner = { ID = 114192, MAKULU_INFO = { targeted = false } },
    
    -- Cooldowns
    BerserkerRage = { ID = 18499, MAKULU_INFO = { targeted = false } },
    Recklessness = { ID = 1719, MAKULU_INFO = { targeted = false } },
    ShatteringThrow = { ID = 64382, MAKULU_INFO = { castTime = 1500 } },
    
    -- Utility
    VictoryRush = { ID = 34428, MAKULU_INFO = { damageType = "physical" } },
    ImpendingVictory = { ID = 103840, MAKULU_INFO = { damageType = "physical" } },
    
    -- Anti-fake abilities
    AntiFakeKick = { Type = "SpellSingleColor", ID = 6552, Hidden = true, Color = "GREEN", Desc = "[2] AntiFakeKick", QueueForbidden = true },
    AntiFakeCC = { Type = "SpellSingleColor", ID = 5246, Hidden = true, Color = "YELLOW", Desc = "[1] AntiFakeCC", QueueForbidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[Action.PlayerClass] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

-- MoP specific units
local player = MakUnit:new("player")
local target = MakUnit:new("target")
local arena1 = MakUnit:new("arena1")
local arena2 = MakUnit:new("arena2")
local arena3 = MakUnit:new("arena3")
local party1 = MakUnit:new("party1")
local party2 = MakUnit:new("party2")
local party3 = MakUnit:new("party3")
local mouseover = MakUnit:new("mouseover")

-- MoP Protection Warrior Buffs
local buffs = {
    defensiveStance = 71,
    battleStance = 2457,
    berserkerStance = 2458,
    battleShout = 6673,
    commandingShout = 469,
    shieldBlock = 2565,
    shieldBarrier = 112048,
    shieldWall = 871,
    lastStand = 12975,
    enragedRegeneration = 55694,
    rallyingCry = 97462,
    vigilance = 114030,
    berserkerRage = 18499,
    recklessness = 1719,
    avatar = 107574,
    bladestorm = 46924,
    skullBanner = 114207,
    demoralizingBanner = 114203,
    mockingBanner = 114192,
    victoryRush = 32216,
    ultimatum = 122509,
    swordAndBoard = 50227,
    enrage = 12880,
    weakenedBlows = 115798,
}

-- MoP Protection Warrior Debuffs
local debuffs = {
    sunderArmor = 58567,
    demoralizing = 1160,
    hamstring = 1715,
    thunderClap = 6343,
    weakenedBlows = 115798,
    colossusSmash = 86346,
}

-- Game state tracking
local gameState = {
    activeEnemies = 1,
    inCombat = false,
    rage = 0,
    timeToAdds = 999,
    isPvP = false,
    swordAndBoardProc = false,
    ultimatumProc = false,
    revengeProc = false,
    executeRange = false,
    needsActiveMitigation = false,
    incomingDamage = 0,
}

local function updateGameState()
    gameState.inCombat = player:InCombat()
    gameState.activeEnemies = MakMulti:GetActiveEnemies(8)
    gameState.rage = player.rage or 0
    gameState.isPvP = Action.IsInPvP or false
    gameState.swordAndBoardProc = player:HasBuff(buffs.swordAndBoard)
    gameState.ultimatumProc = player:HasBuff(buffs.ultimatum)
    gameState.revengeProc = Revenge:IsReady()
    gameState.executeRange = target.exists and target.hp <= 20
    
    -- Calculate incoming damage and mitigation needs
    gameState.incomingDamage = player.hp < 80 and 1 or 0
    gameState.needsActiveMitigation = player.hp < 70 or gameState.incomingDamage > 0
    
    -- TimeToAdds calculation using boss mod timers
    gameState.timeToAdds = MakuluFramework.TankBusterIn and MakuluFramework.TankBusterIn() or 999
end

-- Utility functions
local function shouldBurst()
    return A.GetToggle(2, "BurstMode")
end

local function shouldAoE()
    return gameState.activeEnemies >= 3
end

local function needsRage()
    return gameState.rage < 30
end

local function hasHighRage()
    return gameState.rage >= 80
end

local function shouldUseShieldBlock()
    return gameState.needsActiveMitigation and gameState.rage >= 60 and not player:HasBuff(buffs.shieldBlock)
end

local function shouldUseShieldBarrier()
    return gameState.needsActiveMitigation and gameState.rage >= 20 and not player:HasBuff(buffs.shieldBarrier)
end

-- Core ability callbacks
ShieldSlam:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end

    -- High priority threat generator, use on cooldown or with Sword and Board proc
    if gameState.swordAndBoardProc or spell:IsReady() then
        return spell:Cast(target)
    end
end)

Revenge:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end

    -- Use when available (procs on dodge/parry)
    if gameState.revengeProc then
        return spell:Cast(target)
    end
end)

Devastate:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end

    -- Filler ability and applies Sunder Armor
    return spell:Cast(target)
end)

ThunderClap:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 8 then return end

    -- High priority for AoE and Weakened Blows debuff
    if shouldAoE() or not target:HasDeBuff(debuffs.weakenedBlows) then
        return spell:Cast(target)
    end
end)

Execute:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end
    if not gameState.executeRange then return end

    -- High priority in execute range
    return spell:Cast(target)
end)

-- Active Mitigation
ShieldBlock:Callback(function(spell)
    if not shouldUseShieldBlock() then return end

    -- Primary physical damage mitigation
    return spell:Cast(player)
end)

ShieldBarrier:Callback(function(spell)
    if not shouldUseShieldBarrier() then return end

    -- Absorb shield for magical damage or when Shield Block is on cooldown
    return spell:Cast(player)
end)

-- Rage Spenders
HeroicStrike:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 5 then return end

    -- Use to prevent rage capping or with Ultimatum proc
    if hasHighRage() or gameState.ultimatumProc then
        return spell:Cast(target)
    end
end)

Cleave:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if gameState.activeEnemies < 2 then return end

    -- AoE version of Heroic Strike
    if hasHighRage() or gameState.ultimatumProc then
        return spell:Cast(target)
    end
end)

-- Stance management
DefensiveStance:Callback(function(spell)
    if player:HasBuff(buffs.defensiveStance) then return end

    -- Primary tanking stance
    return spell:Cast(player)
end)

BattleStance:Callback(function(spell)
    if player:HasBuff(buffs.battleStance) then return end
    if gameState.inCombat then return end -- Only switch out of combat

    -- For mobility or specific situations
    return spell:Cast(player)
end)

-- Shouts
BattleShout:Callback(function(spell)
    if player:HasBuff(buffs.battleShout) then return end
    if player:HasBuff(buffs.commandingShout) then return end

    return spell:Cast(player)
end)

CommandingShout:Callback(function(spell)
    if player:HasBuff(buffs.commandingShout) then return end
    if player:HasBuff(buffs.battleShout) then return end

    -- Alternative to Battle Shout for survivability
    if gameState.isPvP or player.hp < 80 then
        return spell:Cast(player)
    end
end)

DemoralizingShout:Callback(function(spell)
    if target.distance > 10 then return end
    if target:HasDeBuff(debuffs.demoralizing) then return end

    -- Damage reduction debuff
    return spell:Cast(target)
end)

ChallengingShout:Callback(function(spell)
    if not shouldAoE() then return end
    if not gameState.inCombat then return end

    -- AoE taunt for emergencies
    return spell:Cast(player)
end)

-- Defensive Cooldowns
ShieldWall:Callback(function(spell)
    if player.hp > 40 then return end

    -- Major defensive cooldown
    return spell:Cast(player)
end)

LastStand:Callback(function(spell)
    if player.hp > 50 then return end

    -- Health increase cooldown
    return spell:Cast(player)
end)

EnragedRegeneration:Callback(function(spell)
    if player.hp > 60 then return end

    -- Healing over time
    return spell:Cast(player)
end)

RallyingCry:Callback(function(spell)
    if not shouldAoE() then return end
    if player.hp > 70 then return end

    -- Raid-wide health increase
    return spell:Cast(player)
end)

Vigilance:Callback(function(spell)
    if not party1.exists then return end
    if party1.hp > 60 then return end

    -- Damage reduction for party member
    return spell:Cast(party1)
end)

-- Utility abilities
Taunt:Callback(function(spell)
    if target.distance > 30 then return end
    if not target.canAttack then return end
    if target.isTargetingMe then return end

    -- Single target taunt
    return spell:Cast(target)
end)

Charge:Callback(function(spell)
    if target.distance < 8 or target.distance > 25 then return end
    if not target.canAttack then return end

    return spell:Cast(target)
end)

HeroicLeap:Callback(function(spell)
    if target.distance < 8 then return end
    if not gameState.inCombat then return end

    -- Gap closer and AoE damage
    return spell:Cast(target)
end)

Pummel:Callback(function(spell)
    if target.distance > 5 then return end
    if not target:IsCasting() then return end
    if not target:IsInterruptible() then return end

    return spell:Cast(target)
end)

IntimidatingShout:Callback(function(spell)
    if target.distance > 8 then return end
    if not gameState.isPvP then return end

    -- PvP fear
    return spell:Cast(target)
end)

SpellReflection:Callback(function(spell)
    if not target:IsCasting() then return end
    if target.distance > 40 then return end

    -- Reflect incoming spells
    return spell:Cast(player)
end)

-- MoP Specific Abilities
Avatar:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    -- Major DPS cooldown
    return spell:Cast(player)
end)

Bladestorm:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if not shouldBurst() and not shouldAoE() then return end

    -- Use for burst or AoE
    return spell:Cast(player)
end)

DragonRoar:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 8 then return end

    -- AoE damage ability
    return spell:Cast(player)
end)

StormBolt:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 20 then return end

    -- High damage ability and stun
    return spell:Cast(target)
end)

Shockwave:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.distance > 10 then return end
    if not shouldAoE() then return end

    -- AoE stun and damage
    return spell:Cast(target)
end)

-- Banners
SkullBanner:Callback(function(spell)
    if not shouldBurst() then return end

    -- Major raid DPS cooldown
    return spell:Cast(player)
end)

DemoralizingBanner:Callback(function(spell)
    if not gameState.inCombat then return end
    if gameState.activeEnemies < 3 then return end

    -- Defensive raid cooldown for heavy damage phases
    return spell:Cast(player)
end)

MockingBanner:Callback(function(spell)
    if not shouldAoE() then return end
    if not gameState.inCombat then return end

    -- AoE taunt banner
    return spell:Cast(player)
end)

-- Cooldowns
BerserkerRage:Callback(function(spell)
    if player:HasBuff(buffs.enrage) then return end
    if needsRage() then return end

    -- Enrage for damage bonus
    return spell:Cast(player)
end)

Recklessness:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    -- Major DPS cooldown
    return spell:Cast(player)
end)

ShatteringThrow:Callback(function(spell)
    if target.distance > 30 then return end
    if not shouldBurst() then return end

    -- Armor reduction
    return spell:Cast(target)
end)

VictoryRush:Callback(function(spell)
    if not player:HasBuff(buffs.victoryRush) then return end
    if player.hp > 80 then return end

    return spell:Cast(target)
end)

-- Racial abilities
ArcaneTorrent:Callback(function(spell)
    if gameState.rage > 70 then return end
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

Berserking:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(player)
end)

BloodFury:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(player)
end)

QuakingPalm:Callback(function(spell)
    if target.distance > 5 then return end
    if not target:IsCasting() then return end

    return spell:Cast(target)
end)

-- Enhanced rotation functions
local function singleTargetRotation()
    -- Priority order for single target based on MoP Protection rotation

    -- Interrupt first
    Pummel()
    SpellReflection()

    -- Active Mitigation priority
    if shouldUseShieldBlock() then
        ShieldBlock()
    end

    if shouldUseShieldBarrier() then
        ShieldBarrier()
    end

    -- Execute range priority
    if gameState.executeRange then
        Execute()
        if hasHighRage() or gameState.ultimatumProc then
            HeroicStrike()
        end
        return
    end

    -- Core threat rotation
    -- Shield Slam on cooldown or with Sword and Board proc
    if gameState.swordAndBoardProc or ShieldSlam:IsReady() then
        ShieldSlam()
    end

    -- Revenge when available (procs on dodge/parry)
    if gameState.revengeProc then
        Revenge()
    end

    -- Maintain Weakened Blows debuff
    if not target:HasDeBuff(debuffs.weakenedBlows) then
        ThunderClap()
    end

    -- Devastate as filler
    Devastate()

    -- Rage management
    if hasHighRage() or gameState.ultimatumProc then
        HeroicStrike()
    end

    -- Utility
    DemoralizingShout()
    BattleShout()
    CommandingShout()
end

local function aoeRotation()
    -- Priority order for AoE (3+ enemies)

    -- Interrupt priority target
    Pummel()

    -- Active Mitigation priority
    if shouldUseShieldBlock() then
        ShieldBlock()
    end

    if shouldUseShieldBarrier() then
        ShieldBarrier()
    end

    -- AoE threat abilities
    ThunderClap() -- High priority for AoE

    -- Revenge when available
    if gameState.revengeProc then
        Revenge()
    end

    -- Shield Slam for single target threat
    if gameState.swordAndBoardProc or ShieldSlam:IsReady() then
        ShieldSlam()
    end

    -- Execute in execute range
    if gameState.executeRange then
        Execute()
    end

    -- Devastate as filler
    Devastate()

    -- AoE rage spenders
    if hasHighRage() or gameState.ultimatumProc then
        if gameState.activeEnemies >= 2 then
            Cleave()
        else
            HeroicStrike()
        end
    end

    -- AoE abilities
    Shockwave()
    DragonRoar()

    -- Emergency AoE taunt
    ChallengingShout()
    MockingBanner()
end

local function pvpRotation()
    -- PvP specific rotation with enhanced control and survivability

    -- Defensive priority
    if player.hp <= 25 then
        ShieldWall()
        LastStand()
    end

    if player.hp <= 50 then
        EnragedRegeneration()
        RallyingCry()
    end

    -- Active mitigation
    if shouldUseShieldBlock() then
        ShieldBlock()
    end

    if shouldUseShieldBarrier() then
        ShieldBarrier()
    end

    -- Interrupt and control
    Pummel()
    IntimidatingShout()
    StormBolt()
    Shockwave()

    -- Burst damage
    if shouldBurst() then
        Recklessness()
        Avatar()
        SkullBanner()
        Bladestorm()
    end

    -- Mobility
    Charge()
    HeroicLeap()

    -- Core rotation with PvP priorities
    if gameState.executeRange then
        Execute()
        HeroicStrike()
    else
        -- Standard rotation but more aggressive
        ShieldSlam()
        Revenge()
        ThunderClap()
        Devastate()
    end

    -- Utility
    Taunt()
    SpellReflection()
end

local function timeToAddsRotation()
    -- Prepare for incoming adds

    -- Save cooldowns for adds
    if gameState.timeToAdds < 5000 then
        -- Use cooldowns just before adds spawn
        Recklessness()
        Avatar()
        SkullBanner()
        Bladestorm()
    end

    -- Pool rage for burst
    if gameState.rage < 60 then
        BerserkerRage() -- Generate rage
    else
        -- Continue normal rotation but conserve resources
        ShieldSlam()
        Revenge()
        ThunderClap()
    end

    -- Prepare active mitigation
    if gameState.timeToAdds < 3000 then
        ShieldBlock()
        ShieldBarrier()
    end
end

-- Enhanced main rotation
local function enhancedMainRotation()
    updateGameState()

    -- Emergency responses
    if player.hp <= 20 then
        ShieldWall()
        LastStand()
    end

    if player.hp <= 40 then
        EnragedRegeneration()
        RallyingCry()
    end

    -- Stance management
    DefensiveStance()

    -- Shout maintenance
    BattleShout()
    CommandingShout()

    -- Racial abilities during burst
    if shouldBurst() then
        BloodFury()
        Berserking()
        ArcaneTorrent()
    end

    -- TimeToAdds logic
    if gameState.timeToAdds < 10000 then
        timeToAddsRotation()
        return
    end

    -- PvP specific logic
    if gameState.isPvP then
        pvpRotation()
        return
    end

    -- Choose rotation based on enemy count
    if shouldAoE() then
        aoeRotation()
    else
        singleTargetRotation()
    end
end

-- Main function A[1] - Standard rotation
A[1] = function(icon)
    RegisterIcon(icon)

    enhancedMainRotation()

    return FrameworkEnd()
end

-- MoP Debug and Enhanced Function
A[3] = function(icon)
    FrameworkStart(icon)
    updateGameState()

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Player HP: ", player.hp)
        MakPrint(2, "Player Rage: ", gameState.rage)
        MakPrint(3, "Active Enemies: ", gameState.activeEnemies)
        MakPrint(4, "In Combat: ", gameState.inCombat)
        MakPrint(5, "Time to Adds: ", gameState.timeToAdds)
        MakPrint(6, "Is PvP: ", gameState.isPvP)
        MakPrint(7, "Sword and Board Proc: ", gameState.swordAndBoardProc)
        MakPrint(8, "Ultimatum Proc: ", gameState.ultimatumProc)
        MakPrint(9, "Revenge Proc: ", gameState.revengeProc)
        MakPrint(10, "Execute Range: ", gameState.executeRange)
        MakPrint(11, "Needs Active Mitigation: ", gameState.needsActiveMitigation)
        MakPrint(12, "Shield Block Active: ", player:HasBuff(buffs.shieldBlock))
        MakPrint(13, "Shield Barrier Active: ", player:HasBuff(buffs.shieldBarrier))
    end

    local awareAlert = A.GetToggle(2, "makAware")
    if awareAlert[1] then
        if Recklessness:IsReady() and shouldBurst() then
            Aware:displayMessage("RECKLESSNESS READY", "Red", 1)
        end
        if Avatar:IsReady() and shouldBurst() then
            Aware:displayMessage("AVATAR READY", "Blue", 1)
        end
        if ShieldSlam:IsReady() and gameState.swordAndBoardProc then
            Aware:displayMessage("SWORD AND BOARD PROC", "Yellow", 1)
        end
        if gameState.rage >= 100 then
            Aware:displayMessage("RAGE CAPPED", "Orange", 1)
        end
        if gameState.ultimatumProc then
            Aware:displayMessage("ULTIMATUM PROC", "Green", 1)
        end
        if gameState.revengeProc then
            Aware:displayMessage("REVENGE READY", "Purple", 1)
        end
        if gameState.executeRange then
            Aware:displayMessage("EXECUTE RANGE", "Purple", 1)
        end
        if gameState.timeToAdds < 5000 and gameState.timeToAdds > 0 then
            Aware:displayMessage("ADDS INCOMING: " .. math.floor(gameState.timeToAdds/1000) .. "s", "Cyan", 1)
        end
        if gameState.needsActiveMitigation and not player:HasBuff(buffs.shieldBlock) and not player:HasBuff(buffs.shieldBarrier) then
            Aware:displayMessage("USE ACTIVE MITIGATION", "White", 1)
        end
        if not target:HasDeBuff(debuffs.weakenedBlows) then
            Aware:displayMessage("APPLY WEAKENED BLOWS", "Cyan", 1)
        end
    end

    -- Enhanced defensive priority
    if player.hp <= 20 then
        if ShieldWall:IsReady() then return FrameworkEnd() end
        if LastStand:IsReady() then return FrameworkEnd() end
    end

    if player.hp <= 40 then
        if EnragedRegeneration:IsReady() then return FrameworkEnd() end
        if RallyingCry:IsReady() then return FrameworkEnd() end
    end

    -- Enhanced active mitigation priority
    if gameState.needsActiveMitigation then
        if shouldUseShieldBlock() and ShieldBlock:IsReady() then
            if ShieldBlock() then return FrameworkEnd() end
        end
        if shouldUseShieldBarrier() and ShieldBarrier:IsReady() then
            if ShieldBarrier() then return FrameworkEnd() end
        end
    end

    if target.exists and target.alive then
        -- Enhanced interrupt priority
        if target:IsCasting() and target:IsInterruptible() then
            if Pummel() then return FrameworkEnd() end
            if StormBolt() then return FrameworkEnd() end
        end

        -- Taunt if not targeting us
        if not target.isTargetingMe and target.canAttack then
            if Taunt() then return FrameworkEnd() end
        end

        -- PvP specific abilities
        if gameState.isPvP then
            if A.Zone ~= "arena" then
                if IntimidatingShout() then return FrameworkEnd() end
                if Shockwave() then return FrameworkEnd() end
            end
        end

        -- Burst phase
        if shouldBurst() then
            if Recklessness() then return FrameworkEnd() end
            if Avatar() then return FrameworkEnd() end
            if SkullBanner() then return FrameworkEnd() end
            if Bladestorm() then return FrameworkEnd() end

            -- Racial abilities during burst
            if BloodFury() then return FrameworkEnd() end
            if Berserking() then return FrameworkEnd() end
        end

        -- TimeToAdds preparation
        if gameState.timeToAdds < 10000 then
            timeToAddsRotation()
        else
            -- Enhanced rotation selection
            if shouldAoE() then
                aoeRotation()
            else
                singleTargetRotation()
            end
        end
    end

    return FrameworkEnd()
end

-- Arena functions
local function enhancedArenaRotation(enemy)
    if not enemy.exists then return end

    -- Interrupt priority
    Pummel("arena", enemy)
    StormBolt("arena", enemy)

    -- CC abilities
    IntimidatingShout("arena", enemy)
    Shockwave("arena", enemy)

    -- Burst damage
    if shouldBurst() then
        Recklessness("arena")
        Avatar("arena")
        Bladestorm("arena")
    end

    -- Execute priority
    if enemy.hp <= 20 then
        Execute("arena", enemy)
        HeroicStrike("arena", enemy)
    else
        -- Standard rotation
        ShieldSlam("arena", enemy)
        Revenge("arena", enemy)
        ThunderClap("arena", enemy)
        Devastate("arena", enemy)
    end

    -- Active mitigation
    if player.hp < 70 then
        ShieldBlock("arena")
        ShieldBarrier("arena")
    end
end

local function enhancedPartyRotation(friendly)
    if not friendly.exists then return end

    -- Support abilities for Protection Warrior
    if friendly.hp < 30 then
        Vigilance("arena", friendly)
        RallyingCry("arena")
    end

    if friendly.hp < 50 then
        DemoralizingBanner("arena")
    end
end

-- Define the arena and party rotation functions
local function arenaRotation(enemy)
    enhancedArenaRotation(enemy)
end

local function partyRotation(friendly)
    enhancedPartyRotation(friendly)
end

A[6] = function(icon)
    RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and target:IsCasting() then
        Pummel()
    end
    if Action.Zone == "arena" then
        arenaRotation(arena1)
        partyRotation(party1)
    end

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena2)
        partyRotation(party2)
    end

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)
    if Action.Zone == "arena" then
        arenaRotation(arena3)
        partyRotation(party3)
    end

    return FrameworkEnd()
end

-- Arena-specific callback functions for MoP Protection Warrior
Pummel:Callback("arena", function(spell, enemy)
    if not enemy:IsCasting() then return end
    if not enemy:IsInterruptible() then return end
    if enemy.distance > 5 then return end
    if enemy:IsKickImmune() then return end

    return spell:Cast(enemy)
end)

StormBolt:Callback("arena", function(spell, enemy)
    if enemy.distance > 20 then return end
    if enemy:IsKickImmune() then return end

    -- Use for interrupt or high damage
    if enemy:IsCasting() and enemy:IsInterruptible() then
        Aware:displayMessage("Storm Bolt - Interrupt", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use for damage during burst
    if shouldBurst() and enemy.hp > 30 then
        Aware:displayMessage("Storm Bolt - Damage", "Red", 1)
        return spell:Cast(enemy)
    end
end)

IntimidatingShout:Callback("arena", function(spell, enemy)
    if enemy.distance > 8 then return end
    if enemy:HasDeBuff(5246) then return end
    if enemy.hp < 30 then return end -- Don't CC low targets

    -- Use for peeling when party members are low
    local peelParty = (party1.exists and party1.hp < 40) or (party2.exists and party2.hp < 40)
    if peelParty and enemy.hp > 50 then
        Aware:displayMessage("Intimidating Shout - Peeling", "Yellow", 1)
        return spell:Cast(enemy)
    end

    -- Use on healers
    if enemy.isHealer and enemy.hp > 60 then
        Aware:displayMessage("Intimidating Shout - Healer", "Green", 1)
        return spell:Cast(enemy)
    end
end)

ShieldSlam:Callback("arena", function(spell, enemy)
    if enemy.distance > 5 then return end

    -- High threat and damage ability
    Aware:displayMessage("Shield Slam - Threat", "Red", 1)
    return spell:Cast(enemy)
end)

Recklessness:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Recklessness - Burst", "Red", 1)
    return spell:Cast(player)
end)

Avatar:Callback("arena", function(spell)
    if not shouldBurst() then return end

    -- Major burst cooldown
    Aware:displayMessage("Avatar - Burst", "Blue", 1)
    return spell:Cast(player)
end)

Bladestorm:Callback("arena", function(spell)
    if not shouldBurst() and gameState.activeEnemies < 2 then return end

    -- Use for burst or when multiple enemies
    if shouldBurst() then
        Aware:displayMessage("Bladestorm - Burst", "Orange", 1)
    else
        Aware:displayMessage("Bladestorm - AoE", "Yellow", 1)
    end
    return spell:Cast(player)
end)

Vigilance:Callback("arena", function(spell, friendly)
    if friendly.hp > 60 then return end

    -- Damage reduction for ally
    Aware:displayMessage("Vigilance - Protection", "Blue", 1)
    return spell:Cast(friendly)
end)

-- Utility functions
local function racials()
    ArcaneTorrent()
    Berserking()
    BloodFury()
    QuakingPalm()
end

local function mopTalents()
    DragonRoar()
    Bladestorm()
    StormBolt()
    Shockwave()
    Avatar()
end

local function baseStuff()
    DefensiveStance()
    BattleShout()
    CommandingShout()
    EnragedRegeneration()
    LastStand()
    ShieldWall()
    BerserkerRage()
end

local function baseStuffCombat()
    Pummel()
    Charge()
    VictoryRush()
    HeroicLeap()
    Taunt()
    SpellReflection()
end

-- Enhanced utility for MoP Protection Warrior
local function mopUtility()
    Pummel()
    IntimidatingShout()
    StormBolt()
    Charge()
    HeroicLeap()
    HeroicThrow()
    ShatteringThrow()
    Taunt()
    ChallengingShout()
    SpellReflection()
end

-- Active mitigation utility
local function activeMitigation()
    ShieldBlock()
    ShieldBarrier()
    ShieldWall()
    LastStand()
    EnragedRegeneration()
    RallyingCry()
    Vigilance()
end

-- Combat tracking
local function combatStart()
    gameState.swordAndBoardProc = false
    gameState.ultimatumProc = false
    gameState.revengeProc = false
end

local function combatEnd()
    gameState.swordAndBoardProc = false
    gameState.ultimatumProc = false
    gameState.revengeProc = false
end

-- Event handling
local eventFrame = CreateFrame("Frame")
eventFrame:RegisterEvent("PLAYER_REGEN_DISABLED")
eventFrame:RegisterEvent("PLAYER_REGEN_ENABLED")
eventFrame:SetScript("OnEvent", function(self, event)
    if event == "PLAYER_REGEN_DISABLED" then
        combatStart()
    elseif event == "PLAYER_REGEN_ENABLED" then
        combatEnd()
    end
end)

-- Initialize
Action[ACTION_CONST_WARRIOR_PROTECTION] = A
