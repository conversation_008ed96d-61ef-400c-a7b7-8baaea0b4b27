local Tinkr, MakuluFramework = ...

local Spell                  = MakuluFramework.Spell
local Cache                  = MakuluFramework.Cache
local Unit                   = MakuluFramework.Unit
local spellState             = MakuluFramework.spellState

local player                 = MakuluFramework.ConstUnits.player

local SpellCache             = Cache:getConstCacheCell()

local IsSpellInRange         = C_Spell.IsSpellInRange

local cantAttack             = "can't attack"
local notFacing              = "not facing"
local los                    = "los of target"
local notUsable              = "not usable on target"
local outOfRange             = "out of range"

local GetTime                = GetTime

function Spell:InRange(target)
    local targetId = rawget(target, "id")
    return self.cache:GetOrSet("Range" .. targetId, function()
        return IsSpellInRange(rawget(self, "wowName"), targetId) == true
    end)
end

function Spell:FallbackRange(target)
    return self.cache:GetOrSet("fallbackRange", function()
        local info = Spell.SpellInfo(self)
        if not info or not info.maxRange or info.maxRange == 0 then return end

        local targetDistance = Unit.Distance(target)
        if not targetDistance then return end

        if info.minRange and targetDistance < info.minRange then return end

        return targetDistance < info.maxRange
    end)
end

local function janky_range(spell, target)
    if spell.hasRange then
        if spell:InRange(target) then
            return true
        end
    end

    return target.inMelee or Spell.FallbackRange(spell, target)
end

function Spell:TinkrRange(target)
    return janky_range(self, target)
end

local CastSpellByName = CastSpellByName
local CastSpellByID = CastSpellByID
local SpellCancelQueuedSpell = SpellCancelQueuedSpell

function Spell:WouldCast(target)
    local suffix = ""
    if target then
        suffix = rawget(target, "guid")
    end

    local would_cast = self.cache:GetOrSet("wouldcast" .. suffix, function()
        if target then
            if not target.exists then return "doesn't exist" end
        end

        if target and not target.isMe then
            if not rawget(self, "heal") then
                if target and not target.canAttack then return cantAttack end
                if not rawget(self, "ignoreFacing") and not target.facing then return notFacing end
            end

            if not rawget(self, "ignoreLos") and not target:Los() then return los end
            if not rawget(self, "ignoreRange") and rawget(self, "targeted") and not janky_range(self, target) then
                return outOfRange
            end
        end
        if target and not Spell.Usable(self, target) then return notUsable end

        return true
    end)

    if would_cast == true then return true end

    return false, would_cast
end

local preSpam = 20

local function regenDelay()
    preSpam = math.random(20, 80)

    return C_Timer.After(1, regenDelay)
end

regenDelay()

function Spell:SpecificBlockers()
    if rawget(self, "offGcd") then return end

    return SpellCache:GetOrSet("Blockers", function()
        if not MakuluFramework.SQW then return false end

        if MakuluFramework.gcd() > MakuluFramework.SQW - preSpam then
            return true
        end
    end)
end

local last_cast_target = nil
local last_cast_name = nil
local last_cast_time = nil

local check_pending_cast = function(name, target)
    if target and last_cast_target then
        if target.guid ~= last_cast_target then return end
    end

    local current = C_Spell.IsCurrentSpell(name)
    if current then
        return true
    end

    local casts = GetPendingCasts()
    if not casts then return end

    for i = 1, #casts do
        local info = C_Spell.GetSpellInfo(casts[i])

        if info.name == name then
            return true
        end
    end
end

function Spell:Cast(target, spellID)
    local wouldCast, reason = Spell.WouldCast(self, target)
    if wouldCast == false then return false, reason end

    local now = GetTime()
    local castTarget = target and Object(rawget(target, "guid"))

    local spellName = rawget(self, "wowName")

    if last_cast_name
        and spellName == last_cast_name
        and last_cast_target == castTarget
        and (now - last_cast_time) < (MakuluFramework.SQW / 1000) then
        spellState.casted = true
        return true
    end

    SpellCancelQueuedSpell()

    -- Check cast method if spellID is given or no
    if spellID then
        CastSpellByID(spellID, castTarget)
    else
        CastSpellByName(spellName, castTarget)
    end

    rawset(self, "lastAttemptTime", (now * 1000))

    spellState.casted = true
    last_cast_target = castTarget
    last_cast_name = spellName
    last_cast_time = now

    -- local name = (target and target.name) or " no target"
    -- print('Casting on ' .. name .. " spell is: " .. rawget(self, "wowName"))
    return true
end

local IsSpellPending = IsSpellPending
local IsMouselooking = IsMouselooking
local MouselookStop = MouselookStop
local MouselookStart = MouselookStart
local Click = Click

local cant_find_unit = "can't find unit position"
local spell_cast_failed = "spell cast failed"

local function apply_slight_randomness(position)
    position.x = position.x + ((math.random(0, 1) - 0.5) / 2)
    position.y = position.y + ((math.random(0, 1) - 0.5) / 2)
end

local function apply_slight_randomness_to_b(position, target)
    target.x = position.x + ((math.random(0, 1) - 0.5) / 2)
    target.y = position.y + ((math.random(0, 1) - 0.5) / 2)
end

function Spell:AoECast(target_or_x, y, z)
    local target_pos
    local skip_random = false

    if type(target_or_x) == "table" then
        target_pos = Unit.Position(target_or_x)

        if not target_pos or not target_pos.x then
            return false, cant_find_unit
        end

        if y then skip_random = true end
    elseif type(target_or_x) == "number" and y and z then
        target_pos = { x = target_or_x, y = y, z = z }
    else
        return false, cant_find_unit
    end

    local wasMouseLooking = IsMouselooking()
    -- print('Casting : ' .. rawget(self, 'wowName'))
    local spell_range = Spell.Range(self)

    local distance = player:DistanceToPos(target_pos.x, target_pos.y, target_pos.z)

    if spell_range < distance then
        return false, outOfRange
    end
    if not player:LoSCoords(target_pos.x, target_pos.y, target_pos.z) then
        return false, los
    end

    if MakuluFramework.gcd() > 0 then
        spellState.casted = true
        return true
    end

    if not skip_random then
        local loops = 0
        local failReason = nil
        local base_pos = { x = target_pos.x, y = target_pos.y, z = target_pos.z }

        while loops < 50 do
            failReason = nil

            apply_slight_randomness_to_b(base_pos, target_pos)
            distance = player:DistanceToPos(target_pos.x, target_pos.y, target_pos.z)

            if spell_range >= distance then
                if player:LoSCoords(target_pos.x, target_pos.y, target_pos.z) then
                    loops = 999
                else
                    failReason = los
                end
            else
                failReason = outOfRange
            end

            loops = loops + 1
        end

        if failReason then
            return false, failReason
        end
    end

    SpellCancelQueuedSpell()
    CastSpellByName(rawget(self, "wowName"))

    if IsSpellPending() ~= 64 then
        return false, spell_cast_failed
    end

    MouselookStop()

    local count = 0
    while count < 10 do
        Click(target_pos.x, target_pos.y, target_pos.z)

        if IsSpellPending() ~= 64 then
            count = 999
        end

        count = count + 1
    end

    if wasMouseLooking then
        MouselookStart()
    end

    spellState.casted = true
    return true
end
