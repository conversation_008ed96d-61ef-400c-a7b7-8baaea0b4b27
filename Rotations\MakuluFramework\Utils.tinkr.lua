local Tinkr, MakuluFramework  = ...
MakuluFramework               = MakuluFramework or _G.MakuluFramework

local Evaluator               = Tinkr:require('Util.Modules.Evaluator')

MakuluFramework.callBlizz     = function(method, ...)
    return Evaluator:CallProtectedFunction(method, ...)
end

local debounceCache           = {}

MakuluFramework.debounceSpell = function(key, min, reset, spell, unit)
    local matching = debounceCache[key]
    local now = GetTime() * 1000

    if not matching then
        debounceCache[key] = now
        return
    end

    if matching + reset < now then
        debounceCache[key] = now
        return
    end

    if matching + min > now then return end

    local result

    if unit then
        result = spell:Cast(unit)
    else
        result = spell:Cast()
    end

    return result
end
