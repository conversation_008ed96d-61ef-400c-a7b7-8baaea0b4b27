if not Ma<PERSON>luValidCheck() then return true end
if not <PERSON><PERSON><PERSON>_magic_number == 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 270 then return end

local CONST                        = Action.Const
local Create                       = Action.Create

local ACTION_CONST_MONK_MISTWEAVER = CONST.MONK_MISTWEAVER

local FrameworkStart               = MakuluFramework.start
local FrameworkEnd                 = MakuluFramework.endFunc
local RegisterIcon                 = MakuluFramework.registerIcon

local AtoL                         = MakuluFramework.AtoL
local MakMulti                     = MakuluFramework.MultiUnits
local TableToLocal                 = MakuluFramework.tableToLocal
local Lists                        = MakuluFramework.lists
local ConstUnits                   = MakuluFramework.ConstUnits
local Debounce                     = MakuluFramework.debounceSpell

local target                       = ConstUnits.target
local focus                        = ConstUnits.focus
local player                       = ConstUnits.player
local enemyHealer                  = ConstUnits.enemyHealer

local arena1                       = ConstUnits.arena1
local arena2                       = ConstUnits.arena2
local arena3                       = ConstUnits.arena3

local party1                       = ConstUnits.party1
local party2                       = ConstUnits.party2
local party3                       = ConstUnits.party3

local healTarget                   = nil
local Aware                        = MakuluFramework.Aware

local Action                       = _G.Action

local HealingEngine    = Action.HealingEngine

local _G, setmetatable             = _G, setmetatable

Aware:enable()

local ActionID = {
    FaelineStomp            = { Type = "Spell", ID = 388193, Texture = 291944 },

    AlphaTiger              = { Type = "Spell", ID = 287503, Hidden = true },
    RecentlyChallenged      = { Type = "Spell", ID = 290512, Hidden = true },
    AncientTeachings        = { Type = "Spell", ID = 388026, Hidden = true },
    AwakenedFaeline         = { Type = "Spell", ID = 389387, Hidden = true },
    TeachingsOfTheMonastery = { Type = "Spell", ID = 202090, Hidden = true },
    VaviciousVificationBuff = { Type = "Spell", ID = 392883, Hidden = true },
    VaviciousVification     = { Type = "Spell", ID = 388812, Hidden = true },
    MistsofLife             = { Type = "Spell", ID = 388548, Hidden = true },
    ChiHarmony              = { Type = "Spell", ID = 448392, Hidden = true },

    SphereHelp              = { Type = "Spell", ID = 410777, MAKULU_INFO = { heal = true } },
    SphereHarm              = { Type = "Spell", ID = 410777, Texture = 274738, Hidden = true, MAKULU_INFO = { damageType = "magic" } },

    RisingSunKick           = { Type = "Spell", ID = 107428, MAKULU_INFO = { damageType = "physical" } },
    BlackoutKick            = { Type = "Spell", ID = 100784, MAKULU_INFO = { damageType = "physical" } },
    TigersPalm              = { Type = "Spell", ID = 100780, MAKULU_INFO = { damageType = "physical" } },
    SpinningCraneKick       = { Type = "Spell", ID = 101546, MAKULU_INFO = { damageType = "physical", targeted = false } },
    RushingWindKick         = { Type = "Spell", ID = 467307, Texture = 388809, MAKULU_INFO = { damageType = "physical" } },
    TouchOfDeath            = { Type = "Spell", ID = 322109, MAKULU_INFO = { damageType = "physical" } },
    Disable                 = { Type = "Spell", ID = 116095, MAKULU_INFO = { damageType = "physical" } },
    GrappleWeapon           = { Type = "Spell", ID = 233759, MAKULU_INFO = { damageType = "physical" } },
    SpearHandStrike         = { Type = "Spell", ID = 116705, MAKULU_INFO = { damageType = "physical", offGcd = true } },

    Prevoke                 = { Type = "Spell", ID = 115546 },
    Detox                   = { Type = "Spell", ID = 115450, MAKULU_INFO = { heal = true } },
    LifeCoccoon             = { Type = "Spell", ID = 116849, MAKULU_INFO = { heal = true } },
    Vivify                  = { Type = "Spell", ID = 116670, MAKULU_INFO = { heal = true, ignoreCasting = true } },
    SoothingMists           = { Type = "Spell", ID = 115175, MAKULU_INFO = { heal = true, ignoreCasting = true, dumbCast = true } },
    RenewingMist            = { Type = "Spell", ID = 115151, MAKULU_INFO = { heal = true } },
    --RenewingMistP            = { Type = "Spell", ID = 115151, FixedTexture = 133667, MAKULU_INFO = { heal = true } },
    EnvelopingMist          = { Type = "Spell", ID = 124682, MAKULU_INFO = { heal = true, ignoreCasting = true } },
    ExpelHarm               = { Type = "Spell", ID = 322101, MAKULU_INFO = { heal = true, targeted = false } },
    HealingElixir           = { Type = "Spell", ID = 122281, MAKULU_INFO = { heal = true, offGcd = true } },
    ThunderFocusTea         = { Type = "Spell", ID = 116680, MAKULU_INFO = { heal = true, offGcd = true, targeted = false } },
    SheilunsGift            = { Type = "Spell", ID = 399491, Texture = 312411 },
    ZenPulse                = { Type = "Spell", ID = 446326 },
    EssenceFont             = { Type = "Spell", ID = 191837 },
    ManaTea                 = { Type = "Spell", ID = 115294 },
    CracklingJadeLightning  = { Type = "Spell", ID = 117952 },

    ChiWave                 = { Type = "Spell", ID = 115098 },

    InvokeChiJi             = { Type = "Spell", ID = 325197, MAKULU_INFO = { heal = true, offGcd = true, targeted = false } },
    InvokeChiJiBuff         = { Type = "Spell", ID = 343820 },

    FortifyingBrew          = { Type = "Spell", ID = 115203 },
    DampenHarm              = { Type = "Spell", ID = 122278 },
    Restoral                = { Type = "Spell", ID = 388615 },
    Revival                 = { Type = "Spell", ID = 115310 },
    Paralysis               = { Type = "Spell", ID = 115078 },

    TigersLust              = { Type = "Spell", ID = 116841 },
    LegSweep                = { Type = "Spell", ID = 119381 },
    SongofChiJi             = { Type = "Spell", ID = 198898 },
    RingofPeace             = { Type = "Spell", ID = 116844 },
    ImprovedDispelTalent    = { Type = "Spell", ID = 388874, Hidden = true },

    CursedSpirit            = { Type = "Spell", ID = 409465, Hidden = true },
    PoisonedSpirit          = { Type = "Spell", ID = 409470, Hidden = true },
    DiseasedSpirit          = { Type = "Spell", ID = 409472, Hidden = true },
    ManaTeaBuff             = { Type = "Spell", ID = 115867, Hidden = true },
    Transcendence           = { Type = "Spell", ID = 101643 },
    TranscendenceTransfer   = { Type = "Spell", ID = 119996 },
    SummonWhiteTigerStatue  = { Type = "Spell", ID = 388686 },
    SummonJadeSerpentStatue = { Type = "Spell", ID = 115313 },
}


local counterMagicDispelList = AtoL({
    "Flame Shock",
    "Moonfire",
    "Sunfire",
    "Vampiric Touch"
})

local ccDispelList = AtoL({
    "Sleep Walk",
    390612, --frost bomb
    "Frost Bomb",
    "Fear",
    "Psychic Horror",
    "Psychic Scream",
    "Freezing Trap",
    "Hammer of Justice",
    "Repentance",
    "Polymorph",
    "Chaos Nova",
    "Havoc",
    "Soul Rot",
    "Landslide",
    "Entangling Roots",
    "Frost Nova",
    "Absolute Zero",
    "Dread of Winter",
    "Silence",
    "Entangling Roots",
    "Landslide",
    "Dragon's Breath",
    "Absoulute Zero",
    "Dead of Winter",
    "Strangulate",
    "Searing Glare",

})

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

Action[ACTION_CONST_MONK_MISTWEAVER] = A
TableToLocal(M, getfenv(1))

local buffs = {
    renewingMists = 119611,
    soothingTotem = 198533,
    soothing = 115175,
    enveloping = 124682,
    chiJiEnvelop = 343820,
    tft = 116680,
    vificiation = 392883,
    topEnvelop = 393988,
    sphereOfHope = 411036,
    sphereOfDispear = 411038,
    manaTea = 115867,
    topRSK = 393988,
    topExpel = 388524,
    vivaciousVivication = 392883,
    craneBuff = 343820,
}

local function calculateHealTarget()
    if target.exists and target.isFriendly then
        healTarget = target
    elseif focus.exists and focus.isFriendly then
        healTarget = focus
    else
        healTarget = nil
    end
end

local function instaCastStuff()
    local castInfo = player.castOrChannelInfo
    if castInfo then
        return castInfo.spellId == SoothingMists.id
    end

    return player:Buff(buffs.tft)
end

local function rwkTFT()
    return player:Buff(buffs.tft) or player:Buff(buffs.topRSK)
end

local arenaKicks = Lists.arenaKicks

local kickPercent = 32
local meldDuration = 0.9
local shortHalfSecond = 620
local channelKickTime = 400
local quickKick = 15

local function generateNewRandomKicks()
    kickPercent = math.random(40, 90)
    meldDuration = math.random(600, 1200) / 1000
    shortHalfSecond = math.random(400, 600) / 1000
    channelKickTime = math.random(300, 800)
    quickKick = math.random(10, 20)

    return C_Timer.After(math.random(15, 30), generateNewRandomKicks)
end

generateNewRandomKicks()

local RenewingStore = {}

local function on_spell_cast_success()
    local timestamp, subevent, hideCaster, sourceGUID, sourceName, sourceFlags, sourceRaidFlags, destGUID, destName, destFlags, destRaidFlags, spellID, spellName, spellSchool =
        CombatLogGetCurrentEventInfo()
    if subevent ~= "SPELL_CAST_SUCCESS" then return end
    if sourceGUID ~= player.guid then return end
    if spellID ~= RenewingMist.id then if (IsPlayerSpell(MistsofLife.id) and spellID ~= LifeCoccoon.id) then return end end

    RenewingStore[destGUID] = GetTime() * 1000
end
MakuluFramework.Events.register("COMBAT_LOG_EVENT_UNFILTERED", on_spell_cast_success)

local function hasChiHarmondy(unit)
  local found = RenewingStore[unit.guid]
  if not found then return false end

  local difference = (GetTime() * 1000) - found

  return difference < 8000
end

local function chiHarmonyRemains(unit)
  local found = RenewingStore[unit.guid]
  if not found then return 0 end

  local difference = (GetTime() * 1000) - found
  if difference > 8000 then return 0 end

  return 8000 - difference
end

SpearHandStrike:Callback(function(spell, unit)
    if not unit.exists then return end
    if not unit.player then return end
    if not unit:IsSafeToKick() then return end

    local castOrChannel = unit.castOrChannelInfo
    if not castOrChannel then return end -- Ensure castOrChannelInfo exists

    if not arenaKicks[castOrChannel.spellId] then return end

    if not castOrChannel.channel and castOrChannel.percent < kickPercent then return end
    if castOrChannel.channel and castOrChannel.elapsed < channelKickTime then return end

    return spell:Cast(unit)
end)

SphereHarm:Callback(function(spell, unit)
    if not target.exists then return end
    if not target.canAttack then return end
    if target:Debuff(buffs.sphereOfDispear) then return end
    if target.totalImmune then return end
    if target.magicImmune then return end

    return spell:Cast(unit)
end)

Vivify:Callback("bigvv", function(spell)
    if not healTarget then return end
    if healTarget.hp > 35 then return end
    if chiHarmonyRemains(healTarget) < 1000 then return end
    if not player:Buff(buffs.vificiation) then return end

    return spell:Cast(healTarget)
end)

Vivify:Callback("vv", function(spell)
    if not healTarget then return end
    if healTarget.hp > 70 then return end
    if player:Buff(buffs.tft) then return end
    if not player:Buff(buffs.vificiation) then return end

    return spell:Cast(healTarget)
end)

FortifyingBrew:Callback(function (spell)
    if player.hp > 60 then return end

    Debounce("FortBrew", 500, 4500, spell)
end)

ThunderFocusTea:Callback(function(spell)
    if not healTarget then return end
    if healTarget.hp > 80 then return end
    if RushingWindKick.cd > 1000 then return end
    --if healTarget.hp < 30 then return end
    if player:Buff(buffs.tft) then return end
    if player:Buff(buffs.topRSK) and RisingSunKick:Cooldown() < 2000 and target.canAttack and target.distance <= 15 then return end
    if player:Buff(buffs.topEnvelop) and EnvelopingMist:Cooldown() < 2000 then return end

    return spell:Cast()
end)

Vivify:Callback(function(spell)
    if not healTarget then return end
    if player:Buff(buffs.tft) then return end

    local castInfo = player.castOrChannelInfo
    if castInfo then
        if castInfo.spellId ~= SoothingMists.id then return end
    else
        if not player:Buff(buffs.vificiation) then return end
    end

    if healTarget.hp > 80 then return end

    return spell:Cast(healTarget)
end)

RenewingMist:Callback("arenaGates", function(spell, unit)
    if player.combatTime > 0 then return end
    if player:Buff(32727) then return end

    if party1.exists and not party1:Buff(buffs.renewingMists) then
        HealingEngine.SetTarget(party1:CallerId(), 1)
        return spell:Cast(unit)
    end

    if party2.exists and not party2:Buff(buffs.renewingMists) then
        HealingEngine.SetTarget(party2:CallerId(), 1)
        return spell:Cast(unit)
    else
        HealingEngine.SetTarget(player:CallerId(), 1)
        return spell:Cast(unit)
    end
end)

RenewingMist:Callback("chi", function(spell)
    if not healTarget then return end
    if not IsPlayerSpell(ChiHarmony.id) then return end
    if healTarget.hp < 60 then return end
    if healTarget.hp > 95 then return end
    if healTarget:IsUnit(player) then return end
    if chiHarmonyRemains(healTarget) > 1000 then return end

    return spell:Cast(healTarget)
end)

SoothingMists:Callback(function(spell)
    if not healTarget then return end
    if player.stayTime < 0.5 or player.speed > 0 then return end

    if healTarget.hp > 90 and healTarget:Buff(buffs.soothingTotem) then return end

    local castInfo = player.castOrChannelInfo
    if castInfo then
        if castInfo.spellId ~= spell.id then return end
        if castInfo.remaining > 1000 then return end
    end

    return spell:Cast(healTarget)
end)

LifeCoccoon:Callback(function(spell)
    if not healTarget then return end

    if healTarget.totalImmune then return end

    if healTarget.hp < 40 then
        Debounce("LowCoccoon" .. healTarget.name, 500, 4500, spell)
    end

    local total, _, _, _, cds = healTarget:AttackersV69()

    if healTarget.hp < 60 and cds > 0 then
        Debounce("LowCoccoonCds" .. healTarget.name, 500, 4500, spell)
    end
end)

EnvelopingMist:Callback("instant", function(spell)
    if not healTarget then return end
    --if healTarget:Buff(buffs.enveloping) then return end
    --if player:Buff(buffs.tft) then return end
    if healTarget.hp > 80 then return end
    if healTarget.hp < 30 and RushingWindKick:Cooldown() <= 1000 then return end
    --if healTarget.hp < 30 then return end

    if player:Buff(buffs.topEnvelop) then
        return spell:Cast(healTarget)
    end 
    
    if player:HasBuffCount(buffs.craneBuff) >= 3 and not player:Buff(buffs.tft) then
        return spell:Cast(healTarget)
    end


    --if healTarget.hp > 90 then
    --    local total, _, _, _, cds = healTarget:AttackersV69()
    --
    --    if cds == 0 and total < 2 then return end
    --end

end)

EnvelopingMist:Callback(function(spell)
    if not healTarget then return end
    if not instaCastStuff() then return end
    if healTarget:Buff(buffs.enveloping) then return end
    if healTarget.hp > 80 then return end

    

    --if healTarget.hp > 80 then
    --    local total, _, _, _, cds = healTarget:AttackersV69()

    --    if cds == 0 and total < 2 then return end
    --end
    return spell:Cast(healTarget)
end)

EnvelopingMist:Callback("noTFT", function(spell)
    if not healTarget then return end
    if RushingWindKick:Cooldown() < 1000 then return end
    if healTarget.hp < 85 then return end

    return spell()
end)

RenewingMist:Callback(function(spell)
    if not healTarget then return end
    if player:Buff(buffs.tft) then return end
    if healTarget:BuffRemains(buffs.renewingMists) > 1000 then return end

    return spell:Cast(healTarget)
end)

Restoral:Callback(function(spell)
    if not healTarget then return end
    if healTarget.hp > 40 then return end

    return spell:Cast(healTarget)
end)

InvokeChiJi:Callback(function(spell)
    if not healTarget then return end
    if not target.exists or not target.canAttack then return end
    if target.distance > 20 then return end
    if healTarget.hp > 90 then return end
    if healTarget.hp < 20 then return end

    --if healTarget.hp > 40 then
    --    local total, _, _, _, cds = healTarget:AttackersV69()
    --
    --    if cds == 0 and total < 2 then return end
    --end

    return spell:Cast()
end)

TouchOfDeath:Callback(function(spell, unit)
    return spell:Cast(unit)
end)

TigersPalm:Callback(function(spell, unit)
    return spell:Cast(unit)
end)

BlackoutKick:Callback(function(spell, unit)
    return spell:Cast(unit)
end)

RushingWindKick:Callback(function(spell, unit)
    if not target.exists then return end
    if not target.canAttack then return end
    if not healTarget then return end
    --if healTarget.hp > 90 and unit.hp > 30 then return end
    if unit.distance > 15 then return end
    local rwkcd = spell:Cooldown()

    if healTarget.hp < 70 then
        return spell:Cast(unit)
    end

    if healTarget.hp < 90 and rwkTFT() then
        return spell:Cast(unit)
    end
end)

ManaTea:Callback(function(spell)
    if not healTarget then return end
    if healTarget.hp < 70 then return end
    if player:HasBuffCount(buffs.manaTea) < 3 then return end

    return spell:Cast(player)
end)

SphereHelp:Callback(function(spell)
    if not healTarget then return end
    if healTarget:BuffRemains(buffs.sphereOfHope) > 1000 then return end

    if healTarget.isMe then return end
    if healTarget.hp < 60 then return end

    local found = MakMulti.party:Find(function (party)
        if party:IsUnit(healTarget) then return end
        return party:Buff(buffs.sphereOfHope)
    end)

    if found and not found.dead then
        if found.hp < healTarget.hp then return end

        if found.hp - healTarget.hp < 20 then return end
    end

    return spell:Cast(healTarget)
end)

ExpelHarm:Callback("melow", function(spell)
    if not healTarget then return end
    if not healTarget.isMe then return end

    if healTarget.hp > 80 then return end

    Debounce("EXPELMELOW", 500, 4500, spell)
end)

ExpelHarm:Callback("top", function(spell)
    if not healTarget then return end
    if not target then return end
    if target.distance > 5 then return end
    if not player:Buff(buffs.topExpel) then return end

    return spell:Cast()
end)

ExpelHarm:Callback(function(spell)
    if player.hp > 80 then return end

    Debounce("PLAYERLOW", 500, 4500, spell)
end)

Detox:Callback(function (spell, unit)
    if not healTarget then return end
    if healTarget.hp < 40 then return end

    if unit:Debuff("Unstable Affliction") then return end
    if unit:Debuff("Vampiric Touch") and healTarget.hp < 80 then return end
    if unit:Debuff(203337) then return end

    local isCounterMagic = IsPlayerSpell(353502)
    if unit:HasDeBuffFromFor(ccDispelList, 620)
        or (isCounterMagic and unit:IsUnit(healTarget) and unit:HasDeBuffFromFor(counterMagicDispelList, 620) ) then 
        if spell:Cast(unit) then return end
    end
end)

GrappleWeapon:Callback(function (spell, unit)
    if not unit.exists then return end
    if not unit.isMelee then return end
    if unit:Buff(446035) then return end

    local classId = unit:ClassID()
    if classId == 11 or classId == 10 then return end

    if not unit.cds then return end

    if unit:Buff("Bladestorm") then return end

    Debounce("GRAPPLECDS" .. unit.name, 500, 4500, spell, unit)
end)

Paralysis:Callback(function(spell, unit)
    if not unit.exists then return end
    if unit:IsUnit(target) then return end
    if not unit:IsUnit(enemyHealer) then return end
    if unit.ccImmune then return end
    if unit.totalImmune then return end
    if unit.magicImmune then return end
    if unit:CCRemains() > 1500 then return end
    if target.hp < 80 and unit.incapacitateDr == 1 then return 
        spell:Cast(unit)
    end

    if target.hp < 40 and unit.incapacitateDr >= .5 then return 
        spell:Cast(unit)
    end
end)

A[3] = function(icon)
    FrameworkStart(icon)
    SetUpHealers()
    calculateHealTarget()
    
    RenewingMist("arenaGates")

    if not target.exists then return end

    if target.exists and target.canAttack then
        -- We always wanna insta KILL BECAUSE WE MAD BOIS
        TouchOfDeath(target)
    end

    LifeCoccoon()
    Restoral()
    InvokeChiJi()
    Vivify("bigvv")
    RenewingMist("chi")
    ThunderFocusTea()
    EnvelopingMist("instant")
    --EnvelopingMist("noTFT")
    RushingWindKick(target)
    Vivify("vv")
    FortifyingBrew()
    ManaTea()
    SphereHelp()
    ExpelHarm("melow")

    RenewingMist()
    SoothingMists()
    --EnvelopingMist("noTFT")
    Vivify()

    if target.exists and target.canAttack then
        EnvelopingMist()
        SphereHarm(target)
        BlackoutKick(target)
        TigersPalm(target)
    end
    EnvelopingMist()
    ExpelHarm()

    return FrameworkEnd()
end

local function areanaEnemies(enemy)
    SpearHandStrike(enemy)
    Paralysis(enemy)
    GrappleWeapon(enemy)
end

local function partyRotation(team)
    Detox(team)
end

A[6] = function(icon)
    RegisterIcon(icon)

    partyRotation(party1)
    areanaEnemies(arena1)

    return FrameworkEnd()
end

A[7] = function(icon)
    RegisterIcon(icon)

    partyRotation(party2)
    areanaEnemies(arena2)

    return FrameworkEnd()
end

A[8] = function(icon)
    RegisterIcon(icon)

    partyRotation(party3)
    areanaEnemies(arena3)

    return FrameworkEnd()
end

A[9] = function(icon)
    RegisterIcon(icon)

    return FrameworkEnd()
end

A[10] = function(icon)
    RegisterIcon(icon)
    partyRotation(player)

    return FrameworkEnd()
end
