if not MakuluValidCheck() then return true end
if not <PERSON><PERSON><PERSON>_magic_number == 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 66 then return end

local FrameworkStart   = MakuluFramwork.start
local FrameworkEnd     = MakuluFramwork.endFunc
local RegisterIcon     = MakuluFramwork.registerIcon

local MakUnit          = MakuluFramwork.Unit
local MakEnemies       = MakuluFramwork.Enemies
local MakSpell         = MakuluFramwork.Spell
local MakMulti         = MakuluFramwork.MultiUnits
local MakParty         = MakuluFramwork.Party
local TableToLocal     = MakuluFramwork.tableToLocal
local MakGcd           = MakuluFramwork.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local Unit       	   = Action.Unit
local Player           = Action.Player
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local AuraIsValid      = Action.AuraIsValid
local LoC              = Action.LossOfControl
local UnitIsUnit	   = _G.UnitIsUnit
local HealingEngine    = Action.HealingEngine
local getmembersAll    = HealingEngine.GetMembersAll()
local _G, setmetatable = _G, setmetatable
local GetSpellTexture  = _G.TMW.GetSpellTexture

local ActionID = {
	--Racials
	WillToSurvive                = { ID = 59752 },
	Stoneform                    = { ID = 20594 },
	Shadowmeld                   = { ID = 58984 },
	EscapeArtist                 = { ID = 20589 },
	GiftOfTheNaaru               = { ID = 59544 },
	Darkflight                   = { ID = 68992 },
	BloodFury                    = { ID = 20572 },
	WillOfTheForsaken            = { ID = 7744  },
	WarStomp                     = { ID = 20549 },
	Berserking                   = { ID = 26297 },
	ArcaneTorrent                = { ID = 50613 },
	RocketJump                   = { ID = 69070 },
	RocketBarrage                = { ID = 69041 },
	QuakingPalm                  = { ID = 107079},
	SpatialRift                  = { ID = 256948},
	LightsJudgment               = { ID = 255647},
	Fireblood                    = { ID = 265221},
	ArcanePulse                  = { ID = 260364},
	BullRush                     = { ID = 255654},
	AncestralCall                = { ID = 274738},
	Haymaker                     = { ID = 287712},
	Regeneratin                  = { ID = 291944},
	BagOfTricks                  = { ID = 312411},
	HyperOrganicLightOriginator  = { ID = 312924},
	TargetEnemy                  = { ID = 44603 },
	StopCast                     = { ID = 61721 },
	PoolResource                 = { ID = 209274},
	FocusParty1                  = { ID = 134314},
	FocusParty2                  = { ID = 134316},
	FocusParty3                  = { ID = 134318},
	FocusParty4                  = { ID = 134320},
	FocusPlayer                  = { ID = 134310},
	AntiFakeKick                 = { Type = "SpellSingleColor", ID = 96231,  Hidden = true,		Color = "GREEN"	    , Desc = "[2] AntiFakeKick",    QueueForbidden = true	},
	AntiFakeCC					 = { Type = "SpellSingleColor", ID = 853,  	Hidden = true,		Color = "YELLOW"	, Desc = "[1] AntiFakeCC",      QueueForbidden = true	},
	--BaselineProtectionPaladinAbilities
	Consecration                 = { ID = 26573 },
	ConsecrationDebuff			 = { ID = 204242 },
	CrusaderStrike               = { ID = 35395 },
	DivineShield                 = { ID = 642   },
	FlashofLight                 = { ID = 19750 },
	HammerofJustice              = { ID = 853   },
	HandofReckoning              = { ID = 62124 },
	Judgment                     = { ID = 20271 },
	SenseUndead                  = { ID = 5502  },
	ShieldoftheRighteous         = { ID = 53600 },
	WordofGlory                  = { ID = 85673 },
	Redemption                   = { ID = 7328  },
	Intercession                 = { ID = 391054},
	DevotionAura                 = { ID = 465   },
	--BaselineProtectionPaladinPassives
	MasteryDivineBulwark         = { ID = 76671 },
	AegisofLight                 = { ID = 353367},
	Riposte                      = { ID = 161800},
	Forbearance                  = { ID = 25771 },
	--ProtectionPaladinClassTreeAbilities
	LayonHands                   = { ID = 633   },
	BlessingofFreedom            = { ID = 1044  },
	HammerofWrath                = { ID = 24275 },
	CleanseToxins                = { ID = 213644},
	AurasoftheResolute           = { ID = 385633},
	CrusaderAura                 = { ID = 32223 },
	TurnEvil                     = { ID = 10326 },
	DivineSteed                  = { ID = 190784},
	Rebuke                       = { ID = 96231 },
	AvengingWrath                = { ID = 384376},
	BlessingofSacrifice          = { ID = 6940  },
	BlessingofProtection         = { ID = 1022  },
	DivineToll                   = { ID = 375576},
	--ProtectionPaladinClassTreePassives
	Obduracy                     = { ID = 385427},
	FistofJustice                = { ID = 234299},
	GreaterJudgment              = { ID = 231663},
	Cavalier                     = { ID = 230332},
	SeasonedWarhorse             = { ID = 376996},
	HolyAegis                    = { ID = 385515},
	Justification                = { ID = 377043},
	Punishment                   = { ID = 403530},
	GoldenPath                   = { ID = 377128},
	SanctifiedPlates             = { ID = 402964},
	UnboundFreedom               = { ID = 305394},
	LightforgedBlessing          = { ID = 403479},
	SealofMercy                  = { ID = 384897},
	Afterimage                   = { ID = 385414},
	SacrificeoftheJust           = { ID = 384820},
	Recompense                   = { ID = 384914},
	UnbreakableSpirit            = { ID = 114154},
	ImprovedBlessingofProtection = { ID = 384909},
	CrusadersReprieve            = { ID = 403042},
	StrengthofConviction         = { ID = 379008},
	JudgmentofLight              = { ID = 183778},
	SealofMight                  = { ID = 385450},
	DivinePurpose                = { ID = 408459},
	SealofAlacrity               = { ID = 385425},
	Incandescence                = { ID = 385464},
	TouchofLight                 = { ID = 385349},
	FaithsArmor                  = { ID = 406101},
	OfDuskandDawn                = { ID = 409441},
	SealoftheCrusader            = { ID = 385728},
	SealofOrder                  = { ID = 385129},
	FadingLight                  = { ID = 109075},
	DivineResonance              = { ID = 355098},
	QuickenedInvocations         = { ID = 379391},
	ZealotsParagon               = { ID = 391142},
	--ProtectionPaladinSpecTreeAbilities
	AvengersShield               = { ID = 31935 },
	HammeroftheRighteous         = { ID = 53595 },
	BlessedHammer                = { ID = 204019},
	ArdentDefender               = { ID = 31850 },
	BlessingofSpellwarding       = { ID = 204018},
	BastionofLight               = { ID = 378974},
	GuardianofAncientKings       = { ID = 86659 },
	EyeofTyr                     = { ID = 387174},
	MomentofGlory                = { ID = 327193},
	--ProtectionPaladinSpecTreePassives
	HolyShield                   = { ID = 152261},
	Redoubt                      = { ID = 280373},
	InnerLight                   = { ID = 386568},
	GrandCrusader                = { ID = 85043 },
	ShiningLight                 = { ID = 321136},
	ImprovedHolyShield           = { ID = 393030},
	Sanctuary                    = { ID = 379021},
	InspiringVanguard            = { ID = 393022},
	BarricadeofFaith             = { ID = 385726},
	ConsecrationinFlame          = { ID = 379022},
	ConsecratedGround            = { ID = 204054},
	TirionsDevotion              = { ID = 392928},
	BulwarkofOrder               = { ID = 209389},
	ImprovedArdentDefender       = { ID = 393114},
	LightoftheTitans             = { ID = 378405},
	StrengthinAdversity          = { ID = 393071},
	CrusadersResolve             = { ID = 380188},
	TyrsEnforcer                 = { ID = 378285},
	RelentlessInquisitor         = { ID = 383388},
	AvengingWrathMight           = { ID = 384442},
	Sentinel                     = { ID = 385438},
	SealofClarity                = { ID = 384815},
	FaithintheLight              = { ID = 379043},
	SealofReprisal               = { ID = 377053},
	HandoftheProtector           = { ID = 315924},
	CrusadersJudgment            = { ID = 204023},
	FocusedEnmity                = { ID = 378845},
	SoaringShield                = { ID = 378457},
	GiftoftheGoldenValkyr        = { ID = 378279},
	UthersCounsel                = { ID = 378425},
	SanctifiedWrath              = { ID = 53376 },
	FerrenMarcussFervor          = { ID = 378762},
	ResoluteDefender             = { ID = 385422},
	BulwarkofRighteousFury       = { ID = 386653},
	InmostLight                  = { ID = 405757},
	FinalStand                   = { ID = 204077},
	RighteousProtector           = { ID = 204074},
	--TemplarTalents
	LightsGuidance               = { ID = 427445},
	ZealousVindication           = { ID = 431463},
	ForWhomtheBellTolls          = { ID = 432929},
	ShaketheHeavens              = { ID = 431533},
	WrathfulDescent              = { ID = 431551},
	SacrosanctCrusade            = { ID = 431730},
	HigherCalling                = { ID = 431687},
	BondsofFellowship            = { ID = 432992},
	UnrelentingCharger           = { ID = 432990},
	EndlessWrath                 = { ID = 432615},
	Sanctification               = { ID = 382430},
	Hammerfall                   = { ID = 432463},
	UndisputedRuling             = { ID = 432626},
	LightsDeliverance            = { ID = 425518},
	--LightsmithTalents
	HolyBulwark                  = { ID = 227817},
	SacredWeapon                 = { ID = 432472},
	RiteofSanctification         = { ID = 433568},
	RiteofAdjuration             = { ID = 433583},
	Solidarity                   = { ID = 432802},
	DivineGuidance               = { ID = 433106},
	BlessedAssurance             = { ID = 433015},
	LayingDownArms               = { ID = 432866},
	DivineInspiration            = { ID = 432964},
	Forewarning                  = { ID = 432804},
	FearNoEvil                   = { ID = 432834},
	Excoriation                  = { ID = 433896},
	SharedResolve                = { ID = 432821},
	Valiance                     = { ID = 432919},
	HammerandAnvil               = { ID = 433718},
	BlessingoftheForge           = { ID = 433011},
}

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function createAction(attributes)
    return Action.Create({
        Type = attributes.Type or "Spell",
        ID = attributes.ID,
        Texture = attributes.Texture,
        FixedTexture = attributes.FixedTexture,
        Color = attributes.Color,
        Desc = attributes.Desc,
        MAKULU_INFO = attributes.MAKULU_INFO,
        Hidden = attributes.Hidden,
        QueueForbidden = attributes.QueueForbidden,
    })
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local buildMakuluFrameworkSpells = function()
	local result = {}
	for k, v in pairs(A) do
		result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
	end
	return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_PALADIN_PROTECTION] = A
TableToLocal(M, getfenv(1))

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local player        = ConstUnit.player
local target        = ConstUnit.target
local focus         = ConstUnit.focus
local mouseover     = ConstUnit.mouseover
local arena1        = ConstUnit.arena1
local arena2        = ConstUnit.arena2
local arena3        = ConstUnit.arena3
local party1        = ConstUnit.party1
local party2        = ConstUnit.party2
local party3        = ConstUnit.party3
local party4        = ConstUnit.party4
local tank          = ConstUnit.tank
local healer        = ConstUnit.healer
local enemyHealer   = ConstUnit.enemyHealer

local unit

Aware:enable()

local buffs = {
	blessingofDuskBuff          = 385126,
	consecrationBuff            = 188370,
	divinePurposeBuff           = 223819,
	shieldoftheRighteousBuff    = 132403,
	avengingWrathBuff           = 31884,
	holyAvengerBuff             = 105809,
	seraphimBuff                = 152262,
	bulwarkofRighteousFuryBuff  = 386652,
	sanctificationBuff          = 424616,
	sanctificationEmpowerBuff   = 424622,
	shiningLightFreeBuff        = 327510,
	ardentDefenderBuff          = 31850,
	bastionofLightBuff          = 378974,
	guardianofAncientKingsBuff  = 86659,
	momentofGloryBuff           = 327193,
	sentinelBuff                = 389539,
	crusaderAura                = 32223 ,
	devotionAura                = 465
}

local debuffs = {

}

local gameState = {
    imCasting = nil,
    imCastingName = nil,
    imCastingRemaining = nil,
    imCastingLength = nil,
}

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local interrupts = {
    { spell = Rebuke },
    { spell = HammerofJustice, isCC = true }
}

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function hasIncomingDamage()
    return incBigDmgIn() < 2000 or incModDmgIn() < 2000
end

local function defensiveActive()
    player = MakUnit:new("player")
    return player:HasBuff(buffs.barkskin, true) or player:HasBuff(buffs.ironbark, true)
end

local function shouldDefensive()
    local incomingDamage = hasIncomingDamage()

    return incomingDamage and not defensiveActive() 
end

--######################################################################################################################################################################################################

local function getCurrentCastInfo()
    local castingInfo = player.castOrChannelInfo

    if not castingInfo then
        return nil, nil, nil, nil
    end

    return castingInfo.spellId, castingInfo.name, castingInfo.remaining, castingInfo.castLength
end

local lastUpdateTime = 0
local updateDelay = 0.5

-- Funktion zur Aktualisierung des Spielzustands
local function updateGameState()
    local currentTime = GetTime()

    local currentCast, currentCastName, currentCastRemains, currentCastLength = getCurrentCastInfo()
    gameState.imCastingRemaining = currentCastRemains

    if (currentTime - lastUpdateTime) > updateDelay then
        gameState.imCasting = currentCast
        gameState.imCastingName = currentCastName
        lastUpdateTime = currentTime 
    end
end

--######################################################################################################################################################################################################

local function PlayerHealthIsSafe()
    return player:HasBuff(A.DivineShield.ID) or player:Health() >= 65 or player:Health() >= 45 and player:HasBuff({A.ArdentDefender.ID, A.GuardianofAncientKings.ID})
end

local function CanJudgment()
    return (not target:HasDeBuff(A.Judgment.ID, true) or A.Judgment:GetSpellCharges() >= 2 or A.Judgment:GetSpellChargesFullRechargeTime() <= A.GetCurrentGCD() + 0.25)
end

local function Movementcheck()
    return not GetToggle(2, "Checkbox2") or GetToggle(2, "Checkbox2") and isStaying
end

--######################################################################################################################################################################################################

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### UTILITIES ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- Intercession
Intercession:Callback('Utilities', function(spell)
    if not A.MouseHasFrame() then return end
    if not mouseover:IsPlayer() then return end
    if not Unit("mouseover"):IsDead() then return end
    
    return spell:Cast(mouseover)
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### PRE COMBAT ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- CrusaderAura
CrusaderAura:Callback('PreCombat', function(spell)
    if A.Zone == "arena" then return end
    if not Player:IsMounted() then return end
    if not A.AurasofSwiftVengeance:IsTalentLearned() then return end
    if player:HasBuff(A.CrusaderAura.ID) then return end

    return spell:Cast(player)
end)

-- DevotionAura
DevotionAura:Callback('PreCombat', function(spell)
    if A.Zone == "arena" then return end
    if Player:IsMounted() then return end
    if player:HasBuff(A.DevotionAura.ID) then return end

    return spell:Cast(player)
end)

-- RiteofSanctification
RiteofSanctification:Callback('PreCombat', function(spell)
    local hasMainHandEnchant, mainHandExpiration, _, _, _, _, _, _ = GetWeaponEnchantInfo()

    if not hasMainHandEnchant or mainHandExpiration <= (1800000 * num(not player.inCombat)) then
        return spell:Cast(player)
    end
end)

-- RiteofAdjuration
RiteofAdjuration:Callback('PreCombat', function(spell)
    local hasMainHandEnchant, mainHandExpiration, _, _, _, _, _, _ = GetWeaponEnchantInfo()

    if not hasMainHandEnchant or mainHandExpiration <= (1800000 * num(not player.inCombat)) then
        return spell:Cast(player)
    end
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### DEFENSIVE ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- LayonHands
LayonHands:Callback('Defensive', function(spell)
    if not player.inCombat then return end

    local protectionThreshold = GetToggle(2, "SelfProtection5")
    if player:HasDeBuff(A.Forbearance.ID) or playerHealth > protectionThreshold then return end

    spell:Cast(player)
end)

-- DivineShield
DivineShield:Callback('Defensive', function(spell)
    if not player.inCombat then return end

    local protectionThreshold = GetToggle(2, "SelfProtection4")
    if player:HasDeBuff(A.Forbearance.ID) or playerHealth > protectionThreshold then return end

    spell:Cast(player)
end)

-- AvengingWrath Cooldowns
AvengingWrath:Callback('Defensive', function(spell, need_defensive)
    if not player.inCombat then return end

    local defensiveOptions = A.GetToggle(2, "defensiveSelect")
    if not need_defensive or not defensiveOptions[3] then return end

    spell:Cast(player)
end)

-- Sentinel Cooldowns
Sentinel:Callback('Defensive', function(spell, need_defensive)
    if not player.inCombat then return end

    local defensiveOptions = A.GetToggle(2, "defensiveSelect")
    if not need_defensive or not defensiveOptions[3] then return end

    spell:Cast(player)
end)

-- GuardianofAncientKings
GuardianofAncientKings:Callback('Defensive', function(spell, need_defensive)
    if not player.inCombat then return end

    local defensiveOptions = A.GetToggle(2, "defensiveSelect")
    local protectionThreshold = GetToggle(2, "SelfProtection3")

    if need_defensive and defensiveOptions[2] then
        return spell:Cast(player)
    end

    if playerHealth <= protectionThreshold then
        return spell:Cast(player)
    end
end)

-- ArdentDefender
ArdentDefender:Callback('Defensive', function(spell, need_defensive)
    if not player.inCombat then return end

    local defensiveOptions = A.GetToggle(2, "defensiveSelect")
    local protectionThreshold = GetToggle(2, "SelfProtection2")

    if need_defensive and defensiveOptions[1] then
        return spell:Cast(player)
    end

    if playerHealth <= protectionThreshold then
        return spell:Cast(player)
    end
end)

-- WordofGlory
WordofGlory:Callback('Defensive', function(spell)
    local protectionThreshold = GetToggle(2, "SelfProtection1")
    if playerHealth > protectionThreshold then return end

    spell:Cast(player)
end)

-- WordofGlory Freecast
WordofGlory:Callback('Freecast', function(spell)
    local freecastThreshold = GetToggle(2, "WoGFreePlayer")
    local hasBuff = Unit("player"):HasBuffs(327510, _, true) > 0 or player:HasBuff(buffs.divinePurposeBuff)
    
    if not hasBuff or playerHealth > freecastThreshold then return end

    spell:Cast(player)
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### COOLDOWNS ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- AvengingWrath Cooldowns
AvengingWrath:Callback('Cooldowns', function(spell)
    if not CDsON() or not inMeleeRange then return end
    spell:Cast(player)
end)

-- Sentinel Cooldowns
Sentinel:Callback('Cooldowns', function(spell)
    if not CDsON() or not inMeleeRange then return end
    spell:Cast(player)
end)

-- MomentofGlory Cooldowns
MomentofGlory:Callback('Cooldowns', function(spell)
    if player:BuffRemains(A.AvengingWrathBuff.ID) >= 15 then return end

    spell:Cast(player)
end)

-- DivineToll Cooldowns
DivineToll:Callback('Cooldowns', function(spell)
    if not CDsON() then return end
    if MultiUnits:GetBySpell(A.HammerofJustice) < 3 then return end

    spell:Cast(target)
end)

-- BastionofLight Cooldowns
BastionofLight:Callback('Cooldowns', function(spell)
    if not CDsON() then return end
    if not (player:HasBuff(A.AvengingWrathBuff.ID) or AvengingWrath:Cooldown() <= 30) then return end

    spell:Cast(player)
end)

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### STANDARD ROTATION ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

-- Consecration PreCombat
Consecration:Callback('PreCombat', function(spell)
	if player.inCombat then return end
    if not inMeleeRange or not Movementcheck() then return end

    local hasConsecrationBuff = player:HasDeBuff(A.Consecration.ID, true)
    local hasConsecrationDebuff = target:HasDeBuff(A.ConsecrationDebuff.ID, true)

    if hasConsecrationBuff and hasConsecrationDebuff then return end
    
    spell:Cast(player)
end)

-- AvengersShield PreCombat
AvengersShield:Callback('PreCombat', function(spell)
	if player.inCombat then return end

    spell:Cast(target)
end)

-- Judgment PreCombat
Judgment:Callback('PreCombat', function(spell)
	if player.inCombat then return end
    if not CanJudgment() then return end
    
    spell:Cast(target)
end)


--################################################################################################################################################################################################################

local function Untilities()
	Intercession('Utilities')
end

local function PreCombat()
	CrusaderAura('PreCombat')
	DevotionAura('PreCombat')
	RiteofSanctification('PreCombat')
	RiteofAdjuration('PreCombat')
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function SelfDefensive()
	needDefensive = shouldDefensive()
	LayonHands('Defensive')
	DivineShield('Defensive')
	AvengingWrath('Defensive', needDefensive)
	Sentinel('Defensive', needDefensive)
	GuardianofAncientKings('Defensive', needDefensive)
	ArdentDefender('Defensive', needDefensive)
	WordofGlory('Defensive')
	WordofGlory('Freecast')
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local function DamageRotationPve()
	Consecration('PreCombat')
	AvengersShield('PreCombat')
	Judgment('PreCombat')

	AvengingWrath('Cooldowns')
	Sentinel('Cooldowns')
	MomentofGlory('Cooldowns')
	DivineToll('Cooldowns')
	BastionofLight('Cooldowns')
end

--################################################################################################################################################################################################################

A[1] = function(icon)
    --AntiFakeCC - Use GetCooldown to ensure the AntiFake CC spell remains usable via 'click' even if it's been blocked
	if A.AntiFakeCC:GetCooldown() == 0 then return A.AntiFakeCC:Show(icon) end
end

A[2] = function(icon)
	local castLeft, _, _, _, notKickAble = Unit("target"):IsCastingRemains()
	if castLeft == 0 then return end

    --AntiFakeKick --Use GetCooldown to ensure the AntiFake CC spell remains usable via 'click' even if it's been blocked
    if A.AntiFakeKick:GetCooldown() == 0 and not notKickAble then return A.AntiFakeKick:Show(icon) end
end

--################################################################################################################################################################################################################

A[3] = function(icon)
	FrameworkStart(icon)
    updateGameState()
    SetUpHealers()

	local CantCast = CantCast()
	if CantCast then return end

	isStaying   	= not player.moving
    stayingTime		= Player.stayTime
	movingTime  	= Player.moveTime
	isMoving 		= player.moving
	combatTime  	= player.combatTime
	playerHealth	= player.hp
	inMeleeRange	= target:Distance() <= 5

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    makInterrupt(interrupts)

    ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

	--Utilities
    Untilities()

     --PreCombat/Defensives
    if not player.inCombat then
        PreCombat()
    else
        SelfDefensive()
    end

    --Damage Rotation PVE
    if target.exists and target.canAttack then
        DamageRotationPve()
    end

	return FrameworkEnd()
end

--################################################################################################################################################################################################################

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### ARENA ROTATION ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------



---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local enemyRotation = function(enemy)
	if not enemy.exists then return end

end
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---### PARTY ROTATION ###
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

local partyRotation = function(friendly)
    if not friendly.exists then return end

end

--################################################################################################################################################################################################################

A[6] = function(icon)
	RegisterIcon(icon)
    if targetForInterrupt(interrupts) then return TabTarget() end

    partyRotation(party1)
	enemyRotation(arena1)

	return FrameworkEnd()
end

--################################################################################################################################################################################################################

A[7] = function(icon)
	RegisterIcon(icon)
    partyRotation(party2)
	enemyRotation(arena2)

	return FrameworkEnd()
end

--################################################################################################################################################################################################################

A[8] = function(icon)
	RegisterIcon(icon)
    partyRotation(party3)
	enemyRotation(arena3)

	return FrameworkEnd()
end

--################################################################################################################################################################################################################

A[9] = function(icon)
	RegisterIcon(icon)
	partyRotation(party4)

	return FrameworkEnd()
end

--################################################################################################################################################################################################################

A[10] = function(icon)
	RegisterIcon(icon)
	partyRotation(player)

	return FrameworkEnd()
end

--################################################################################################################################################################################################################
-- NOTES
--################################################################################################################################################################################################################
-- [1] is AntiFake CC rotation (limited, usually is single color like 0x00FF00 which is green)
-- [2] is AntiFake Kick rotation (racial, primary specialization interrupt spell)
-- [3] is Rotation (old launcher called it Single, supports all actions)
-- [4] is Secondary (old launcher called it AoE) rotation (supports all actions)
-- [5] is Trinket rotation (racial, specialization's spells which can remove CC)
-- [6] is Passive rotation (limited actions, usually @raid1, @party1, @arena1 and additional binds - for more info look notes in the launcher)
-- [7] is Passive rotation (limited actions, usually @raid2, @party2, @arena2)
-- [8] is Passive rotation (limited actions, usually @raid3, @party3, @arena3)
--Passive rotation doesn't require START button use like it does [1] -> [5] rotations
