if not MakuluValidCheck() then return true end
if not Makulu_magic_number == 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 70 then return end

local FrameworkStart   = MakuluFramwork.start
local FrameworkEnd     = MakuluFramwork.endFunc
local RegisterIcon     = MakuluFramwork.registerIcon

local MakUnit          = MakuluFramwork.Unit
local MakSpell         = MakuluFramwork.Spell
local TableToLocal     = MakuluFramwork.tableToLocal
local MakGcd           = MakuluFramwork.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local Trinket          = MakuluFramework.Trinket
local Debounce         = MakuluFramework.debounceSpell
local ConstSpells      = MakuluFramework.constantSpells
local MakMulti         = MakuluFramework.MultiUnits

local _G, setmetatable = _G, setmetatable

local ActionID       = {
	-- Racials
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    RocketJump = { ID = 69070 },
    RocketBarrage = { ID = 69041 },
    QuakingPalm = { ID = 107079 },
    SpatialRift = { ID = 256948 },
    LightsJudgment = { ID = 255647 },
    Fireblood = { ID = 265221 },
    ArcanePulse = { ID = 260364 },
    BullRush = { ID = 255654 },
    AncestralCall = { ID = 274738 },
    Haymaker = { ID = 287712 },
    Regeneratin = { ID = 291944 },
    BagOfTricks = { ID = 312411 }, 
    HyperOrganicLightOriginator = { ID = 312924 },

	-- Baseline
	CrusaderStrike = { ID = 35395, FixedTexture = 135891, MAKULU_INFO = { damageType = "physical" } },
	Judgement = { ID = 20271,MAKULU_INFO = { damageType = "magic" } },
	TemplarsVerdict = { ID = 85256, MAKULU_INFO = { damageType = "magic" } },
	Consecration = { ID = 26573, MAKULU_INFO = { damageType = "magic" } },
	DivineShield = { ID = 642 },
	HandofReckoning = { ID = 62124 },
	Intercession = { ID = 391054, Texture = 7328 },
	Redemption = { ID = 7328 },
	FlashofLight = { ID = 19750 },
    FlashofLightParty = { ID = 19750, Texture = 10326, Hidden = true },
	HammerofJustice = { ID = 853, MAKULU_INFO = { damageType = "magic" } },
	WordofGlory = { ID = 85673, Texture = 167136 },
	ShieldoftheRighteous = { ID = 53600 },
	DevotionAura = { ID = 465},
	DivineProtection = { ID = 403876 },

	--Paladin Tree
	LayonHands = { ID = 633 },
    LayonHandsPassive = { ID = 633, Texture = 139, Hidden = true },
    LayonHandsPassiveToo = { ID = 471195, Texture = 139, Hidden = true },
	BlessingofFreedom = { ID = 1044 },
	HammerofWrath = { ID = 24275 },
	CleanseToxins = { ID = 213644, FixedTexture = 133667 },
	TurnEvil = { ID = 10326 },
	Repentance = { ID = 20066, MAKULU_INFO = { damageType = "magic" } },
	BlindingLight = { ID = 115750, MAKULU_INFO = { damageType = "magic" } },
	Rebuke = { ID = 96231, MAKULU_INFO = { damageType = "physical", ignoreCasting = true, offGcd = true } },
	AvengingWrath = { ID = 31884 },
	BlessingofProtection = { ID = 1022 },
	DivineToll = { ID = 375576, FixedTexture = 3565448 },
	BlessingofSacrifice = { ID = 6940 },

	--Retribution Tree
	BladeofJustice = { ID = 184575, MAKULU_INFO = { damageType = "magic" } },
	DivineStorm = { ID = 53385, MAKULU_INFO = { damageType = "magic" } },
	FinalVerdict = { ID = 383328, MAKULU_INFO = { damageType = "magic" } },
	JusticarsVengeance = { ID = 215661, MAKULU_INFO = { damageType = "magic" } },
	ShieldofVengeance = { ID = 184662 },
	Crusade = { ID = 231895 },
	FinalReckoning = { ID = 343721, MAKULU_INFO = { damageType = "magic" } },
	ExecutionSentence = { ID = 343527, MAKULU_INFO = { damageType = "magic" } },
	WakeofAshes = { ID = 255937, Texture = 403695, MAKULU_INFO = { damageType = "magic" } },
	EmpyreanLegacy = { ID = 387170, Hidden = true },
	DivineArbiter = { ID = 404306, Hidden = true },
	HolyBlade = { ID = 383342, Hidden = true },
	CrusadingStrikes = { ID = 404542, Hidden = true },
	BlessedChampion = { ID = 403010, Hidden = true },
	VanguardsMomentum = { ID = 383314, Hidden = true },
	BoundlessJudgment = { ID = 405278, Hidden = true },
	TemplarStrike = { ID = 407480, FixedTexture = 135891, MAKULU_INFO = { damageType = "magic" } },
	TemplarSlash = { ID = 406647, FixedTexture = 135891, MAKULU_INFO = { damageType = "magic" } },
	DivineHammer = { ID = 198034, MAKULU_INFO = { damageType = "magic" } },
	ConsecratedBlade = { ID = 404834, Hidden = true },

	--Buff Trackers
	EmpyreanPowerBuff = { ID = 326733, Hidden = true },
	Forbearance = { ID = 25771, Hidden = true },
	DivineResonance = { ID = 384027, Hidden = true },
	Expurgation = { ID = 383344, Hidden = true },
	JudgmentDebuff = { ID = 197277, Hidden = true },

    --Talents
	DivineAuxiliary = { ID = 406158, Hidden = true },
	ExecutionersWill = { ID = 406940, Hidden = true },
	TemplarStrikeStuff = { ID = 406646, Hidden = true },
	UnboundFreedom = { ID = 305394, Hidden = true },
	LightsCelerity = { ID = 403698, Hidden = true },
	RadiantGlory = { ID = 458359, Hidden = true },
    BladeofVengeance = { ID = 403826, Hidden = true },
    VengefulWrath = { ID = 406835, Hidden = true },
    HolyFlames = { ID = 406545, Hidden = true },
    TempestoftheLightBringer = { ID = 383396, Hidden = true },
    LightsRevocation = { ID = 146956, Hidden = true },
    EmpyreanWard = { ID = 387791, Hidden = true },

    --Hero Stuff
    HammerofLight = {  ID = 427453, Texture = 403695, MAKULU_INFO = { damageType = "magic" } },
    LightsGuidance = { ID = 427445, Hidden = true },
    EternalFlame = { ID = 427448, Texture = 167136, },

    --PVP
	BlessingofSanctuary = { ID = 210256, Texture = 20066 },
    BlessingofSpellwarding = { ID = 204018, Texture = 62124 },

    --Other Pixels for Off Use
	SenseUndead = { ID = 5502 },
	FyralaththeDreamrender = { ID = 206448 },

    --Items
    Healthstone = { Type = "Item", ID = 5512, Hidden = true },
    TemperedPotion1 = { Type = "Potion", ID = 212263, Texture = 176108, Hidden = true },
    TemperedPotion2 = { Type = "Potion", ID = 212264, Texture = 176108, Hidden = true },
    TemperedPotion3 = { Type = "Potion", ID = 212265, Texture = 176108, Hidden = true },
    PotionofUnwaveringFocus1 = { Type = "Potion", ID = 212257, Texture = 176108, Hidden = true },
    PotionofUnwaveringFocus2 = { Type = "Potion", ID = 212258, Texture = 176108, Hidden = true },
    PotionofUnwaveringFocus3 = { Type = "Potion", ID = 212259, Texture = 176108, Hidden = true },
    FrontlinePotion = { Type = "Potion", ID = 212262, Texture = 176108, Hidden = true },
    AlgariManaPotion = { Type = "Potion", ID = 212241, Texture = 176108, Hidden = true },

    ArenaPreparation = { ID = 32727, Hidden = true }, 
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
	local result = {}
	for k, v in pairs(A) do
		result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
	end
	return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_PALADIN_RETRIBUTION] = A

TableToLocal(M, getfenv(1))
Aware:enable()


local player = ConstUnit.player
local target = ConstUnit.target
local focus = ConstUnit.focus
local pet = ConstUnit.pet
local arena1 = ConstUnit.arena1
local arena2 = ConstUnit.arena2
local arena3 = ConstUnit.arena3
local party1 = ConstUnit.party1
local party2 = ConstUnit.party2
local party3 = ConstUnit.party3
local party4 = ConstUnit.party4
local healer = ConstUnit.healer
local enemyHealer = ConstUnit.enemyHealer
local tank = ConstUnit.tank
local mouseover = ConstUnit.mouseover

local gameState = {
    imCasting = nil,
    imCastingName = nil,
    imCastingRemaining = 0,
    minTalentedCdRemains = nil,
    cursorCheck = false,
    shouldAoE = false,
    activeEnemies = 0,
    dsCastable = false,
}

local buffs = {
    avengingWrath = 31884,
    crusade = 231895,
    empyreanPower = 326733,
    empyreanLegacy = 387178,
    divineArbiter = 406975,
    divineHammer = 198034, --INFA - CHECK THIS
    divineResonance = 384029,
    blessingofAnshee = 445206,


}

local debuffs = {
    executionSentence = 343527,
}

local function num(val)
    if val then return 1 else return 0 end
end


local interrupts = {
    {spell = Rebuke },
    {spell = HammerofJustice, isCC = true, aoe = false},
    {spell = BlindingLight, isCC = true, aoe = true, distance = 2},
}

local function shouldBurst()
    --target = MakUnit:new("target")
	
    if not target.bigButtons then return false end
    if A.BurstIsON("target") then
        --if A.Zone ~= "arena" then
        --    local activeEnemies = MultiUnits:GetActiveUnitPlates()
        --    for enemy in pairs(activeEnemies) do
        --        if ActionUnit(enemy):Health() > (A.Judgement:GetSpellDescription()[1] * 10 ) or target.isDummy then
        --            return true
        --        end
        --    end
        --else
        return true
    end
end
    --return false
--end

local cacheContext     = MakuluFramework.Cache

local constCell = cacheContext:getConstCacheCell()
local function enemiesInMelee()
    return constCell:GetOrSet("enemiesInMelee", function() 
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0

        for enemyGUID in pairs(activeEnemies) do -- Jack will fix our enemies check soon
            local enemy = MakUnit:new(enemyGUID) 
            if enemy.distance <= 7 and not enemy:IsTotem() and not enemy.isPet then  -- I haven't tested the new totem yet
                total = total + 1
            end 
        end  
        
        return total 
    end)
end

local function enemiesInRebuke()
    return constCell:GetOrSet("enemiesInRebuke", function() 
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0

        for enemyGUID in pairs(activeEnemies) do -- Jack will fix our enemies check soon
            local enemy = MakUnit:new(enemyGUID) 
            if Rebuke:InRange(enemy) and not enemy:IsTotem() and not enemy.isPet then  -- I haven't tested the new totem yet
                total = total + 1
            end 
        end  
        
        return total 
    end)
end

local function autoTarget()
    if A.Zone == "arena" then return false end
    if not player.inCombat then return false end

    for _, spellInfo in ipairs(interrupts) do
        if target:ShouldInterrupt(spellInfo.spell, spellInfo.isCC, spellInfo.aoe, spellInfo.distance) then
            return false
        end
    end

    if Rebuke:InRange(target) and target.exists then return false end

    if enemiesInRebuke() > 0 and A.GetToggle(2, "oorTarget") then
        return true
    end
end

local function arenaHealthCheck(threshhold)
    --Return if any of the 3 arena units are below the threshhold
    if arena1.exists and party1.hp < threshhold then return true end
    if arena2.exists and party2.hp < threshhold then return true end
    if arena3.exists and party3.hp < threshhold then return true end
    return false
end

local function activeEnemies()
    return math.max(enemiesInMelee(), 1)
end

local function hasIncomingDamage()
    return incBigDmgIn() < 2000 or incModDmgIn() < 2000
end

local function defensiveActive()
    player = MakUnit:new("player")
    return player:BuffFrom(MakLists.Defensive) 
end

local function shouldDefensive()
    local incomingDamage = hasIncomingDamage()

    return incomingDamage and not defensiveActive() 
end

local lastUpdateTime = 0
local updateDelay = 0.5
local combatTime = 0
local inMelee = false
local holyPower	= 0
local function updateGameState()


    local currentTime = GetTime()
    combatTime = player.combatTime
    inMelee = target:Distance() <= 3
    holyPower	= player.holyPower
    gameState.activeEnemies = activeEnemies()

    --finishers->add_action( "variable,name=ds_castable,value=(spell_targets.divine_storm>=2|buff.empyrean_power.up|!talent.final_verdict&talent.tempest_of_the_lightbringer)&!buff.empyrean_legacy.up&!(buff.divine_arbiter.up&buff.divine_arbiter.stack>24)" );
    gameState.dsCastable = (gameState.activeEnemies >= 2 or player:Buff(buffs.empyreanPower) or (not IsPlayerSpell(A.FinalVerdict.ID) and IsPlayerSpell(A.TempestoftheLightBringer.ID))) and not player:Buff(buffs.empyreanLegacy) and not (player:Buff(buffs.divineArbiter) and player:HasBuffCount(buffs.divineArbiter) > 24)

end


--############################################################################ GENERAL ###################################################################################--

Intercession:Callback("general", function(spell)
    if not A.GetToggle(2, "mouseoverRes") then return end
    if not player.combat then return end
    if not mouseover.exists then return end
    if not mouseover.isFriendly then return end
    if mouseover.isPet then return end
    if not mouseover.dead then return end
    if not spell:InRange(mouseover) then return end

    return spell:Cast()
end)

Redemption:Callback("general", function(spell)
    if not A.GetToggle(2, "mouseoverRes") then return end
    if player.combat then return end
    if not mouseover.exists then return end
    if not mouseover.isFriendly then return end
    if not mouseover.dead then return end
    if not spell:InRange(mouseover) then return end

    return spell:Cast()
end)

FlashofLight:Callback("general", function(spell)
    if player.hp > A.GetToggle(2, "FlashLightHP") then return end
    if player.inCombat and not IsPlayerSpell(A.LightsCelerity.ID) then return end
    if player.hp > 30 and target.hp < 20 then return end
    return spell:Cast(player)
end)

FlashofLight:Callback("generalOOC", function(spell)
    if player.hp >= 80 then return end
    if player.inCombat then return end
    return spell:Cast(player)
end)

DivineProtection:Callback("general", function(spell)
    if player.hp > 60 then return end
    if player.hp > 40 and target.hp < 20 then return end
    return spell:Cast(player)
end)

ShieldofVengeance:Callback("general", function(spell)
    if player.hp > 70 then return end
    if not player.inCombat then return end
    return spell:Cast(player)
end)

WordofGlory:Callback("general", function(spell)
    if player.hp > A.GetToggle(2, "WogHP") then return end
    if not player.inCombat then return end
    if player.hp > 40 and target.hp < 20 then return end
    return spell:Cast(player)
end)

EternalFlame:Callback("general", function(spell)
    if player.hp > A.GetToggle(2, "EFHP") then return end
    if not player.inCombat then return end
    if player.hp > 40 and target.hp < 20 then return end
    return spell:Cast(player)
end)

DivineShield:Callback("general", function(spell)
    if LayonHands.used > 0 and LayonHands.used < 1000 then return end
    if LayonHandsPassiveToo.used > 0 and LayonHandsPassiveToo.used < 1000 then return end
    if IsPlayerSpell(A.LightsRevocation.ID) and LayonHands:Cooldown() < 2000 and LayonHandsPassiveToo:Cooldown() < 2000 then return end
    if player:HasDeBuff(A.Forbearance.ID) and not IsPlayerSpell(A.LightsRevocation.ID) then return end
    if not player.inCombat then return end
    if player.totalImmune then return end
    if player.hp > A.GetToggle(2, "DivineShieldHP") then return end
    return spell:Cast(player)
end)

BlessingofProtection:Callback("general", function(spell)
    if Action.Zone == "arena" then return end
    if mouseover and mouseover.isFriendly and mouseover:IsUnit(tank) then return end -- I don't trust these users to not be doing something weird in game.
    if target and target.isFriendly and target:IsUnit(tank) then return end -- I don't trust these users to not be doing something weird in game.
    local roleM = UnitGroupRolesAssigned(mouseover:CallerId())
    if roleM == "TANK" then return end
    local roleT = UnitGroupRolesAssigned(target:CallerId())
    if roleT == "TANK" then return end
    if player:HasDeBuff(A.Forbearance.ID) then return end
    if DivineShield:Cooldown() < 2000 then return end
    if not player.inCombat then return end
    if player.hp > A.GetToggle(2, "BopHP") then return end
    return spell:Cast(player)
end)

DevotionAura:Callback("general", function(spell)
    if player:HasBuff(A.DevotionAura.ID) then return end
    return spell:Cast(player)
end)

local function general()
    DivineShield("general")
    BlessingofProtection("general")
    ShieldofVengeance("general")
    DivineProtection("general")
    FlashofLight("general")
    WordofGlory("general")
    Redemption("general")
    DevotionAura("general")
    FlashofLight("generalOOC")
end



--### APL: 3/1/25

--############################################################################ COOLDOWNS #############################################################################--
--cooldowns->add_action( "lights_judgment,if=spell_targets.lights_judgment>=2|!raid_event.adds.exists|raid_event.adds.in>75|raid_event.adds.up" );
LightsJudgment:Callback("cooldowns", function(spell)
    if shouldBurst() and gameState.activeEnemies >= 2 then return spell:Cast(target) end
end)

--cooldowns->add_action( "fireblood,if=buff.avenging_wrath.up|buff.crusade.up&buff.crusade.stack=10|debuff.execution_sentence.up" );
Fireblood:Callback("cooldowns", function(spell)
    if shouldBurst() and (player:Buff(buffs.avengingWrath) or (player:Buff(buffs.crusade) and player:HasBuffCount(buffs.crusade) == 10) or target:HasDeBuff(debuffs.executionSentence)) then return spell:Cast(player) end
end)

--  cooldowns->add_action( "shield_of_vengeance,if=fight_remains>15&(!talent.execution_sentence|!debuff.execution_sentence.up)&!buff.divine_hammer.up" );
--todo - question on this
ShieldofVengeance:Callback("cooldowns", function(spell)
    if combatTime < 1 then return end
    if player.hp > 70 then return end
    if shouldBurst() and (not A.ExecutionSentence:IsTalentLearned() or not target:HasDeBuff(debuffs.executionSentence)) and not player:HasBuff(buffs.divineHammer) then return spell:Cast(player) end
end)

--cooldowns->add_action( "execution_sentence,if=(!buff.crusade.up&cooldown.crusade.remains>15|buff.crusade.stack=10|cooldown.avenging_wrath.remains<0.75|cooldown.avenging_wrath.remains>15|talent.radiant_glory)
--&(holy_power>=4&time<5|holy_power>=3&time>5|holy_power>=2&(talent.divine_auxiliary|talent.radiant_glory))&(!talent.divine_hammer|cooldown.divine_hammer.remains)&(target.time_to_die>8&!talent.executioners_will|target.time_to_die>12)
--&cooldown.wake_of_ashes.remains<gcd" );
ExecutionSentence:Callback("cooldowns", function(spell)
    if shouldBurst() and 
    (not player:Buff(buffs.crusade) and (Crusade:Cooldown() > 15000 or player:HasBuffCount(buffs.crusade) == 10) or AvengingWrath:Cooldown() < 750 or AvengingWrath:Cooldown() > 15000 or A.RadiantGlory:IsTalentLearned()) and 
    ((holyPower >= 4 and combatTime < 5) or (holyPower >= 3 and combatTime > 5) or (holyPower >= 2 and (A.DivineAuxiliary:IsTalentLearned() or A.RadiantGlory:IsTalentLearned()) and (not A.DivineHammer.IsTalentLearned() or DivineHammer:Cooldown() > 500) and
    WakeofAshes:Cooldown() <= MakGcd())) then 
        return spell:Cast(target) 
    end
end)

--cooldowns->add_action( "avenging_wrath,if=(holy_power>=4&time<5|holy_power>=3&time>5|holy_power>=2&talent.divine_auxiliary&(cooldown.execution_sentence.remains=0|cooldown.final_reckoning.remains=0))&(!raid_event.adds.up|target.time_to_die>10)" );
AvengingWrath:Callback("cooldowns", function(spell)
    if A.RadiantGlory:IsTalentLearned() then return end
    if not IsPlayerSpell(A.AvengingWrath.ID) then return end
    if not inMelee then return end
    if shouldBurst() and (holyPower >= 4 or (holyPower >= 3 and MakGcd() > 5) or (holyPower >= 2 and A.DivineAuxiliary:IsTalentLearned() and (ExecutionSentence:Cooldown() == 0 or FinalReckoning:Cooldown() == 0))) then return spell:Cast(player) end
end)

--cooldowns->add_action( "crusade,if=holy_power>=5&time<5|holy_power>=3&time>5" );
Crusade:Callback("cooldowns", function(spell)
    if A.RadiantGlory:IsTalentLearned() then return end
    if not IsPlayerSpell(A.Crusade.ID) then return end
    if not inMelee then return end
    if shouldBurst() and ((holyPower >= 5 and combatTime < 5) or (holyPower >= 3 and combatTime > 5)) then return spell:Cast(player) end
end)

--cooldowns->add_action( "final_reckoning,if=(holy_power>=4&time<8|holy_power>=3&time>=8|holy_power>=2&(talent.divine_auxiliary|talent.radiant_glory))&(cooldown.avenging_wrath.remains>10|cooldown.crusade.remains&(!buff.crusade.up|buff.crusade.stack>=10)|talent.radiant_glory&(buff.avenging_wrath.up|talent.crusade&cooldown.wake_of_ashes.remains<gcd))&(!raid_event.adds.exists|raid_event.adds.up|raid_event.adds.in>40)" );
FinalReckoning:Callback("cooldowns", function(spell)
    if not target.bigButtons then return end
    if not inMelee then return end
    if shouldBurst() and ((holyPower >= 4 and combatTime < 8) or (holyPower >= 3 and combatTime >= 8) or (holyPower >= 2 and (A.DivineAuxiliary:IsTalentLearned() or A.RadiantGlory:IsTalentLearned())) and (AvengingWrath:Cooldown() > 10 or (Crusade:Cooldown() > 0 and (not player:Buff(buffs.crusade) or player:HasBuffCount(buffs.crusade) >= 10)) or (A.RadiantGlory:IsTalentLearned() and (player:Buff(buffs.avengingWrath) or (A.Crusade:IsTalentLearned() and WakeofAshes:Cooldown() < MakGcd()))))) then return spell:Cast() end
end)

local function cooldowns()
    LightsJudgment("cooldowns")
    Fireblood("cooldowns")
    ShieldofVengeance("cooldowns")
    ExecutionSentence("cooldowns")
    AvengingWrath("cooldowns")
    Crusade("cooldowns")
    FinalReckoning("cooldowns")
end

--############################################################################ FINISHERS #############################################################################--

--finishers->add_action( "hammer_of_light" );
HammerofLight:Callback("finishers", function(spell)
    return spell:Cast(target)
end)

--finishers->add_action( "divine_hammer,if=holy_power=5" );
DivineHammer:Callback("finishers", function(spell)
    if not inMelee then return end
    if shouldBurst() then return spell:Cast(target) end
end)

--finishers->add_action( "divine_storm,if=variable.ds_castable&!buff.hammer_of_light_ready.up(cooldown.divine_hammer.remains|!talent.divine_hammer)&(!talent.crusade|cooldown.crusade.remains>gcd*3|buff.crusade.up&buff.crusade.stack<10|talent.radiant_glory)" );
DivineStorm:Callback("finishers", function(spell)
    if Action.Zone ~= "arena" and gameState.dsCastable and not IsSpellOverlayed(A.HammerofLight.ID) and (not A.Crusade:IsTalentLearned() or Crusade:Cooldown() > MakGcd() * 3 or (player:Buff(buffs.crusade) and player:HasBuffCount(buffs.crusade) < 10) or A.RadiantGlory:IsTalentLearned()) then return spell:Cast(target) end
end)

--finishers->add_action( "divine_storm,if=variable.ds_castable&!buff.hammer_of_light_ready.up&(!talent.crusade|cooldown.crusade.remains>gcd*3|buff.crusade.up&buff.crusade.stack<10|talent.radiant_glory)" );
DivineStorm:Callback("finishers", function(spell)
    if Action.Zone ~= "arena" and gameState.dsCastable and not IsSpellOverlayed(A.HammerofLight.ID) and (not A.Crusade:IsTalentLearned() or Crusade:Cooldown() > MakGcd() * 3 or (player:Buff(buffs.crusade) and player:HasBuffCount(buffs.crusade) < 10) or A.RadiantGlory:IsTalentLearned()) then return spell:Cast(target) end
end)

--finishers->add_action( "justicars_vengeance,if=(!talent.crusade|cooldown.crusade.remains>gcd*3|buff.crusade.up&buff.crusade.stack<10|talent.radiant_glory)&!buff.hammer_of_light_ready.up&(cooldown.divine_hammer.remains|!talent.divine_hammer)" );
JusticarsVengeance:Callback("finishers", function(spell)
    if A.FinalVerdict:IsTalentLearned() then return end
    if (not A.Crusade:IsTalentLearned() or Crusade:Cooldown() > MakGcd() * 3 or (player:Buff(buffs.crusade) and player:HasBuffCount(buffs.crusade) < 10) or A.RadiantGlory:IsTalentLearned()) and not IsSpellOverlayed(A.HammerofLight.ID) then return spell:Cast(target) end
end)

--finishers->add_action( "templars_verdict,if=(!talent.crusade|cooldown.crusade.remains>gcd*3|buff.crusade.up&buff.crusade.stack<10|talent.radiant_glory)&!buff.hammer_of_light_ready.up&(cooldown.divine_hammer.remains|!talent.divine_hammer)" );
FinalVerdict:Callback("finishers", function(spell)
    if A.JusticarsVengeance:IsTalentLearned() then return end
    if (not A.Crusade:IsTalentLearned() or Crusade:Cooldown() > MakGcd() * 3 or (player:Buff(buffs.crusade) and player:HasBuffCount(buffs.crusade) < 10) or A.RadiantGlory:IsTalentLearned()) and not IsSpellOverlayed(A.HammerofLight.ID) then return spell:Cast(target) end
end)

local function finishers()
    HammerofLight("finishers")
    DivineHammer("finishers")
    DivineStorm("finishers")
    JusticarsVengeance("finishers")
    FinalVerdict("finishers")
end

--############################################################################ GENERATORS ###########################################################################--

--generators->add_action( "call_action_list,name=finishers,if=(holy_power=5|holy_power=4&buff.divine_resonance.up|buff.all_in.up)&cooldown.wake_of_ashes.remains" );

--generators->add_action( "templar_slash,if=buff.templar_strikes.remains<gcd*2" );
--Not sure how to get templar strike buff its weird
--TemplarSlash:Callback("generators", function(spell)
--    if not inMelee then return end
--    if IsSpellOverlayed(A.TemplarSlash.ID) and player:BuffRemains(buffs.templarstrikes) < (MakGcd() * 2) then return spell:Cast(target) end
--end)

--generators->add_action( "templar_slash,if=buff.templar_strikes.remains<gcd*2" );
TemplarSlash:Callback("generators", function(spell)
    if not inMelee then return end
    if A.CrusadingStrikes:IsTalentLearned() then return end
    if IsSpellOverlayed(A.TemplarSlash.ID) then return spell:Cast(target) end
end)

-- generators->add_action( "blade_of_justice,if=!dot.expurgation.ticking&talent.holy_flames" );
BladeofJustice:Callback("generators", function(spell)
    if not inMelee then return end
    if not target:HasDeBuff(A.Expurgation.ID) and IsPlayerSpell(A.HolyFlames.ID) then return spell:Cast(target) end
end)

--generators->add_action( "wake_of_ashes,if=(!talent.lights_guidance|holy_power>=2&talent.lights_guidance)&(cooldown.avenging_wrath.remains>6|cooldown.crusade.remains>6|talent.radiant_glory)&
--(!talent.execution_sentence|cooldown.execution_sentence.remains>4|target.time_to_die<8)&(!raid_event.adds.exists|raid_event.adds.in>10|raid_event.adds.up)" );
WakeofAshes:Callback("generators", function(spell)
    if not target.bigButtons then return end
    if not inMelee then return end
    if shouldBurst() and (not A.LightsGuidance:IsTalentLearned() or (holyPower >= 2 and A.LightsGuidance:IsTalentLearned())) and 
    (AvengingWrath:Cooldown() > 6000 or Crusade:Cooldown() > 6000 or A.RadiantGlory:IsTalentLearned()) and (not A.ExecutionSentence:IsTalentLearned() or ExecutionSentence:Cooldown() > 4000) then return spell:Cast() end
end)

--generators->add_action( "divine_toll,if=holy_power<=2&(!raid_event.adds.exists|raid_event.adds.in>10|raid_event.adds.up)&(cooldown.avenging_wrath.remains>15|cooldown.crusade.remains>15|talent.radiant_glory|fight_remains<8)" );
DivineToll:Callback("generators", function(spell)
    local dtRange = A.GetToggle(2, "divineTollRange")
    if not shouldBurst() then return end
    if target.distance > dtRange then return end
    if holyPower <= 2 and (AvengingWrath:Cooldown() > 15000 or Crusade:Cooldown() > 15000 or A.RadiantGlory:IsTalentLearned()) then return spell:Cast(target) end
end)

--finishers

--generators->add_action( "templar_slash,if=buff.templar_strikes.remains<gcd&spell_targets.divine_storm>=2" );
TemplarSlash:Callback("generators2", function(spell)
    if not inMelee then return end
    if A.CrusadingStrikes:IsTalentLearned() then return end
    if gameState.activeEnemies >= 2 and IsSpellOverlayed(A.TemplarSlash.ID) then return spell:Cast(target) end
end)

--generators->add_action( "blade_of_justice,if=(holy_power<=3|!talent.holy_blade)&(spell_targets.divine_storm>=2&talent.blade_of_vengeance)" );
BladeofJustice:Callback("generators", function(spell)
    if (holyPower <= 3 or not A.HolyBlade:IsTalentLearned()) and gameState.activeEnemies >= 2 and A.BladeofVengeance:IsTalentLearned() then return spell:Cast(target) end
end)

--generators->add_action( "hammer_of_wrath,if=(spell_targets.divine_storm<2|!talent.blessed_champion)&(holy_power<=3|target.health.pct>20|!talent.vanguards_momentum)&(buff.blessing_of_anshe.up)" );
HammerofWrath:Callback("generators", function(spell)
    if (gameState.activeEnemies < 2 or not A.BlessedChampion:IsTalentLearned()) and (holyPower <= 3 or target.hp > 20 or not A.VanguardsMomentum:IsTalentLearned()) and player:HasBuff(buffs.blessingofAnshee) then return spell:Cast(target) end
end)

--generators->add_action( "templar_strike" );
TemplarStrike:Callback("generators", function(spell)
    if not inMelee then return end
    if A.CrusadingStrikes:IsTalentLearned() then return end
    return spell:Cast(target)
end)

--generators->add_action( "judgment" );
Judgement:Callback("generators", function(spell)
    return spell:Cast(target)
end)

--generators->add_action( "blade_of_justice" );
BladeofJustice:Callback("generators", function(spell)
    return spell:Cast(target)
end)

--generators->add_action( "hammer_of_wrath,if=(spell_targets.divine_storm<2|!talent.blessed_champion)" );
HammerofWrath:Callback("generators2", function(spell)
    if (gameState.activeEnemies < 2 or not A.BlessedChampion:IsTalentLearned()) then return spell:Cast(target) end
end)

--generators->add_action( "templar_slash" );
TemplarSlash:Callback("generators3", function(spell)
    if not inMelee then return end
    if A.CrusadingStrikes:IsTalentLearned() then return end
    return spell:Cast(target)
end)

--generators->add_action( "crusader_strike" );
CrusaderStrike:Callback("generators", function(spell)
    if not inMelee then return end
    if A.CrusadingStrikes:IsTalentLearned() then return end
    if A.TemplarStrikeStuff:IsTalentLearned() then return end
    return spell:Cast(target)
end)

--generators->add_action( "hammer_of_wrath" );
HammerofWrath:Callback("generators3", function(spell)
    return spell:Cast(target)
end)

--generators->add_action( "arcane_torrent" );
ArcaneTorrent:Callback("generators", function(spell)
    if not inMelee then return end
    return spell:Cast(player)
end)

local function generators()
    --generators->add_action( "call_action_list,name=finishers,if=(holy_power=5|holy_power=4&buff.divine_resonance.up|buff.all_in.up)&cooldown.wake_of_ashes.remains" );
    if (holyPower == 5 or (holyPower == 4 and player:Buff(buffs.divineResonance))) and WakeofAshes:Cooldown() > 500 then finishers() end
    TemplarSlash("generators")
    WakeofAshes("generators")
    DivineToll("generators")
    if combatTime > 2 then finishers() end
    TemplarSlash("generators2")
    BladeofJustice("generators")
    HammerofWrath("generators")
    TemplarStrike("generators")
    Judgement("generators")
    BladeofJustice("generators")
    HammerofWrath("generators2")
    TemplarSlash("generators3")
    if target.hp <= 20 or player:Buff(buffs.avengingWrath) or player:Buff(buffs.crusade) or player:Buff(buffs.empyreanPower) then finishers() end
    CrusaderStrike("generators")
    finishers()
    HammerofWrath("generators3")
    ArcaneTorrent("generators")
end

--############################################################################ PVP STUFF ##############################################################################--

--Rebuke:Callback("pvp", function(spell)
--    if not inMelee then return end
--    if not enemy.pvpKick then return end
--    return spell:Cast(target)
--end)

A[3] = function(icon)
	FrameworkStart(icon)
    updateGameState()

    --Rebuke("pvp")

    if Action.Zone ~= "arena" then
        makInterrupt(interrupts)
    end

    if player.inCombat then
        Intercession("general")
    end

    if target.exists and target.hp > 0 and target.canAttack and Judgement:InRange(target) and not player:Debuff(410201) then
        general()
    
        if shouldBurst() then
            if Trinket(1, "Damage") then Trinket1() end
            if Trinket(2, "Damage") then Trinket2() end

            local damagePotion = Action.GetToggle(2, "damagePotion")
            local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
            local potionExhausted = Action.GetToggle(2, "potionExhausted")
            local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")
            local damagePotionObject = Action.DetermineUsableObject("player", nil, nil, true, nil, A.TemperedPotion1, A.TemperedPotion2, A.TemperedPotion3, A.PotionofUnwaveringFocus1, A.PotionofUnwaveringFocus2, A.PotionofUnwaveringFocus3)

            if damagePotionObject and damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
                local shouldPot = player:Buff(buffs.avengingWrath) or player:Buff(buffs.crusade) or target:Debuff(debuffs.executionSentence, true)
                if shouldPot then
                    return damagePotionObject:Show(icon)
                end
            end
        end
        cooldowns()
        generators()
    end


	return FrameworkEnd()
end

--##########################################################################ARENA STUFF#####################################################################################################

-------------------------------------------------------------------------## ARENA ENEMY ##--------------------------------------------------------------------------------------------------
-- Hoj healer if he is not in cc and has more than 50% dr
HammerofJustice:Callback("arena_healer", function(spell, enemy)
    if enemy.ccImmune then return end
    if not spell:InRange(enemy) then return end
    if not enemy:IsUnit(enemyHealer) then return end
    if enemy:Debuff(203337) then return end
    if enemy.stunDr < 0.5 then return end
    if enemy:IsTarget() then return end
    if enemy:CCRemains() > 1500 then return end
    if player:Debuff(410201) then return end
    Aware:displayMessage("HOJ - Enemy Healer", "Blue", 1)
    return spell:Cast(enemy)
end)

-- Hoj kill target if the enemy team's healer is cced for more than 2 seconds and the kill target is not in cc and has more than 50% dr and is lower than 50% hp
HammerofJustice:Callback("arena_kill", function(spell, enemy)
    if enemy.ccImmune then return end
    if not spell:InRange(enemy) then return end
    if not enemy:IsTarget() then return end
    if enemy:Debuff(203337) then return end
    --if enemy.stunDr < 0.5 then return end
    if enemy:IsUnit(enemyHealer) then return end
    if enemyHealer:CCRemains() < 2000 then return end
    --if not enemyHealer:HasDebuffFromFor(MakLists.CC, 500) then return end
    --if enemy.hp > 50 then return end
    if player:Debuff(410201) then return end

    if enemy.stunDr == 1 then
        Aware:displayMessage("HOJ - KT - Enemy Healer CCed", "Red", 1)
        return spell:Cast(enemy)
    end

    if enemy.stunDr < 0.5 and enemy.hp < 50 then
        Aware:displayMessage("HOJ - KT - Enemy Healer CCed", "Red", 1)
        return spell:Cast(enemy)
    end
end)

-- PVP Kick with Rebuke
Rebuke:Callback("arena", function(spell, enemy)
    if enemy:IsKickImmune() then return end
    if not spell:InRange(enemy) then return end
    if enemy.distance > 10 then return end
    if player:Debuff(410201) then return end
    if not enemy:CastingFromFor(MakLists.arenaKicks, 620) then return end
    return spell:Cast(enemy)
end)

-------------------------------------------------------------------------## M+ PARTY ##-----------------------------------------------------------------------------------------------------

CleanseToxins:Callback("mplus", function(spell, friendly) 
    local iNeedCleanse = player.diseased or player.poisoned
    local shouldDispel = friendly.diseased or friendly.poisoned

    --Hopefully this makes it self prio
    if iNeedCleanse then
        if not friendly.isMe then return end
    end

    if shouldDispel then
        return Debounce("cleanse", 1000, 2500, spell, friendly)
    end
end)


-------------------------------------------------------------------------## ARENA PARTY ##--------------------------------------------------------------------------------------------------


-- BOP partry if they are lower than slider values and not total immune and friendly is not a paladin
BlessingofProtection:Callback("arena_party", function(spell, friendly)
    local hasPhys = MakMulti.arena:Any(function(enemy) return not enemy.isHealer and not enemy.isCaster end)
    if Action.Zone ~= "arena" then return end
    if not hasPhys then return end
    if friendly:IsMe() and DivineShield:Cooldown() < 2000 and not IsPlayerSpell(A.LightsRevocation.ID) then return end
    if friendly.hp > A.GetToggle(2, "BopHPParty") then return end
    if friendly.hp > 40 and target.hp < 20 then return end
    if ((arena1.exists and not arena1:IsMelee()) and (arena2.exists and not arena2:IsMelee()) and (arena3.exists and not arena3:IsMelee())) then return end
    if friendly:HasDeBuff(A.Forbearance.ID) then return end
    if friendly:IsTotalImmune() then return end
    if friendly:ClassID() == 2 and not friendly:IsMe() then return end
    --Aware:displayMessage("Blessing of Protection on Party", "White", 1)
    return spell:Cast(friendly)
end)

BlessingofProtection:Callback("dungeon_party", function(spell, friendly)
    if Action.Zone == "arena" then return end
    if friendly:IsUnit(tank) then return end
    local role = UnitGroupRolesAssigned(friendly:CallerId())
    if role == "TANK" then return end
    if friendly:IsMe() and DivineShield:Cooldown() < 2000 and not IsPlayerSpell(A.LightsRevocation.ID) then return end
    if friendly.hp > A.GetToggle(2, "BopHPParty") then return end
    if friendly:HasDeBuff(A.Forbearance.ID) then return end
    if friendly:IsTotalImmune() then return end
    if friendly:ClassID() == 2 and not friendly:IsMe() then return end
    --Aware:displayMessage("Blessing of Protection on Party", "White", 1)
    return spell:Cast(friendly)
end)

BlessingofSpellwarding:Callback("arena_party", function(spell, friendly)
    local hasMagicDamage = MakMulti.arena:Any(function(enemy) return not enemy.isHealer and enemy.isCaster end)
    if not hasMagicDamage then return end
    if friendly:IsMe() and DivineShield:Cooldown() < 2000 and not IsPlayerSpell(A.LightsRevocation.ID) then return end
    if friendly.hp > A.GetToggle(2, "BopHPParty") then return end
    if friendly.hp > 40 and target.hp < 20 then return end
    --if ((arena1.exists and not arena1:IsMelee()) and (arena2.exists and not arena2:IsMelee()) and (arena3.exists and not arena3:IsMelee())) then return end
    if friendly:HasDeBuff(A.Forbearance.ID) then return end
    if friendly:IsTotalImmune() then return end
    if friendly:ClassID() == 2 and not friendly:IsMe()  then return end
    --Aware:displayMessage("Blessing of Spellwarding on Party", "White", 1)
    return spell:Cast(friendly)
end)

-- LOH party if they are lower than slider values and not total immune and friendly is not a paladin
LayonHandsPassive:Callback("arena_party", function(spell, friendly)
    if IsPlayerSpell(EmpyreanWard.id) then return end
    if friendly.totalImmune then return end
    if friendly:IsMe() and DivineShield:Cooldown() < 2000 and not IsPlayerSpell(A.LightsRevocation.ID) then return end
    if friendly.hp > A.GetToggle(2, "LohHPParty") then return end
    if friendly.hp > 40 and target.hp < 20 and A.Zone == "arena" then return end
    if friendly:HasDeBuff(A.Forbearance.ID) then return end
    if friendly:IsTotalImmune() then return end
    if friendly:ClassID() == 2 and not friendly:IsMe() then return end
    --Aware:displayMessage("Lay on Hands on Party", "White", 1)
    return spell:Cast(friendly)
end)

-- LOH party if they are lower than slider values and not total immune and friendly is not a paladin
LayonHandsPassiveToo:Callback("arena_party", function(spell, friendly)
    if not IsPlayerSpell(EmpyreanWard.id) then return end
    if friendly.totalImmune then return end
    if friendly:IsMe() and DivineShield:Cooldown() < 2000 and not IsPlayerSpell(A.LightsRevocation.ID) then return end
    if friendly.hp > A.GetToggle(2, "LohHPParty") then return end
    if friendly.hp > 40 and target.hp < 20 and A.Zone == "arena" then return end
    if friendly:HasDeBuff(A.Forbearance.ID) then return end
    if friendly:IsTotalImmune() then return end
    if friendly:ClassID() == 2 and not friendly:IsMe() then return end
    --Aware:displayMessage("Lay on Hands on Party", "White", 1)
    return spell:Cast(friendly)
end)

-- Blessing of Sacrifice party if they are lower than slider values
BlessingofSacrifice:Callback("arena_party", function(spell, friendly)
    if A.IsInPvP or not A.GetToggle(2, "sacTankOnly") then
        if friendly:IsMe() then return end
        if friendly.hp > 40 and target.hp < 20 then return end
        if player.hp < 40 then return end
        if friendly:IsTotalImmune() then return end

        if friendly.hp < A.GetToggle(2, "SacHP") then
            return spell:Cast(friendly)
        end

        if friendly.hp < 60 and friendly.hp < player.hp and (not healer.exists or healer:CCRemains() > 2000) then
            return spell:Cast(friendly)
        end
    else
        if A.GetToggle(2, "sacTankOnly") then
            --if not friendly:IsUnit(tank) then return end
            local role = UnitGroupRolesAssigned(friendly:CallerId())
            if role ~= "TANK" then return end
            if player.hp < 40 then return end

            local tankBusterIn = MakuluFramework.DBM_TankBusterIn()
            if tankBusterIn < 1500 then
                --Aware:displayMessage("Blessing of Sacrifice on Tank Buster", "White", 1)
                return spell:Cast(friendly)
            end
        end
    end
end)

-- FOL party if they are lower than slider values and we have lights celerity talent
FlashofLightParty:Callback("arena_party", function(spell, friendly)
    if friendly.hp > A.GetToggle(2, "FlashLightHPParty") then return end
    if not A.LightsCelerity:IsTalentLearned() then return end
    if friendly.hp > 40 and target.hp < 20 then return end
    if friendly:IsTotalImmune() and not healer.cc then return end
    --Aware:displayMessage("Flash of Light on Party", "White", 1)
    return spell:Cast(friendly)
end)

-- Word of Glory party if they are lower than slider values
WordofGlory:Callback("arena_party", function(spell, friendly)
    if friendly.hp > A.GetToggle(2, "WogHPParty") then return end
    if friendly.hp > 40 and target.hp < 20 then return end
    --maybe add healer.cc check later
    --Aware:displayMessage("Word of Glory on Party", "White", 1)
    return spell:Cast(friendly)
end)

-- Word of Glory party if they are lower than slider values
EternalFlame:Callback("arena_party", function(spell, friendly)
    if friendly.hp > A.GetToggle(2, "EFHPParty") then return end
    if friendly.hp > 40 and target.hp < 20 then return end
    --maybe add healer.cc check later
    --Aware:displayMessage("Eternal Flame on Party", "White", 1)
    return spell:Cast(friendly)
end)

--Blessing of Sanctuary if friendly is healer and has a buff from our maklists
BlessingofSanctuary:Callback("arena_party", function(spell, friendly)
    if friendly:IsMe() then return end
    --if not arenaHealthCheck(90) then return end
    if not friendly.isHealer then return end
    if not friendly:HasDeBuffFromFor(MakLists.sanc, 500) then return end
    Aware:displayMessage("Blessing of Sanctuary on Party", "White", 1)
    return spell:Cast(friendly)
end)

local rootCheck = {
    78675,
}

--Freedom Player with Party
BlessingofFreedom:Callback("arena_party", function(spell, friendly)
    if friendly:IsMe() then return end
    if not IsPlayerSpell(A.UnboundFreedom.ID) then return end
    if target.hp < 10 then return end

    if friendly:IsUnit(healer) and
        friendly:HasDeBuffFromFor(rootCheck, 700) and friendly.rooted then
            return spell:Cast(friendly)
    end

    if player:HasDeBuffFromFor(MakLists.freedom, 1150) then
        return spell:Cast(friendly)
    end

    local suleymanClap = UnitPower("target") >= 90 and target.npcId == 212826
    if suleymanClap and friendly:ClassID() ~= 2 then -- Don't cast on other Paladins
        return spell:Cast(friendly)
    end
end)


local enemyRotation = function(enemy)
    if player:Debuff(410201) then return end
    if Action.Zone ~= "arena" then return end
	if not enemy.exists then return end
    if enemy.hp <= 0 then return end

    HammerofJustice("arena_healer", enemy)
    HammerofJustice("arena_kill", enemy)
    Rebuke("arena", enemy)

end

local partyRotation = function(friendly)
    local partySize = GetNumGroupMembers()
    if partySize > 5 then return end
    if not friendly.exists then return end
    if friendly.hp <= 0 then return end
    if friendly:IsDeadOrGhost() then return end
    BlessingofSanctuary("arena_party", friendly)
    LayonHandsPassive("arena_party", friendly)
    LayonHandsPassiveToo("arena_party", friendly)
    CleanseToxins("mplus", friendly)
    BlessingofProtection("arena_party", friendly)
    BlessingofProtection("dungeon_party", friendly)
    BlessingofSpellwarding("arena_party", friendly)
    BlessingofSacrifice("arena_party", friendly)
    --FlashofLightParty("arena_party", friendly)
    WordofGlory("arena_party", friendly)
    EternalFlame("arena_party", friendly)
    BlessingofFreedom("arena_party", friendly)

end

A[6] = function(icon)
	RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and targetForInterrupt(interrupts) then return TabTarget() end
    if autoTarget() then return TabTarget() end
    partyRotation(party1)
	enemyRotation(arena1)

	return FrameworkEnd()
end

A[7] = function(icon)
	RegisterIcon(icon)
    partyRotation(party2)
	enemyRotation(arena2)

	return FrameworkEnd()
end

A[8] = function(icon)
	RegisterIcon(icon)
    partyRotation(party3)
	enemyRotation(arena3)

	return FrameworkEnd()
end

A[9] = function(icon)
	RegisterIcon(icon)
	partyRotation(party4)

	return FrameworkEnd()
end

A[10] = function(icon)
	RegisterIcon(icon)
	partyRotation(player)

	return FrameworkEnd()
end
