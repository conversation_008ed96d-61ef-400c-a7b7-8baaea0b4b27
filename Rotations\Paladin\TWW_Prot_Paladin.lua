if not <PERSON><PERSON>luValidCheck() then return true end
if not <PERSON><PERSON><PERSON>_magic_number == 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 66 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local TableToLocal     = MakuluFramework.tableToLocal
local ConstUnit        = MakuluFramework.ConstUnits
local cacheContext     = MakuluFramework.Cache
local Trinket          = MakuluFramework.Trinket
local Aware            = MakuluFramework.Aware
local RegisterEvent    = MakuluFramework.Events.register
local ConstCell        = cacheContext:getConstCacheCell()

local Action           = _G.Action
local Player           = Action.Player
local MultiUnits       = Action.MultiUnits

local BossMods         = Action.BossMods

local _G, setmetatable = _G, setmetatable

--[[

]]--
local ActionID = {
    BlessingofFreedom = { ID = 1044 },
    BlessingofProtection = { ID = 1022 },
    BlessingofSacrifice = { ID = 6940 },
    BlindingLight = { ID = 115750 },
    ConcentrationAura = { ID = 317920 },
    Consecration = { ID = 26573 },
    CrusaderAura = { ID = 32223 },
    DevotionAura = { ID = 465 },
    DivineShield = { ID = 642 },
    DivineSteed = { ID = 190784 },
    DivineToll = { ID = 375576 },
    FlashofLight = { ID = 19750 },
    HammerofJustice = { ID = 853 },
    HammerofWrath = { ID = 24275 },
    HandofReckoning = { ID = 62124 },
    Intercession = { ID = 391054 },
    Judgment = { ID = 275779 },
    LayonHands = { ID = 633 },
    Rebuke = { ID = 96231 },
    Redemption = { ID = 7328 },
    SenseUndead = { ID = 5502 },
    ShieldoftheRighteous = { ID = 53600 },
    WordofGlory = { ID = 85673 },

    ArdentDefender = { ID = 31850 },
    AvengersShield = { ID = 31935 },
    AvengingWrath = { ID = 31884 },
    BlessedHammer = { ID = 204019 },
    BlessingofSpellwarding = { ID = 204018 },
    CleanseToxins = { ID = 213644 },
    EyeofTyr = { ID = 387174 },
    GuardianofAncientKings = { ID = 86659 },
    RiteofSanctification = { ID = 433568 },

    MomentofGlory = { ID = 327193 },
    BastionofLight = { ID = 378974 },
    LightsGuidance = { ID = 427445 },
    Redoubt = { ID = 280373 },
    RighteousProtector = { ID = 204074 },

    HolyBulwark = { ID = 432459 },
    SacredWeapon = { ID = 432472 },

    HammerofLight = { ID = 427453 },
    HammeroftheRighteous = { ID = 53595 },
    CrusaderStrike = { ID = 35395 },
    BulwarkofRighteousFury = { ID = 386653 },
    InmostLight = { ID = 405757 },
    BlessedAssurance = { ID = 433015 },
    LightsDeliverance = { ID = 425518 },
    Hammerfall = { ID = 432463 },
    OfDuskandDawn = { ID = 409441, Hidden = true },
    RefiningFire = { ID = 469883, Hidden = true },
    HammerandAnvil = { ID = 433718, Hidden = true },
    ShaketheHeavens = { ID = 431533, Hidden = true },
    SanctifiedWrath = { ID = 53376, Hidden = true },
}

local A, M = MakuluFramework.CreateActionVar(ActionID)
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
	local result = {}
	for k, v in pairs(A) do
		result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
	end
	return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_PALADIN_PROTECTION] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local HPG_Abilities = {
    [275779] = true,  -- Judgment
    [204019] = true,  -- Blessed Hammer
    [31935]  = true,  -- Avenger's Shield
    [24275]  = true,  -- Hammer of Wrath
    [53595]  = true,  -- Hammer of the Righteous
}

local hpg_counter = 0
local dawn_stacks = 0

local player = ConstUnit.player
local target = ConstUnit.target
local focus = ConstUnit.focus
local mouseover = ConstUnit.mouseover
local pet = ConstUnit.pet
local arena1 = ConstUnit.arena1
local arena2 = ConstUnit.arena2
local arena3 = ConstUnit.arena3
local party1 = ConstUnit.party1
local party2 = ConstUnit.party2
local party3 = ConstUnit.party3
local party4 = ConstUnit.party4
local healer = ConstUnit.healer
local enemyHealer = ConstUnit.enemyHealer

local gameState = {}

local buffs = {
    arena_preparation = 32727,
    power_infusion = 10060,

    avenging_wrath = 31884,
    sentinel = 389539,
    hammer_of_light_free = 0,
    shake_the_heavens = 431536,
    sacred_weapon = 432502,
    shining_light = 327510,
    bulwark_of_righteous_fury = 386652,
    consecration = 188370,

    shield_of_the_righteous = 32403,
    holy_bulwark = 432607,
    ardent_defender = 31850,
    blessing_of_freedom = 1044,
    blessing_of_protection = 1022,
    divine_shield = 640,
    guardian_of_ancient_kings = 86659,

    crusader_aura = 32223,
    devotion_aura = 465,
    concentration_aura = 371920,

    blessed_assurance = 433019,
    divine_guidance = 460822,
    blessing_of_dawn = 385127,
    hammer_of_light = 427453,

    shining_light_free = 327510,
    sotr = 53600,

    bastion_of_light = 378974,
}

local debuffs = {
    exhaustion = 57723,
    forbearance = 25771,
}

local interrupts = {
    { spell = Rebuke },
    { spell = HammerofJustice, isCC = true },
    { spell = BlindingLight, isCC = true, aoe = true, distance = 3 },
}

local function num(val)
    if val then return 1 else return 0 end
end

local function shouldBurst()
    return makBurst()
end

local function EnemiesInSpellRange(makulu_spell)
    return ConstCell:GetOrSet("enemiesIn" .. makulu_spell.id, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and not enemy:IsTotem() and not enemy.isPet then
                if (player.inCombat and enemy.inCombat) or (not player.inCombat and not enemy.inCombat) or enemy.isDummy then
                    total = total + 1
                end
            end
        end
        return total
    end)
end

local function TotemsInSpellRange(makulu_spell)
    return ConstCell:GetOrSet("totemsIn" .. makulu_spell.id, function()
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            if makulu_spell:InRange(enemy) and enemy.inCombat and enemy:IsTotem() and not enemy.isFriendly then
                return true
            end
        end 
        return false
    end)
end

local function AutoTarget()
    if not player.inCombat then return false end

    if A.GetToggle(2, "autotarget") then
        for _, spellInfo in ipairs(interrupts) do
            if target:ShouldInterrupt(spellInfo.spell, spellInfo.isCC, spellInfo.aoe, spellInfo.distance) then
                return false
            end
        end
    end

    if A.GetToggle(2, "autotarget") and TotemsInSpellRange(Rebuke) and not target:IsTotem() then
        return true
    end

    if A.GetToggle(2, "autotaunt") and gameState.ShouldTaunt == "Switch" then
        return true
    end

    if Rebuke:InRange(target) and target.exists then return false end

    if A.GetToggle(2, "targetmelee") and EnemiesInSpellRange(Rebuke) > 0 then
        return true
    end
end

local function OnSpellCastSuccess()
    local _, eventType, _, sourceGUID, _, _, _, _, _, _, _, spellID = CombatLogGetCurrentEventInfo()
    if eventType == "SPELL_CAST_SUCCESS" and sourceGUID == UnitGUID("player") then
        if HPG_Abilities[spellID] then
            hpg_counter = hpg_counter + 1

            -- Check if we gained a Blessing of Dawn stack
            if hpg_counter >= 3 then
                hpg_counter = hpg_counter - 3
                dawn_stacks = math.min(dawn_stacks + 1, 2)  -- Max 2 stacks
            end
        end
    end
end

RegisterEvent("COMBAT_LOG_EVENT_UNFILTERED", OnSpellCastSuccess)

local function calculate_hpg_to_2dawn()
    local hpgs_needed_for_next_stack = 3 - hpg_counter
    local hpg_to_2dawn = (2 - dawn_stacks) * 3 - hpg_counter
    -- hpg_to_2dawn ranges from -2 to 6
    return hpg_to_2dawn
end

local function updateGameState()
    gameState = {
        TWW1has2P = player:Has2Set(),
        TWW1has4P = player:Has4Set(),
        ShouldTaunt = MakuluFramework.TauntStatus(HandofReckoning),
        tank_buster_in = MakuluFramework.DBM_TankBusterIn() or 1000000,
    }

    if not A.GetToggle(2, "usedbm") then
        gameState.tank_buster_in = 1000000
    end

    gameState.judgmentHP = 1 + num(player:TalentKnown(SanctifiedWrath.id) and (player:Buff(buffs.avenging_wrath) or player:Buff(buffs.sentinel))) + (2 * num(player:Buff(buffs.bastion_of_light)))
end

--actions.cooldowns=lights_judgment,if=spell_targets.lights_judgment>=2|!raid_event.adds.exists|raid_event.adds.in>75|raid_event.adds.up
LightsJudgment:Callback("cooldown", function(spell)
    if not A.GetToggle(1, "Racial") then return end

    if EnemiesInSpellRange(Rebuke) >= 2 then
        return spell:Cast(target)
    end
end)

-- actions.cooldowns+=/avenging_wrath
AvengingWrath:Callback("cooldown", function(spell)
    if shouldBurst() and Rebuke:InRange(target) then
        return spell:Cast()
    end
end)

-- actions.cooldowns+=/moment_of_glory,if=(buff.avenging_wrath.remains<15|(time>10))
MomentofGlory:Callback("cooldown", function(spell)
    if player:BuffRemains(buffs.avenging_wrath) < 15000 then
        return spell:Cast()
    end
end)

-- actions.cooldowns+=/divine_toll,if=spell_targets.shield_of_the_righteous>=3
DivineToll:Callback("cooldown", function(spell)
    if EnemiesInSpellRange(Rebuke) >= 3 then
        return spell:Cast()
    end
end)

-- actions.cooldowns+=/bastion_of_light,if=buff.avenging_wrath.up|cooldown.avenging_wrath.remains<=30
BastionofLight:Callback("cooldown", function(spell)
    if player:Buff(buffs.avenging_wrath) or AvengingWrath.cd <= 30000 then
        return spell:Cast()
    end
end)

-- actions.cooldowns+=/fireblood,if=buff.avenging_wrath.remains>8
Fireblood:Callback("cooldown", function(spell)
    if not A.GetToggle(1, "Racial") then return end
    if A.GetToggle(2, "firebloodDef") then return end
    if player:BuffRemains(buffs.avenging_wrath) > 800 then
        return spell:Cast()
    end
end)

-- actions+=/call_action_list,name=cooldowns
local function cooldown_rot()
    LightsJudgment("cooldown")
    AvengingWrath("cooldown")
    MomentofGlory("cooldown")
    DivineToll("cooldown")
    BastionofLight("cooldown")
    Fireblood("cooldown")
end

local function IsUsingDefensive()
    if player:Buff(buffs.ardent_defender) or player:Buff(buffs.guardian_of_ancient_kings) or player:Buff(buffs.divine_shield) or player:Buff(buffs.blessing_of_protection) then
        return true
    end
    return false
end

--Bleed
Fireblood:Callback("bleed", function(spell)
    if player.bleeding then
        return spell:Cast()
    end
end)

-- actions.defensives=ardent_defender
ArdentDefender:Callback("defensive", function(spell)
    if not IsUsingDefensive() and target.ttd > 5000 then
        if player.hp < A.GetToggle(2, "ArdentDefenderHP") or gameState.tank_buster_in < 1500 then
            return spell:Cast()
        end
    end
end)

GuardianofAncientKings:Callback("defensive", function(spell)
    if not IsUsingDefensive() and target.ttd > 5000 then
        if player.hp < A.GetToggle(2, "GuardianHP") or gameState.tank_buster_in < 1500 then
            return spell:Cast()
        end
    end
end)

DivineShield:Callback("defensive", function(spell)
    if not IsUsingDefensive() and target.ttd > 5000 then
        if player.hp < A.GetToggle(2, "DivineShieldHP") or gameState.tank_buster_in < 1500 then
            return spell:Cast()
        end
    end
end)

BlessingofProtection:Callback("defensive", function(spell)
    if player.hp < 15 and target.ttd > 5000 and not IsUsingDefensive() then
        return spell:Cast()
    end
end)

WordofGlory:Callback("defensive", function(spell)
    if player:Buff(buffs.shining_light_free) and player.hp < A.GetToggle(2, "WoGHPFree") then
        return spell:Cast()
    end

    if player.hp < A.GetToggle(2, "WoGHP") and not player:Buff(buffs.shining_light_free) then
        return spell:Cast()
    end
end)

-- actions+=/call_action_list,name=defensives
local function defensives_rot()
    WordofGlory("defensive")
    ArdentDefender("defensive")
    GuardianofAncientKings("defensive")
    DivineShield("defensive")
    BlessingofProtection("defensive")
end

--actions.standard=judgment,target_if=min:debuff.judgment.remains,if=charges>=2|full_recharge_time<=gcd.max
Judgment:Callback("standard", function(spell)
    if spell.frac >= 2 or spell:TimeToFullCharges() <= A.GetGCD() * 1000 then
        return spell:Cast(target)
    end
end)

--actions.standard+=/hammer_of_light,if=buff.hammer_of_light_free.remains<2|buff.shake_the_heavens.remains<1|!buff.shake_the_heavens.up|cooldown.eye_of_tyr.remains<1.5|fight_remains<2
HammerofLight:Callback("standard", function(spell)
    if player:BuffRemains(buffs.hammer_of_light_free) < 2000 or player:BuffRemains(buffs.shake_the_heavens) < 1000 or not player:Buff(buffs.shake_the_heavens) or EyeofTyr.cd < 1500 then
        return spell:Cast()
    end
end)

--actions.standard+=/eye_of_tyr,if=(hpg_to_2dawn=5|!talent.of_dusk_and_dawn.enabled)&talent.lights_guidance.enabled
--actions.standard+=/eye_of_tyr,if=(hpg_to_2dawn=1|buff.blessing_of_dawn.stack>0)&talent.lights_guidance.enabled
EyeofTyr:Callback("standard", function(spell)
    if not shouldBurst() then return end

    if IsSpellOverlayed(HammerofLight.id) then return end
    if not Rebuke:InRange(target) then return end
    
    if not player:TalentKnown(LightsGuidance.id) then return end

    return spell:Cast()
end)

--actions.standard+=/shield_of_the_righteous,if=!buff.hammer_of_light_ready.up&(buff.luck_of_the_draw.up&((holy_power+judgment_holy_power>=5)|(!talent.righteous_protector.enabled|cooldown.righteous_protector_icd.remains=0)))
--actions.standard+=/shield_of_the_righteous,if=!buff.hammer_of_light_ready.up&set_bonus.thewarwithin_season_2_4pc&((holy_power+judgment_holy_power>5)|(holy_power+judgment_holy_power>=5&cooldown.righteous_protector_icd.remains=0))
--actions.standard+=/shield_of_the_righteous,if=!set_bonus.thewarwithin_season_2_4pc&(!talent.righteous_protector.enabled|cooldown.righteous_protector_icd.remains=0)&!buff.hammer_of_light_ready.up
ShieldoftheRighteous:Callback("standard", function(spell)
    if IsSpellOverlayed(HammerofLight.id) then return end
    if not Rebuke:InRange(target) then return end
    
    if (player:Buff(buffs.luck_of_the_draw) and ((player.holyPower + gameState.judgmentHP >= 5) or (not player:TalentKnown(RighteousProtector.id) or spell.used > 1000))) then
        return spell:Cast()
    end

    if player:Has4Set() and ((player.holyPower + gameState.judgmentHP > 5) or (player.holyPower + gameState.judgmentHP >= 5 and player:TalentKnown(RighteousProtector.id) and spell.used > 1000)) then
        return spell:Cast()
    end

    if not player:Has4Set() and (not player:TalentKnown(RighteousProtector.id) or spell.used > 1000) then
        return spell:Cast()
    end
end)

--actions.standard+=/judgment,target_if=min:debuff.judgment.remains,if=spell_targets.shield_of_the_righteous>3&buff.bulwark_of_righteous_fury.stack>=3&holy_power<3
Judgment:Callback("standard2", function(spell)
    if EnemiesInSpellRange(Rebuke) > 3 and player:HasBuffCount(buffs.bulwark_of_righteous_fury) >= 3 and player.holyPower < 3 then
        return spell:Cast(target)
    end
end)

--actions.standard+=/avengers_shield,if=!buff.bulwark_of_righteous_fury.up&talent.bulwark_of_righteous_fury.enabled&spell_targets.shield_of_the_righteous>=3
AvengersShield:Callback("standard", function(spell)
    if target.ttd < 10000 then return end

    if not player:Buff(buffs.bulwark_of_righteous_fury) and player:TalentKnown(BulwarkofRighteousFury.id) and EnemiesInSpellRange(Rebuke) >= 3 then
        return spell:Cast(target)
    end
end)

--actions.standard+=/hammer_of_the_righteous,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3&!buff.avenging_wrath.up
--actions.standard+=/blessed_hammer,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3&!buff.avenging_wrath.up
--actions.standard+=/crusader_strike,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<2&!buff.avenging_wrath.up
CrusaderStrike:Callback("standard", function(spell)
    if not Rebuke:InRange(target) then return end
    if player:Buff(buffs.blessed_assurance) and EnemiesInSpellRange(Rebuke) < (2 + player:TalentKnownInt(BlessedHammer.id) + player:TalentKnownInt(HammeroftheRighteous.id)) and not player:Buff(buffs.avenging_wrath) then
        return spell:Cast(target)
    end
end)

--actions.standard+=/judgment,target_if=min:debuff.judgment.remains,if=charges>=2|full_recharge_time<=gcd.max -- This is literally already called first priority.

--actions.standard+=/consecration,if=buff.divine_guidance.stack=5
Consecration:Callback("standard", function(spell)
    if not player:Buff(buffs.consecration) and player:HasBuffCount(buffs.divine_guidance) == 5 and player.stayTime > 0.5 and Rebuke:InRange(target) and spell.used > 2000 then
        return spell:Cast()
    end
end)

--actions.standard+=/holy_armaments,if=next_armament=sacred_weapon&(!buff.sacred_weapon.up|(buff.sacred_weapon.remains<6&!buff.avenging_wrath.up&cooldown.avenging_wrath.remains<=30))
SacredWeapon:Callback("standard", function(spell)
    local weaponReady = C_Spell.GetSpellTexture(432459) == 5927637
    if not weaponReady then return end

    if (not player:Buff(buffs.sacred_weapon) or (player:BuffRemains(buffs.sacred_weapon) < 6000 and not player:Buff(buffs.avenging_wrath) and AvengingWrath.cd <= 30000)) then
        return spell:Cast()
    end
end)

--actions.standard+=/hammer_of_wrath
HammerofWrath:Callback("standard", function(spell)
    return spell:Cast(target)
end)


-- actions.standard+=/crusader_strike,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<2&!buff.avenging_wrath.up
HammerofLight:Callback("standard2", function(spell)
    if player:Buff(buffs.blessed_assurance) and EnemiesInSpellRange(Rebuke) < 2 and not player:Buff(buffs.avenging_wrath) then
        return spell:Cast(target)
    end
end)

--actions.standard+=/divine_toll,if=(!raid_event.adds.exists|raid_event.adds.in>10)
--DivineToll:Callback("standard", function(spell)
--    if not BossMods:HasAdds() or BossMods:AddsIn() > 10000 then
--        return spell:Cast()
--    end
--end)

--actions.standard+=/avengers_shield,if=talent.refining_fire.enabled
AvengersShield:Callback("standard2", function(spell)
    if target.ttd < 10000 then return end

    if player:TalentKnown(RefiningFire.id) then
        return spell:Cast(target)
    end
end)

--actions.standard+=/judgment,target_if=min:debuff.judgment.remains,if=(buff.avenging_wrath.up&talent.hammer_and_anvil.enabled)
Judgment:Callback("standard3", function(spell)
    if player:Buff(buffs.avenging_wrath) and player:TalentKnown(HammerandAnvil.id) then
        return spell:Cast(target)
    end
end)

--actions.standard+=/holy_armaments,if=next_armament=holy_bulwark&charges=2
HolyBulwark:Callback("standard", function(spell)
    local bulwarkReady = C_Spell.GetSpellTexture(432459) == 5927636
    if not bulwarkReady then return end

    if spell.frac >= 2 then
        return spell:Cast()
    end
end)

--actions.standard+=/judgment,target_if=min:debuff.judgment.remains
Judgment:Callback("standard4", function(spell)

    return spell:Cast(target)
end)

--actions.standard+=/avengers_shield,if=!buff.shake_the_heavens.up&talent.shake_the_heavens.enabled
AvengersShield:Callback("standard3", function(spell)
    if target.ttd < 10000 then return end

    if not player:Buff(buffs.shake_the_heavens) and player:TalentKnown(ShaketheHeavens.id) then
        return spell:Cast(target)
    end
end)

--actions.standard+=/hammer_of_the_righteous,if=(buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3)|buff.shake_the_heavens.up
--actions.standard+=/blessed_hammer,if=(buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3)|buff.shake_the_heavens.up
--actions.standard+=/crusader_strike,if=(buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<2)|buff.shake_the_heavens.up
CrusaderStrike:Callback("standard2", function(spell)
    if not Rebuke:InRange(target) then return end
    if player:Buff(buffs.blessed_assurance) and EnemiesInSpellRange(Rebuke) < 2 + player:TalentKnownInt(HammeroftheRighteous.id) + player:TalentKnownInt(BlessedHammer.id) then
        return spell:Cast(target)
    end

    if player:Buff(buffs.shake_the_heavens) then
        return spell:Cast(target)
    end
end)

--actions.standard+=/avengers_shield,if=!talent.lights_guidance.enabled
AvengersShield:Callback("standard4", function(spell)
    if not player:TalentKnown(LightsGuidance.id) then
        return spell:Cast(target)
    end
end)

--actions.standard+=/consecration,if=!consecration.up
Consecration:Callback("standard2", function(spell)
    if not player:Buff(buffs.consecration) and player.stayTime >= 0.5 and Rebuke:InRange(target) and Consecration.used > 2000 then
        return spell:Cast()
    end
end)

--actions.standard+=/eye_of_tyr,if=(talent.inmost_light.enabled&raid_event.adds.in>=45|spell_targets.shield_of_the_righteous>=3)&!talent.lights_deliverance.enabled
EyeofTyr:Callback("standard2", function(spell)
    if not shouldBurst() then return end

    if IsSpellOverlayed(HammerofLight.id) then return end
    if not Rebuke:InRange(target) then return end

    if (player:TalentKnown(InmostLight.id) or EnemiesInSpellRange(Rebuke) >= 3) and not player:TalentKnown(LightsDeliverance.id) then
        return spell:Cast()
    end
end)

--actions.standard+=/holy_armaments,if=next_armament=holy_bulwark
HolyBulwark:Callback("standard2", function(spell)
    local bulwarkReady = C_Spell.GetSpellTexture(432459) == 5927636
    if not bulwarkReady then return end

    return spell:Cast()
end)

--actions.standard+=/blessed_hammer
--actions.standard+=/hammer_of_the_righteous
--actions.standard+=/crusader_strike
CrusaderStrike:Callback("standard3", function(spell)
    if not Rebuke:InRange(target) then return end
    return spell:Cast(target)
end)

--actions.standard+=/word_of_glory,if=buff.shining_light_free.up&(talent.blessed_assurance.enabled|(talent.lights_guidance.enabled&cooldown.hammerfall_icd.remains=0))
WordofGlory:Callback("standard", function(spell)
    if player:Buff(buffs.shining_light_free) and (player:TalentKnown(BlessedAssurance.id) or (player:TalentKnown(LightsGuidance.id) and ShieldoftheRighteous.used > 1000 and spell.used > 1000)) then
        return spell:Cast()
    end
end)

--actions.standard+=/avengers_shield
AvengersShield:Callback("standard5", function(spell)
    if target.ttd < 10000 then return end

    return spell:Cast(target)
end)

--actions.standard+=/eye_of_tyr,if=!talent.lights_deliverance.enabled
EyeofTyr:Callback("standard3", function(spell)
    if not shouldBurst() then return end

    if IsSpellOverlayed(HammerofLight.id) then return end
    if not Rebuke:InRange(target) then return end

    if not player:TalentKnown(LightsDeliverance.id) then
        return spell:Cast()
    end
end)

--actions.standard+=/word_of_glory,if=buff.shining_light_free.up
WordofGlory:Callback("standard2", function(spell)
    if player:Buff(buffs.shining_light_free) then
        return spell:Cast()
    end
end)

-- actions.standard+=/arcane_torrent,if=holy_power<5
ArcaneTorrent:Callback("standard", function(spell)
    if player.holyPower < 5 then
        return spell:Cast()
    end
end)

AvengersShield:Callback("oncd", function(spell)
    if A.GetToggle(2, "asonCD") then
        return spell:Cast(target)
    end
end)

EyeofTyr:Callback("templar", function(spell)
    if player:TalentKnown(425518) and ShieldofRigteous:InRange(target) then
        return spell:Cast(player)
    end
end)

HammerofLight:Callback("templar", function(spell)
    if player:TalentKnown(427453) and ShieldofRigteous:InRange(target) then
        return spell:Cast(player)
    end
end)

-- actions+=/call_action_list,name=standard
local function standard_rot()
    AvengersShield("oncd")
    Judgment("standard")
    HammerofLight("standard")
    EyeofTyr("standard")
    ShieldoftheRighteous("standard")
    Judgment("standard2")
    AvengersShield("standard")
    CrusaderStrike("standard")
    Consecration("standard")
    SacredWeapon("standard")
    HammerofWrath("standard")
    HammerofLight("standard2")
    AvengersShield("standard2")
    Judgment("standard3")
    HolyBulwark("standard")
    Judgment("standard4")
    AvengersShield("standard3")
    CrusaderStrike("standard2")
    AvengersShield("standard4")
    Consecration("standard2")
    EyeofTyr("standard2")
    HolyBulwark("standard2")
    CrusaderStrike("standard3")
    WordofGlory("standard")
    AvengersShield("standard5")
    EyeofTyr("standard3")
    WordofGlory("standard2")
    ArcaneTorrent("standard")
end

Intercession:Callback(function(spell)
    if not A.GetToggle(2, "mouseoverRes") then return end
    if not player.combat then return end
    if not mouseover.exists then return end
    if not mouseover.isFriendly then return end
    if not mouseover.dead then return end
    if not spell:InRange(mouseover) then return end
    
    return spell:Cast()
end)

Redemption:Callback(function(spell)
    if not A.GetToggle(2, "mouseoverRes") then return end
    if player.combat then return end
    if not mouseover.exists then return end
    if not mouseover.isFriendly then return end
    if not mouseover.dead then return end
    if not spell:InRange(mouseover) then return end

    return spell:Cast()
end)

HandofReckoning:Callback(function(spell)
    local noAggro = UnitThreatSituation("player", target:CallerId())
    if noAggro == 0 or noAggro == 2 then
        return spell:Cast(target)
    end
end)

CleanseToxins:Callback("self", function(spell)
    if player:Poisoned() or player:Diseased() then
        return spell:Cast()
    end
end)

A[3] = function(icon)
	FrameworkStart(icon)
    updateGameState()

    makInterrupt(interrupts)

    Intercession()
    Redemption()

    if player.inCombat and MakuluFramework.TankDefensive() then
        if Trinket(13, "Defensive") then
            Trinket1()
        end
        if Trinket(14, "Defensive") then
            Trinket2()
        end
    end

    --Calling this in A[3] will cause hangs when Blizzard UI breaks item cooldowns. 
   --[[if player.inCombat and player.hp < 50 then
        if MakuluFramework.CanUseHealthStone() then
            HealthStone()
        end

        if MakuluFramework.CanUseHealthPotion() then
            HealthPotion()
        end
    end]]

    if player.inCombat then -- Def
        Fireblood("bleed")
        CleanseToxins("self")
        defensives_rot()
    end

    if target.exists and target.canAttack then
        HandofReckoning()
        if shouldBurst() then
            cooldown_rot()
        end
        standard_rot()
    end

	return FrameworkEnd()
end

LayonHands:Callback("partyrot", function(spell, pmember)
    if pmember:Debuff(debuffs.forbearance) then return end

    local lohphp = A.GetToggle(2, "PartyLoHHP")
    if lohphp == 0 then return end
    if pmember.hp < lohphp then
        return spell:Cast(pmember)
    end
end)

BlessingofProtection:Callback("partyrot", function(spell, pmember)
    if pmember:Debuff(debuffs.forbearance) then return end

    local bopphp = A.GetToggle(2, "PartyBoPHP")
    if bopphp == 0 then return end
    if pmember.hp < bopphp then
        return spell:Cast(pmember)
    end
end)

WordofGlory:Callback("partyrotfree", function(spell, pmember)
    local wogpfhp = A.GetToggle(2, "PartyWoGHPFree")
    if wogpfhp == 0 then return end
    if player:Buff(buffs.shining_light_free) and pmember.hp < wogpfhp then
        return spell:Cast(pmember)
    end
end)

WordofGlory:Callback("partyrot", function(spell, pmember)
    local wogphp = A.GetToggle(2, "PartyWoGHP")
    if wogphp == 0 then return end
    if pmember.hp < wogphp then
        return spell:Cast(pmember)
    end
end)

CleanseToxins:Callback("partyrot", function(spell, pmember)
    if pmember:Poisoned() or pmember:Diseased() then
        return spell:Cast(pmember)
    end
end)

local enemyRotation = function(enemy)
	if not enemy.exists then return end
    if enemy.hp <= 0 then return end
    if player.mounted then return end
    if player.stealthed then return end

end


local partyRotation = function(friendly)
    if not friendly.exists then return end
    if friendly.hp <= 0 then return end
    if player.mounted then return end
    if player.stealthed then return end
    if IsResting() then return end

    LayonHands("partyrot", friendly)
    BlessingofProtection("partyrot", friendly)
    WordofGlory("partyrotfree", friendly)
    WordofGlory("partyrot", friendly)
    CleanseToxins("partyrot", friendly)
end

A[6] = function(icon)
	RegisterIcon(icon)
    if targetForInterrupt(interrupts) then
        return TabTarget()
    end
    if AutoTarget() then
        return TabTarget()
    end
	enemyRotation(arena1)
	partyRotation(party1)

	return FrameworkEnd()
end

A[7] = function(icon)
	RegisterIcon(icon)
	enemyRotation(arena2)
	partyRotation(party2)

	return FrameworkEnd()
end

A[8] = function(icon)
	RegisterIcon(icon)
	enemyRotation(arena3)
	partyRotation(party3)

	return FrameworkEnd()
end

A[9] = function(icon)
	RegisterIcon(icon)
	partyRotation(party4)

	return FrameworkEnd()
end

A[10] = function(icon)
	RegisterIcon(icon)
	partyRotation(player)

	return FrameworkEnd()
end