local TMW                                                                = _G.TMW
local CNDT                                                               = TMW.CNDT
local Env                                                                = CNDT.Env
local Action                                                             = Action
local Listener                                                           = Action.Listener
local GetToggle                                                          = Action.GetToggle
local SetToggle                                                          = Action.SetToggle
local GetGCD                                                             = Action.GetGCD
local TeamCache                                                          = Action.TeamCache
local Player                                                             = Action.Player
local MultiUnits                                                         = Action.MultiUnits
local Unit                                                               = Action.Unit
local IsUnitEnemy                                                        = Action.IsUnitEnemy
local IsUnitFriendly                                                     = Action.IsUnitFriendly
local HealingEngine                                                      = Action.HealingEngine
local ActiveUnitPlates                                                   = MultiUnits:GetActiveUnitPlates()
local AuraIsValid                                                        = Action.AuraIsValid
local TeamCacheFriendly                                                  = TeamCache.Friendly
local TeamCacheFriendlyIndexToPLAYERs                                    = TeamCacheFriendly.IndexToPLAYERs
local IsIndoors, UnitIsUnit                                              = IsIndoors, UnitIsUnit
local next, pairs, type, print                                           = next, pairs, type, print
local math_floor                                                         = math.floor
local math_ceil                                                          = math.ceil
local tinsert                                                            = table.insert
local select, table                                                      = select, table
local CombatLogGetCurrentEventInfo                                       = _G.CombatLogGetCurrentEventInfo
local UnitGUID, UnitIsUnit, UnitDamage, UnitAttackSpeed, UnitAttackPower = UnitGUID, UnitIsUnit, UnitDamage,
    UnitAttackSpeed, UnitAttackPower
local _G, setmetatable, select, math                                     = _G, setmetatable, select, math
local UnitAura                                                           = _G.UnitAura
local UnitIsPlayer                                                       = _G.UnitIsPlayer
local GetSpellCooldown                                                   = _G.GetSpellCooldown
local HasPetUI                                                           = _G.HasPetUI
local GetTime                                                            = _G.GetTime
local IsUsableSpell                                                      = IsUsableSpell
local IsMounted                                                          = _G.IsMounted
local UnitPowerType                                                      = UnitPowerType
local onGCD                                                              = TMW.OnGCD

local CONST                                                              = Action.Const
local Create                                                             = Action.Create

local ACTION_CONST_SHAMAN_RESTORATION                                      = CONST.SHAMAN_RESTORATION
local ACTION_CONST_AUTOTARGET             = CONST.AUTOTARGET
local ACTION_CONST_STOPCAST               = CONST.STOPCAST
local ActiveUnitPlates 					  = MultiUnits:GetActiveUnitPlates()
local getmembersAll 					  = HealingEngine.GetMembersAll()

Action[ACTION_CONST_SHAMAN_RESTORATION] = {
    -- Racial
    ArcaneTorrent              = Create({ Type = "Spell", ID = 50613 }),
    ArcanePulse                = Create({ Type = "Spell", ID = 260364 }),
    QuakingPalm                = Create({ Type = "Spell", ID = 107079 }),
    Darkflight                 = Create({ Type = "Spell", ID = 68992 }),
    Haymaker                   = Create({ Type = "Spell", ID = 287712 }),
    WarStomp                   = Create({ Type = "Spell", ID = 20549 }),
    BullRush                   = Create({ Type = "Spell", ID = 255654 }),
    GiftofNaaru                = Create({ Type = "Spell", ID = 59544 }),
    Shadowmeld                 = Create({ Type = "Spell", ID = 58984 }), -- usable in Action Core
    Stoneform                  = Create({ Type = "Spell", ID = 20594 }),
    WilloftheForsaken          = Create({ Type = "Spell", ID = 7744 }),  -- not usable in APL but user can Queue it
    EscapeArtist               = Create({ Type = "Spell", ID = 20589 }), -- not usable in APL but user can Queue it
    EveryManforHimself         = Create({ Type = "Spell", ID = 59752 }), -- not usable in APL but user can Queue it
    BagofTricks                = Create({ Type = "Spell", ID = 312411 }),


    -- Shaman Primary Spells
    AncestralGuidiance         = Create({ Type = "Spell", ID = 108281 }),
    AncestralSpirit            = Create({ Type = "Spell", ID = 2008 }),
    AstralRecall               = Create({ Type = "Spell", ID = 556 }),
    AstralShift                = Create({ Type = "Spell", ID = 108271 }),
    Bloodlust                  = Create({ Type = "Spell", ID = 2825 }),
    CapacitorTotem             = Create({ Type = "Spell", ID = 192058 }),
    ChainHeal                  = Create({ Type = "Spell", ID = 1064 }),
    ChainLightning             = Create({ Type = "Spell", ID = 188443 }),
    EarthElemental             = Create({ Type = "Spell", ID = 198103 }),
    EarthShield                = Create({ Type = "Spell", ID = 974 }),
    ElementalOrbit             = Create({ Type = "Spell", ID = 383010, Hidden = true }),
    EarthgrabTotem             = Create({ Type = "Spell", ID = 51485 }),
    FlameShock                 = Create({ Type = "Spell", ID = 188389 }),
    FlametongueWeapon          = Create({ Type = "Spell", ID = 318038 }),
    FrostShock                 = Create({ Type = "Spell", ID = 196840 }),
    GhostWolf                  = Create({ Type = "Spell", ID = 2645 }),
    GreaterPurge               = Create({ Type = "Spell", ID = 378773 }),
    HealingStreamTotem         = Create({ Type = "Spell", ID = 5394 }),
    HealingSurge               = Create({ Type = "Spell", ID = 8004 }),
    Hex                        = Create({ Type = "Spell", ID = 51514 }),
    ImprovedPurifySpirit       = Create({ Type = "Spell", ID = 383016, Hidden = true }),
    LavaBurst                  = Create({ Type = "Spell", ID = 51505 }),
    LightningBolt              = Create({ Type = "Spell", ID = 188196 }),
    LightningShield            = Create({ Type = "Spell", ID = 192106 }),
    LightningLasso             = Create({ Type = "Spell", ID = 305483 }),
    ManaSpringTotem            = Create({ Type = "Spell", ID = 381930 }),
    NaturesSwiftness           = Create({ Type = "Spell", ID = 378081 }),
    PoisonCleansingTotem       = Create({ Type = "Spell", ID = 383013, Hidden = true }),
    PrimalStrike               = Create({ Type = "Spell", ID = 73899 }),
    Purge                      = Create({ Type = "Spell", ID = 370 }),
    SpiritwalkersGrace         = Create({ Type = "Spell", ID = 79206 }),
    Spiritwalk                 = Create({ Type = "Spell", ID = 58875 }),
    StoneskinTotem             = Create({ Type = "Spell", ID = 383017 }),
    TremorTotem                = Create({ Type = "Spell", ID = 8143 }),
    TotemicRecall              = Create({ Type = "Spell", ID = 108285 }),
    WindShear                  = Create({ Type = "Spell", ID = 57994 }),

    -- Restoration Shaman Specialization Spells
    AcidRain                   = Create({ Type = "Spell", ID = 378443, Hidden = true }),
    Ascendance                 = Create({ Type = "Spell", ID = 114052 }),
    AncestralVision            = Create({ Type = "Spell", ID = 212048 }),
    CloudburstTotem            = Create({ Type = "Spell", ID = 157153 }),
    CloudburstTotemBuff        = Create({ Type = "Spell", ID = 157504 }),
    Downpour                   = Create({ Type = "Spell", ID = 207778 }),
    EarthenWallTotem           = Create({ Type = "Spell", ID = 198838 }),
    EarthlivingWeapon          = Create({ Type = "Spell", ID = 382021 }),
    EverRisingTide			   = Create({ Type = "Spell", ID = 382029 }),
    HealingRain                = Create({ Type = "Spell", ID = 73920 }),
    HealingTideTotem           = Create({ Type = "Spell", ID = 108280 }),
    HealingWave                = Create({ Type = "Spell", ID = 77472 }),
    HighTide                   = Create({ Type = "Spell", ID = 157154 }),
    ManaTideTotem              = Create({ Type = "Spell", ID = 16191 }),
    PrimordialWave             = Create({ Type = "Spell", ID = 375982 }),
    PurifySpirit               = Create({ Type = "Spell", ID = 77130 }),
	Riptide                    = Create({ Type = "Spell", ID = 61295 }),
    SpiritLinkTotem            = Create({ Type = "Spell", ID = 98008 }),
    Stormkeeper                = Create({ Type = "Spell", ID = 383009 }),
    UnleashLife                = Create({ Type = "Spell", ID = 73685 }),
    WaterShield                = Create({ Type = "Spell", ID = 52127 }),
    Wellspring                 = Create({ Type = "Spell", ID = 197995 }),

    -- Restoration Shaman PVP Spells
    CounterStrikeTotem         = Create({ Type = "Spell", ID = 204331 }),
    GroundingTotem             = Create({ Type = "Spell", ID = 204336 }),
    SkyFuryTotem		       = Create({ Type = "Spell", ID = 204330 }),
    StaticFieldTotem           = Create({ Type = "Spell", ID = 355580 }),
    UnleashShield              = Create({ Type = "Spell", ID = 356736 }),

    -- Restoration Shaman Buffs and Debuff IDs
    HighTideBuff               = Create({ Type = "Spell", ID = 288675, Hidden = true }),
    LavaSurgeBuff              = Create({ Type = "Spell", ID = 77762, Hidden = true }),
    PrimordialWaveBuff         = Create({ Type = "Spell", ID = 375986, Hidden = true }),
    QuakingDebuff              = Create({ Type = "Spell", ID = 240447, Hidden = true }),
    TidalWavesBuff             = Create({ Type = "Spell", ID = 53390, Hidden = true	}),
}

local DangerousClassList                                                 = {
    ["MAGE"]  = true,
    ["DRUID"] = true,
}
local GroundableKickList                                                 = {
    [118] = true,    -- Poly
    [605] = true,    -- Mind control
    [5782] = true,   -- Fear
    [20066] = true,  -- Repentance
    [33786] = true,  -- Cyclone
    [51514] = true,  -- Hex
    [116858] = true, -- Chaos bolt
    [199786] = true, -- Glacial spike
    [204437] = true, -- Lasso
    [323673] = true, -- Mindgames
    [375901] = true, -- Mindgames
    [360806] = true, -- Sleep walk
}

local KickingHealList                                                    = {
    [1064] = true,   -- chain heal
    [2061] = true,   -- Flash heal,
    [8004] = true,   -- Healing surge
    [8936] = true,   -- Regrowth
    [19750] = true,  -- Flash heal
    [32375] = true,  -- Mass dispel
    [47540] = true,  -- Penance
    [48438] = true,  -- Wild Growth
    [64843] = true,  -- Divine hymn
    [77472] = true,  -- Healing wave
    [82326] = true,  -- Holy light
    [115175] = true, -- Soothing Mist
    [116670] = true, -- Vivify
    [120517] = true, -- Halo
    [124682] = true, -- Enveloping
    [191837] = true, -- Essense font
    [194509] = true, -- Radiance
    [200652] = true, -- Tyr's
    [234153] = true, -- Drain life
    [289666] = true, -- Greater heal
    [202771] = true, -- Full Moon
    [274283] = true, -- Other full moon?
    [316009] = true, -- UA
    [323701] = true, -- Mindagames
}

local TremorList                                                         = {
    [5782] = true,   -- Fear
    [360806] = true, -- Sleep walk
}

local CastingKnockList                                                   = {
    [19434] = true,  -- Aimed shot
    [113656] = true, -- Fists of Fury
    [117418] = true, -- Fists of Fury
    [198013] = true, -- Eye beam
}

local PurgeList                                                          = {
    ["Power Infusion"] = true,
    ["Blessing of Protection"] = true,
    ["Combustion"] = true,
    ["Icy Veins"] = true,
    ["Nullifying Shroud"] = true,
    ["Thorns"] = true,
    ["Alter Time"] = true,
    ["Spiritwalkers Grace"] = true,
}

local player                                                             = "player"
local s                                                                  = {

    StormKeeper                = "Stormkeeper",
    LavaSurge                  = "Lava Surge",
    Ascendance                 = "Ascendance",
    PrimordialWave             = "Primordial Wave",


    EarthShieldStr             = "Earth Shield",
    LightningShieldStr         = "Lightning Shield",
    GhostWolfStr               = "Ghost Wolf",

    player                     = "player",
    targetString               = "target",
    focusString                = "focus",
    arenaString                = "arena",

    arenaFrameName             = "JackShamanArenaFrame",
    arenaPrepString            = "Arena Preparation",
    channelingString           = "Channeling",

    party1Text                 = "party1",
    party2Text                 = "party2",

    arena1Text                 = "arena1",
    arena2Text                 = "arena2",
    arena3Text                 = "arena3",
}

local arenas                                                             = { s.arena1Text, s.arena2Text, s.arena3Text }

-------------------------------
-------------------------------
-- Talents known info
-------------------------------
-------------------------------

local function buildSpellNameLookup()
    for _, v in pairs(Action[ACTION_CONST_SHAMAN_RESTORATION]) do
        local name = GetSpellInfo(v.ID)
        v.SPELL_NAME = name
    end
end

buildSpellNameLookup()

-- To create covenant use next code:
Action:CreateEssencesFor(ACTION_CONST_SHAMAN_RESTORATION)

local function getCooldownIncGCD(spellId, noGCD)
    local start, duration = GetSpellCooldown(spellId)

    if duration and duration ~= 0 then
        return (duration - (TMW.time - start)) * 1000
    end

    return 0
end

local function getCooldown(spellId, noGCD)
    local start, duration = GetSpellCooldown(spellId)

    if duration and duration ~= 0 then
        return (onGCD(duration) and 0) or (duration - (TMW.time - start)) * 1000
    end

    return 0
end
local A                          = setmetatable(Action[ACTION_CONST_SHAMAN_ELEMENTAL], { __index = Action })

local acidRainKnown              = A.AcidRain:IsTalentLearned()
local highTideKnown              = A.HighTide:IsTalentLearned()
local earthLivingWeaponKnown     = A.EarthlivingWeapon:IsTalentLearned()
local improvedPurifySpiritKnown  = A.ImprovedPurifySpirit:IsTalentLearned()
local poisonCleaningTotemKnown   = A.PoisonCleansingTotem:IsTalentLearned()
local elementalOrbitKnown        = A.ElementalOrbit:IsTalentLearned()


TMW:RegisterCallback("TMW_ACTION_HEALINGENGINE_UNIT_UPDATE", function(callbackEvent, thisUnit, db, QueueOrder)
    local unitID, Role = thisUnit.Unit, thisUnit.Role
    
    if A.PurifySpirit:IsReady(unitID) and thisUnit.useDispel and not QueueOrder.useDispel[Role] and AuraIsValid(unitID, "UseDispel", "Dispel") then
        QueueOrder.useDispel[Role] = true
        
        local offsetMap = {
            SELF = db.OffsetSelfDispel,
            HEALER = db.OffsetHealersDispel,
            TANK = db.OffsetTanksDispel,
            DEFAULT = db.OffsetDamagersDispel
        }
        
        local offset = offsetMap[thisUnit.isSelf and "SELF" or Role] or offsetMap.DEFAULT
        thisUnit:SetupOffsets(offset, thisUnit.isSelf and 15 or Role == "HEALER" and 25 or 20)
        return
    end
end)

A[1] = function(icon)

end

A[2] = function(icon)

end

--################################################################################################################################################################################################################

A[3] = function(icon)
    local inCombat = Unit(player):CombatTime() > 0
    local isMoving = Player:IsMoving()
    local isStaying = A.Player:IsStayingTime() > 0.3
    local isMovingFor = A.Player:IsMovingTime()
	local combatTime = Unit(player):CombatTime()
	local getmembersAll = HealingEngine.GetMembersAll()
    local rangeUnitCount = MultiUnits:GetByRange(40)
	local pullTTD = MultiUnits.GetByRangeAreaTTD(40)
    local hasGrace = Unit(player):HasBuffs(A.SpiritwalkersGrace.ID) > 0
    local quakingDebuff = Unit(player):HasDeBuffs(A.QuakingDebuff.ID)
    
    local function teamHPcheck(threshhold)
        return HealingEngine.GetBelowHealthPercentUnits(threshhold)
    end

    local function countPartyWithRiptide()
        local count = 0
        local partyMembers = {"party1", "party2", "party3", "party4", "player"}
    
        for _, unitID in ipairs(partyMembers) do
            if UnitExists(unitID) and Unit(unitID):HasBuffs(A.Riptide.ID, true) > 0 then
                count = count + 1
            end
        end
    
        return count
    end
    
    local function pveCleanse(unitID)
        local useDispel = HealingEngine.GetOptionsByUnitID(unitID)
        if A.PurifySpirit:IsReady(unitID) and useDispel and AuraIsValid(unitID, "UseDispel", "Dispel") then
            return A.PurifySpirit
        end
    end
    
    local function pvePurge(unitID)
        if (AuraIsValid(unitID, "UsePurge", "PurgeHigh") or AuraIsValid(unitID, "UsePurge", "PurgeLow")) and (A.Purge:IsReady(unitID) or A.GreaterPurge:IsReady(unitID)) then
            return A.Purge:IsReady(unitID) and A.Purge or A.GreaterPurge
        end
    end
    
    local function pveKick(unit)
        local name, _, _, startTime, _, _, _, notInterruptible = UnitCastingInfo(unit)
        if not name then
            name, _, _, startTime, _, _, notInterruptible = UnitChannelInfo(unit)
        end
    
        if name and not notInterruptible then
            local currentTime = GetTime() * 1000
            local elapsedTime = (currentTime - startTime) / 1000
            if elapsedTime >= 0.5 and A.WindShear:IsReady(unit) then
                return A.WindShear
            end
        end
    end

    local function pveHex()
        local mobName = select(1, UnitName("mouseover"))
        if mobName == "Incorporeal Being" and A.Hex:IsReady("mouseover") then
            return A.Hex:Show(icon)
        end
    end

    local function pveCleanseAfflicted()
        local mobName = select(1, UnitName("mouseover"))
        if mobName == "Afflicted Soul" and improvedPurifySpiritKnown and A.PurifySpirit:IsReady("mouseover") then
            return A.CleanseSpirit:Show(icon)
        end
    end
    
    -- PVE Utility
    if teamHPcheck(50) == 0 then
        local kickAction = newPveKick("target")
        if kickAction then 
            return kickAction:Show(icon)
        end

        local pveKick = pveKick()
        if pveKick then
            return pveKick:Show(icon)
        end
        
        local pveHex = pveHex()
        if pveHex then 
            return pveHex:Show(icon)
        end

        local pveCleanse = pveCleanse()
        if pveCleanse then
            return pveCleanse:Show(icon)
        end

        local pveCleanseAfflicted = pveCleanseAfflicted()
        if pveCleanseAfflicted then
            return pveCleanseAfflicted:Show(icon)
        end

        local pvePurge = pvePurge()
        if pvePurge then
            return pvePurge:Show(icon)
        end

    end

    -- Self Defensives
    if A.AncestrialGuidance:IsReady() and Unit("player"):HealthPercent() <= 70 and teamHPcheck(50) == 0 then
        return A.AncestrialGuidance:Show(icon)
    end

    if A.AstralShift:IsReady() and Unit("player"):HealthPercent() <= 50 then
        return A.AstralShift:Show(icon)
    end

    -- Quaking Stop Casting
    if quakingDebuff > 0 and Unit(player):IsCastingRemains() > quakingDebuff + 0.5 then
        return A:Show(icon, ACTION_CONST_STOPCAST)
    end

    -- OOC Things
    -- Enchant Weapon
    if not inCombat and teamHPcheck(70) == 0 then
        if earthLivingWeaponKnown then
            if A.EarthlivingWeapon:IsReady(player) and Unit(player):HasBuffs(A.EarthlivingWeapon.ID) == 0 then
                return A.EarthlivingWeapon:Show(icon)
            end
        else
            if A.FlametongueWeapon:IsReady(player) and Unit(player):HasBuffs(A.FlametongueWeapon.ID) == 0 then
                return A.FlametongueWeapon:Show(icon)
            end
        end
    end

    -- M+ Healing Rotation
    local function mpHealingRotation(unit)

        local partySize = GetNumGroupMembers()

        -- OOC Stuff
        if not inCombat then
            if A.AncestralVision:IsReady(unit) and UnitIsDeadOrGhost(unit) then
                return A.AncestralVision:Show(icon)
            end

            if A.WaterShield:IsReady(player) and Unit(player):HasBuffs(A.WaterShield.ID) == 0 then
                return A.WaterShield:Show(icon)
            end

            local esTargets = {"party1", "party2", "party3", "party4"}

            for _, target in ipairs(esTargets) do
                if (Unit(target):IsTank()) and Unit(target):HasBuffs(A.EarthShield.ID, true) <= 1 then
                    HealingEngine.SetTarget(target, 1)
                    return A.EarthShield:Show(icon)
                end
            end

            if A.EarthShield:IsReady(player) and Unit(player):HasBuffs(A.EarthShield.ID) == 0 and (partySize == 1 or elementalOrbitKnown)  then
                HealingEngine.SetTarget(player, 1)
                return A.EarthShield:Show(icon)
            end

            if A.HealingSurge:IsReady(unit) and Unit(unit):HealthPercent() <= 90 then
                return A.HealingSurge:Show(icon)
            end
        end

        if A.EarthlivingWeapon:IsReady() and teamHPcheck(95) == 0 and Unit(player):HasBuffs(A.EarthlivingWeapon.ID) == 0 then
            return A.EarthlivingWeapon:Show(icon)
        end

        if A.WaterShield:IsReady(player) and teamHPcheck(95) == 0 and Unit(player):HasBuffs(A.WaterShield.ID) == 0 then
            return A.WaterShield:Show(icon)
        end

        if A.EarthShield:IsReady(unit) and teamHPcheck(80) == 0 and Unit(unit):IsTank() and Unit(unit):HasBuffs(A.EarthShield.ID, true) <= 1 then
            return A.EarthShield:Show(icon)
        end

        if A.ManaTideTotem:IsReady() and Unit(player):ManaPercentage() < 70 and teamHPcheck(65) == 0 then 
            return A.ManaTideTotem:Show(icon) 
        end

        if A.SpiritLinkTotem:IsReady() and Unit(unit):GetRange() <= 40 and teamHPcheck(40) >= 2 then
            return A.SpiritLinkTotem:Show(icon)
        end

        if A.Ascendance:IsReady() and teamHPcheck(85) >= 2 and Unit(unit):GetRange() <= 35 and Unit(unit):HealthPercent() <= 50 then
            return A.Ascendance:Show(icon)
        end

        if A.HealingTideTotem:IsReady() and teamHPcheck(50) >= 3 and Unit(unit):GetRange() <= 40 then
            return A.HealingTideTotem:Show(icon) 
        end

        if A.CloudburstTotem:IsReady() and teamHPcheck(65) == 0 and Unit(unit):HealthPercent() >= 95 then
            return A.Darkflight:Show(icon)
        end

        if A.HealingStreamTotem:IsReady() and teamHPcheck(65) == 0 and Unit(unit):HealthPercent() >= 95 then
            return A.HealingStreamTotem:Show(icon)
        end

        if A.ChainHeal:IsReady(unit) and teamHPcheck(85) >= 3 and Unit(unit):GetRange() <= 40 then
            return A.ChainHeal:Show(icon)
        end

        if A.Riptide:IsReady(unit) and Unit(unit):HealthPercent() <= 95 and Unit(unit):HasBuffs(A.Riptide.ID) == 0 then
            return A.Riptide:Show(icon)
        end

        if A.PrimordialWave:IsReady(unit) and Uunit(unit):HasBuffs(A.Riptide.ID) == 0 and Unit(unit):HealthPercent() <= 95 then
            return A.PrimordialWave:Show(icon)
        end

		if A.HealingRain:IsReady() and isStaying and teamHPcheck(75) == 0 and countPartyWithRiptide() >= 1 then
            return A.HealingRain:Show(icon) 
        end

        if A.HealingWave:IsReady(unit) and Unit(player):HasBuffs(A.PrimordialWaveBuff.ID) and Unit(unit):HealthPercent() <= 85 and Unit(unit):HasBuffs(A.Riptide.ID) then
            return A.HealingWave:Show(icon)
        end

        if A.HealingSurge:IsReady(unit) and Unit(unit):HealthPercent() <= 75 then
            return A.HealingSurge:Show(icon)
        end

        if A.StormKeeper:IsReady() and rangeUnitCount >= 2 and Unit(s.targetString):GetRange() <= 40 then
            return A.StormKeeper:Show(icon)
        end

        if A.ChainLightning:IsReady(s.targetString) and rangeUnitCount >= 2 then
            return A.ChainLightning:Show(icon)
        end

        if A.FlameShock:IsReady(s.targetString) and Unit(s.targetString):HasDeBuffs(A.FlameShock.ID) == 0 then
            return A.FlameShock:Show(icon)
        end

        if A.LavaBurst:IsReady(s.targetString) then
            return A.LavaBurst:Show(icon)
        end

        if A.LightningBolt:IsReady(s.targetString) then
            return A.LightningBolt:Show(icon)
        end

    end

    -- Raid Healing Rotation
    local function raidHealingRotation(unit)
        -- Implementation here
    end

    -- PVP Healing Rotation
    local function pvpHealingRotation(unit)
        -- Implementation here
    end

    -- Rotation Calls
    local function selectRotation(unit)
        if A.Zone == "arena" then
            return pvpHealingRotation(unit)
        elseif A.Zone ~= "arena" then
            local partySize = GetNumGroupMembers()
            
            if partySize <= 5 then
                return mpHealingRotation(unit)
            elseif partySize > 5 then
                return raidHealingRotation(unit)
            end
        end
    end

    if A.IsUnitFriendly(s.targetString) then
        local unit = s.targetString
        if selectRotation(unit) then return true end
    elseif A.IsUnitFriendly(s.focusString) then
        local unit = s.focusString
        if selectRotation(unitID) then return true end
    end

end


local function pvpPartyRotation(icon, unitID)

end 

local function pvpArenaRotation(icon, unitID)

end



A[6] = function(icon)

end



A[7] = function(icon)

end



A[8] = function(icon)

end

-- [1] is AntiFake CC rotation (limited, usually is single color like 0x00FF00 which is green)
-- [2] is AntiFake Kick rotation (racial, primary specialization interrupt spell)
-- [3] is Rotation (old launcher called it Single, supports all actions)
-- [4] is Secondary (old launcher called it AoE) rotation (supports all actions)
-- [5] is Trinket rotation (racial, specialization's spells which can remove CC)
-- [6] is Passive rotation (limited actions, usually @raid1, @party1, @arena1 and additional binds - for more info look notes in the launcher)
-- [7] is Passive rotation (limited actions, usually @raid2, @party2, @arena2)
-- [8] is Passive rotation (limited actions, usually @raid3, @party3, @arena3)
--Passive rotation doesn't require START button use like it does [1] -> [5] rotations 
