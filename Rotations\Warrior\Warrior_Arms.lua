-- APL UPDATE 031825

if not MakuluValidCheck() then return true end
if not Makulu_magic_number == 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 71 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local MakMulti         = MakuluFramework.MultiUnits
local TableToLocal     = MakuluFramework.tableToLocal
local MakGcd           = MakuluFramework.gcd
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local ConstSpells      = MakuluFramework.constantSpells
local PVE              = MakuluFramework.PVE
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket

local _G, setmetatable = _G, setmetatable

local ActionID       = {
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    RocketJump = { ID = 69070 },
    RocketBarrage = { ID = 69041 },
    QuakingPalm = { ID = 107079 },
    SpatialRift = { ID = 256948 },
    LightsJudgment = { ID = 255647 },
    Fireblood = { ID = 265221 },
    ArcanePulse = { ID = 260364 },
    BullRush = { ID = 255654 },
    AncestralCall = { ID = 274738 },
    Haymaker = { ID = 287712 },
    Regeneratin = { ID = 291944 },
    BagOfTricks = { ID = 312411 }, 
    HyperOrganicLightOriginator = { ID = 312924 },

    Disarm = { ID = 236077, MAKULU_INFO = { damageType = "physical" } },
    Charge = { ID = 100, },
    Slam = { ID = 1464, MAKULU_INFO = { damageType = "physical" } },
    Whirlwind = { ID = 1680, MAKULU_INFO = { damageType = "physical" } },
    Hamstring = { ID = 1715, MAKULU_INFO = { damageType = "physical" } },
    Execute = { ID = 5308, MAKULU_INFO = { damageType = "physical" } },
    Pummel = { ID = 6552, MAKULU_INFO = { damageType = "physical" } },
    BattleShout = { ID = 6673 },
    VictoryRush = { ID = 34428, MAKULU_INFO = { damageType = "physical" } },
    HeroicThrow = { ID = 57755, MAKULU_INFO = { damageType = "physical" } },
    Rend = { ID = 772, MAKULU_INFO = { damageType = "physical" } },
    Cleave = { ID = 845, MAKULU_INFO = { damageType = "physical" } },
    DemoralizingShout = { ID = 1160 },
    ChallengingShout = { ID = 1161 },

	AntiFakeKick = { Type = "SpellSingleColor", ID = 6552,  Hidden = true,		Color = "GREEN"	    , Desc = "[2] AntiFakeKick",    QueueForbidden = true	},
	AntiFakeCC	 = { Type = "SpellSingleColor", ID = 107570,  	Hidden = true,		Color = "YELLOW"	, Desc = "[1] AntiFakeCC",      QueueForbidden = true	},

    Intervene = { ID = 3411 },
    IntimidatingShout = { ID = 5246, Texture = 355, MAKULU_INFO = { damageType = "physical" } },
    IntimidatingShoutPvE = { ID = 5246, Hidden = true, MAKULU_INFO = { damageType = "physical" } },
    ThunderClap = { ID = 6343, MAKULU_INFO = { damageType = "physical" } },
    HeroicLeap = { ID = 6544 },
    Overpower = { ID = 7384, MAKULU_INFO = { damageType = "physical" } },
    MortalStrike = { ID = 12294, MAKULU_INFO = { damageType = "physical" } },
    ImprovedWhirlwind = { ID = 12950 },
    LastStand = { ID = 12975 },
    BerserkerRage = { ID = 18499 },
    SpellReflection = { ID = 23920 },
    SuddenDeath = { ID = 29725, MAKULU_INFO = { damageType = "physical" } },
    SuddenDeathBuff = { ID = 52437 },
    SecondWind = { ID = 29838 },
    Shockwave = { ID = 46968, MAKULU_INFO = { damageType = "physical" } },
    ShatteringThrow = { ID = 64382, MAKULU_INFO = { damageType = "physical" } },
    RallyingCry = { ID = 97462 },
    DoubleTime = { ID = 103827 },
    StormBolt = { ID = 107570 },
    Avatar = { ID = 107574 },
    DiebytheSword = { ID = 118038 },
    AngerManagement = { ID = 152278 },
    ColossusSmash = { ID = 167105, MAKULU_INFO = { damageType = "physical" } },
    IgnorePain = { ID = 190456 },
    ImpendingVictory = { ID = 202168 },
    FervorofBattle = { ID = 202316 },
    Massacre = { ID = 281001 },
    ExecuteToo = { ID = 281000, MAKULU_INFO = { damageType = "physical" } },
    ExecuteTooo = { ID = 163201, MAKULU_INFO = { damageType = "physical" } },
    Bladestorm = { ID = 227847, Texture = 273409, MAKULU_INFO = { damageType = "physical" } },
    Ravager = { ID = 228920, Texture = 273409, MAKULU_INFO = { damageType = "physical" } },
    Skullsplitter = { ID = 260643, MAKULU_INFO = { damageType = "physical" } },
    SweepingStrikes = { ID = 260708, MAKULU_INFO = { damageType = "physical" } },
    Dreadnaught = { ID = 262150, MAKULU_INFO = { damageType = "physical" } },
    Warbreaker = { ID = 262161, MAKULU_INFO = { damageType = "physical" } },
    CollateralDamage = { ID = 334779 },
    ChampionsSpear = { ID = 376079, FixedTexture = 3565453 },
    MercilessBonegrinder = { ID = 383317, MAKULU_INFO = { damageType = "physical" } },
    SharpenBlade = { ID = 198817, MAKULU_INFO = { damageType = "physical" } },
    SharpenedBlades = { ID = 383341, MAKULU_INFO = { damageType = "physical" } },
    ThunderousRoar = { ID = 384318, MAKULU_INFO = { damageType = "physical" } },
    ImprovedHeroicThrow = { ID = 386034, MAKULU_INFO = { damageType = "physical" } },
    BattleStance = { ID = 386164 },
    DefensiveStance = { ID = 386208 },
    Unhinged = { ID = 386628, MAKULU_INFO = { damageType = "physical" } },
    ExecutionersPrecision = { ID = 386634 },
    Demolish = { ID = 436358, MAKULU_INFO = { damageType = "physical" } },
    BitterImmunity = { ID = 383762 },
    Bloodletting = { ID = 383154, Hidden = true },
    SlayersDominance = { ID = 444767, Hidden = true },
    Juggernaut = { ID = 383292, Hidden = true },
    FierceFollowthrough = { ID = 444773, Hidden = true },
    StrengthofArms = { ID = 400803, Hidden = true },
    Opportunist = { ID = 444774, Hidden = true },


    Healthstone = { Type = "Item", ID = 5512, Hidden = true },
    TemperedPotion1 = { Type = "Potion", ID = 212263, Texture = 176108, Hidden = true },
    TemperedPotion2 = { Type = "Potion", ID = 212264, Texture = 176108, Hidden = true },
    TemperedPotion3 = { Type = "Potion", ID = 212265, Texture = 176108, Hidden = true },
    PotionofUnwaveringFocus1 = { Type = "Potion", ID = 212257, Texture = 176108, Hidden = true },
    PotionofUnwaveringFocus2 = { Type = "Potion", ID = 212258, Texture = 176108, Hidden = true },
    PotionofUnwaveringFocus3 = { Type = "Potion", ID = 212259, Texture = 176108, Hidden = true },
    FrontlinePotion = { Type = "Potion", ID = 212262, Texture = 176108, Hidden = true },
    AlgariManaPotion = { Type = "Potion", ID = 212241, Texture = 176108, Hidden = true },

    ArenaPreparation = { ID = 32727, Hidden = true }, 
}

local function createAction(attributes)
    return Action.Create({
        Type = attributes.Type or "Spell",
        ID = attributes.ID,
        Texture = attributes.Texture,
        FixedTexture = attributes.FixedTexture,
        Color = attributes.Color,
        Desc = attributes.Desc,
        MAKULU_INFO = attributes.MAKULU_INFO,
        Hidden = attributes.Hidden,
        QueueForbidden = attributes.QueueForbidden,
    })
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
	local result = {}
	for k, v in pairs(A) do
		result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
	end
	return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_WARRIOR_ARMS] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end

local player = ConstUnit.player
local target = ConstUnit.target
local focus = ConstUnit.focus
local mouseover = ConstUnit.mouseover
local pet = ConstUnit.pet
local arena1 = ConstUnit.arena1
local arena2 = ConstUnit.arena2
local arena3 = ConstUnit.arena3
local party1 = ConstUnit.party1
local party2 = ConstUnit.party2
local party3 = ConstUnit.party3
local party4 = ConstUnit.party4
local healer = ConstUnit.healer
local enemyHealer = ConstUnit.enemyHealer

local gameState = {
    imCasting = nil,
    imCastingName = nil,
    imCastingRemaining = 0,
    minTalentedCdRemains = nil,
    cursorCheck = false,
    shouldAoE = false,
    should2T = false,
}

local buffs = {
	avatar = 107574,
    battleShout = 6673,
    defensiveStance = 386208,
    battleStance = 386164,
    collateralDamage = 334783,
    mercilessBonegrinder = 383316,
    sweepingStrikes = 260708,
    suddenDeath = 29725,
    opportunist = 456120,
    juggernaut = 383290,
    recklessness = 1719,
    ravager = 228920,
    colossalMight = 440989,
}

local debuffs = {
    championsMight = 376080,
    gushingWound = 385042,
    colossusSmash = 208086,
    executionersPrecision = 386633,
    strikeVulnerabilities = 394173,
    rend = 388539,
    markedForExecution = 445584,
    deepWound = 262115,
    lethalBlows = 455485,
    martialProwess = 7384,
}


--Player:AddTier("Tier31", { 217236, 217237, 217238, 217239, 217240, })
--local T31has2P = Player:HasTier("Tier31", 2)
--local T31has4P = Player:HasTier("Tier31", 4)

local cacheContext     = MakuluFramework.Cache

local constCell = cacheContext:getConstCacheCell()
local function enemiesInMelee()
    return constCell:GetOrSet("enemiesInMelee", function() 
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0

        for enemyGUID in pairs(activeEnemies) do -- Jack will fix our enemies check soon
            local enemy = MakUnit:new(enemyGUID) 
            if Slam:InRange(enemy) and not enemy:IsTotem() and not enemy.isPet then  -- I haven't tested the new totem yet
                total = total + 1
            end 
        end  
        
        return total
    end)
end

local function activeEnemies()
    return enemiesInMelee()
end

local interrupts = {
    { spell = Pummel },
    { spell = StormBolt, isCC = true },
    { spell = Shockwave, isCC = true, aoe = true, distance = 5 },
    { spell = IntimidatingShoutPvE, isCC = true, aoe = true, distance = 2 }
}

local function shouldBurst()
    if A.BurstIsON("target") then
        --if A.Zone ~= "arena" then
        --    local activeEnemies = MultiUnits:GetActiveUnitPlates()
        --    for enemy in pairs(activeEnemies) do
        --        if ActionUnit(enemy):Health() > (A.Slam:GetSpellDescription()[1] * 10) or target.isDummy or target.isBoss then
        --            return true
        --        end
        --    end
        --else
            return true
        end
    --end
    --return false
end

local function hasIncomingDamage()
    return incBigDmgIn() < 2000 or incModDmgIn() < 2000
end

local function defensiveActive()
    return player:BuffFrom(MakLists.Defensive) or UnitGetTotalAbsorbs("player") >= player.maxHealth * 0.15
end

local function shouldDefensive()
    local incomingDamage = hasIncomingDamage()

    return incomingDamage and not defensiveActive()
end

local function orbsActive()
    local cacheKey = "orbsActive"
    
    return constCell:GetOrSet(cacheKey, function() 
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            local enemyCast = enemy.castInfo
            local orb = enemyCast and enemyCast.spellId == 461904
            if HeroicThrow:InRange(enemy) and orb then
                return true
            end
        end
        
        return false
    end)
end

local function autoTarget()
    if not player.inCombat then return false end
    if A.IsInPvP then return false end

    if gameState.orbsActive then return false end

    for _, spellInfo in ipairs(interrupts) do
        if target:ShouldInterrupt(spellInfo.spell, spellInfo.isCC, spellInfo.aoe, spellInfo.distance) then
            return false
        end
    end

    if Slam:InRange(target) and target.exists then return false end

    if gameState.activeEnemies > 0 and A.GetToggle(2, "oorTarget") then
        return true
    end
end

local function updateGameState()
    gameState.shouldAoE = activeEnemies() >= 3 and A.GetToggle(2, "AoE") and A.Zone ~= "arena"
    gameState.should2T = activeEnemies() >= 2 and A.GetToggle(2, "AoE") and A.Zone ~= "arena"
    gameState.executePhase = target.hp <= 20 or (target.hp <= 35 and A.Massacre:IsTalentLearned())
    gameState.activeEnemies = activeEnemies()
    gameState.orbsActive = orbsActive()

end

--###########################################################################################################################################################################################
--                                                                CALLBACKS
--###########################################################################################################################################################################################

--################################################################ STANCES ##################################################################################################################
BattleStance:Callback(function(spell)
    if GetToggle(2, "StanceMode") == "1" then
        if player.hp > 50 and not player:Buff(buffs.battleStance) then
            return spell:Cast(player)
        end
    end

    if GetToggle(2, "StanceMode") == "2" and not player:Buff(buffs.battleStance) then
        return spell:Cast(player)
    end
end)

DefensiveStance:Callback(function(spell)
    if GetToggle(2, "StanceMode") == "1" then
        if player.hp <= 50 and not player:Buff(buffs.defensiveStance) then
            return spell:Cast(player)
        end
    end

    if GetToggle(2, "StanceMode") == "3" and not player:Buff(buffs.defensiveStance) then
        return spell:Cast(player)
    end
end)

local function stances()
    BattleStance()
    DefensiveStance()
end

--############################################################# BASE STUFF #################################################################################################################

DiebytheSword:Callback(function(spell)
    if not player.inCombat then return end
    if player.hp > GetToggle(2, "DiebytheSwordSlider") then return end

    return spell:Cast(player)
end)


BattleShout:Callback(function(spell)
    if player.inCombat then return end
    if not MakMulti.party:Any(function(unit) return not unit:Buff(buffs.battleShout) and unit.distance < 40 end) then return end


    return spell:Cast(player)
end)

Charge:Callback(function(spell)
    if player.inCombat then return end
    if Action.Zone == "arena" then return end
    if Unit("target"):GetRange() < 8 then return end
    if Unit("target"):GetRange() > 25 then return end

    return spell:Cast(target)
end)

VictoryRush:Callback("heal", function(spell)
    if IsPlayerSpell(A.ImpendingVictory.ID) then return end
    if player.hp > GetToggle(2, "VictoryRushSlider") then return end

    return spell:Cast(target)
end)

IgnorePain:Callback(function(spell)
    if not player.inCombat then return end
    if player.hp > GetToggle(2, "IgnorePainSlider") then return end

    return spell:Cast(player)
end)

ImpendingVictory:Callback("heal", function(spell)
    if not player.inCombat then return end
    if not IsPlayerSpell(A.ImpendingVictory.ID) then return end
    if player.hp > GetToggle(2, "VictoryRushSlider") then return end

    return spell:Cast(target)
end)

BitterImmunity:Callback("heal", function(spell)
    if not player.inCombat then return end
    if player.hp > GetToggle(2, "BitterImmunitySlider") then return end

    return spell:Cast(player)
end)

RallyingCry:Callback(function(spell)
    if not player.inCombat then return end
    if player.hp > GetToggle(2, "RallyingCrySlider") then return end

    return spell:Cast(player)
end)

RallyingCry:Callback("party", function(spell)
    if not player.inCombat then return end
    if not party1.exists or party1.hp > GetToggle(2, "RallyingCrySlider") then return end
    if not party2.exists or party2.hp > GetToggle(2, "RallyingCrySlider") then return end
    if not party3.exists or party3.hp > GetToggle(2, "RallyingCrySlider") then return end
    if not party4.exists or party4.hp > GetToggle(2, "RallyingCrySlider") then return end

    return spell:Cast(player)
end)

BerserkerRage:Callback(function(spell)
    if not player:HasDeBuff(MakLists.feared) then return end

    return Debounce("berRage", 300, 2000, spell)
end)

SpellReflection:Callback(function(spell)
    if not Action.Zone == "arena" then return end
    if (arena1.exists and arena1:CastingFromFor(MakLists.arenaSpellReflect, 500) and (arena1.distance > 5 or Pummel:Cooldown() > 1000)) or (arena2.exists and arena2:CastingFromFor(MakLists.arenaSpellReflect, 500) and (arena2.distance > 5 or Pummel:Cooldown() > 1000)) or (arena3.exists and arena3:CastingFromFor(MakLists.arenaSpellReflect, 500) and (arena1.distance > 5 or Pummel:Cooldown() > 1000)) then
        return spell:Cast(target)
    end
end)

ShatteringThrow:Callback(function(spell)
    if target:HasBuffFromFor(MakLists.shatteringBuffs, 500) then 
        return spell:Cast(target)
    end
end)

SharpenBlade:Callback(function(spell)
    if not IsPlayerSpell(A.SharpenBlade.ID) then return end
    if Action.Zone ~= "arena" and Action.Zone ~= "pvp" then return end
    if target.hp > 70 then return end

    return spell:Cast(target)
end)

local function baseStuff()
    DiebytheSword()
    BattleShout()
    Charge()
    VictoryRush("heal")
    IgnorePain()
    ImpendingVictory("heal")
    BitterImmunity("heal")
    RallyingCry()
    RallyingCry("party")
    BerserkerRage()
    SpellReflection()
    ShatteringThrow()
    SharpenBlade()
end

--###############################################################################  RACIALS        ###########################################################################################
--default_->add_action( "arcane_torrent,if=cooldown.mortal_strike.remains>1.5&rage<50" );
ArcaneTorrent:Callback(function(spell)
    if shouldBurst() and MortalStrike:Cooldown() > 1500 and player.rage < 50 then
        return spell:Cast(player)
    end
end)

--default_->add_action( "lights_judgment,if=debuff.colossus_smash.down&cooldown.mortal_strike.remains" );
LightsJudgment:Callback(function(spell)
    if shouldBurst() and not target:HasDeBuff(debuffs.colossusSmash) and MortalStrike:Cooldown() > 0 then

        return spell:Cast(target)
    end
end)

--default_->add_action( "bag_of_tricks,if=debuff.colossus_smash.down&cooldown.mortal_strike.remains" );
BagOfTricks:Callback(function(spell)
    if shouldBurst() and not target:HasDeBuff(debuffs.colossusSmash) and MortalStrike:Cooldown() > 0 then
        return spell:Cast(target)
    end
end)

--default_->add_action( "berserking,if=target.time_to_die>180&buff.avatar.up|target.time_to_die<180&variable.execute_phase&buff.avatar.up|target.time_to_die<20" );
Berserking:Callback(function(spell)
    if not shouldBurst() then return end
    if player:HasBuff(buffs.avatar) then
        return spell:Cast(player)
    end

    if gameState.executePhase or player:HasBuff(buffs.avatar) then
        return spell:Cast(player)
    end
end)

--default_->add_action( "blood_fury,if=debuff.colossus_smash.up" );
BloodFury:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if not shouldBurst() then return end
    if not target:HasDeBuff(debuffs.colossusSmash) then return end
    
    return spell:Cast(player)    
end)

--default_->add_action( "fireblood,if=debuff.colossus_smash.up" );
Fireblood:Callback(function(spell)
    if target.totalImmune or target.physImmune then return end
    if not shouldBurst() then return end
    if not target:HasDeBuff(debuffs.colossusSmash) then return end

    return spell:Cast(player)
end)

--default_->add_action( "ancestral_call,if=debuff.colossus_smash.up" );
AncestralCall:Callback(function(spell)
    if not shouldBurst() then return end
    if not target:HasDeBuff(debuffs.colossusSmash) then return end

    return spell:Cast(player)
end)


local function racials()
    ArcaneTorrent()
    LightsJudgment()
    BagOfTricks()
    Berserking()
    BloodFury()
    Fireblood()
    AncestralCall()
end




--###############################################################################  COLOSSUS ST       ##################################################################################

--colossus_st->add_action( "rend,if=dot.rend.remains<=gcd" );
Rend:Callback("colossus_st", function(spell)
    if target:DebuffRemains(debuffs.rend) <= MakGcd() then
        return spell:Cast(target)
    end
end)

--colossus_st->add_action( "thunderous_roar" );
ThunderousRoar:Callback("colossus_st", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(player)
end)

--colossus_st->add_action( "ravager,if=cooldown.colossus_smash.remains<=gcd" );
Ravager:Callback("colossus_st", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if ColossusSmash:Cooldown() <= MakGcd() and Warbreaker:Cooldown() <= MakGcd() then
        return spell:Cast(player)
    end
end)

--colossus_st->add_action( "champions_spear" );
ChampionsSpear:Callback("colossus_st", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(player)
end)

--colossus_st->add_action( "avatar,if=raid_event.adds.in>15" );
Avatar:Callback("colossus_st", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(player)
end)

--colossus_st->add_action( "colossus_smash" );
ColossusSmash:Callback("colossus_st", function(spell)
    if not shouldBurst() then return end
    if IsPlayerSpell(A.Warbreaker.ID) then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(target)
end)

--colossus_st->add_action( "warbreaker" );
Warbreaker:Callback("colossus_st", function(spell)
    if not shouldBurst() then return end
    if not IsPlayerSpell(A.Warbreaker.ID) then return end
    if target.totalImmune or target.physImmune then return end

    return spell:Cast(player)
end)

--colossus_st->add_action( "mortal_strike" );
MortalStrike:Callback("colossus_st", function(spell)

    return spell:Cast(target)
end)

--colossus_st->add_action( "demolish" );
Demolish:Callback("colossus_st", function(spell)
    if not shouldBurst() then return end

    return spell:Cast(target)
end)

--colossus_st->add_action( "skullsplitter" );
Skullsplitter:Callback("colossus_st", function(spell)

    return spell:Cast(target)
end)

--colossus_st->add_action( "execute" );
Execute:Callback("colossus_st", function(spell)

    return spell:Cast(target)
end)
ExecuteToo:Callback("colossus_st", function(spell)

    return spell:Cast(target)
end)
ExecuteTooo:Callback("colossus_st", function(spell)

    return spell:Cast(target)
end)

--colossus_st->add_action( "overpower" );
Overpower:Callback("colossus_st", function(spell)

    return spell:Cast(target)
end)

--colossus_st->add_action( "rend,if=dot.rend.remains<=gcd*5" );
Rend:Callback("colossus_st2", function(spell)
    if target:DebuffRemains(debuffs.rend) <= MakGcd() * 5 then
        return spell:Cast(target)
    end
end)

--colossus_st->add_action( "slam" );
Slam:Callback("colossus_st", function(spell)

    return spell:Cast(target)
end)


local function colossus_st()
    Rend("colossus_st")
    ThunderousRoar("colossus_st")
    Ravager("colossus_st")
    ChampionsSpear("colossus_st")
    Avatar("colossus_st")
    ColossusSmash("colossus_st")
    Warbreaker("colossus_st")
    MortalStrike("colossus_st")
    Demolish("colossus_st")
    Skullsplitter("colossus_st")
    Execute("colossus_st")
    ExecuteToo("colossus_st")
    ExecuteTooo("colossus_st")
    Overpower("colossus_st")
    Rend("colossus_st2")
    Slam("colossus_st")
end

--###############################################################################  COLOSSUS EXECUTE       #############################################################################

--colossus_execute->add_action( "sweeping_strikes,if=active_enemies=2" );
SweepingStrikes:Callback("colossus_execute", function(spell)
    if target.totalImmune or target.physImmune then return end
    if gameState.activeEnemies == 2 then
        return spell:Cast(player)
    end
end)

--colossus_execute->add_action( "rend,if=dot.rend.remains<=gcd&!talent.bloodletting" );
Rend:Callback("colossus_execute", function(spell)
    if target:DebuffRemains(debuffs.rend) <= MakGcd() and not A.Bloodletting:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--colossus_execute->add_action( "thunderous_roar" );
ThunderousRoar:Callback("colossus_execute", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_execute->add_action( "champions_spear" );
ChampionsSpear:Callback("colossus_execute", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_execute->add_action( "ravager,if=cooldown.colossus_smash.remains<=gcd" );
Ravager:Callback("colossus_execute", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if ColossusSmash:Cooldown() <= MakGcd() and Warbreaker:Cooldown() <= MakGcd() then
        return spell:Cast(player)
    end
end)

--colossus_execute->add_action( "avatar" );
Avatar:Callback("colossus_execute", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_execute->add_action( "colossus_smash" );
ColossusSmash:Callback("colossus_execute", function(spell)
    if not shouldBurst() then return end
    if IsPlayerSpell(A.Warbreaker.ID) then return end

    return spell:Cast(target)
end)

--colossus_execute->add_action( "warbreaker" );
Warbreaker:Callback("colossus_execute", function(spell)
    if not shouldBurst() then return end
    if not IsPlayerSpell(A.Warbreaker.ID) then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_execute->add_action( "execute,if=buff.juggernaut.remains<=gcd&talent.juggernaut" );
Execute:Callback("colossus_execute", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() and A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("colossus_execute", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() and A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)
ExecuteTooo:Callback("colossus_execute", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() and A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--colossus_execute->add_action( "skullsplitter,if=rage<85" );
Skullsplitter:Callback("colossus_execute", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player.rage <85 then 
        return spell:Cast(player) 
    end
end)

--colossus_execute->add_action( "demolish,if=debuff.colossus_smash.up" );
Demolish:Callback("colossus_execute", function(spell)
    if not shouldBurst() then return end
    if not target:HasDeBuff(debuffs.colossusSmash) then return end

    return spell:Cast(target)
end)

--colossus_execute->add_action( "mortal_strike,if=debuff.executioners_precision.stack=2&!dot.ravager.remains|!talent.executioners_precision" );
MortalStrike:Callback("colossus_execute", function(spell)
    if target:HasDeBuffCount(debuffs.executionersPrecision) == 2 and (not player:HasBuff(buffs.ravager) or not A.ExecutionersPrecision:IsTalentLearned()) then
        return spell:Cast(target)
    end
end)

--colossus_execute->add_action( "overpower,if=rage<50" );
Overpower:Callback("colossus_execute", function(spell)
    if player.rage < 50 then
        return spell:Cast(target)
    end
end)

--colossus_execute->add_action( "execute,if=rage>=40&talent.executioners_precision" );
Execute:Callback("colossus_execute2", function(spell)
    if player.rage >= 40 and A.ExecutionersPrecision:IsTalentLearned() then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("colossus_execute2", function(spell)
    if player.rage >= 40 and A.ExecutionersPrecision:IsTalentLearned() then
        return spell:Cast(target)
    end
end)
ExecuteTooo:Callback("colossus_execute2", function(spell)
    if player.rage >= 40 and A.ExecutionersPrecision:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--colossus_execute->add_action( "overpower" );
Overpower:Callback("colossus_execute2", function(spell)
    return spell:Cast(target)
end)

--colossus_execute->add_action( "bladestorm" );
Bladestorm:Callback("colossus_execute", function(spell)
    if not shouldBurst() then return end

    return spell:Cast(player)
end)

--colossus_execute->add_action( "execute" );
Execute:Callback("colossus_execute3", function(spell)
    return spell:Cast(target)
end)
ExecuteToo:Callback("colossus_execute3", function(spell)
    return spell:Cast(target)
end)
ExecuteTooo:Callback("colossus_execute3", function(spell)
    return spell:Cast(target)
end)

--colossus_execute->add_action( "mortal_strike" );
MortalStrike:Callback("colossus_execute2", function(spell)
    return spell:Cast(target)
end)


local function colossus_execute()
    SweepingStrikes("colossus_execute")
    Rend("colossus_execute")
    ThunderousRoar("colossus_execute")
    ChampionsSpear("colossus_execute")
    Ravager("colossus_execute")
    Avatar("colossus_execute")
    ColossusSmash("colossus_execute")
    Warbreaker("colossus_execute")
    Execute("colossus_execute")
    ExecuteToo("colossus_execute")
    ExecuteTooo("colossus_execute")
    Skullsplitter("colossus_execute")
    Demolish("colossus_execute")
    MortalStrike("colossus_execute")
    Overpower("colossus_execute")
    Execute("colossus_execute2")
    ExecuteToo("colossus_execute2")
    ExecuteTooo("colossus_execute2")
    Overpower("colossus_execute2")
    Bladestorm("colossus_execute")
    Execute("colossus_execute3")
    ExecuteToo("colossus_execute3")
    ExecuteTooo("colossus_execute3")
    MortalStrike("colossus_execute2")
end

--###############################################################################  COLOSSUS_SWEEP   ###################################################################################

--colossus_sweep->add_action( "thunder_clap,if=!dot.rend.remains&!buff.sweeping_strikes.up" );
ThunderClap:Callback("colossus_sweep", function(spell)
    if target.totalImmune or target.physImmune then return end
    if not target:HasDeBuff(debuffs.rend) and not player:HasBuff(buffs.sweepingStrikes) then
        return spell:Cast(target)
    end
end)

--colossus_sweep->add_action( "rend,if=dot.rend.remains<=gcd&buff.sweeping_strikes.up" );
Rend:Callback("colossus_sweep", function(spell)
    if target:DebuffRemains(debuffs.rend) <= MakGcd() and player:HasBuff(buffs.sweepingStrikes) then
        return spell:Cast(target)
    end
end)

--colossus_sweep->add_action( "thunderous_roar" );
ThunderousRoar:Callback("colossus_sweep", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_sweep->add_action( "sweeping_strikes" );
SweepingStrikes:Callback("colossus_sweep", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_sweep->add_action( "champions_spear" );
ChampionsSpear:Callback("colossus_sweep", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_sweep->add_action( "ravager,if=cooldown.colossus_smash.ready" );
Ravager:Callback("colossus_sweep", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if ColossusSmash:Cooldown() <= MakGcd() and Warbreaker:Cooldown() <= MakGcd()  then
        return spell:Cast(player)
    end
end)

--colossus_sweep->add_action( "avatar" );
Avatar:Callback("colossus_sweep", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_sweep->add_action( "colossus_smash" );
ColossusSmash:Callback("colossus_sweep", function(spell)
    if not shouldBurst() then return end
    if IsPlayerSpell(A.Warbreaker.ID) then return end

    return spell:Cast(target)
end)

--colossus_sweep->add_action( "warbreaker" );
Warbreaker:Callback("colossus_sweep", function(spell)
    if not shouldBurst() then return end
    if not IsPlayerSpell(A.Warbreaker.ID) then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_sweep->add_action( "mortal_strike" );
MortalStrike:Callback("colossus_sweep", function(spell)

    return spell:Cast(target)
end)

--colossus_sweep->add_action( "demolish,if=debuff.colossus_smash.up" );
Demolish:Callback("colossus_sweep", function(spell)
    if not shouldBurst() then return end
    if target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(target)
    end
end)

--colossus_sweep->add_action( "overpower" );
Overpower:Callback("colossus_sweep", function(spell)

    return spell:Cast(target)
end)

--colossus_sweep->add_action( "execute" );
Execute:Callback("colossus_sweep", function(spell)

    return spell:Cast(target)
end)
ExecuteToo:Callback("colossus_sweep", function(spell)

    return spell:Cast(target)
end)
ExecuteTooo:Callback("colossus_sweep", function(spell)

    return spell:Cast(target)
end)

--colossus_sweep->add_action( "whirlwind,if=talent.fervor_of_battle" );
Whirlwind:Callback("colossus_sweep", function(spell)
    if A.FervorofBattle:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--colossus_sweep->add_action( "cleave,if=talent.fervor_of_battle" );
Cleave:Callback("colossus_sweep", function(spell)
    if not IsPlayerSpell(A.Cleave.ID) then return end
    if A.FervorofBattle:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--colossus_sweep->add_action( "thunder_clap,if=dot.rend.remains<=8&buff.sweeping_strikes.down" );
ThunderClap:Callback("colossus_sweep2", function(spell)
    if target:DebuffRemains(debuffs.rend) <= 8000 and not player:HasBuff(buffs.sweepingStrikes) then
        return spell:Cast(target)
    end
end)

--colossus_sweep->add_action( "rend,if=dot.rend.remains<=5" );
Rend:Callback("colossus_sweep2", function(spell)
    if target:DebuffRemains(debuffs.rend) <= 5000 then
        return spell:Cast(target)
    end
end)

--colossus_sweep->add_action( "slam" );
Slam:Callback("colossus_sweep", function(spell)

    return spell:Cast(target)
end)

local function colossus_sweep()
    ThunderClap("colossus_sweep")
    Rend("colossus_sweep")
    ThunderousRoar("colossus_sweep")
    SweepingStrikes("colossus_sweep")
    ChampionsSpear("colossus_sweep")
    Ravager("colossus_sweep")
    Avatar("colossus_sweep")
    ColossusSmash("colossus_sweep")
    Warbreaker("colossus_sweep")
    MortalStrike("colossus_sweep")
    Demolish("colossus_sweep")
    Overpower("colossus_sweep")
    Execute("colossus_sweep")
    ExecuteToo("colossus_sweep")
    ExecuteTooo("colossus_sweep")
    Whirlwind("colossus_sweep")
    Cleave("colossus_sweep")
    ThunderClap("colossus_sweep2")
    Rend("colossus_sweep2")
    Slam("colossus_sweep")
end


--###############################################################################  COLOSSUS AOE       #################################################################################

--colossus_aoe->add_action( "cleave,if=!dot.deep_wounds.remains" );
Cleave:Callback("colossus_aoe", function(spell)
    if not IsPlayerSpell(A.Cleave.ID) then return end
    if not target:HasDeBuff(debuffs.deepWounds) then
        return spell:Cast(target)
    end
end)

--colossus_aoe->add_action( "thunder_clap,if=!dot.rend.remains" );
ThunderClap:Callback("colossus_aoe", function(spell)
    if not target:HasDeBuff(debuffs.rend) then
        return spell:Cast(target)
    end
end)

--colossus_aoe->add_action( "thunderous_roar" );
ThunderousRoar:Callback("colossus_aoe", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_aoe->add_action( "avatar" );
Avatar:Callback("colossus_aoe", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_aoe->add_action( "sweeping_strikes" );
SweepingStrikes:Callback("colossus_aoe", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_aoe->add_action( "warbreaker" );
Warbreaker:Callback("colossus_aoe", function(spell)
    if not shouldBurst() then return end
    if not IsPlayerSpell(A.Warbreaker.ID) then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_aoe->add_action( "ravager" );
Ravager:Callback("colossus_aoe", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_aoe->add_action( "champions_spear" );
ChampionsSpear:Callback("colossus_aoe", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_aoe->add_action( "colossus_smash" );
ColossusSmash:Callback("colossus_aoe", function(spell)
    if not shouldBurst() then return end
    if IsPlayerSpell(A.Warbreaker.ID) then return end

    return spell:Cast(target)
end)

--colossus_aoe->add_action( "cleave" );
Cleave:Callback("colossus_aoe2", function(spell)
    if not IsPlayerSpell(A.Cleave.ID) then return end
    return spell:Cast(target)
end)

--colossus_aoe->add_action( "bladestorm,if=talent.unhinged|talent.merciless_bonegrinder" );
Bladestorm:Callback("colossus_aoe", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if A.Unhinged:IsTalentLearned() or A.MercilessBonegrinder:IsTalentLearned() then
        return spell:Cast(player)
    end
end)

--colossus_aoe->add_action( "thunder_clap,if=dot.rend.remains<5" );
ThunderClap:Callback("colossus_aoe2", function(spell)
    if target:DebuffRemains(debuffs.rend) < 5000 then
        return spell:Cast(target)
    end
end)

--colossus_aoe->add_action( "demolish,if=buff.colossal_might.stack=10&debuff.colossus_smash.remains>=2|cooldown.colossus_smash.remains>=7" );
Demolish:Callback("colossus_aoe", function(spell)
    if not shouldBurst() then return end
    if (player:HasBuffCount(buffs.colossalMight) == 10 and (target:DebuffRemains(debuffs.colossusSmash) >= 2000) or ((ColossusSmash:Cooldown() >= 7000 or Warbreaker:Cooldown() >= 7000))) then
        return spell:Cast(target)
    end

end)

--colossus_aoe->add_action( "mortal_strike" );
MortalStrike:Callback("colossus_aoe", function(spell)

    return spell:Cast(target)
end)

--colossus_aoe->add_action( "overpower" );
Overpower:Callback("colossus_aoe", function(spell)

    return spell:Cast(target)
end)

--colossus_aoe->add_action( "thunder_clap" );
ThunderClap:Callback("colossus_aoe3", function(spell)

    return spell:Cast(target)
end)

--colossus_aoe->add_action( "skullsplitter,if=buff.sweeping_strikes.up" );
Skullsplitter:Callback("colossus_aoe", function(spell)

    return spell:Cast(target)
end)

--colossus_aoe->add_action( "execute" );
Execute:Callback("colossus_aoe", function(spell)
    
    return spell:Cast(target)
end)
ExecuteToo:Callback("colossus_aoe", function(spell)

    return spell:Cast(target)
end)
ExecuteTooo:Callback("colossus_aoe", function(spell)

    return spell:Cast(target)
end)


--colossus_aoe->add_action( "bladestorm" );
Bladestorm:Callback("colossus_aoe2", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--colossus_aoe->add_action( "whirlwind" );
Whirlwind:Callback("colossus_aoe", function(spell)
    return spell:Cast(target)
end)

local function colossus_aoe()
    Cleave("colossus_aoe")
    ThunderClap("colossus_aoe")
    ThunderousRoar("colossus_aoe")
    Avatar("colossus_aoe")
    SweepingStrikes("colossus_aoe")
    Warbreaker("colossus_aoe")
    Ravager("colossus_aoe")
    ChampionsSpear("colossus_aoe")
    ColossusSmash("colossus_aoe")
    Cleave("colossus_aoe2")
    Bladestorm("colossus_aoe")
    ThunderClap("colossus_aoe2")
    Demolish("colossus_aoe")
    MortalStrike("colossus_aoe")
    Overpower("colossus_aoe")
    ThunderClap("colossus_aoe3")
    Skullsplitter("colossus_aoe")
    Execute("colossus_aoe")
    ExecuteToo("colossus_aoe")
    ExecuteTooo("colossus_aoe")
    Bladestorm("colossus_aoe2")
    Whirlwind("colossus_aoe")
end

--###############################################################################  SLAYER ST       ####################################################################################

--slayer_st->add_action( "rend,if=dot.rend.remains<=gcd" );
Rend:Callback("slayer_st", function(spell)
    if target:DebuffRemains(debuffs.rend) <= MakGcd() then
        return spell:Cast(target)
    end
end)

--slayer_st->add_action( "thunderous_roar" );
ThunderousRoar:Callback("slayer_st", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_st->add_action( "avatar,if=cooldown.colossus_smash.remains<=5|debuff.colossus_smash.up" );
Avatar:Callback("slayer_st", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if (ColossusSmash:Cooldown() <= 5000 and Warbreaker:Cooldown() <= 5000) or target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(player)
    end
end)

--slayer_st->add_action( "champions_spear,if=debuff.colossus_smash.up|buff.avatar.up" );
ChampionsSpear:Callback("slayer_st", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if target:HasDeBuff(debuffs.colossusSmash) or player:HasBuff(buffs.avatar) then
        return spell:Cast(player)
    end
end)

--slayer_st->add_action( "ravager,if=cooldown.colossus_smash.remains<=gcd" );
Ravager:Callback("slayer_st", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if ColossusSmash:Cooldown() <= MakGcd() and Warbreaker:Cooldown() <= MakGcd() then
        return spell:Cast(player)
    end
end)

--slayer_st->add_action( "colossus_smash" );
ColossusSmash:Callback("slayer_st", function(spell)
    if not shouldBurst() then return end
    if IsPlayerSpell(A.Warbreaker.ID) then return end

    return spell:Cast(target)
end)

--slayer_st->add_action( "warbreaker" );
Warbreaker:Callback("slayer_st", function(spell)
    if not shouldBurst() then return end
    if not IsPlayerSpell(A.Warbreaker.ID) then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_st->add_action( "execute,if=buff.juggernaut.remains<=gcd*2&talent.juggernaut|buff.sudden_death.stack=2|buff.sudden_death.remains<=gcd*3|debuff.marked_for_execution.stack=3" );
Execute:Callback("slayer_st", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() * 2 and A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
    if player:HasBuffCount(buffs.suddenDeath) == 2 or player:BuffRemains(buffs.suddenDeath) <= MakGcd() * 3 or target:HasDeBuffCount(debuffs.markedForExecution) == 3 then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer_st", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() * 2 and A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
    if player:HasBuffCount(buffs.suddenDeath) == 2 or player:BuffRemains(buffs.suddenDeath) <= MakGcd() * 3 or target:HasDeBuffCount(debuffs.markedForExecution) == 3 then
        return spell:Cast(target)
    end
end)
ExecuteTooo:Callback("slayer_st", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() * 2 and A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
    if player:HasBuffCount(buffs.suddenDeath) == 2 or player:BuffRemains(buffs.suddenDeath) <= MakGcd() * 3 or target:HasDeBuffCount(debuffs.markedForExecution) == 3 then
        return spell:Cast(target)
    end
end)

--slayer_st->add_action( "overpower,if=buff.opportunist.up" );
--AddTier Stuff Later
Overpower:Callback("slayer_st", function(spell)
    if player:HasBuff(buffs.opportunist) then
        return spell:Cast(target)
    end
end)

--slayer_st->add_action( "mortal_strike" );
MortalStrike:Callback("slayer_st", function(spell)

    return spell:Cast(target)
end)

--slayer_st->add_action( "bladestorm,if=(cooldown.colossus_smash.remains>=gcd*4|cooldown.warbreaker.remains>=gcd*4)|debuff.colossus_smash.remains>=gcd*4" );
Bladestorm:Callback("slayer_st", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if ColossusSmash:Cooldown() >= MakGcd() * 4 or Warbreaker:Cooldown() >= MakGcd() * 4 or target:HasDeBuff(debuffs.colossusSmash) >= MakGcd() * 4 then
        return spell:Cast(player)
    end
end)

--slayer_st->add_action( "skullsplitter" );
Skullsplitter:Callback("slayer_st", function(spell)

    return spell:Cast(target)
end)




--slayer_st->add_action( "overpower" );
Overpower:Callback("slayer_st2", function(spell)

    return spell:Cast(target)
end)

--slayer_st->add_action( "rend,if=dot.rend.remains<=8" );
Rend:Callback("slayer_st2", function(spell)
    if target:DebuffRemains(debuffs.rend) <= 8000 then
        return spell:Cast(target)
    end
end)

--slayer_st->add_action( "execute,if=!talent.juggernaut" );
Execute:Callback("slayer_st2", function(spell)
    if not A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer_st2", function(spell)
    if not A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)
ExecuteTooo:Callback("slayer_st2", function(spell)
    if not A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--slayer_st->add_action( "cleave" );
Cleave:Callback("slayer_st", function(spell)
    if not IsPlayerSpell(A.Cleave.ID) then return end
    return spell:Cast(target)
end)

--slayer_st->add_action( "slam" );
Slam:Callback("slayer_st", function(spell)

    return spell:Cast(target)
end)

--slayer_st->add_action( "storm_bolt,if=buff.bladestorm.up" );
StormBolt:Callback("slayer_st", function(spell)
    if Action.Zone == "arena" then return end
    if player:HasBuff(buffs.bladestorm) then
        return spell:Cast(target)
    end
end)

local function slayer_st()
    Rend("slayer_st")
    ThunderousRoar("slayer_st")
    Avatar("slayer_st")
    ChampionsSpear("slayer_st")
    Ravager("slayer_st")
    ColossusSmash("slayer_st")
    Warbreaker("slayer_st")
    Execute("slayer_st")
    ExecuteToo("slayer_st")
    ExecuteTooo("slayer_st")
    Overpower("slayer_st")
    MortalStrike("slayer_st")
    Bladestorm("slayer_st")
    Skullsplitter("slayer_st")
    Overpower("slayer_st2")
    Rend("slayer_st2")
    Execute("slayer_st2")
    ExecuteToo("slayer_st2")
    ExecuteTooo("slayer_st2")
    Cleave("slayer_st")
    Slam("slayer_st")
    StormBolt("slayer_st")
end

--###############################################################################  SLAYER EXECUTE    ##################################################################################

--slayer_execute->add_action( "sweeping_strikes,if=active_enemies=2" );
SweepingStrikes:Callback("slayer_execute", function(spell)
    if target.totalImmune or target.physImmune then return end
    if gameState.activeEnemies == 2 then
        return spell:Cast(player)
    end
end)

--slayer_execute->add_action( "rend,if=dot.rend.remains<=gcd&!talent.bloodletting" );
Rend:Callback("slayer_execute", function(spell)
    if target:DebuffRemains(debuffs.rend) <= MakGcd() and not IsPlayerSpell(A.Bloodletting.ID) then
        return spell:Cast(target)
    end
end)
--slayer_execute->add_action( "thunderous_roar" );
ThunderousRoar:Callback("slayer_execute", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_execute->add_action( "avatar,if=cooldown.colossus_smash.remains<=5|debuff.colossus_smash.up" );
Avatar:Callback("slayer_execute", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if (ColossusSmash:Cooldown() <= 5000 and Warbreaker:Cooldown() <= 5000) or target:HasDeBuff(debuffs.colossusSmash) then
        return spell:Cast(player)
    end
end)

--slayer_execute->add_action( "champions_spear,if=debuff.colossus_smash.up|buff.avatar.up" );
ChampionsSpear:Callback("slayer_execute", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if target:HasDeBuff(debuffs.colossusSmash) or player:HasBuff(buffs.avatar) then
        return spell:Cast(player)
    end
end)

--slayer_execute->add_action( "ravager,if=cooldown.colossus_smash.remains<=gcd" );
Ravager:Callback("slayer_execute", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if ColossusSmash:Cooldown() <= MakGcd() and Warbreaker:Cooldown() <= MakGcd()  then
        return spell:Cast(player)
    end
end)

--slayer_execute->add_action( "warbreaker" );
Warbreaker:Callback("slayer_execute", function(spell)
    if not shouldBurst() then return end
    if not IsPlayerSpell(A.Warbreaker.ID) then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_execute->add_action( "colossus_smash" );
ColossusSmash:Callback("slayer_execute", function(spell)
    if not shouldBurst() then return end
    if IsPlayerSpell(A.Warbreaker.ID) then return end

    return spell:Cast(target)
end)

--slayer_execute->add_action( "execute,if=buff.juggernaut.remains<=gcd&talent.juggernaut" );
Execute:Callback("slayer_execute", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() and A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer_execute", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() and A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)
ExecuteTooo:Callback("slayer_execute", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() and A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--slayer_execute->add_action( "bladestorm,if=(debuff.executioners_precision.stack=2&(debuff.colossus_smash.remains>4|cooldown.colossus_smash.remains>15))|!talent.executioners_precision" );
Bladestorm:Callback("slayer_execute", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if target:HasDeBuffCount(debuffs.executionersPrecision) == 2 and (target:DebuffRemains(debuffs.colossusSmash) > 4000 or (target:HasDeBuffCount(debuffs.executionersPrecision) == 2 and ((ColossusSmash:Cooldown() > 15000 or Warbreaker:Cooldown() > 15000) or not IsPlayerSpell(A.ExecutionersPrecision.ID)))) then
        return spell:Cast(player)
    end
end)

--slayer_execute->add_action( "skullsplitter,if=rage<=40" );
Skullsplitter:Callback("slayer_execute", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player.rage <= 40 then
        return spell:Cast(player)
    end
end)

--slayer_execute->add_action( "overpower,if=buff.martial_prowess.stack<2&buff.opportunist.up&talent.opportunist&(talent.bladestorm|talent.ravager&rage<85)" );
Overpower:Callback("slayer_execute", function(spell)
    if player:HasBuffCount(buffs.martialProwess) < 2 and player:HasBuff(buffs.opportunist) and A.Opportunist:IsTalentLearned() and (A.Bladestorm:IsTalentLearned() or (A.Ravager:IsTalentLearned() and player.rage < 85)) then
        return spell:Cast(target)
    end
end)

--slayer_execute->add_action( "mortal_strike,if=dot.rend.remains<2|debuff.executioners_precision.stack=2&!dot.ravager.remains" );
MortalStrike:Callback("slayer_execute", function(spell)
    if target:DebuffRemains(debuffs.rend) < 2000 or (target:HasDeBuffCount(debuffs.executionersPrecision) == 2 and not player:HasBuff(buffs.ravager)) then
        return spell:Cast(target)
    end
end)


--slayer_execute->add_action( "overpower,if=rage<=40&buff.martial_prowess.stack<2&talent.fierce_followthrough" );
Overpower:Callback("slayer_execute2", function(spell)
    if player.rage <= 40 and player:HasBuffCount(buffs.martialProwess) < 2 and A.FierceFollowthrough:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--slayer_execute->add_action( "execute,if=rage>20" );
Execute:Callback("slayer_execute2", function(spell)
    if player.rage > 20 then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer_execute2", function(spell)
    if player.rage > 20 then
        return spell:Cast(target)
    end
end)
ExecuteTooo:Callback("slayer_execute2", function(spell)
    if player.rage > 20 then
        return spell:Cast(target)
    end
end)

--slayer_execute->add_action( "overpower" );
Overpower:Callback("slayer_execute3", function(spell)

    return spell:Cast(target)
end)

--slayer_execute->add_action( "storm_bolt,if=buff.bladestorm.up" );
StormBolt:Callback("slayer_execute", function(spell)
    if Action.Zone == "arena" then return end
    if player:HasBuff(buffs.bladestorm) then
        return spell:Cast(target)
    end
end)

local function slayer_execute()
    SweepingStrikes("slayer_execute")
    Rend("slayer_execute")
    ThunderousRoar("slayer_execute")
    Avatar("slayer_execute")
    ChampionsSpear("slayer_execute")
    Ravager("slayer_execute")
    Warbreaker("slayer_execute")
    ColossusSmash("slayer_execute")
    Execute("slayer_execute")
    ExecuteToo("slayer_execute")
    ExecuteTooo("slayer_execute")
    Bladestorm("slayer_execute")
    Skullsplitter("slayer_execute")
    Overpower("slayer_execute")
    MortalStrike("slayer_execute")
    Overpower("slayer_execute2")
    Execute("slayer_execute2")
    ExecuteToo("slayer_execute2")
    ExecuteTooo("slayer_execute2")
    Overpower("slayer_execute3")
    StormBolt("slayer_execute")
end

--###############################################################################  SLAYER SWEEP       ##################################################################################

--slayer_sweep->add_action( "thunder_clap,if=!dot.rend.remains&!buff.sweeping_strikes.up" );
ThunderClap:Callback("slayer_sweep", function(spell)
    if not target:HasDeBuff(debuffs.rend) and not player:HasBuff(buffs.sweepingStrikes) then
        return spell:Cast(target)
    end
end)

--slayer_sweep->add_action( "thunderous_roar" );
ThunderousRoar:Callback("slayer_sweep", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_sweep->add_action( "sweeping_strikes" );
SweepingStrikes:Callback("slayer_sweep", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_sweep->add_action( "rend,if=dot.rend.remains<=gcd" );
Rend:Callback("slayer_sweep", function(spell)
    if target:DebuffRemains(debuffs.rend) <= MakGcd() then
        return spell:Cast(target)
    end
end)

--slayer_sweep->add_action( "champions_spear" );
ChampionsSpear:Callback("slayer_sweep", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_sweep->add_action( "avatar" );
Avatar:Callback("slayer_sweep", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_sweep->add_action( "colossus_smash" );
ColossusSmash:Callback("slayer_sweep", function(spell)
    if not shouldBurst() then return end
    if IsPlayerSpell(A.Warbreaker.ID) then return end

    return spell:Cast(target)
end)

--slayer_sweep->add_action( "warbreaker" );
Warbreaker:Callback("slayer_sweep", function(spell)
    if not shouldBurst() then return end
    if not IsPlayerSpell(A.Warbreaker.ID) then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_sweep->add_action( "skullsplitter,if=buff.sweeping_strikes.up" );
Skullsplitter:Callback("slayer_sweep", function(spell)
    if player:HasBuff(buffs.sweepingStrikes) then
        return spell:Cast(target)
    end
end)

--slayer_sweep->add_action( "execute,if=buff.juggernaut.remains<=gcd*2" );
Execute:Callback("slayer_sweep", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() * 2 or target:HasDeBuffCount(debuffs.markedForExecution) == 3 or player:HasBuffCount(buffs.suddenDeath) == 2 or player:BuffRemains(buffs.suddenDeath) <= MakGcd() * 3 then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer_sweep", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() * 2 or target:HasDeBuffCount(debuffs.markedForExecution) == 3 or player:HasBuffCount(buffs.suddenDeath) == 2 or player:BuffRemains(buffs.suddenDeath) <= MakGcd() * 3 then
        return spell:Cast(target)
    end
end)
ExecuteTooo:Callback("slayer_sweep", function(spell)
    if player:BuffRemains(buffs.juggernaut) <= MakGcd() * 2 or target:HasDeBuffCount(debuffs.markedForExecution) == 3 or player:HasBuffCount(buffs.suddenDeath) == 2 or player:BuffRemains(buffs.suddenDeath) <= MakGcd() * 3 then
        return spell:Cast(target)
    end
end)

--slayer_sweep->add_action( "bladestorm,if=(cooldown.colossus_smash.remains>=gcd*4|cooldown.warbreaker.remains>=gcd*4)|debuff.colossus_smash.remains>=gcd*4" );
Bladestorm:Callback("slayer_sweep", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if ColossusSmash:Cooldown() >= MakGcd() * 4 or Warbreaker:Cooldown() >= MakGcd() * 4 or target:HasDeBuff(debuffs.colossusSmash) >= MakGcd() * 4 then
        return spell:Cast(player)
    end
end)

--slayer_sweep->add_action( "overpower,if=buff.opportunist.up" );
Overpower:Callback("slayer_sweep", function(spell)
    if player:HasBuff(buffs.opportunist) then
        return spell:Cast(target)
    end
end)

--slayer_sweep->add_action( "mortal_strike" );
MortalStrike:Callback("slayer_sweep", function(spell)

    return spell:Cast(target)
end)

--slayer_sweep->add_action( "overpower" );
Overpower:Callback("slayer_sweep2", function(spell)

    return spell:Cast(target)
end)

--slayer_sweep->add_action( "thunder_clap,if=dot.rend.remains<=8&buff.sweeping_strikes.down" )
ThunderClap:Callback("slayer_sweep2", function(spell)
    if target:DebuffRemains(debuffs.rend) <= 8000 and not player:HasBuff(buffs.sweepingStrikes) then
        return spell:Cast(target)
    end
end)

--slayer_sweep->add_action( "rend,if=dot.rend.remains<=5" );
Rend:Callback("slayer_sweep2", function(spell)
    if target:DebuffRemains(debuffs.rend) <= 5000 then
        return spell:Cast(target)
    end
end)

--slayer_sweep->add_action( "cleave,if=talent.fervor_of_battle&!buff.martial_prowess.up" );
Cleave:Callback("slayer_sweep", function(spell)
    if not IsPlayerSpell(A.Cleave.ID) then return end
    if A.FervorofBattle:IsTalentLearned() and not player:HasBuff(buffs.martialProwess) then
        return spell:Cast(target)
    end
end)

--slayer_sweep->add_action( "whirlwind,if=talent.fervor_of_battle" );
Whirlwind:Callback("slayer_sweep", function(spell)
    if A.FervorofBattle:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--slayer_sweep->add_action( "execute,if=!talent.juggernaut" );
Execute:Callback("slayer_sweep2", function(spell)
    if not A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer_sweep2", function(spell)
    if not A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)
ExecuteTooo:Callback("slayer_sweep2", function(spell)
    if not A.Juggernaut:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--slayer_sweep->add_action( "slam" );
Slam:Callback("slayer_sweep", function(spell)

    return spell:Cast(target)
end)

--slayer_sweep->add_action( "storm_bolt,if=buff.bladestorm.up" );
StormBolt:Callback("slayer_sweep", function(spell)
    if Action.Zone == "arena" then return end
    if player:HasBuff(buffs.bladestorm) then
        return spell:Cast(target)
    end
end)

local function slayer_sweep()
    ThunderClap("slayer_sweep")
    ThunderousRoar("slayer_sweep")
    SweepingStrikes("slayer_sweep")
    Rend("slayer_sweep")
    ChampionsSpear("slayer_sweep")
    Avatar("slayer_sweep")
    ColossusSmash("slayer_sweep")
    Warbreaker("slayer_sweep")
    Skullsplitter("slayer_sweep")
    Execute("slayer_sweep")
    ExecuteToo("slayer_sweep")
    ExecuteTooo("slayer_sweep")
    Bladestorm("slayer_sweep")
    Overpower("slayer_sweep")
    MortalStrike("slayer_sweep")
    Overpower("slayer_sweep2")
    ThunderClap("slayer_sweep2")
    Rend("slayer_sweep2")
    Cleave("slayer_sweep")
    Whirlwind("slayer_sweep")
    Execute("slayer_sweep2")
    ExecuteToo("slayer_sweep2")
    ExecuteTooo("slayer_sweep2")
    Slam("slayer_sweep")
    StormBolt("slayer_sweep")
end

--###############################################################################  SLAYER AOE       ####################################################################################

--slayer_aoe->add_action( "thunder_clap,if=!dot.rend.remains" );
ThunderClap:Callback("slayer_aoe", function(spell)
    if not target:HasDeBuff(debuffs.rend) then
        return spell:Cast(target)
    end
end)

--slayer_aoe->add_action( "sweeping_strikes" );
SweepingStrikes:Callback("slayer_aoe", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_aoe->add_action( "thunderous_roar" );
ThunderousRoar:Callback("slayer_aoe", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_aoe->add_action( "avatar" );
Avatar:Callback("slayer_aoe", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_aoe->add_action( "champions_spear" );
ChampionsSpear:Callback("slayer_aoe", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_aoe->add_action( "ravager,if=cooldown.colossus_smash.remains<=gcd" );
Ravager:Callback("slayer_aoe", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if ColossusSmash:Cooldown() <= MakGcd() then
        return spell:Cast(player)
    end
end)

--slayer_aoe->add_action( "warbreaker" );
Warbreaker:Callback("slayer_aoe", function(spell)
    if not shouldBurst() then return end
    if not IsPlayerSpell(A.Warbreaker.ID) then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_aoe->add_action( "colossus_smash" );
ColossusSmash:Callback("slayer_aoe", function(spell)
    if not shouldBurst() then return end
    if IsPlayerSpell(A.Warbreaker.ID) then return end

    return spell:Cast(target)
end)

--slayer_aoe->add_action( "cleave" );
Cleave:Callback("slayer_aoe", function(spell)
    if not IsPlayerSpell(A.Cleave.ID) then return end
    return spell:Cast(target)
end)

--slayer_aoe->add_action( "execute,if=buff.sudden_death.up&buff.imminent_demise.stack<3|buff.juggernaut.remains<3&talent.juggernaut" );
Execute:Callback("slayer_aoe", function(spell)
    if player:HasBuff(buffs.suddenDeath) and (target:HasDeBuffCount(debuffs.markedForExecution) < 3 or (player:BuffRemains(buffs.juggernaut) < 3000 and A.Juggernaut:IsTalentLearned())) then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer_aoe", function(spell)
    if player:HasBuff(buffs.suddenDeath) and (target:HasDeBuffCount(debuffs.markedForExecution) < 3 or (player:BuffRemains(buffs.juggernaut) < 3000 and A.Juggernaut:IsTalentLearned())) then
        return spell:Cast(target)
    end
end)
ExecuteTooo:Callback("slayer_aoe", function(spell)
    if player:HasBuff(buffs.suddenDeath) and (target:HasDeBuffCount(debuffs.markedForExecution) < 3 or (player:BuffRemains(buffs.juggernaut) < 3000 and A.Juggernaut:IsTalentLearned())) then
        return spell:Cast(target)
    end
end)

--slayer_aoe->add_action( "bladestorm" );
Bladestorm:Callback("slayer_aoe", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer_aoe->add_action( "overpower,if=buff.sweeping_strikes.up&(buff.opportunist.up|talent.dreadnaught&!talent.juggernaut)" );
Overpower:Callback("slayer_aoe", function(spell)
    if player:HasBuff(buffs.sweepingStrikes) and (player:HasBuff(buffs.opportunist) or (A.Dreadnaught:IsTalentLearned() and not A.Juggernaut:IsTalentLearned())) then
        return spell:Cast(target)
    end
end)

--slayer_aoe->add_action( "mortal_strike,if=buff.sweeping_strikes.up" );
MortalStrike:Callback("slayer_aoe", function(spell)
    if player:HasBuff(buffs.sweepingStrikes) then
        return spell:Cast(target)
    end
end)

--slayer_aoe->add_action( "execute,if=buff.sweeping_strikes.up&debuff.executioners_precision.stack<2&talent.executioners_precision|debuff.marked_for_execution.up" );
Execute:Callback("slayer_aoe2", function(spell)
    if player:HasBuff(buffs.sweepingStrikes) and ((target:HasDeBuffCount(debuffs.executionersPrecision) < 2 and A.ExecutionersPrecision:IsTalentLearned()) or target:HasDeBuff(debuffs.markedForExecution)) then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer_aoe2", function(spell)
    if player:HasBuff(buffs.sweepingStrikes) and ((target:HasDeBuffCount(debuffs.executionersPrecision) < 2 and A.ExecutionersPrecision:IsTalentLearned()) or target:HasDeBuff(debuffs.markedForExecution)) then
        return spell:Cast(target)
    end
end)
ExecuteTooo:Callback("slayer_aoe2", function(spell)
    if player:HasBuff(buffs.sweepingStrikes) and ((target:HasDeBuffCount(debuffs.executionersPrecision) < 2 and A.ExecutionersPrecision:IsTalentLearned()) or target:HasDeBuff(debuffs.markedForExecution)) then
        return spell:Cast(target)
    end
end)

--slayer_aoe->add_action( "skullsplitter,if=buff.sweeping_strikes.up" );
Skullsplitter:Callback("slayer_aoe", function(spell)
    if player:HasBuff(buffs.sweepingStrikes) then
        return spell:Cast(target)
    end
end)

--slayer_aoe->add_action( "overpower,if=buff.opportunist.up|talent.dreadnaught" );
Overpower:Callback("slayer_aoe2", function(spell)
    if player:HasBuff(buffs.opportunist) or A.Dreadnaught:IsTalentLearned() then
        return spell:Cast(target)
    end
end)

--slayer_aoe->add_action( "mortal_strike" );
MortalStrike:Callback("slayer_aoe2", function(spell)

    return spell:Cast(target)
end)

--slayer_aoe->add_action( "overpower" );
Overpower:Callback("slayer_aoe3", function(spell)

    return spell:Cast(target)
end)

--slayer_aoe->add_action( "thunder_clap" );
ThunderClap:Callback("slayer_aoe2", function(spell)

    return spell:Cast(target)
end)

--slayer_aoe->add_action( "execute" );
Execute:Callback("slayer_aoe3", function(spell)

    return spell:Cast(target)
end)
ExecuteToo:Callback("slayer_aoe3", function(spell)

    return spell:Cast(target)
end)
ExecuteTooo:Callback("slayer_aoe3", function(spell)

    return spell:Cast(target)
end)

--slayer_aoe->add_action( "whirlwind" );
Whirlwind:Callback("slayer_aoe", function(spell)

    return spell:Cast(target)
end)

--slayer_aoe->add_action( "skullsplitter" );
Skullsplitter:Callback("slayer_aoe2", function(spell)

    return spell:Cast(target)
end)

--slayer_aoe->add_action( "slam" );
Slam:Callback("slayer_aoe", function(spell)

    return spell:Cast(target)
end)

--slayer_aoe->add_action( "storm_bolt,if=buff.bladestorm.up" );
StormBolt:Callback("slayer_aoe", function(spell)
    if Action.Zone == "arena" then return end
    if player:HasBuff(buffs.bladestorm) then
        return spell:Cast(target)
    end
end)

local function slayer_aoe()
    ThunderClap("slayer_aoe")
    SweepingStrikes("slayer_aoe")
    ThunderousRoar("slayer_aoe")
    Avatar("slayer_aoe")
    ChampionsSpear("slayer_aoe")
    Ravager("slayer_aoe")
    Warbreaker("slayer_aoe")
    ColossusSmash("slayer_aoe")
    Cleave("slayer_aoe")
    Execute("slayer_aoe")
    ExecuteToo("slayer_aoe")
    ExecuteTooo("slayer_aoe")
    Bladestorm("slayer_aoe")
    Overpower("slayer_aoe")
    MortalStrike("slayer_aoe")
    Execute("slayer_aoe2")
    ExecuteToo("slayer_aoe2")
    ExecuteTooo("slayer_aoe2")
    Skullsplitter("slayer_aoe")
    Overpower("slayer_aoe2")
    MortalStrike("slayer_aoe2")
    Overpower("slayer_aoe3")
    ThunderClap("slayer_aoe2")
    Execute("slayer_aoe3")
    ExecuteToo("slayer_aoe3")
    ExecuteTooo("slayer_aoe3")
    Whirlwind("slayer_aoe")
    Skullsplitter("slayer_aoe2")
    Slam("slayer_aoe")
    StormBolt("slayer_aoe")
end

--################################################################################################################################################################################################################

A[1] = function(icon)
    --AntiFakeCC - Use GetCooldown to ensure the AntiFake CC spell remains usable via 'click' even if it's been blocked
	if A.AntiFakeCC:GetCooldown() == 0 then return A.AntiFakeCC:Show(icon) end
end

A[2] = function(icon)
	local castLeft, _, _, _, notKickAble = Unit("target"):IsCastingRemains()
	if castLeft == 0 then return end

    --AntiFakeKick --Use GetCooldown to ensure the AntiFake CC spell remains usable via 'click' even if it's been blocked
    if A.AntiFakeKick:GetCooldown() == 0 and not notKickAble then return A.AntiFakeKick:Show(icon) end
end

--################################################################################################################################################################################################################

A[3] = function(icon)
	FrameworkStart(icon)
    updateGameState()

    local awareAlert = A.GetToggle(2, "makAware")

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Nothing to see here")
    end

    if Action.Zone ~= "arena" then PVE.interrupt(interrupts) end

    stances()
    baseStuff()

	if target.exists and target.canAttack and Slam:InRange(target) and not player:Debuff(410201) then
        
        if shouldBurst() then
            local damagePotion = Action.GetToggle(2, "damagePotion")
            local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
            local potionExhausted = Action.GetToggle(2, "potionExhausted")
            local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")
            local damagePotionObject = Action.DetermineUsableObject("player", nil, nil, true, nil, A.TemperedPotion1, A.TemperedPotion2, A.TemperedPotion3, A.PotionofUnwaveringFocus1, A.PotionofUnwaveringFocus2, A.PotionofUnwaveringFocus3)

            if damagePotionObject and damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
                local shouldPot = player:Buff(buffs.recklessness)
                if shouldPot then
                    return damagePotionObject:Show(icon)
                end
            end
            if Trinket(1, "Damage") then Trinket1() end
            if Trinket(2, "Damage") then Trinket2() end
            racials()

        end

        if IsPlayerSpell(A.Demolish.ID) then
            if gameState.activeEnemies > 2 then
                colossus_aoe()
            elseif gameState.executePhase then
                colossus_execute()
            elseif gameState.activeEnemies == 2 and not gameState.executePhase then
                colossus_sweep()
            else
                colossus_st()
            end
        end

        if IsPlayerSpell(A.SlayersDominance.ID) then
            if gameState.activeEnemies > 2 then
                slayer_aoe()
            elseif gameState.executePhase then
                slayer_execute()
            elseif gameState.activeEnemies == 2 and not gameState.executePhase then
                slayer_sweep()
            else
                slayer_st()
            end
        end

        if not IsPlayerSpell(A.Demolish.ID) and not IsPlayerSpell(A.SlayersDominance.ID) then
            if gameState.activeEnemies > 2 then
                colossus_aoe()
            elseif gameState.executePhase then
                colossus_execute()
            elseif gameState.activeEnemies == 2 and not gameState.executePhase then
                colossus_sweep()
            else
                colossus_st()
            end
        end
      

    end


	return FrameworkEnd()
end

--## ARENA ENEMY STUFFS ##--

Pummel:Callback("arena", function(spell, enemy)
    if enemy:IsKickImmune() then return end
    if target.hp < 20 then return end
    if not enemy:CastingFromFor(MakLists.arenaKicks, 620) then return end

    return spell:Cast(enemy)
end)

Pummel:Callback("test", function(spell, enemy)
    if enemy.hp > 80 then return end

    return spell:Cast(enemy)
end)

StormBolt:Callback("arena_healer", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy.ccImmune then return end
    if enemy.distance > 20 then return end
    if not enemy:IsUnit(enemyHealer) then return end
    if enemy:IsTarget() then return end
    if target.hp > 50 then return end
    if enemy.stunDr < 0.5 then return end
    if enemy:CCRemains() > 2000 then return end
    if aware then Aware:displayMessage("SB - Enemy Healer - KT Low", "Blue", 1) end
    return spell:Cast(enemy)
end)

StormBolt:Callback("arena_kill", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy.ccImmune then return end
    if enemy.distance > 20 then return end
    if not enemy:IsTarget() then return end
    if enemy.stunDr < 0.5 then return end
    if enemyHealer.exists and enemy:IsUnit(enemyHealer) then return end
    if enemyHealer:CCRemains() < 2000 then return end
    --if enemy.hp > 50 then return end
    if aware then Aware:displayMessage("SB - KT - Healer CCed", "Red", 1) end
    return spell:Cast(enemy)
end)

StormBolt:Callback("arena_nohealer_kill", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy.ccImmune then return end
    if enemyHealer.exists then return end
    if enemy.distance > 20 then return end
    if not enemy:IsTarget() then return end
    if enemy.stunDr < 0.5 then return end
    if enemy.hp > 50 then return end
    if aware then Aware:displayMessage("SB - KT - No Enemy Healer Exists", "Red", 1) end
    return spell:Cast(enemy)
end)

Charge:Callback("charge_fear", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy:IsTarget() then return end
    if enemy.distance < 5 then return end
    if target.hp > 60 then return end
    if not enemy:IsUnit(enemyHealer) then return end
    if IntimidatingShout:Cooldown() > 700 then return end
    if enemy.totalImmune then return end
    if enemy.ccImmune then return end
    if enemy.disorientDr < 0.5 then return end
    if enemyHealer:CCRemains() > 1500 then return end
    if aware then Aware:displayMessage("Charge - Enemy Healer - To Fear", "Blue", 1) end
    return spell:Cast(enemy)
end)

IntimidatingShout:Callback("arena", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy.ccImmune then return end
    --if not spell:InRange(enemy) then return end
    if not enemy:IsUnit(enemyHealer) then return end
    if enemy:IsTarget() then return end
    if target.hp > 60 then return end
    if target.totalImmune then return end
    if enemy.disorientDr < 0.5 then return end
    if enemyHealer:CCRemains() > 1500 then return end
    if aware then Aware:displayMessage("Fear - Enemy Healer - KT Low", "Blue", 1) end
    return spell:Cast(enemy)
end)

Disarm:Callback("arena", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy.totalImmune then return end
    if enemy.physicalImmune then return end
    if enemy.ccRemains > 700 then return end
    if enemy.distance > 10 then return end
    if enemy:Buff(446035) then return end
    if enemy:Buff(227847) then return end
    if not enemy:HasBuffFromFor(MakLists.Disarm, 500) then return end
    if aware then Aware:displayMessage("Disarm - Enemy - Bursting", "White", 1) end
    return spell:Cast(enemy)
end)

--## ARENA PARTY STUFFS ##--
Intervene:Callback("party", function(spell, friendly)
    if friendly:IsUnit(player) then return end
    if friendly.hp > 40 then return end
    if player.hp < 40 then return end
    if friendly.hp > target.hp then return end
    if target.hp < 30 then return end

    return spell:Cast(friendly)
end)


local enemyRotation = function(enemy)
	if not enemy.exists then return end
    if player:Debuff(410201) then return end
    Pummel("arena", enemy)
    StormBolt("arena_healer", enemy)
    StormBolt("arena_kill", enemy)
    StormBolt("arena_nohealer_kill", enemy)
    Charge("charge_fear", enemy)
    IntimidatingShout("arena", enemy)
    Disarm("arena", enemy)
end

local enemyRotationTest = function(enemy)
    if not enemy.exists then return end

    Pummel("test", enemy)   
end


local partyRotation = function(friendly)
    if not friendly.exists then return end

    Intervene("party", friendly)
end

A[6] = function(icon)
	RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and targetForInterrupt(interrupts) then return TabTarget() end
    if autoTarget() then return TabTarget() end
    if Action.Zone == "arena" then
	    enemyRotation(arena1)
	    partyRotation(party1)
    end
        --enemyRotationTest(target)

	return FrameworkEnd()
end

A[7] = function(icon)
	RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena2)
        partyRotation(party2)
    end

	return FrameworkEnd()
end

A[8] = function(icon)
	RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena3)
        partyRotation(party3)
    end

	return FrameworkEnd()
end

A[9] = function(icon)
	RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(MakUnit:new("arena4"))
        partyRotation(MakUnit:new("party4"))
    end

	return FrameworkEnd()
end

A[10] = function(icon)
	RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(MakUnit:new("arena5"))
        partyRotation(player)
    end

	return FrameworkEnd()
end
