--[[
- Spell Reflection PVE/PVP
local function canReflect()
    for unitID in pairs(ActiveUnitPlates) do
        for spellID in pairs(reflectSpells) do
            if Unit(unitID):IsCastingRemains(spellID) > 0 then
                return true
            end
        end
    end
    return false
end

APL Whenever that happens

]]


if not MakuluValidCheck() then return true end
if not Makulu_magic_number == 2347956243324 then return true end

if GetSpecializationInfo(GetSpecialization()) ~= 72 then return end

local FrameworkStart   = MakuluFramework.start
local FrameworkEnd     = MakuluFramework.endFunc
local RegisterIcon     = MakuluFramework.registerIcon

local MakUnit          = MakuluFramework.Unit
local MakSpell         = MakuluFramework.Spell
local TableToLocal     = MakuluFramework.tableToLocal
local MakLists         = MakuluFramework.lists
local ConstUnit        = MakuluFramework.ConstUnits
local Aware            = MakuluFramework.Aware

local Action           = _G.Action
local ActionUnit       = Action.Unit
local Player           = Action.Player
local MultiUnits       = Action.MultiUnits
local GetToggle		   = Action.GetToggle
local Unit             = Action.Unit
local Debounce         = MakuluFramework.debounceSpell
local Trinket          = MakuluFramework.Trinket
local player = ConstUnit.player
local target = ConstUnit.target
local focus = ConstUnit.focus
local mouseover 
local pet = ConstUnit.pet
local arena1 = ConstUnit.arena1
local arena2 = ConstUnit.arena2
local arena3 = ConstUnit.arena3
local party1 = ConstUnit.party1
local party2 = ConstUnit.party2
local party3 = ConstUnit.party3
local party4 = ConstUnit.party4
local enemyHealer = ConstUnit.enemyHealer
local ConstSpells      = MakuluFramework.constantSpells

local _G, setmetatable = _G, setmetatable

local ActionID       = {

    -- Racials
    WillToSurvive = { ID = 59752 },
    Stoneform = { ID = 20594 },
    Shadowmeld = { ID = 58984 },
    EscapeArtist = { ID = 20589 },
    GiftOfTheNaaru = { ID = 59544 },
    Darkflight = { ID = 68992 },
    BloodFury = { ID = 20572 },
    WillOfTheForsaken = { ID = 7744 },
    WarStomp = { ID = 20549 },
    Berserking = { ID = 26297 },
    ArcaneTorrent = { ID = 50613 },
    RocketJump = { ID = 69070 },
    RocketBarrage = { ID = 69041 },
    QuakingPalm = { ID = 107079 },
    SpatialRift = { ID = 256948 },
    LightsJudgment = { ID = 255647 },
    Fireblood = { ID = 265221 },
    ArcanePulse = { ID = 260364 },
    BullRush = { ID = 255654 },
    AncestralCall = { ID = 274738 },
    Haymaker = { ID = 287712 },
    Regeneratin = { ID = 291944 },
    BagOfTricks = { ID = 312411 }, 
    HyperOrganicLightOriginator = { ID = 312924 },

    -- Random
    TargetEnemy = { ID = 44603 },
    StopCast = { ID = 61721 },
    PoolResource = { ID = 209274 },
    DelayIcon = { ID = 20579 },

	AntiFakeKick = { Type = "SpellSingleColor", ID = 6552,  Hidden = true,		Color = "GREEN"	    , Desc = "[2] AntiFakeKick",    QueueForbidden = true	},
	AntiFakeCC	 = { Type = "SpellSingleColor", ID = 107570,  	Hidden = true,		Color = "YELLOW"	, Desc = "[1] AntiFakeCC",      QueueForbidden = true	},


	-- Abilities
    BattleShout = { ID = 6673 },
    Charge = { ID = 100 },
    HeroicThrow = { ID = 57755, MAKULU_INFO = { damageType = "physical" } },
    Pummel = { ID = 6552, MAKULU_INFO = { damageType = "physical", ignoreCasting = true, offGcd = true }  },
    Slam = { ID = 1464, MAKULU_INFO = { damageType = "physical" } },
    VictoryRush = { ID = 34428, MAKULU_INFO = { damageType = "physical" } },
    DefensiveStance = { ID = 386208 },
    
    -- Talents
    Avatar = { ID = 107574 },
    BerserkerRage = { ID = 18499 },
    BerserkersTorment = { ID = 390123, Hidden = true },
    BloodandThunder = { ID = 384277, Hidden = true },
    DoubleTime = { ID = 103827, Hidden = true },
    CrushingForce = { ID = 382764, Hidden = true },
    FrothingBerserker = { ID = 215571, Hidden = true },
    ImmovableObject = { ID = 394307, Hidden = true },
    IntimidatingShout = { ID = 5246, Texture = 355, MAKULU_INFO = { damageType = "physical" } },
    HeroicLeap = { ID = 6544 },
    ImpendingVictory = { ID = 202168, MAKULU_INFO = { damageType = "physical" } },
    OverwhelmingRage = { ID = 382767, Hidden = true },
    RallyingCry = { ID = 97462 },
    RumblingEarth = { ID = 275339, Hidden = true },
    Shockwave = { ID = 46968, MAKULU_INFO = { damageType = "physical" } },
    SonicBoom = { ID = 390725, Hidden = true },
    ChampionsSpear = { ID = 376079, FixedTexture = 3565453, MAKULU_INFO = { damageType = "physical" } },
    SpellReflection = { ID = 23920 },
    StormBolt = { ID = 107570, MAKULU_INFO = { damageType = "physical" } },
    ThunderClap = { ID = 6343, Texture = 272824, MAKULU_INFO = { damageType = "physical" } },
    ThunderousRoar = { ID = 384318, MAKULU_INFO = { damageType = "physical" } },
    TitanicThrow = { ID = 384090, MAKULU_INFO = { damageType = "physical" } },
    WreckingThrow = { ID = 384110, MAKULU_INFO = { damageType = "physical" } },
    BerserkerStance = { ID = 386196 },

    -- Pool
    Pool = { ID = 999910 },

	-- Abilities
    Bloodbath = { ID = 335096, MAKULU_INFO = { damageType = "physical" } },
    CrushingBlow = { ID = 335097, MAKULU_INFO = { damageType = "physical" } },
    Execute = { ID = 280735, MAKULU_INFO = { damageType = "physical" } },
    ExecuteToo = { ID = 5308, MAKULU_INFO = { damageType = "physical" } },
    Whirlwind = { ID = 190411, MAKULU_INFO = { damageType = "physical" } },


	-- Other Talents
    AngerManagement = { ID = 152278, Hidden = true },
    Annihilator = { ID = 383916, Hidden = true },
    AshenJuggernaut = { ID = 392536, Hidden = true },
    Bladestorm = { ID = 227847, Texture = 228920, MAKULU_INFO = { damageType = "physical" } },
    Bloodthirst = { ID = 23881, MAKULU_INFO = { damageType = "physical" } },
    ColdSteelHotBlood = { ID = 383959, Hidden = true },
    DancingBlades = { ID = 391683, Hidden = true },
    Frenzy = { ID = 335077, Hidden = true },
    ImprovedWhirlwind = { ID = 12950, Hidden = true },
    ImprovedExecute = { ID = 316402, Hidden = true },
    Massacre = { ID = 206315, Hidden = true },
    MeatCleaver = { ID = 280392, Hidden = true },
    OdynsFury = { ID = 385059, MAKULU_INFO = { damageType = "physical" } },
    Onslaught = { ID = 315720, MAKULU_INFO = { damageType = "physical" } },
    RagingBlow = { ID = 85288, MAKULU_INFO = { damageType = "physical" } },
    Rampage = { ID = 184367, MAKULU_INFO = { damageType = "physical" } },
    Ravager = { ID = 228920, MAKULU_INFO = { damageType = "physical" } },
    RecklessAbandon = { ID = 396749, Hidden = true },
    Recklessness = { ID = 1719 },
    StormofSwords = { ID = 388903, Hidden = true },
    SuddenDeath = { ID = 280721, Hidden = true },
    Tenderize = { ID = 388933, Hidden = true },
    TitanicRage = { ID = 394329, Hidden = true },
    TitansTorment = { ID = 390135, Hidden = true },
    WrathandFury = { ID = 392936, Hidden = true },
    ViciousContempt = { ID = 383885, Hidden = true },

    -- Other Things (sort these into correct slots later)
    EnragedRegeneration = { ID = 184364 },
    Hamstring = { ID = 1715, MAKULU_INFO = { damageType = "physical" } },
    PiercingHowl = { ID = 12323, MAKULU_INFO = { damageType = "physical" } },
    IgnorePain = { ID = 190456 },
    Disarm = { ID = 236077, MAKULU_INFO = { damageType = "physical" } },
    Intervene = { ID = 3411 },
    Taunt = { ID = 355 },
    ShatteringThrow = { ID = 64382, MAKULU_INFO = { damageType = "physical" } },
    BitterImmunity = { ID = 383762 },
    ChampionsMight = { ID = 386284, Hidden = true },
    SlaughteringStrikes = { ID = 388004, Hidden = true },
    Unhinged = { ID = 386628, Hidden = true },

    -- Hero Talents
    SlayersDominance = { ID = 444767, Hidden = true },
    LightningStrikes = { ID = 434969, Hidden = true },
    ThunderBlast = { ID = 435222, Texture = 272824, MAKULU_INFO = { damageType = "magic" } },

    -- Lotions and Potions
    Healthstone = { Type = "Item", ID = 5512, Hidden = true },
    TemperedPotion1 = { Type = "Potion", ID = 212263, Texture = 176108, Hidden = true },
    TemperedPotion2 = { Type = "Potion", ID = 212264, Texture = 176108, Hidden = true },
    TemperedPotion3 = { Type = "Potion", ID = 212265, Texture = 176108, Hidden = true },
    PotionofUnwaveringFocus1 = { Type = "Potion", ID = 212257, Texture = 176108, Hidden = true },
    PotionofUnwaveringFocus2 = { Type = "Potion", ID = 212258, Texture = 176108, Hidden = true },
    PotionofUnwaveringFocus3 = { Type = "Potion", ID = 212259, Texture = 176108, Hidden = true },
    FrontlinePotion = { Type = "Potion", ID = 212262, Texture = 176108, Hidden = true },
    AlgariManaPotion = { Type = "Potion", ID = 212241, Texture = 176108, Hidden = true },

    -- Arena Preparation (you cheeky bastard - you never work)
    ArenaPreparation = { ID = 32727, Hidden = true },  

    -- PVP Talents
    DeathWish = { ID = 199261 },
}

local function createAction(attributes)
    return Action.Create({
        Type = attributes.Type or "Spell",
        ID = attributes.ID,
        Texture = attributes.Texture,
        FixedTexture = attributes.FixedTexture,
        Color = attributes.Color,
        Desc = attributes.Desc,
        MAKULU_INFO = attributes.MAKULU_INFO,
        Hidden = attributes.Hidden,
        QueueForbidden = attributes.QueueForbidden,
    })
end

local A = {}
for name, attributes in pairs(ActionID) do
    A[name] = createAction(attributes)
end
for name, attributes in pairs(ConstSpells) do -- add this for loop
    A[name] = createAction(attributes)
end
A = setmetatable(A, { __index = Action })

local buildMakuluFrameworkSpells = function()
	local result = {}
	for k, v in pairs(A) do
		result[k] = MakSpell:new(v.ID, v.MAKULU_INFO, v)
	end
	return result
end
local M = buildMakuluFrameworkSpells()

Action[ACTION_CONST_WARRIOR_FURY] = A

TableToLocal(M, getfenv(1))
Aware:enable()

local function num(val)
    if val then return 1 else return 0 end
end



local gameState = {
    imCasting = nil,
    imCastingName = nil,
    imCastingRemaining = 0,
    minTalentedCdRemains = nil,
    cursorCheck = false,
    shouldAoE = false,
}

local buffs = {
	avatar = 107574,
    battleShout = 6673,
    warMachine = 262232,
    ashenJuggernaut = 392537,
    bloodcraze = 393951,
    dancingBlades = 391688,
    enrage = 184362,
    frenzy = 335082,
    furiousBloodthirst = 423211,
    meatCleaver = 85739,
    mercilessAssault = 409983,
    recklessness = 1719,
    suddenDeath = 280776,
    hurricane = 390581,
    gushingWound = 385042,
    championsMight = 386286,
    berserkerStance = 386196,
    defensiveStance = 386208,
    enragedRegeneration = 184364,
    brutalFinish = 446918,
    ravager = 228920,

}

local debuffs = {
    championsMight = 376080,
    gushingWound = 385042,
    odynsFury = 385060,
    markedForExecution = 445584,
}


--Player:AddTier("Tier31", { 217236, 217237, 217238, 217239, 217240, })
--local T31has2P = Player:HasTier("Tier31", 2)
--local T31has4P = Player:HasTier("Tier31", 4)

local cacheContext     = MakuluFramework.Cache

local constCell = cacheContext:getConstCacheCell()
local function enemiesInMelee()
    return constCell:GetOrSet("enemiesInMelee", function() 
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        local total = 0

        for enemyGUID in pairs(activeEnemies) do -- Jack will fix our enemies check soon
            local enemy = MakUnit:new(enemyGUID) 
            if Slam:InRange(enemy) and not enemy:IsTotem() and not enemy.isPet then  -- I haven't tested the new totem yet
                total = total + 1
            end 
        end  
        
        return total 
    end)
end

local function activeEnemies()
    return enemiesInMelee()
end


local interrupts = {
    {spell = Pummel },
    {spell = StormBolt, isCC = true},
    {spell = Shockwave, isCC = true, aoe = true, distance = 5},
}

local function shouldBurst()
    if A.BurstIsON("player") then
        --if A.Zone ~= "arena" then
            --local activeEnemies = MultiUnits:GetActiveUnitPlates()
            --for enemy in pairs(activeEnemies) do
            --    if ActionUnit(enemy):Health() > (A.Slam:GetSpellDescription()[1] * 15) then
            --        return true
            --    end
            --end
        --else
        --    return true
        --end
        return true
    end
    return false
end

local function makTrinket(icon)
    if not shouldBurst() then return end
    if not pet.exists then return end
    --[[local gladiatorTrinkets = {
        208307, -- Verdant Combatant's Medallion
        209764, -- Verdant Aspirant's Medallion 
        209346, -- Verdant Gladiator's Medallion
        211606, -- Draconic Combatant's Medallion
        216369, -- Draconic Aspirant's Medallion
        216282, -- Draconic Gladiator's Medallion
    }
    
    for _, trinket in pairs(gladiatorTrinkets) do
        if IsEquippedItem(trinket) then
            return false
        end
    end]]
    
    if A.Trinket1:IsReady("target") then
        return A.Trinket1:Show(icon)
    end

    if A.Trinket2:IsReady("target") then
        return A.Trinket2:Show(icon)
    end

end

local function hasIncomingDamage()
    return incBigDmgIn() < 2000 or incModDmgIn() < 2000
end

local function defensiveActive()
    player = MakUnit:new("player")
    for defensive, _ in pairs(MakLists.Defensive) do
        if player:Buff(defensive) then
            return true
        end
    end
    return UnitGetTotalAbsorbs("player") >= player.maxHealth * 0.15
end

local function shouldDefensive()
    local incomingDamage = hasIncomingDamage()

    return incomingDamage and not defensiveActive()
end

local function debuffCount(spellId)
    local activeEnemies = MultiUnits:GetActiveUnitPlates()
    local debuffCount = 0

    for enemyGUID in pairs(activeEnemies) do
        local enemy = MakUnit:new(enemyGUID)
        if enemy:Debuff(spellId, true) then 
            debuffCount = debuffCount + 1
        end 
    end  
    
    return debuffCount
end

local function missingBuff(spellID)
    if ActionUnit("player"):HasBuffs(spellID) == 0 then
        return true
    end
    
    for i = 1, 4 do
        local unitID = "party" .. i
        if UnitExists(unitID) and ActionUnit(unitID):HasBuffs(spellID) == 0 and A.BattleShout:IsSpellInRange(unitID) then
            return true
        end
    end
    return false
end

local function battleShoutCount() 
    local partyUnits = {party1, party2, party3, party4, player}
    local total = 0
    for _, unit in ipairs(partyUnits) do
        if unit:Buff(buffs.battleShout, true) then
            total = total + 1
        end
    end 

    return total
end

local function bloodthirstCrit()

    local critChance = GetCritChance()

    if player:HasBuff(buffs.recklessness) then
        critChance = critChance + 20
    end

    if player:HasBuff(buffs.mercilessAssault) then
        critChance = critChance + player:HasBuffCount(buffs.mercilessAssault) * 12
    end

    if player:HasBuff(buffs.bloodcraze) then
        critChance = critChance + player:HasBuffCount(buffs.bloodcraze) * 15
    end

    return critChance
end

local function orbsActive()
    local cacheKey = "orbsActive"
    
    return constCell:GetOrSet(cacheKey, function() 
        local activeEnemies = MultiUnits:GetActiveUnitPlates()
        
        for enemyGUID in pairs(activeEnemies) do
            local enemy = MakUnit:new(enemyGUID)
            local enemyCast = enemy.castInfo
            local orb = enemyCast and enemyCast.spellId == 461904
            if HeroicThrow:InRange(enemy) and orb then
                return true
            end
        end
        
        return false
    end)
end

local function autoTarget()
    if not player.inCombat then return false end
    if A.IsInPvP then return false end

    if gameState.orbsActive then return false end

    for _, spellInfo in ipairs(interrupts) do
        if target:ShouldInterrupt(spellInfo.spell, spellInfo.isCC, spellInfo.aoe, spellInfo.distance) then
            return false
        end
    end

    if Slam:InRange(target) and target.exists then return false end

    if gameState.activeEnemies > 0 and A.GetToggle(2, "oorTarget") then
        return true
    end
end

local lastUpdateTime = 0
local updateDelay = 0.5
local function updateGameState()


    local currentTime = GetTime() 

    gameState.shouldAoE = activeEnemies() >= 2 and Action.GetToggle(2, "AoE") and Action.Zone ~= "arena"

    gameState.orbsActive = orbsActive()

    gameState.activeEnemies = activeEnemies()

end

--###########################################################################################################################################################################################
--                                                                CALLBACKS
--###########################################################################################################################################################################################

--################################################################ STANCES ##################################################################################################################
BerserkerStance:Callback(function(spell)
    if GetToggle(2, "StanceMode") == "1" then
        if player.hp > 50 and not player:Buff(buffs.berserkerStance) then
            return spell:Cast(player)
        end
    end

    if GetToggle(2, "StanceMode") == "2" and not player:Buff(buffs.berserkerStance) then
        return spell:Cast(player)
    end
end)

DefensiveStance:Callback(function(spell)
    if GetToggle(2, "StanceMode") == "1" then
        if player.hp <= 50 and not player:Buff(buffs.defensiveStance) then
            return spell:Cast(player)
        end
    end

    if GetToggle(2, "StanceMode") == "3" and not player:Buff(buffs.defensiveStance) then
        return spell:Cast(player)
    end
end)

--###############################################################################  RACIALS        ###########################################################################################
--default_->add_action( "arcane_torrent,if=cooldown.mortal_strike.remains>1.5&rage<50" );
ArcaneTorrent:Callback(function(spell)
    if not shouldBurst() then return end
    
    return spell:Cast(player)
end)

--default_->add_action( "lights_judgment,if=debuff.colossus_smash.down&cooldown.mortal_strike.remains" );
LightsJudgment:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)

--default_->add_action( "bag_of_tricks,if=debuff.colossus_smash.down&cooldown.mortal_strike.remains" );
BagOfTricks:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)

--default_->add_action( "berserking,if=target.time_to_die>180&buff.avatar.up|target.time_to_die<180&variable.execute_phase&buff.avatar.up|target.time_to_die<20" );
Berserking:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if player:HasBuff(buffs.enrage) then
        return spell:Cast(player)
    end
end)

--default_->add_action( "blood_fury,if=debuff.colossus_smash.up" );
BloodFury:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)    
end)

--default_->add_action( "fireblood,if=debuff.colossus_smash.up" );
Fireblood:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--default_->add_action( "ancestral_call,if=debuff.colossus_smash.up" );
AncestralCall:Callback(function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)


local function racials()
    ArcaneTorrent()
    LightsJudgment()
    BagOfTricks()
    Berserking()
    BloodFury()
    Fireblood()
    AncestralCall()
end

--############################################################# BASE STUFF #################################################################################################################

Pummel:Callback("base", function(spell)
    if not target.pvpKick then return end

    return spell:Cast(target)
end)

BattleShout:Callback(function(spell)
    if player.inCombat then return end
    if player:HasBuff(buffs.battleShout) then return end

    return spell:Cast(player)
end)

BattleShout:Callback("party", function(spell)
    if not party1.exists or party1:Buff(buffs.battleShout) then return end
    if not party2.exists or party2:Buff(buffs.battleShout) then return end
    if not party3.exists or party3:Buff(buffs.battleShout) then return end
    if not party4.exists or party4:Buff(buffs.battleShout) then return end
    
    return spell:Cast(player)
end)

Charge:Callback(function(spell)
    if Action.Zone == "arena" then return end
    if player.inCombat then return end
    if Unit("target"):GetRange() < 8 then return end
    if Unit("target"):GetRange() > 25 then return end

    return spell:Cast(target)
end)

Bloodthirst:Callback("heal", function(spell)

    if not player:HasBuff(buffs.enragedRegeneration) then return end

    return spell:Cast(target)
end)

-- CHECKTHIS
VictoryRush:Callback("heal", function(spell)
    if not player.inCombat then return end
    if IsPlayerSpell(A.ImpendingVictory.ID) then return end
    if player.hp > GetToggle(2, "VictoryRushSlider") then return end

    return spell:Cast(target)
end)

-- CHECKTHIS
ImpendingVictory:Callback("heal", function(spell)
    if not player.inCombat then return end
    if not IsPlayerSpell(A.ImpendingVictory.ID) then return end
    if player.hp > GetToggle(2, "VictoryRushSlider") then return end

    return spell:Cast(target)
end)

EnragedRegeneration:Callback("heal", function(spell)
    if not player.inCombat then return end
    if player.hp > GetToggle(2, "EnragedRegenerationSlider") then return end

    return spell:Cast(player)
end)

BitterImmunity:Callback("heal", function(spell)
    if not player.inCombat then return end
    if player:HasBuff(buffs.enragedRegeneration) then return end
    if player.hp > GetToggle(2, "BitterImmunitySlider") then return end

    return spell:Cast(player)
end)

RallyingCry:Callback(function(spell)
    if not player.inCombat then return end
    if player.hp > GetToggle(2, "RallyingCrySlider") then return end

    return spell:Cast(player)
end)

RallyingCry:Callback("party", function(spell)
    if not player.inCombat then return end
    if not party1.exists or party1.hp > GetToggle(2, "RallyingCrySlider") then return end
    if not party2.exists or party2.hp > GetToggle(2, "RallyingCrySlider") then return end
    if not party3.exists or party3.hp > GetToggle(2, "RallyingCrySlider") then return end
    if not party4.exists or party4.hp > GetToggle(2, "RallyingCrySlider") then return end

    return spell:Cast(player)
end)

BerserkerRage:Callback(function(spell)
    if not player:HasDeBuff(MakLists.feared) then return end

    return Debounce("berRage", 350, 2500, spell)
end)


SpellReflection:Callback(function(spell)
    if not Action.Zone == "arena" then return end
    if (arena1.exists and arena1:CastingFromFor(MakLists.arenaSpellReflect, 500) and (arena1.distance > 5 or Pummel:Cooldown() > 1000)) or (arena2.exists and arena2:CastingFromFor(MakLists.arenaSpellReflect, 500) and (arena2.distance > 5 or Pummel:Cooldown() > 1000)) or (arena3.exists and arena3:CastingFromFor(MakLists.arenaSpellReflect, 500) and (arena1.distance > 5 or Pummel:Cooldown() > 1000)) then
        return spell:Cast(target)
    end
end)

ShatteringThrow:Callback(function(spell)
    if target:HasBuffFromFor(MakLists.shatteringBuffs, 500) then 
        return spell:Cast(target)
    end
end)

local function baseStuff()
    BerserkerStance()
    DefensiveStance()
    BattleShout()
    BattleShout("party")
    EnragedRegeneration("heal")
    BitterImmunity("heal")
    RallyingCry()
    RallyingCry("party")
    BerserkerRage()
    SpellReflection()
end

local function baseStuffCombat()
    Pummel("base")
    Charge()
    Bloodthirst("heal")
    VictoryRush("heal")
    ImpendingVictory("heal")
    ShatteringThrow()
end

--###############################################################################  SLAYER  #########################################################################################
-- APL UPDATE 031825

--slayer->add_action( "recklessness" );
Recklessness:Callback("slayer", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
        return spell:Cast(player)
end)

--slayer->add_action( "avatar,if=cooldown.recklessness.remains" );
Avatar:Callback("slayer", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if Recklessness:Cooldown() > 0 then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "execute,if=buff.ashen_juggernaut.up&buff.ashen_juggernaut.remains<=gcd" );
Execute:Callback("slayer", function(spell)
    if player:HasBuff(buffs.ashenJuggernaut) and player:BuffRemains(buffs.ashenJuggernaut) <= A.GetGCD() * 1000 then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer", function(spell)
    if player:HasBuff(buffs.ashenJuggernaut) and player:BuffRemains(buffs.ashenJuggernaut) <= A.GetGCD() * 1000 then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "champions_spear,if=buff.enrage.up&(cooldown.bladestorm.remains>=2|cooldown.bladestorm.remains>=16&debuff.marked_for_execution.stack=3)" );
ChampionsSpear:Callback("slayer", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if player:HasBuff(buffs.enrage) and (Bladestorm:Cooldown() >= 2000 or (Bladestorm:Cooldown() >= 16000 and target:HasDeBuffCount(debuffs.markedForExecution) == 3)) then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "bladestorm,if=buff.enrage.up&(talent.reckless_abandon&cooldown.avatar.remains>=24|talent.anger_management&cooldown.recklessness.remains>=24)" );
Bladestorm:Callback("slayer", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if player:HasBuff(buffs.enrage) and ((IsPlayerSpell(A.RecklessAbandon.ID) and Avatar:Cooldown() >= 24000) or (IsPlayerSpell(A.AngerManagement.ID) and Recklessness:Cooldown() >= 24000)) then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "odyns_fury,if=(buff.enrage.up|talent.titanic_rage)&cooldown.avatar.remains" );
OdynsFury:Callback("slayer", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if (player:HasBuff(buffs.enrage) or IsPlayerSpell(A.TitanicRage.ID)) and Avatar:Cooldown() > 500 then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "whirlwind,if=active_enemies>=2&talent.meat_cleaver&buff.meat_cleaver.stack=0" );
Whirlwind:Callback("slayer", function(spell)
    if target.totalImmune or target.physImmune then return end
    if activeEnemies() >= 2 and IsPlayerSpell(A.MeatCleaver.ID) and player:HasBuffCount(buffs.meatCleaver) == 0 then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "execute,if=buff.sudden_death.stack=2&buff.sudden_death.remains<7" );
Execute:Callback("slayer2", function(spell)
    if player:HasBuffCount(buffs.suddenDeath) == 2 and player:BuffRemains(buffs.suddenDeath) < 7000 then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer2", function(spell)
    if player:HasBuffCount(buffs.suddenDeath) == 2 and player:BuffRemains(buffs.suddenDeath) < 7000 then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "execute,if=buff.sudden_death.up&buff.sudden_death.remains<2" );
Execute:Callback("slayer3", function(spell)
    if player:HasBuff(buffs.suddenDeath) and player:BuffRemains(buffs.suddenDeath) < 2000 then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer3", function(spell)
    if player:HasBuff(buffs.suddenDeath) and player:BuffRemains(buffs.suddenDeath) < 2000 then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "execute,if=buff.sudden_death.up&buff.imminent_demise.stack<3&cooldown.bladestorm.remains<25" );
Execute:Callback("slayer4", function(spell)
    if player:HasBuff(buffs.suddenDeath) and player:HasBuffCount(buffs.imminentDemise) < 3 and Bladestorm:Cooldown() < 25000 then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer4", function(spell)
    if player:HasBuff(buffs.suddenDeath) and player:HasBuffCount(buffs.imminentDemise) < 3 and Bladestorm:Cooldown() < 25000 then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "onslaught,if=talent.tenderize" );
Onslaught:Callback("slayer", function(spell)
    if target.totalImmune or target.physImmune then return end
    if IsPlayerSpell(A.Tenderize.ID) then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "rampage,if=!buff.enrage.up|buff.slaughtering_strikes.stack>=4" );
Rampage:Callback("slayer", function(spell)
    if target.totalImmune or target.physImmune then return end
    if not player:HasBuff(buffs.enrage) or player:HasBuffCount(buffs.slaughteringStrikes) >= 4 then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "crushing_blow,if=action.raging_blow.charges=2|buff.brutal_finish.up&(!debuff.champions_might.up|debuff.champions_might.up&debuff.champions_might.remains>gcd)" );
CrushingBlow:Callback("slayer", function(spell)
    if target.totalImmune or target.physImmune then return end
    if RagingBlow:Charges() == 2 or (player:HasBuff(buffs.brutalFinish) and (not target:HasDeBuff(debuffs.championsMight) or (target:HasDeBuff(debuffs.championsMight) and target:DebuffRemains(debuffs.championsMight) > A.GetGCD() * 1000))) then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "thunderous_roar,if=buff.enrage.up&!buff.brutal_finish.up" );
ThunderousRoar:Callback("slayer", function(spell)
    if target.totalImmune or target.physImmune then return end
    if not shouldBurst() then return end
    if player:HasBuff(buffs.enrage) and not player:HasBuff(buffs.brutalFinish) then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "execute,if=debuff.marked_for_execution.stack=3" );
Execute:Callback("slayer5", function(spell)
    if target.totalImmune or target.physImmune then return end
    if target:HasDeBuffCount(debuffs.markedForExecution) == 3 then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("slayer5", function(spell)
    if target.totalImmune or target.physImmune then return end
    if target:HasDeBuffCount(debuffs.markedForExecution) == 3 then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "bloodbath,if=buff.bloodcraze.stack>=1|(talent.uproar&dot.bloodbath_dot.remains<40&talent.bloodborne)|buff.enrage.up&buff.enrage.remains<gcd" );
Bloodbath:Callback("slayer", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player:HasBuffCount(buffs.bloodcraze) >= 1 or (IsPlayerSpell(A.Uproar.ID) and target:DebuffRemains(debuffs.bloodbathDot) < 40000 and IsPlayerSpell(A.Bloodborne.ID)) or (player:HasBuff(buffs.enrage) and player:BuffRemains(buffs.enrage) < A.GetGCD() * 1000) then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "raging_blow,if=buff.brutal_finish.up&buff.slaughtering_strikes.stack<5&(!debuff.champions_might.up|debuff.champions_might.up&debuff.champions_might.remains>gcd)" );
RagingBlow:Callback("slayer", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player:HasBuff(buffs.brutalFinish) and player:HasBuffCount(buffs.slaughteringStrikes) < 5 and (not target:HasDeBuff(debuffs.championsMight) or (target:HasDeBuff(debuffs.championsMight) and target:DebuffRemains(debuffs.championsMight) > A.GetGCD() * 1000)) then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "rampage,if=action.raging_blow.charges<=1&rage>=100&talent.anger_management&buff.recklessness.down" );
Rampage:Callback("slayer2", function(spell)
    if target.totalImmune or target.physImmune then return end
    if RagingBlow:Charges() <= 1 and player.rage >= 100 and IsPlayerSpell(A.AngerManagement.ID) and not player:HasBuff(buffs.recklessness) then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "rampage,if=rage>=120|talent.reckless_abandon&buff.recklessness.up&buff.slaughtering_strikes.stack>=3" );
Rampage:Callback("slayer3", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player.rage >= 120 or (IsPlayerSpell(A.RecklessAbandon.ID) and player:HasBuff(buffs.recklessness) and player:HasBuffCount(buffs.slaughteringStrikes) >= 3) then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "bloodbath,if=(buff.bloodcraze.stack>=4|crit_pct_current>=85)" );
Bloodbath:Callback("slayer2", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player:HasBuffCount(buffs.bloodcraze) >= 4 or GetCritChance() >= 85 then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "crushing_blow" );
CrushingBlow:Callback("slayer2", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer->add_action( "bloodbath" );
Bloodbath:Callback("slayer3", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer->add_action( "raging_blow,if=buff.opportunist.up" );
RagingBlow:Callback("slayer2", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player:HasBuff(buffs.opportunist) then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "bloodthirst,if=target.health.pct<35&talent.vicious_contempt&buff.bloodcraze.stack>=2" );
Bloodthirst:Callback("slayer", function(spell)
    if target.totalImmune or target.physImmune then return end
    if target.hp < 35 and IsPlayerSpell(A.ViciousContempt.ID) and player:HasBuffCount(buffs.bloodcraze) >= 2 then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "rampage,if=rage>=100&talent.anger_management&buff.recklessness.up" );
Rampage:Callback("slayer4", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player.rage >= 100 and IsPlayerSpell(A.AngerManagement.ID) and player:HasBuff(buffs.recklessness) then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "bloodthirst,if=buff.bloodcraze.stack>=4|crit_pct_current>=85" );
Bloodthirst:Callback("slayer2", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player:HasBuffCount(buffs.bloodcraze) >= 4 or GetCritChance() >= 85 then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "raging_blow" );
RagingBlow:Callback("slayer3", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)

--slayer->add_action( "bloodthirst" );
Bloodthirst:Callback("slayer3", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)

--slayer->add_action( "rampage" );
Rampage:Callback("slayer5", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--slayer->add_action( "execute" );
Execute:Callback("slayer6", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)
ExecuteToo:Callback("slayer6", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)

--slayer->add_action( "whirlwind,if=talent.improved_whirlwind" );
Whirlwind:Callback("fixme", function(spell)
    if target.totalImmune or target.physImmune then return end
    if IsPlayerSpell(A.ImprovedWhirlwind.ID) then
        return spell:Cast(player)
    end
end)

--slayer->add_action( "slam,if=!talent.improved_whirlwind" );
Slam:Callback("slayer", function(spell)
    if target.totalImmune or target.physImmune then return end
    if not IsPlayerSpell(A.ImprovedWhirlwind.ID) then
        return spell:Cast(target)
    end
end)

--slayer->add_action( "storm_bolt,if=buff.bladestorm.up" );
StormBolt:Callback("slayer", function(spell)
    if Action.Zone == "arena" then return end
    if target.totalImmune or target.physImmune then return end
    if player:HasBuff(buffs.bladestorm) then
        return spell:Cast(target)
    end
end)

local function slayer()
    Recklessness("slayer")
    Avatar("slayer")
    Execute("slayer")
    ExecuteToo("slayer")
    ChampionsSpear("slayer")
    Bladestorm("slayer")
    OdynsFury("slayer")
    Whirlwind("slayer")
    Execute("slayer2")
    ExecuteToo("slayer2")
    Execute("slayer3")
    ExecuteToo("slayer3")
    Execute("slayer4")
    ExecuteToo("slayer4")
    Onslaught("slayer")
    Rampage("slayer")
    CrushingBlow("slayer")
    ThunderousRoar("slayer")
    Execute("slayer5")
    ExecuteToo("slayer5")
    Bloodbath("slayer")
    RagingBlow("slayer")
    Rampage("slayer2")
    Rampage("slayer3")
    Bloodbath("slayer2")
    CrushingBlow("slayer2")
    Bloodbath("slayer3")
    RagingBlow("slayer2")
    Bloodthirst("slayer")
    Rampage("slayer4")
    Bloodthirst("slayer2")
    RagingBlow("slayer3")
    Bloodthirst("slayer3")
    Rampage("slayer5")
    Execute("slayer6")
    ExecuteToo("slayer6")
    Whirlwind("fixme")
    Slam("slayer")
    StormBolt("slayer")
end

--###############################################################################  SLAYER  #########################################################################################
-- APL UPDATE 022825

--thane->add_action( "recklessness" );
Recklessness:Callback("thane", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
        return spell:Cast(player)
end)

--thane->add_action( "avatar" );
Avatar:Callback("thane", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
        return spell:Cast(player)
end)

--thane->add_action( "ravager" );
Ravager:Callback("thane", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
        return spell:Cast(player)
end)

--thane->add_action( "thunder_blast,if=buff.enrage.up&talent.meat_cleaver" );
ThunderBlast:Callback("thane", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if player:HasBuff(buffs.enrage) and IsPlayerSpell(A.MeatCleaver.ID) then
        return spell:Cast(player)
    end
end)

--thane->add_action( "thunder_clap,if=buff.meat_cleaver.stack=0&talent.meat_cleaver&active_enemies>=2" );
ThunderClap:Callback("thane", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player:HasBuffCount(buffs.meatCleaver) == 0 and IsPlayerSpell(A.MeatCleaver.ID) and activeEnemies() >= 2 then
        return spell:Cast(player)
    end
end)

--thane->add_action( "thunderous_roar,if=buff.enrage.up" );
ThunderousRoar:Callback("thane", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if player:HasBuff(buffs.enrage) then
        return spell:Cast(player)
    end
end)

--thane->add_action( "champions_spear,if=buff.enrage.up" );
ChampionsSpear:Callback("thane", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if player:HasBuff(buffs.enrage) then
        return spell:Cast(player)
    end
end)

--thane->add_action( "odyns_fury,if=(buff.enrage.up|talent.titanic_rage)&cooldown.avatar.remains" );
OdynsFury:Callback("thane", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if (player:HasBuff(buffs.enrage) or IsPlayerSpell(A.TitanicRage.ID)) and Avatar:Cooldown() > 500 then
        return spell:Cast(player)
    end
end)

--thane->add_action( "rampage,if=buff.enrage.down" );
Rampage:Callback("thane", function(spell)
    if target.totalImmune or target.physImmune then return end
    if not player:HasBuff(buffs.enrage) then
        return spell:Cast(player)
    end
end)

--thane->add_action( "execute,if=talent.ashen_juggernaut&buff.ashen_juggernaut.remains<=gcd" );
Execute:Callback("thane", function(spell)
    if target.totalImmune or target.physImmune then return end
    if IsPlayerSpell(A.AshenJuggernaut.ID) and player:BuffRemains(buffs.ashenJuggernaut) <= A.GetGCD() * 1000 then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("thane", function(spell)
    if target.totalImmune or target.physImmune then return end
    if IsPlayerSpell(A.AshenJuggernaut.ID) and player:BuffRemains(buffs.ashenJuggernaut) <= A.GetGCD() * 1000 then
        return spell:Cast(target)
    end
end)

--thane->add_action( "rampage,if=talent.bladestorm&cooldown.bladestorm.remains<=gcd&!debuff.champions_might.up" );
Rampage:Callback("thane2", function(spell)
    if target.totalImmune or target.physImmune then return end
    if IsPlayerSpell(A.Bladestorm.ID) and Bladestorm:Cooldown() <= A.GetGCD() * 1000 and not target:HasDeBuff(debuffs.championsMight) then
        return spell:Cast(player)
    end
end)

--thane->add_action( "bladestorm,if=buff.enrage.up&talent.unhinged" );
Bladestorm:Callback("thane", function(spell)
    if not shouldBurst() then return end
    if target.totalImmune or target.physImmune then return end
    if player:HasBuff(buffs.enrage) and IsPlayerSpell(A.Unhinged.ID) then
        return spell:Cast(player)
    end
end)

--thane->add_action( "bloodbath,if=buff.bloodcraze.stack>=2" );
Bloodbath:Callback("thane", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player:HasBuffCount(buffs.bloodcraze) >= 2 then
        return spell:Cast(player)
    end
end)

--thane->add_action( "rampage,if=rage>=115&talent.reckless_abandon&buff.recklessness.up&buff.slaughtering_strikes.stack>=3" );
Rampage:Callback("thane3", function(spell)
    if target.totalImmune or target.physImmune then return end
    if player.rage >= 115 and IsPlayerSpell(A.RecklessAbandon.ID) and player:HasBuff(buffs.recklessness) and player:HasBuffCount(buffs.slaughteringStrikes) >= 3 then
        return spell:Cast(player)
    end
end)

--thane->add_action( "crushing_blow" );
CrushingBlow:Callback("thane", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--thane->add_action( "bloodbath" );
Bloodbath:Callback("thane2", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--thane->add_action( "onslaught,if=talent.tenderize" );
Onslaught:Callback("thane", function(spell)
    if target.totalImmune or target.physImmune then return end
    if IsPlayerSpell(A.Tenderize.ID) then
        return spell:Cast(target)
    end
end)

--thane->add_action( "rampage" );
Rampage:Callback("thane4", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--thane->add_action( "bloodthirst,if=talent.vicious_contempt&target.health.pct<35&buff.bloodcraze.stack>=2|!dot.ravager.remains&buff.bloodcraze.stack>=3|active_enemies>=6" );
Bloodthirst:Callback("thane", function(spell)
    if target.totalImmune or target.physImmune then return end
    if IsPlayerSpell(A.ViciousContempt.ID) and target.hp < 35 and player:HasBuffCount(buffs.bloodcraze) >= 2 or (not player:HasBuff(buffs.ravager) and player:HasBuffCount(buffs.bloodcraze) >= 3) or activeEnemies() >= 6 then
        return spell:Cast(target)
    end
end)

--thane->add_action( "raging_blow" );
RagingBlow:Callback("thane", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)

--thane->add_action( "execute,if=talent.ashen_juggernaut" );
Execute:Callback("thane2", function(spell)
    if target.totalImmune or target.physImmune then return end
    if IsPlayerSpell(A.AshenJuggernaut.ID) then
        return spell:Cast(target)
    end
end)
ExecuteToo:Callback("thane2", function(spell)
    if target.totalImmune or target.physImmune then return end
    if IsPlayerSpell(A.AshenJuggernaut.ID) then
        return spell:Cast(target)
    end
end)

--thane->add_action( "thunder_blast" );
ThunderBlast:Callback("thane2", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

--thane->add_action( "bloodthirst" );
Bloodthirst:Callback("thane2", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)

--thane->add_action( "execute" );
Execute:Callback("thane3", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)
ExecuteToo:Callback("thane3", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(target)
end)

--thane->add_action( "thunder_clap" );
ThunderClap:Callback("thane2", function(spell)
    if target.totalImmune or target.physImmune then return end
    return spell:Cast(player)
end)

local function thane()
    Recklessness("thane")
    Avatar("thane")
    Ravager("thane")
    ThunderBlast("thane")
    ThunderClap("thane")
    ThunderousRoar("thane")
    ChampionsSpear("thane")
    OdynsFury("thane")
    Rampage("thane")
    Execute("thane")
    ExecuteToo("thane")
    Rampage("thane2")
    Bladestorm("thane")
    Bloodbath("thane")
    Rampage("thane3")
    CrushingBlow("thane")
    Bloodbath("thane2")
    Onslaught("thane")
    Rampage("thane4")
    Bloodthirst("thane")
    RagingBlow("thane")
    Execute("thane2")
    ExecuteToo("thane2")
    ThunderBlast("thane2")
    Bloodthirst("thane2")
    Execute("thane3")
    ExecuteToo("thane3")
    ThunderClap("thane2")
end

--################################################################################################################################################################################################################

A[1] = function(icon)
    --AntiFakeCC - Use GetCooldown to ensure the AntiFake CC spell remains usable via 'click' even if it's been blocked
	if A.AntiFakeCC:GetCooldown() == 0 then return A.AntiFakeCC:Show(icon) end
end

A[2] = function(icon)
	local castLeft, _, _, _, notKickAble = Unit("target"):IsCastingRemains()
	if castLeft == 0 then return end

    --AntiFakeKick --Use GetCooldown to ensure the AntiFake CC spell remains usable via 'click' even if it's been blocked
    if A.AntiFakeKick:GetCooldown() == 0 and not notKickAble then return A.AntiFakeKick:Show(icon) end
end

--################################################################################################################################################################################################################

A[3] = function(icon)
	FrameworkStart(icon)
    updateGameState()

    mouseover = MakUnit:new("mouseover")

    local awareAlert = A.GetToggle(2, "makAware")

    if A.GetToggle(2, "makDebug") then
        MakPrint(1, "Enemies in Range: ", activeEnemies())
        MakPrint(2, "Target Disorient DR: ", target.disorientDr)
        MakPrint(3, "Target Total Immune: ", target.totalImmune)
        MakPrint(4, "Target CC: ", target.cc)
        MakPrint(5, "Target CC Remains: ", target:CCRemains())
        MakPrint(6, "Enemy Healer Exists: ", enemyHealer.exists)
        MakPrint(7, "PVP Kick arena1 ", arena1.pvpKick)
        MakPrint(8, "PVP Kick arena2 ", arena2.pvpKick)
        MakPrint(9, "PVP Kick arena3 ", arena3.pvpKick)
    end

    if Action.Zone ~= "arena" then makInterrupt(interrupts) end

    baseStuff()

	if target.exists and target.hp > 0 and target.canAttack and Slam:InRange(target) and not player:Debuff(410201) then
        
        baseStuffCombat()

        if shouldBurst() then
            local damagePotion = Action.GetToggle(2, "damagePotion")
            local potionLustOnly = Action.GetToggle(2, "potionLustOnly")
            local potionExhausted = Action.GetToggle(2, "potionExhausted")
            local potionExhaustedSlider = Action.GetToggle(2, "potionExhaustedSlider")
            local damagePotionObject = Action.DetermineUsableObject("player", nil, nil, true, nil, A.TemperedPotion1, A.TemperedPotion2, A.TemperedPotion3, A.PotionofUnwaveringFocus1, A.PotionofUnwaveringFocus2, A.PotionofUnwaveringFocus3)
    
            if damagePotionObject and damagePotion and ((potionLustOnly and player.bloodlust) or (potionExhausted and player:SatedRemains() > potionExhaustedSlider * 60000) or not potionLustOnly) then
                local shouldPot = player:Buff(buffs.recklessness)
                if shouldPot then
                    return damagePotionObject:Show(icon)
                end
            end
            if Trinket(1, "Damage") then Trinket1() end
            if Trinket(2, "Damage") then Trinket2() end
            racials()
        end
   

        if gameState.shouldAoE then
            if IsPlayerSpell(A.SlayersDominance.ID) then
                slayer()
            elseif IsPlayerSpell(A.LightningStrikes.ID) then
                thane()
            else
                slayer()
            end
        else
            if IsPlayerSpell(A.SlayersDominance.ID) then
                slayer()
            elseif IsPlayerSpell(A.LightningStrikes.ID) then
                thane()
            else
                slayer()
            end
        end
    end


	return FrameworkEnd()
end

--################################################################################################################################################################################################################

--## ARENA ENEMY STUFFS ##--

Pummel:Callback("arena", function(spell, enemy)
    if enemy:IsKickImmune() then return end
    if target.hp < 20 then return end
    if not enemy:CastingFromFor(MakLists.arenaKicks, 620) then return end

    return spell:Cast(enemy)
end)


StormBolt:Callback("arena_healer", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy.ccImmune then return end
    if enemy.distance > 20 then return end
    if not enemy:IsUnit(enemyHealer) then return end
    if enemy:IsTarget() then return end
    if target.hp > 50 then return end
    if enemy.stunDr < 0.5 then return end
    if enemy:CCRemains() > 2000 then return end
    if aware then Aware:displayMessage("SB - Enemy Healer - KT Low", "Blue", 1) end
    return spell:Cast(enemy)
end)

StormBolt:Callback("arena_kill", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy.ccImmune then return end
    if not enemy:IsTarget() then return end
    if enemy.distance > 20 then return end
    if enemy.stunDr < 0.5 then return end
    if enemyHealer.exists and enemy:IsUnit(enemyHealer) then return end
    if enemyHealer:CCRemains() < 2000 then return end
    if enemy.hp > 50 then return end
    if aware then Aware:displayMessage("SB - KT - Healer CCed", "Red", 1) end
    return spell:Cast(enemy)
end)

StormBolt:Callback("arena_nohealer_kill", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy.ccImmune then return end
    if enemyHealer.exists then return end
    if enemy.distance > 20 then return end
    if not enemy:IsTarget() then return end
    if enemy.stunDr < 0.5 then return end
    if enemy.hp > 50 then return end
    if aware then Aware:displayMessage("SB - KT - No Enemy Healer Exists", "Red", 1) end
    return spell:Cast(enemy)
end)

Charge:Callback("charge_fear", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy:IsTarget() then return end
    if enemy.distance < 5 then return end
    if target.hp > 60 then return end
    if not enemy:IsUnit(enemyHealer) then return end
    if Charge:Fraction() < 1.5 and HeroicLeap:Cooldown() > 500 then return end
    if IntimidatingShout:Cooldown() > 500 then return end
    if enemy.totalImmune then return end
    if enemy.ccImmune then return end
    if enemy.disorientDr < 0.5 then return end
    if enemyHealer:CCRemains() > 1500 then return end
    if aware then Aware:displayMessage("Charge - Enemy Healer - To Fear", "Blue", 1) end
    return spell:Cast(enemy)
end)

IntimidatingShout:Callback("arena", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy.ccImmune then return end
    --if not spell:InRange(enemy) then return end
    if not enemy:IsUnit(enemyHealer) then return end
    if enemy:IsTarget() then return end
    if target.hp > 60 then return end
    if target.totalImmune then return end
    if enemy.disorientDr < 0.5 then return end
    if enemyHealer:CCRemains() > 1500 then return end
    if aware then Aware:displayMessage("Fear - Enemy Healer - KT Low", "Blue", 1) end
    return spell:Cast(enemy)
end)

Disarm:Callback("arena", function(spell, enemy)
    local aware = A.GetToggle(2, "makArenaAware")
    if enemy.totalImmune then return end
    if enemy.physicalImmune then return end
    if enemy.ccRemains > 700 then return end
    if enemy.distance > 10 then return end
    if enemy:Buff(446035) then return end
    if enemy:Buff(227847) then return end
    if not enemy:HasBuffFromFor(MakLists.Disarm, 500) then return end
    if aware then Aware:displayMessage("Disarm - Enemy - Bursting", "White", 1) end
    return spell:Cast(enemy)
end)

--## ARENA PARTY STUFFS ##--
Intervene:Callback("party", function(spell, friendly)
    if friendly:IsUnit(player) then return end
    if friendly.hp > 40 then return end
    if player.hp < 40 then return end
    if friendly.hp > target.hp then return end
    if target.hp < 30 then return end

    return spell:Cast(friendly)
end)


local enemyRotation = function(enemy)
	if not enemy.exists then return end
    if player:Debuff(410201) then return end

    Pummel("arena", enemy)
    StormBolt("arena_healer", enemy)
    StormBolt("arena_kill", enemy)
    StormBolt("arena_nohealer_kill", enemy)
    Charge("charge_fear", enemy)
    IntimidatingShout("arena", enemy)
    Disarm("arena", enemy)
end

local enemyRotationTest = function(enemy)
    if not enemy.exists then return end

    Pummel("test", enemy)   
end


local partyRotation = function(friendly)
    if not friendly.exists then return end

    Intervene("party", friendly)
end

A[6] = function(icon)
	RegisterIcon(icon)
    if A.GetToggle(2, "AutoInterrupt") and targetForInterrupt(interrupts) then return TabTarget() end
    if autoTarget() then return TabTarget() end
    if Action.Zone == "arena" then
	    enemyRotation(arena1)
	    partyRotation(party1)
    end
        --enemyRotationTest(target)

	return FrameworkEnd()
end

A[7] = function(icon)
	RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(arena2)
        partyRotation(party2)
    end

	return FrameworkEnd()
end

A[8] = function(icon)
	RegisterIcon(icon)
    if Action.Zone == "arena" then
        --print("were in a8 inside arena")
        enemyRotation(arena3)
        partyRotation(party3)
    end

	return FrameworkEnd()
end

A[9] = function(icon)
	RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(MakUnit:new("arena4"))
        partyRotation(MakUnit:new("party4"))
    end

	return FrameworkEnd()
end

A[10] = function(icon)
	RegisterIcon(icon)
    if Action.Zone == "arena" then
        enemyRotation(MakUnit:new("arena5"))
        partyRotation(player)
    end

	return FrameworkEnd()
end
