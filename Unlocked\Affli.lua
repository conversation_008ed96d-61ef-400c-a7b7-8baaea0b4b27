local dmc = ...

if (not dmc) or (not dmc.IsInWorld) then
  return
end

local unlockList = {
  "CastSpellByName",
  "UseItemByName",
}

local localOverrides = {}

local localenv = setmetatable(
  {},
  {
    __index = function(self, func)
      return dmc[func] or localOverrides[func] or _G[func]
    end
  }
)

for i = 1, #unlockList do
  local funcname = unlockList[i]
  local func = _G[funcname]
  localenv[funcname] = function(...) return dmc.SecureCode(func, ...) end
end

setfenv(1, localenv)

local IsSpellInRange = IsSpellInRange
local GetSpellInfo = GetSpellInfo
local GetSpellCooldown = GetSpellCooldown
local GetSpellCharges = GetSpellCharges
local GetTime = GetTime
local UnitGUID = UnitGUID
local UnitExists = UnitExists
local UnitIsDeadOrGhost = UnitIsDeadOrGhost
local UnitIsUnit = UnitIsUnit
local UnitIsFriend = UnitIsFriend
local UnitIsPlayer = UnitIsPlayer
local UnitAffectingCombat = UnitAffectingCombat
local UnitPower = UnitPower
local UnitPowerMax = UnitPowerMax
local UnitHealth = UnitHealth
local UnitHealthMax = UnitHealthMax
local UnitIsDead = UnitIsDead
local IsUsableSpell = IsUsableSpell
local UnitClass = UnitClass
local UnitPowerType = UnitPowerType
local UnitCastingInfo = UnitCastingInfo
local UnitChannelInfo = UnitChannelInfo
local IsMouseButtonDown = IsMouseButtonDown
local SpellStopTargeting = SpellStopTargeting
local SpellStopCasting = SpellStopCasting
local MouselookStart = MouselookStart
local SpellIsTargeting = SpellIsTargeting
local UnitAura = UnitAura
local CastSpellByName = CastSpellByName
local IsPlayerSpell = IsPlayerSpell

local pairs = pairs
local ipairs = ipairs

-- Load cache files

local function printres(res)
    if res == nil then
        return 'nil'
    elseif res == true then
        return 'true'
    elseif res == false then
        return 'false'
    elseif type(res) == 'table' then
        return 'table'
    else
        return res
    end
end

print('Loading cache files...')

local res1, res2 = RequireFile("./Utils/Cache.lua", localOverrides)

if type(res2) == 'table' then
    for k, v in pairs(res2) do
        localOverrides[k] = v
    end
end
print('Require file res1: ' .. printres(res1) .. ' res2: ' .. printres(res2))

print('Loading aura files...')

res1, res2 = RequireFile("./Utils/Auras.lua", localOverrides)
if type(res2) == 'table' then
    for k, v in pairs(res2) do
        localOverrides[k] = v
    end
end

print('Require file res1: ' .. printres(res1) .. ' res2: ' .. printres(res2))

local lastSpellName = nil
local lastSpellTime = 0

local function Cast(spell, target)
    if lastSpellName == spell and GetTime() - lastSpellTime < 0.1 then
        return true
    end

    lastSpellName = spell
    lastSpellTime = GetTime()

    print('Casting ' .. spell .. ' on' .. (target or 'self'))
    return CastSpellByName(spell, target) or true
end

print('Loading DR files...')

res1, res2 = RequireFile("./Utils/DRTracking.lua", localOverrides)
if type(res2) == 'table' then
    for k, v in pairs(res2) do
        localOverrides[k] = v
    end
end
print('Require file res1: ' .. printres(res1) .. ' res2: ' .. printres(res2))

-- region SPELLS

local ClassIsMelee = {
    [0] = false, -- UNKNOWN
    [1] = true, -- WARRIOR
    [2] = true, -- PALADIN
    [3] = false, -- HUNTER
    [4] = true, -- ROGUE
    [5] = false, -- PRIEST
    [6] = true, -- DEATHKNIGHT
    [7] = false, -- SHAMAN
    [8] = false, -- MAGE
    [9] = false, -- WARLOCK
    [10] = true, -- MONK
    [11] = false, -- DRUID
    [12] = true, -- DEMONHUNTER
    [13] = false, -- EVOKER
}
local ClassNeedsPeels = {
    [0] = false, -- UNKNOWN
    [1] = true, -- WARRIOR
    [2] = true, -- PALADIN
    [3] = true, -- HUNTER
    [4] = true, -- ROGUE
    [5] = false, -- PRIEST
    [6] = true, -- DEATHKNIGHT
    [7] = false, -- SHAMAN
    [8] = false, -- MAGE
    [9] = false, -- WARLOCK
    [10] = true, -- MONK
    [11] = false, -- DRUID
    [12] = true, -- DEMONHUNTER
    [13] = false, -- EVOKER
}
local reverseMagicList = {
    freezingTrap = GetSpellInfo(3355),
    freezingTrapHonorTalent = GetSpellInfo(203337),
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    ringOfFrost = GetSpellInfo(82691),
    repentance = GetSpellInfo(20066),
    HammerOfJustice = GetSpellInfo(853),  -- stun
}
local burstlist = {
    doomwinds = GetSpellInfo(384352), --doomwinds
    combustion = GetSpellInfo(190319), --combustion
    meta = GetSpellInfo(191427), --meta
    meta2 = GetSpellInfo(162264), --meta2
    shadowblades = GetSpellInfo(121471), --shadowblades
    shadowDance = GetSpellInfo(185313), --shadow dance
    shadowDance2 = GetSpellInfo(185422), --shadow dance
    trueshot = GetSpellInfo(288613), --trueshot
    coordinatedAssault = GetSpellInfo(360952), --coordinated assault
    incarnFeral = GetSpellInfo(102543), --incarn feral
    incarnElune = GetSpellInfo(103560), --incarn elune
    recklessness = GetSpellInfo(1719), --recklessness
    avatar = GetSpellInfo(107574), --avatar
    adrenalineRush = GetSpellInfo(13750), --adrenaline rush
    icyVeins = GetSpellInfo(12472), --icy veins
    pillarofFrost = GetSpellInfo(51271), --pillar of frost
    abomLimb = GetSpellInfo(383269), --abom limb
    avengingWrath = GetSpellInfo(231895), -- avenging wrath
    crusade = GetSpellInfo(384392),
    crusade2 = GetSpellInfo(231895),
    serenity = GetSpellInfo(152173), --serenity
    ascendance = GetSpellInfo(114051),
    danseMacabre = GetSpellInfo(393969),
}
local totalImmunityList = {
    Cyclone = GetSpellInfo(33786),
    iceBlock = GetSpellInfo(45438),
    divineShield = GetSpellInfo(642), 
    aspectOfTheTurtle = GetSpellInfo(186265),
    imprison = GetSpellInfo(221527),
    banish = GetSpellInfo(710),
    diamondIce = GetSpellInfo(203340),
    tranquilityPVPTalent = GetSpellInfo(362486),
    cloakOfShadows = GetSpellInfo(31224),
    dispersion = GetSpellInfo(79811),
    dispersion2 = GetSpellInfo(47585),
    netherwalk = GetSpellInfo(196555),
    burrow = GetSpellInfo(409293),
    blessingOfSpellwarding = GetSpellInfo(204018),
    lifeCocoon = GetSpellInfo(116849),
    spellReflect = GetSpellInfo(23920),
    netherWard = GetSpellInfo(212295),

    -- Disorient effects
    blindingSleet = GetSpellInfo(207167),
    dragonsBreath = GetSpellInfo(31661),
    blind = GetSpellInfo(2094),
    fear = GetSpellInfo(118699),
    howlOfTerror = GetSpellInfo(5484),
    intimidatingShout1 = GetSpellInfo(5246),
    intimidatingShout2 = GetSpellInfo(316593),
    intimidatingShout3 = GetSpellInfo(316595),

    -- Incapacitate effects
    hibernate = GetSpellInfo(2637),
    incapacitatingRoar = GetSpellInfo(99),
    freezingTrap = GetSpellInfo(3355),
    freezingTrapHonorTalent = GetSpellInfo(203337),
    scatterShot = GetSpellInfo(213691),
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    ringOfFrost = GetSpellInfo(82691),
    repentance = GetSpellInfo(20066),
    shackleUndead = GetSpellInfo(9484),
    sap = GetSpellInfo(6770),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
}
local breakableCC = {
    -- Disorient effects
    blindingSleet = GetSpellInfo(207167),
    dragonsBreath = GetSpellInfo(31661),
    blind = GetSpellInfo(2094),
    fear = GetSpellInfo(118699),
    howlOfTerror = GetSpellInfo(5484),
    intimidatingShout1 = GetSpellInfo(5246),
    intimidatingShout2 = GetSpellInfo(316593),
    intimidatingShout3 = GetSpellInfo(316595),
    sigilMisery = GetSpellInfo(207684),

    -- Incapacitate effects
    hibernate = GetSpellInfo(2637),
    incapacitatingRoar = GetSpellInfo(99),
    freezingTrap = GetSpellInfo(3355),
    freezingTrapHonorTalent = GetSpellInfo(203337),
    scatterShot = GetSpellInfo(213691),
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    ringOfFrost = GetSpellInfo(82691),
    repentance = GetSpellInfo(20066),
    shackleUndead = GetSpellInfo(9484),
    sap = GetSpellInfo(6770),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
}
local interruptList = {
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
    convoke = GetSpellInfo(323764),
    penance = GetSpellInfo(186723),
    repentance = GetSpellInfo(20066),
    ringOfFrost = GetSpellInfo(113724),
    cyclone = GetSpellInfo(33786),
    mindControl = GetSpellInfo(605),
    songOfChiji = GetSpellInfo(198898),
    fear = GetSpellInfo(5782),
    hibernate = GetSpellInfo(2637),
    banish = GetSpellInfo(710),
    turnEvil = GetSpellInfo(10326),
    sleepWalk = GetSpellInfo(360806),
    summonFelhunter = GetSpellInfo(691),
    summonImp = GetSpellInfo(688),
    summonSuccubus = GetSpellInfo(712),
    massDispel = GetSpellInfo(32375),
    shiftingPower = GetSpellInfo(314791),
    revivePet = GetSpellInfo(982),
    stormkeeper = GetSpellInfo(191634),
    kindredSpirits = GetSpellInfo(326434),
    drainLife = GetSpellInfo(234153),
    chaosBolt = GetSpellInfo(116858),
    greaterPyro = GetSpellInfo(203286),
    glacialSpike = GetSpellInfo(199786),
    soulFire = GetSpellInfo(6353),
    unstableAffliction = GetSpellInfo(342938),
    summonTyrant = GetSpellInfo(265187),
    eyeBeam = GetSpellInfo(198013),
    voidTorrent = GetSpellInfo(263165),
    soothingMist = GetSpellInfo(115175),
    vampiricTouch = GetSpellInfo(34914),
    handofGuldan = GetSpellInfo(105174),
    arcaneSurge = GetSpellInfo(365350),
    stellarFlare = GetSpellInfo(202347),
    nullifyingShroud = GetSpellInfo(378464),
    eternitySurge = GetSpellInfo(382411),
    disintegrate = GetSpellInfo(356995), --not sure if worth
    fullMoon = GetSpellInfo(274283),
    halfMoon = GetSpellInfo(274282),
    tyrsDeliverance = GetSpellInfo(200652),
    shadowFury = GetSpellInfo(30283),
    ringOfFire = GetSpellInfo(353082), --not sure if worth
    rayOfFrost = GetSpellInfo(205021),
    mindGames = GetSpellInfo(375901),
    summonfelguard = GetSpellInfo(30146),
    frostBomb = GetSpellInfo(390612),  
    callDreadStalkers = GetSpellInfo(104316),  
    fireBall = GetSpellInfo(133),  
    frostBolt = GetSpellInfo(116),
    ebonMight = GetSpellInfo(395152),
    upheavel = GetSpellInfo(396286),
    drainLife = GetSpellInfo(234153),
    maleficRapture = GetSpellInfo(324536),
}
local interruptListLookup = {}

for k, v in pairs(interruptList) do
    interruptListLookup[v] = true
end

local glimpse = {
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
    repentance = GetSpellInfo(20066),
    cyclone = GetSpellInfo(33786),
    mindControl = GetSpellInfo(605),
    songOfChiji = GetSpellInfo(198898),
    fear = GetSpellInfo(5782),
    hibernate = GetSpellInfo(2637),
    banish = GetSpellInfo(710),
    turnEvil = GetSpellInfo(10326),
    sleepWalk = GetSpellInfo(360806),
    massDispel = GetSpellInfo(32375),
    shiftingPower = GetSpellInfo(314791),
    stormkeeper = GetSpellInfo(191634),
    kindredSpirits = GetSpellInfo(326434),
}

local glimpseLookup = {}

for k, v in pairs(glimpse) do
    glimpseLookup[v] = true
end

local highPrioStompList = {
    counterstrikeTotem = 105451,
    tremorTotem = 5913,
    groundingTotem = 5925,
    skyfuryTotem = 105427,
    capacitorTotem = 61245,
    staticFieldTotem = 179867,
    spiritLinkTotem = 53006,
    healingTideTotem = 59764,
    healingStreamTotem = 3527,
    manaSpringTotem = 193620,
    warBanner = 119052,
    psyfiend = 101398,
    felObelisk = 179193,
    observer = 107100,
    earthGrabTotem = 60561,
    earthbindTotem = 2630,
    manaTideTotem = 10467,
    poisonCleansingTotem = 5923,
    stoneskinTotem = 194117,
}
local purgeList = {
    tipTheScales = GetSpellInfo(370553),
    nullifyingShroud = GetSpellInfo(378464),
    naturesSwiftness = GetSpellInfo(132158),
    blessingOfSpellwarding = GetSpellInfo(204018),
    netherWard = GetSpellInfo(212295),
    felDomination = GetSpellInfo(333889),
    ghostWolf = GetSpellInfo(2645),
    amplifyCurse = GetSpellInfo(328774),
    blessingOfProtection = GetSpellInfo(1022),
    divineFavor = GetSpellInfo(210294),
    temporalShield = GetSpellInfo(198111),
    powerInfusion = GetSpellInfo(10060),
    spiritwalkersGrace = GetSpellInfo(79206),
    iceFloes = GetSpellInfo(108839),
    bloodlust = GetSpellInfo(2825),
    heroism = GetSpellInfo(32182),
    holyWard = GetSpellInfo(213610),
    soulHarvest = GetSpellInfo(196098),
    thorns = GetSpellInfo(305497),
    alterTime = GetSpellInfo(342246),
    timeWarp = GetSpellInfo(80353),
    iceBarrier = GetSpellInfo(11426),
    prismaticBarrier = GetSpellInfo(235450),
    blazingBarrier = GetSpellInfo(235313),
    tempestBarrier = GetSpellInfo(382290),
    temporalVelocity = GetSpellInfo(382824),
    powerWordFortitude = GetSpellInfo(21562),
    powerWordShield = GetSpellInfo(17),
    levitate = GetSpellInfo(111759),
    bodyAndSoul = GetSpellInfo(65081),
    prayerOfMending = GetSpellInfo(41635),
    renew = GetSpellInfo(139),
    divineHymn = GetSpellInfo(64844),
    lifebloom = GetSpellInfo(33763),
    rejuvenation = GetSpellInfo(774),
    regrowth = GetSpellInfo(8936),
    wildGrowth = GetSpellInfo(48438),
    markOfTheWild = GetSpellInfo(1126),
    soulOfTheForest = GetSpellInfo(114108),
    riptide = GetSpellInfo(61295),
    earthShield = GetSpellInfo(974),
    tidalWaves = GetSpellInfo(53390),
    swirlingCurrents = GetSpellInfo(378102),
    envelopingMist = GetSpellInfo(124682),
}
local cantRootList = {
    totemGrabRoot = GetSpellInfo(116947),
    chainsOfIce = GetSpellInfo(204085),
    remorselessWinter = GetSpellInfo(233395),          
    entanglingRoots = GetSpellInfo(339),
    earthenGrasp = GetSpellInfo(235963),
    natureGrasp = GetSpellInfo(170855),
    massEntanglement = GetSpellInfo(102359),
    landslide = GetSpellInfo(355689),
    entrapment = GetSpellInfo(393456),
    steelTrap = GetSpellInfo(162480),
    steelclawTrap = GetSpellInfo(273909),
    trackerNet = GetSpellInfo(212638),
    superStickyTar = GetSpellInfo(201158),
    frostNova = GetSpellInfo(122),
    freeze = GetSpellInfo(33395),
    freezingCold = GetSpellInfo(386770),
    frostbite = GetSpellInfo(198121),
    voidTendrilGrasp = GetSpellInfo(114404),
    entrenchedinFlame = GetSpellInfo(233582),
    disable = GetSpellInfo(116706),
    clash = GetSpellInfo(324382),
    earthgrab = GetSpellInfo(64695),
    earthUnleashed = GetSpellInfo(356738),
    surgeofPower = GetSpellInfo(285515),
    thunderstruck = GetSpellInfo(199042),
    warbringer = GetSpellInfo(356356),
}
local damageReduction = {
    blur = GetSpellInfo(198589),
    unendingResolve = GetSpellInfo(104773),
    dampenHarm = GetSpellInfo(122278),
    fortifyingBrew = GetSpellInfo(243435),
    survivalInstincts = GetSpellInfo(61336),
    barkskin = GetSpellInfo(22812),
    ironbark = GetSpellInfo(102342),
    temporalShield = GetSpellInfo(198111),
    temporalShield2 = GetSpellInfo(198144),
}

local affliSpells = {
    corruption = GetSpellInfo(172),
    agony = GetSpellInfo(980),
    unstableAffliction = GetSpellInfo(316099),
    siphonLife = GetSpellInfo(63106),
    drainLife = GetSpellInfo(234153),
    drainSoul = GetSpellInfo(198590),
    curseOfWeakness = GetSpellInfo(702),
    curseOfExhaustion = GetSpellInfo(334275),
    curseOfTounges = GetSpellInfo(1714),
    fear = GetSpellInfo(5782),
    mortalCoil = GetSpellInfo(6789),
    shadowfury = GetSpellInfo(30283),
    maleficRapture = GetSpellInfo(324536),
    phantomSingularity = GetSpellInfo(205179),
    soulRot = GetSpellInfo(386997),
    darkGlare = GetSpellInfo(205180),
    darkPact = GetSpellInfo(108416),
    unendingResolve = GetSpellInfo(104773),
    soulBurn = GetSpellInfo(385899),
}

local affliBuffs = {
    tormentedCrescendo = GetSpellInfo(387075),
}

local dhTalents = {
    ascendingFlame = 428603,
    fieryBrand = 204021,
    fieryDemise = 389220,
    collectiveAnguish = 390152,
    stokeTheFlames = 393827,
    burningBlood = 390213,
    chaosImprint = 356510,
    tormentor = 207029,
    reverseMagic = 205604,
    bigGrasp = 205630,
    glimpse = 354489,
}

-- endregion SPELLS

local healerSpecIds = {
    [65]   = true, -- Holy Paladin
    [105]  = true, -- Restoration Druid
    [256]  = true, -- Discipline Priest
    [257]  = true, -- Holy Priest
    [264]  = true, -- Restoration Shaman
    [270]  = true, -- Mistweaver Monk
    [1468] = true, -- Pres evoker
}

local casterSpecIds = {
    [62]   = true, -- Arcane Mage
    [63]   = true, -- Fire Mage
    [64]   = true, -- Frost Mage
    [102]  = true, -- Balance Druid
    [105]  = true, -- Restoration Druid
    [258]  = true, -- Shadow Priest
    [262]  = true, -- Elemental Shaman
    [265]  = true, -- Affliction Warlock
    [266]  = true, -- Demonology Warlock
    [267]  = true, -- Destruction Warlock
    [1467] = true, -- Devoker
    [1473] = true, -- Augvoker
}

local casterLockList = affliSpells.curseOfTounges

local zugzugLockList = affliSpells.curseOfExhaustion

local dkLockList = affliSpells.curseOfWeakness

local hunterLockList = affliSpells.curseOfExhaustion

-- local ClassPeelList = {
--     [0] = {}, -- UNKNOWN
--     [1] = { [dhSpells.sigilChains] = true, [dhSpells.sigilMisery] = true, [dhSpells.imprison] = true }, -- WARRIOR
--     [2] = { [dhSpells.sigilChains] = true, [dhSpells.sigilMisery] = true, [dhSpells.imprison] = true }, -- PALADIN
--     [3] = { [dhSpells.sigilChains] = true, [dhSpells.sigilMisery] = true, [dhSpells.imprison] = true }, -- HUNTER
--     [4] = { [dhSpells.sigilChains] = true, [dhSpells.sigilMisery] = true, [dhSpells.imprison] = true }, -- ROGUE
--     [5] = { [dhSpells.sigilSilence] = true, [dhSpells.sigilMisery] = true, [dhSpells.sigilChains] = true, [dhSpells.imprison] = true }, -- PRIEST
--     [6] = { [dhSpells.sigilSilence] = true, [dhSpells.sigilMisery] = true, [dhSpells.sigilChains] = true, [dhSpells.imprison] = true }, -- DEATHKNIGHT
--     [7] = { [dhSpells.sigilSilence] = true, [dhSpells.sigilMisery] = true, [dhSpells.sigilChains] = true, [dhSpells.imprison] = true }, -- SHAMAN
--     [8] = { [dhSpells.sigilSilence] = true, [dhSpells.sigilMisery] = true, [dhSpells.imprison] = true }, -- MAGE
--     [9] = { [dhSpells.sigilSilence] = true, [dhSpells.sigilMisery] = true, [dhSpells.imprison] = true }, -- WARLOCK
--     [10] = { [dhSpells.sigilChains] = true, [dhSpells.sigilMisery] = true, [dhSpells.imprison] = true }, -- MONK
--     [11] = { [dhSpells.sigilSilence] = true, [dhSpells.sigilMisery] = true, [dhSpells.sigilChains] = true, [dhSpells.imprison] = true }, -- DRUID
--     [12] = { [dhSpells.sigilChains] = true, [dhSpells.sigilMisery] = true, [dhSpells.imprison] = true }, -- DEMONHUNTER
--     [13] = { [dhSpells.sigilSilence] = true, [dhSpells.sigilMisery] = true, [dhSpells.imprison] = true }, -- EVOKER
-- }

local SpecPeelList = {
    [0] = affliSpells.curseOfExhaustion, -- UNKNOWN

    -- MAGE
    [62] = casterLockList, -- Arcane
    [63] = casterLockList, -- Fire
    [64] = casterLockList, -- Frost

    -- PALADIN
    [65] = casterLockList, -- holy
    [66] = zugzugLockList, -- prot
    [70] = zugzugLockList, -- ret

    -- WARR
    [71] = zugzugLockList, -- arms
    [72] = zugzugLockList, -- fury
    [73] = zugzugLockList, -- prot

    -- DRUID
    [102] = casterLockList, -- balance
    [103] = zugzugLockList, -- feral
    [104] = zugzugLockList, -- guardian
    [105] = casterLockList, -- resto

    -- DK
    [250] = dkLockList, -- blood
    [251] = dkLockList, -- frost
    [252] = dkLockList, -- unholy

    -- Hunter
    [253] = hunterLockList, -- beast
    [254] = hunterLockList, -- mark
    [255] = hunterLockList, -- surv

    -- Priest
    [256] = casterLockList, -- disc
    [257] = casterLockList, -- holy
    [258] = casterLockList, -- shadow

    -- Rogue
    [259] = zugzugLockList, -- assa
    [260] = zugzugLockList, -- outlaw
    [261] = zugzugLockList, -- sub

    -- Shaman
    [262] = casterLockList, -- ele
    [263] = dkLockList, -- enh
    [264] = casterLockList, -- resto

    -- Warlock
    [265] = casterLockList, -- aff
    [266] = casterLockList, -- demo
    [267] = casterLockList, -- destro

    -- Monk
    [268] = zugzugLockList, -- brew
    [269] = zugzugLockList, -- ww
    [270] = casterLockList, -- mw

    -- DH
    [577] = zugzugLockList, -- havoc
    [581] = zugzugLockList, -- veng

    -- Evoker
    [1467] = casterLockList, -- Devo
    [1468] = casterLockList, -- Pres
    [1473] = casterLockList, -- Aug
}


-- Variable to store the name of the last spell

-- Utility functions

local globalState = {
    enemyHealer = nil,
    enemyDps = {},
    enemyCanHit = {},
    enemyCanHitBursting = {},
    enemyMaxHp = 100,
    enemyMinHp = 100,
    enemyCasters = 0,
    enemyMelee = 0,
    enemyBursting = false,

    teamHealer = nil,
    teamDps = {},
    teamMaxHp = 100,
    teamMinHp = 100,
    teamCasters = 0,
    teamMelee = 0,
    teamBursting = false,
}

-- region UTILITY

local function IsInArena()
    local inInstance, instanceType = IsInInstance()
    return inInstance and instanceType == "arena"
end

local function castTarget(unit)
    local castTargetUnit = nil
    if IsGuid(unit) then
        castTargetUnit = UnitCastingTarget(unit)
    else
        castTargetUnit = UnitCastingTarget(UnitGUID(unit))
    end
    if IsGuid(castTargetUnit) then
        return castTargetUnit
    else
        return nil
    end
end

local rosterCache  = CacheContext:getConstCacheCell()

local function getRoster()
    return rosterCache:GetOrSet("roster", function()
        local numGroupMembers = GetNumGroupMembers()
        local prefix = nil
        if IsInRaid() then
            prefix = "raid"
        else
            prefix = "party"
            numGroupMembers = numGroupMembers - 1
        end

        if not prefix or numGroupMembers <= 0 then
            if not prefix and numGroupMembers ~= 0 then
                print("Unknown group type")
            end
            return { "player" }
        end

        local roster = {}
        for i = 1, numGroupMembers do
            local unit = prefix .. i
            roster[i] = unit
        end
        return roster
    end)
end

local function getEnemies()
    return rosterCache:GetOrSet("enemies", function()
        if not IsInArena() then return { "target" } end

        local enemies = {}
        for i = 1, 5 do
            local unit = "arena" .. i
            if UnitExists(unit) and not UnitIsDeadOrGhost(unit) then
                table.insert(enemies, unit)
            end

            unit = "arenapet" .. i
            if UnitExists(unit) and not UnitIsDeadOrGhost(unit) then
                table.insert(enemies, unit)
            end
        end

        return enemies
    end)
end

local function unitMoving(unit)
    return GetUnitSpeed(unit) > 0
end

local function inCombat()
    return UnitAffectingCombat("player")
end

local function getFury()
    return UnitPower("player", 17)
end

local function isHealer(unit)
    local specId = UnitSpecializationID(unit)
    return (not specId and false) or healerSpecIds[specId] or false
end

local function inMelee(unit)
    return IsSpellInRange(dhSpells.soulCarver, unit) == 1
end

local losRangeCache = CacheContext:getConstCacheCell()

local function unitLos(unit, base)
    base = base or "player"
    local key = (UnitGUID(unit) or unit) .. (UnitGUID(base) or base) .. "LOS"

    return losRangeCache:GetOrSet(key, function()
        local x1, y1, z1 = GetUnitPosition(base)
        local x2, y2, z2 = GetUnitPosition(unit)
        if not x1 or not x2 then return false end

        local hitFlags = bit.bor(0x1, 0x10, 0x100)

        local x,y,z = TraceLine(x1, y1, z1 + 2.25, x2, y2, z2 + 2.25, hitFlags)

        return x == 0 and y == 0 and z == 0
    end)
end

local function unitDistance(unit)
    local key = (UnitGUID(unit) or unit) .. "RANGE"

    return losRangeCache:GetOrSet(key, function()
        return GetDistance2D("player", unit) or 100
    end)
end

local function unitDistancePositions(x1, y1, x2, y2)
    local dx = x2 - x1
    local dy = y2 - y1
    return math.sqrt(dx * dx + dy * dy)
end

local function unitHp(unit)
    return (UnitHealth(unit) / UnitHealthMax(unit)) * 100
end

local function spellCharges(spell)
    return GetSpellCharges(spell)
end

local function CanCast(spell, charges)
    if charges and spellCharges(spell) == 0 then
        return false
    end

    local start, duration = GetSpellCooldown(spell)
    local cdLeft = (start == 0 and 0) or (start + duration - GetTime())

    return cdLeft < 0.2
end

local function getUnitClass(unit)
    return select(3, UnitClass(unit))
end

local function likelyMelee(unit)
    local class = getUnitClass(unit)
    if class == nil then return false end
    return ClassIsMelee[class] or false
end

local function isCaster(unit)
    local specId = UnitSpecializationID(unit)
    return (not specId and false) or casterSpecIds[specId] or healerSpecIds[specId] or false
end

local function isMelee(unit)
    local specId = UnitSpecializationID(unit)
    return (not specId and false) or (not casterSpecIds[specId] and not healerSpecIds[specId])  or false
end 

local function isTargettingCaste(unit)
    local targetUnitId = unit .. "target"
    return isCaster(targetUnitId) -- and UnitIsFriend(targetUnitId)
end

local function isCastingOrChanneling(unit)
    local casting, _, _, startTime, endTime, _, _, interruptable = UnitCastingInfo(unit)
    if not casting then
        casting, _, _, startTime, endTime, _, interruptable = UnitChannelInfo(unit)
    end

    if casting then
        local elapsedTime = (GetTime() * 1000) - startTime
        return casting, elapsedTime, interruptable, endTime
    end

    return casting or false
end

local function calculateNewPosition(x, y, z, angle, speed, time)
    -- Calculate the change in position
    local dx = speed * time * math.cos(angle)
    local dy = speed * time * math.sin(angle)
    local dz = 0 -- Assuming no change in the z-axis

    -- Update the position
    local newX = x + dx
    local newY = y + dy
    local newZ = z + dz

    return newX, newY, newZ
end

local function calculateStrafeOffset(unit)
    local flags = GetUnitMovementFlags(unit)

    if not flags or flags == 0 then return 0 end

    flags = bit.band(flags, 15)

    if flags == 9 then
        return 45
    elseif flags == 8 then
        return 90
    elseif flags == 5 then
        return -45
    elseif flags == 4 then
        return -90
    end

    return 0
end

local function predictTargetPostion(unit)
    local playerX, playerY, playerZ = GetUnitPosition(unit)
    local facing = UnitFacing(unit)
    local speed = GetUnitSpeed(unit)
    if not facing or not speed then return end

    local strafeOffset = calculateStrafeOffset(unit)
    strafeOffset = strafeOffset * (math.pi / 180)

    return calculateNewPosition(playerX, playerY, playerZ, facing - strafeOffset, speed, 0.5)
end

local function CastAtPosition(oX, oY, oZ, spell)
    Cast(spell)
    local i = -100
    local mouselookup = IsMouseButtonDown(2)
    if mouselookup then MouselookStop() end
    while SpellIsTargeting() and i <= 100 do
        ClickPosition(oX, oY, oZ)
        i = i + 1
        oZ = i
    end
    if mouselookup then MouselookStart() end
    if i >= 100 and SpellIsTargeting() then
        SpellStopTargeting()
    end

    return not SpellIsTargeting()
end

local function CastGroundSpeed(spell, target, withPrediction)
    local oX, oY, oZ = GetUnitPosition(target)
    local speed = GetUnitSpeed(target)
    if speed > 0 and withPrediction then
        oX, oY, oZ = predictTargetPostion(target)
    end

    return CastAtPosition(oX, oY, oZ, spell)
end

local function CastGroundFallback(spell, target)
    return CastGroundSpeed(spell, target, true) or CastGroundSpeed(spell, target, false)
end

local function CalculateFacingAngle(playerX, playerY, targetX, targetY)
    local deltaY = targetY - playerY
    local deltaX = targetX - playerX
    local angle = math.atan2(deltaY, deltaX)

    -- convert from radians to degrees
    angle = angle * (180 / math.pi)

    -- adjust for game orientation (0 degrees is south, 90 is west)
    angle = angle - 90
    if angle < 0 then
        angle = 360 + angle
    end

    return angle
end

local function facehack(target)
    if target then
        local playerX, playerY, playerZ = GetUnitPosition("player")
        local targetX, targetY, targetZ = GetUnitPosition(target)
        local angle = CalculateFacingAngle(playerX, playerY, targetX, targetY)
        if true or not UnitFacing(target) then
            local radians = (angle + 90) * (math.pi / 180)
            FaceDirection(radians, true)
        end
    end
end

local function isTargetImmune(target)
    if not unitLos(target) then return true end
    return hasBuffInList(target, totalImmunityList)
end

local function targetHasDamageReduction(target)
    if not unitLos(target) then return true end
    return hasBuffInList(target, damageReduction)
end

local function targetIsBursting(target)
    return hasBuffInList(target, burstlist)
end

local function inBreakableCC(target)
    if not unitLos(target) then return true end
    for _, cc in pairs(breakableCC) do
        if hasDebuff(target, cc) then
            return true
        end
    end

    return false
end

local function isTargetImmuneRemaining(target, debuffRemaain, buffRemain)
    if not unitLos(target) then return true end
    for _, immunity in pairs(totalImmunityList) do
        local remaining = hasDebuffRemaining(target, immunity)
        if remaining > debuffRemaain then
            return true
        end

        remaining = hasDebuffRemaining(target, immunity)
        if remaining > buffRemain then
            return true
        end
    end

    return false
end

local function rooted(target)
    return hasDebuffInList(target, cantRootList)
end

local draw = Draw:New()

local function targetDraw()
    if UnitExists("target") then
        local playerX, playerY, playerZ = GetUnitPosition("player")
        local targetX, targetY, targetZ = GetUnitPosition("target")
        draw:SetColor(255, 0, 0, 255)

        if unitDistance("target") < 30 and unitLos("target") then
            draw:SetColor(0, 255, 0, 255)
        end

        draw:Line(playerX, playerY, playerZ, targetX, targetY, targetZ)
    end
end

local purgeTarget = function (target)
    if not CanCast(dhSpells.consumeMagic) then return false end

    return hasBuffInList(target, purgeList) and Cast(dhSpells.consumeMagic)
end

local lastMovedTime = GetTime()

local function trackMovement()
    local movementType = GetUnitMovementFlags("player")

    if movementType ~= 0 then
        lastMovedTime = GetTime()
    end
end

C_Timer.NewTicker(0.01, trackMovement)

local function getStayingTime()
    return GetTime() - lastMovedTime
end

-- endregion UTILITY

local healthStone = GetItemInfo(5512)

local useHealthStone = function ()
    local cooldown = GetItemCooldown(healthStone)
    if cooldown > 0 then return false end

    if not hasBuff("player", affliSpells.soulBurn) and CanCast(affliSpells.soulBurn) then
        return Cast(affliSpells.soulBurn)
    end

    return UseItemByName(healthStone) or true
end

local defensives = function (player)
    local health = unitHp(player)
    local hasDefensive = hasBuff(player, affliSpells.unendingResolve) or hasBuff(player, affliSpells.darkPact)

    -- if health < 50 and useHealthStone() then
    --     return true
    -- end

    if CanCast(affliSpells.darkPact) and ((globalState.enemyBursting and not hasDefensive and health < 80)
            or health < 60) then
        return Cast(affliSpells.darkPact)
    end

    if CanCast(affliSpells.unendingResolve) and ((globalState.enemyBursting and not hasDefensive and health < 60)
            or health < 40) then
        return Cast(affliSpells.unendingResolve)
    end

    return false
end

local kickPercent = 40
local channelKickTime = 600

local function generateNewRandomKicks()
    kickPercent = math.random(40, 80)
    channelKickTime = math.random(500, 700)

    return C_Timer.After(math.random(1.5, 3), generateNewRandomKicks)
end

generateNewRandomKicks()

local kickAnyone = function ()
    local enemies = getEnemies()

    for _, enemy in ipairs(enemies) do
        if isCastingOrChanneling(enemy) and unitLos(enemy) and UnitIsPlayer(enemy) then
            local casting, elapsed, notinterrupt, endTime = isCastingOrChanneling(enemy)
            if casting then
                if interruptListLookup[casting] then
                    if elapsed > channelKickTime and not notinterrupt  and CanCast(dhSpells.disrupt) and unitDistance(enemy) <= 10 and isTargetImmune(enemy) == false then
                        return Cast(dhSpells.disrupt, enemy)
                    elseif glimpseLookup[casting] then
                        local timeLeft = endTime - (GetTime() * 1000)
                        local target = castTarget(enemy)
                        print('CC is being cast and we can avoid it taget is' .. (target or 'nil'))
                        if target and target == UnitGUID("player") then
                            if timeLeft < 150 and IsPlayerSpell(dhTalents.glimpse) and CanCast(dhSpells.vengfulRetreat) then
                                print('We can retreat out of this!')
                                return Cast(dhSpells.vengfulRetreat)
                            end
                            print('CC being cast on me')
                        end
                    end
                end
            end
        end
    end
end

local function loadGlobalState()
    globalState = {
        enemyHealer = nil,
        enemyLowestPlayer = nil,
        enemyDps = {},
        enemyCanHit = {},
        enemyCanHitBursting = {},
        enemyMaxHp = 0,
        enemyMinHp = 100,
        enemyCasters = 0,
        enemyMelee = 0,
        enemyBursting = false,

        teamHealer = nil,
        teamDps = {},
        teamMaxHp = 0,
        teamMinHp = 100,
        teamCasters = 0,
        teamMelee = 0,
        teamBursting = false,
    }

    local enemies = getEnemies()
    for _, enemy in ipairs(enemies) do
        if UnitExists(enemy) and not UnitIsDeadOrGhost(enemy) and not IsInArena() or UnitIsPlayer(enemy) then
            local hp = unitHp(enemy)

            if hp > globalState.enemyMaxHp then
                globalState.enemyMaxHp = hp
            end

            if hp < globalState.enemyMinHp then
                globalState.enemyMinHp = hp
                globalState.enemyLowestPlayer = enemy
            end

            -- Throw at healer if we can :D
            if isHealer(enemy) then
                globalState.enemyHealer = enemy
            else
                table.insert(globalState.enemyDps, enemy)

                if targetIsBursting(enemy) then
                    globalState.enemyBursting = true

                    if unitDistance(enemy) <= 40 and unitLos(enemy) and not inBreakableCC(enemy) and not isTargetImmune(enemy) then
                        table.insert(globalState.enemyCanHitBursting, enemy)
                    end
                end

                if isCaster(enemy) then
                    globalState.enemyCasters = globalState.enemyCasters + 1
                else
                    globalState.enemyMelee = globalState.enemyMelee + 1
                end
            end

            if unitDistance(enemy) <= 40 and unitLos(enemy) and not inBreakableCC(enemy) and not isTargetImmune(enemy) then
                table.insert(globalState.enemyCanHit, enemy)
            end
        end
    end

    local roster = getRoster()
    for _, team in ipairs(roster) do
        if UnitIsPlayer(team) then
            local hp = unitHp(team)

            if hp > globalState.teamMaxHp then
                globalState.teamMaxHp = hp
            end

            if hp < globalState.teamMinHp then
                globalState.teamMinHp = hp
            end

            -- Throw at healer if we can :D
            if isHealer(team) then
                globalState.teamHealer = team
            else
                table.insert(globalState.teamDps, team)

                if targetIsBursting(team) then
                    globalState.teamBursting = true
                end

                if isCaster(team) then
                    globalState.teamCasters = globalState.teamCasters + 1
                else
                    globalState.teamMelee = globalState.teamMelee + 1
                end
            end
        end
    end
end

local function applySpellIfDontHave(spell)
    if not CanCast(spell) then return false end

    for _, enemy in ipairs(globalState.enemyCanHit) do
        if not hasDebuff(enemy, spell) then
            facehack(enemy)
            return Cast(spell, enemy)
        end
    end
end

local function refreshIfLow()
    for _, enemy in ipairs(globalState.enemyCanHit) do
        if not hasDebuff(enemy, spell) then
            return Cast(spell, enemy)
        end
    end
end

local function rotation()
    local player = "player"
    local target = "target"



    loadGlobalState()

    if UnitIsDeadOrGhost(player) then return end

    -- if not UnitExists(target) then return end
    -- if not UnitCanAttack(player, target) or unitDistance(target) >= 40 then return end
    if UnitExists(target) and not UnitCanAttack(player, target) then return end

    if defensives(player) then return true end

    -- Refresh current agonys 
    for _, enemy in ipairs(globalState.enemyCanHit) do
        local agonyRemain = hasDebuffRemaining(enemy, affliSpells.agony)
        if agonyRemain > 0 and agonyRemain < 4 and CanCast(affliSpells.agony) then
            facehack(enemy)
            return Cast(affliSpells.agony, enemy)
        end
    end

    if hasBuff(player, affliBuffs.tormentedCrescendo) and CanCast(affliSpells.maleficRapture) then
        return Cast(affliSpells.maleficRapture)
    end

    local playerCasting, _, _, endTime = isCastingOrChanneling(player)

    if playerCasting then
        return false
    end

    -- Use curse to insta spread agony and corruption
    local soulShards = UnitPower(player, 7)
    if soulShards > 0 then
        for _, enemy in ipairs(globalState.enemyCanHit) do
            if not hasDebuff(enemy, affliSpells.corruption) and not hasDebuff(enemy, affliSpells.agony) then
                local specId = UnitSpecializationID(enemy)
                local curse = (specId and SpecPeelList[specId]) or affliSpells.curseOfExhaustion

                if CanCast(curse) then
                    facehack(enemy)
                    return Cast(curse, enemy)
                end
            end
        end
    end

    local stayingTime = getStayingTime()

    if stayingTime > 0.2 and CanCast(affliSpells.drainLife) then
        local potentialTarget = nil
        local hasSoulRot = false
        for _, enemy in ipairs(globalState.enemyCanHit) do
            if hasDebuff(enemy, affliSpells.soulRot) then
                hasSoulRot = true
            else
                potentialTarget = enemy
            end
        end

        if hasSoulRot and potentialTarget then
            facehack(potentialTarget)
            return Cast(affliSpells.drainLife, potentialTarget)
        end
    end

    local playerHealth = unitHp(player)
    if playerHealth < 80 and CanCast(affliSpells.mortalCoil) then
        for _, enemy in ipairs(globalState.enemyCanHit) do
            facehack(enemy)
            return Cast(affliSpells.mortalCoil, enemy)
        end
    end

    -- Put unstable affliction onto players they don't have it
    if stayingTime > 0.2 and applySpellIfDontHave(affliSpells.unstableAffliction) then return true end

    -- Put agony on players they don't have it
    if applySpellIfDontHave(affliSpells.agony) then return true end

    -- Put corruption on players they don't have it
    if applySpellIfDontHave(affliSpells.corruption) then return true end

    -- Put phantom singularity on players they don't have it
    if applySpellIfDontHave(affliSpells.phantomSingularity) then return true end

    -- Put siphon life onto players they don't have it
    if applySpellIfDontHave(affliSpells.siphonLife) then return true end

    if globalState.enemyMinHp < 50 then
        if CanCast(affliSpells.darkGlare) then
            return Cast(affliSpells.darkGlare)
        end

        if stayingTime > 0.2 and globalState.enemyMinHp < 20 and globalState.enemyLowestPlayer and unitLos(globalState.enemyLowestPlayer) and CanCast(affliSpells.drainSoul) then
            facehack(globalState.enemyLowestPlayer)
            return Cast(affliSpells.drainSoul, globalState.enemyLowestPlayer)
        end

        if stayingTime > 0.2 and soulShards > 0 and globalState.enemyMinHp < 30 and CanCast(affliSpells.maleficRapture) then
            return Cast(affliSpells.maleficRapture)
        end
    end

    if stayingTime > 0.2 and CanCast(affliSpells.soulRot) then
        for _, enemy in ipairs(globalState.enemyCanHit) do
            facehack(enemy)
            return Cast(affliSpells.soulRot, enemy)
        end
    end

    if stayingTime > 0.2 and CanCast(affliSpells.drainSoul) then
        for _, enemy in ipairs(globalState.enemyCanHit) do
            facehack(enemy)
            return Cast(affliSpells.drainSoul, enemy)
        end
    end

    return false
end

local function visualRender()
    draw:ClearCanvas()

    targetDraw()

    return C_Timer.After(0.05, visualRender)
end

local function mainLoop()
    CacheContext:resetCache()
    rotation()

    return C_Timer.After(math.random(0.08, 0.15), mainLoop)
end

local _, _, homeLatency, worldLatency = GetNetStats()
local TargetSQW = (worldLatency + 20)
if SQW ~= TargetSQW then
    print('Set SQW to ' .. TargetSQW)
    SetCVar("SpellQueueWindow", TargetSQW)
end

local function Main()
    print("Jack Affli Loaded now")
    WASD_Utility.TrackDR()

    mainLoop()
end

local function LoadAddon()
  if IsInWorld() then
    print("Loading Addon...")
    C_Timer.After(1, Main)
    C_Timer.After(1, visualRender)
    return
  end
  C_Timer.After(0.04, LoadAddon)
end

LoadAddon()