local dmc = ...

if (not dmc) or (not dmc.IsInWorld) then
    return
end

local unlockList = {
    "ClassId",
    "CastSpellByName",
    "UnitCanAttack",
    "IsSpellInRange",
    "UnitHealth",
    "UnitExists",
    "UnitIsPlayer",
    "UnitIsDeadOrGhost",
    "UnitAura"
}

local localenv = setmetatable(
    {},
    {
        __index = function(self, func)
            return dmc[func] or _G[func]
        end
    }
)

for i = 1, #unlockList do
    local funcname = unlockList[i]
    local func = _G[funcname]
    localenv[funcname] = function(...) return dmc.SecureCode(func, ...) end
end

setfenv(1, localenv)

local draw = Draw:New()

local function targetDraw()

end

local pairs = pairs
local ipairs = ipairs

local function getRuneCount()
    local count = 0
    for i = 1, 6 do
        local start, duration, runeReady = GetRuneCooldown(i)
        if runeReady then
            count = count + 1
        end
    end

    return count
end

local activePopups = {}
local ICON_SIZE = 36
local OFFSET_X = -100
local OFFSET_Y = 250

local function ShowSpellPopup(spellID, message)
    for _, popup in ipairs(activePopups) do
        if popup.text:GetText() == message then
            return
        end
    end

    local popup = CreateFrame("Frame", "SpellPopupFrame" .. #activePopups + 1, UIParent)
    table.insert(activePopups, popup)
    
    popup:SetSize(ICON_SIZE, ICON_SIZE)
    if #activePopups == 1 then
        popup:SetPoint("TOPLEFT", UIParent, "CENTER", OFFSET_X, OFFSET_Y)
    else
        popup:SetPoint("TOP", activePopups[#activePopups - 1], "BOTTOM", 0, -10)
    end
    
    local texture = popup:CreateTexture(nil, "BACKGROUND")
    texture:SetAllPoints()
    texture:SetTexture(GetSpellTexture(spellID))
    
    local spellName = GetSpellInfo(spellID)
    
    popup.text = popup:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    popup.text:SetPoint("LEFT", texture, "RIGHT", 10, 0)
    popup.text:SetText(message)
    
    C_Timer.After(3, function()
        popup:Hide()
        for i, p in ipairs(activePopups) do
            if p == popup then
                table.remove(activePopups, i)
                break
            end
        end
        for i, p in ipairs(activePopups) do
            if i == 1 then
                p:SetPoint("TOPLEFT", UIParent, "CENTER", OFFSET_X, OFFSET_Y)
            else
                p:SetPoint("TOP", activePopups[i - 1], "BOTTOM", 0, -10)
            end
        end
    end)
end

local CacheSection = {}
CacheSection.__index = CacheSection

local rawget, rawset = rawget, rawset
local setmetatable = setmetatable

local cacheIndex = function(self, key)
    local property = rawget(self, key)
    if property ~= nil then
        return property
    end

    return CacheSection[key]
end

function CacheSection:new(duration)
    local cacheSection = {
        dirty = false,
        data = {},
        lastUpdate = 0,
        duration = duration,
    }

    setmetatable(cacheSection, { __index = cacheIndex })
    self.__index = self

    return cacheSection
end

function CacheSection:newWeak()
    local weakData = {}
    setmetatable(weakData, { __mode = "v" })

    local cacheSection = {
        dirty = false,
        data = weakData,
    }

    setmetatable(cacheSection, self)
    self.__index = self

    return cacheSection
end

function CacheSection:Get(key)
    if rawget(self, "dirty") then
        return nil
    end

    return rawget(self, "data")[key]
end

function CacheSection:GetOrSet(key, callback)
    local dirty = rawget(self, "dirty")

    if dirty then
        rawset(self, "data", {})
        rawset(self, "dirty", false)
    end

    local data = rawget(self, "data")

    local found = data[key]
    if found ~= nil then
        return found
    end

    local value = callback()
    data[key] = value

    return value
end

local CacheContext = {
    cacheIdx = 0,
    cache = {},

    constCacheIdx = 0,
    constCache = {},     -- These are cache items which aren't cleared between calls

    perCombatIdx = 0,    -- These again are const but we just only dirty the cache when out of combat
    perCombatCache = {}, -- These are only reset when we're out of combat

    weakCache = CacheSection:newWeak(),

    getCell = function(self)
        local toReturn = self.cache[self.cacheIdx]
        if toReturn ~= nil then
            toReturn.dirty = true
            self.cacheIdx = self.cacheIdx + 1

            return toReturn
        end

        local newCacheObject = CacheSection:new()
        self.cache[self.cacheIdx] = newCacheObject
        self.cacheIdx = self.cacheIdx + 1

        return newCacheObject
    end,

    getConstCacheCell = function(self)
        local newCacheObject = CacheSection:new()
        self.constCache[self.constCacheIdx] = newCacheObject
        self.constCacheIdx = self.constCacheIdx + 1

        return newCacheObject
    end,

    getCombatCacheCell = function(self)
        local newCacheObject = CacheSection:new()
        self.perCombatCache[self.perCombatIdx] = newCacheObject
        self.perCombatIdx = self.perCombatIdx + 1

        return newCacheObject
    end,

    resetCache = function(self)
        self:dirtyAllConstCells()
        self.cacheIdx = 0
    end,

    dirtyAllCells = function(self)
        for i = 0, self.cacheIdx - 1 do
            self.cache[i].dirty = true
        end
    end,

    dirtyAllConstCells = function(self)
        local time = GetTime()
        for i = 0, self.constCacheIdx - 1 do
            local cell = self.constCache[i]
            local resetTime = rawget(cell, "duration")

            if resetTime then
                local lastUpdate = rawget(cell, "lastUpdate")
                if lastUpdate == 0 or time - lastUpdate > resetTime then
                    cell.dirty = true
                    cell.lastUpdate = time
                end
            else
                cell.dirty = true
            end
        end
    end
}

local globalState = {
    enemyHealer = nil,
    enemyDps = {},
    enemyCanHit = {},
    enemyMaxHp = 100,
    enemyMinHp = 100,
    enemyCasters = 0,
    enemyMelee = 0,
    enemyBursting = false,

    teamHealer = nil,
    teamDps = {},
    teamTargets = {},
    teamMaxHp = 100,
    teamMinHp = 100,
    teamCasters = 0,
    teamMelee = 0,
    teamBursting = false,
    teamHasRogue = false,
}

CacheContext = CacheContext

local interruptList = {
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
    convoke = GetSpellInfo(323764),
    penance = GetSpellInfo(186723),
    repentance = GetSpellInfo(20066),
    ringOfFrost = GetSpellInfo(113724),
    cyclone = GetSpellInfo(33786),
    mindControl = GetSpellInfo(605),
    songOfChiji = GetSpellInfo(198898),
    fear = GetSpellInfo(5782),
    hibernate = GetSpellInfo(2637),
    banish = GetSpellInfo(710),
    turnEvil = GetSpellInfo(10326),
    sleepWalk = GetSpellInfo(360806),
    summonFelhunter = GetSpellInfo(691),
    summonImp = GetSpellInfo(688),
    summonSuccubus = GetSpellInfo(712),
    massDispel = GetSpellInfo(32375),
    shiftingPower = GetSpellInfo(314791),
    revivePet = GetSpellInfo(982),
    stormkeeper = GetSpellInfo(191634),
    kindredSpirits = GetSpellInfo(326434),
    drainLife = GetSpellInfo(234153),
    chaosBolt = GetSpellInfo(116858),
    greaterPyro = GetSpellInfo(203286),
    glacialSpike = GetSpellInfo(199786),
    soulFire = GetSpellInfo(6353),
    unstableAffliction = GetSpellInfo(342938),
    summonTyrant = GetSpellInfo(265187),
    eyeBeam = GetSpellInfo(198013),
    voidTorrent = GetSpellInfo(263165),
    soothingMist = GetSpellInfo(115175),
    vampiricTouch = GetSpellInfo(34914),
    handofGuldan = GetSpellInfo(105174),
    arcaneSurge = GetSpellInfo(365350),
    stellarFlare = GetSpellInfo(202347),
    nullifyingShroud = GetSpellInfo(378464),
    eternitySurge = GetSpellInfo(382411),
    disintegrate = GetSpellInfo(356995), --not sure if worth
    fullMoon = GetSpellInfo(274283),
    halfMoon = GetSpellInfo(274282),
    tyrsDeliverance = GetSpellInfo(200652),
    shadowFury = GetSpellInfo(30283),
    ringOfFire = GetSpellInfo(353082), --not sure if worth
    rayOfFrost = GetSpellInfo(205021),
    mindGames = GetSpellInfo(375901),
    summonfelguard = GetSpellInfo(30146),
    frostBomb = GetSpellInfo(390612),  
    callDreadStalkers = GetSpellInfo(104316),  
    fireBall = GetSpellInfo(133),  
    frostBolt = GetSpellInfo(116),
    ebonMight = GetSpellInfo(395152),
    upheavel = GetSpellInfo(396286),
    drainLife = GetSpellInfo(234153),
    maleficRapture = GetSpellInfo(324536),
}
local interruptListLookup = {}

for k, v in pairs(interruptList) do
    interruptListLookup[v] = true
end

local totalImmunityList = {
    Cyclone = GetSpellInfo(33786),
    iceBlock = GetSpellInfo(45438),
    divineShield = GetSpellInfo(642), 
    aspectOfTheTurtle = GetSpellInfo(186265),
    imprison = GetSpellInfo(221527),
    banish = GetSpellInfo(710),
    diamondIce = GetSpellInfo(203340),
    tranquilityPVPTalent = GetSpellInfo(362486),
    cloakOfShadows = GetSpellInfo(31224),
    dispersion = GetSpellInfo(79811),
    dispersion2 = GetSpellInfo(47585),
    netherwalk = GetSpellInfo(196555),
    burrow = GetSpellInfo(409293),
    blessingOfSpellwarding = GetSpellInfo(204018),
    lifeCocoon = GetSpellInfo(116849),
    spellReflect = GetSpellInfo(23920),
    netherWard = GetSpellInfo(212295),
    evasion = GetSpellInfo(5277),

    -- Disorient effects
    blindingSleet = GetSpellInfo(207167),
    dragonsBreath = GetSpellInfo(31661),
    blind = GetSpellInfo(2094),
    fear = GetSpellInfo(118699),
    howlOfTerror = GetSpellInfo(5484),
    intimidatingShout1 = GetSpellInfo(5246),
    intimidatingShout2 = GetSpellInfo(316593),
    intimidatingShout3 = GetSpellInfo(316595),

    -- Incapacitate effects
    hibernate = GetSpellInfo(2637),
    incapacitatingRoar = GetSpellInfo(99),
    freezingTrap = GetSpellInfo(3355),
    freezingTrapHonorTalent = GetSpellInfo(203337),
    scatterShot = GetSpellInfo(213691),
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    ringOfFrost = GetSpellInfo(82691),
    repentance = GetSpellInfo(20066),
    shackleUndead = GetSpellInfo(9484),
    sap = GetSpellInfo(6770),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
}

local MakuluFramwork = {}

local frameworkContext = {
    cache = CacheContext,
    dmc = dmc,
}

print('Loading framework files...')
local res1, res2 = RequireFile("./Framework/Framework.lua", frameworkContext)
if res1 then
    MakuluFramwork = res2
    print('Framework loaded!')
else
    print('Failed to load framework!')
    print(res1, res2)
end

local kickPercent = 40
local channelKickTime = 600

local spell = MakuluFramwork.Spell
local unit = MakuluFramwork.Unit
local frameworkClear = MakuluFramwork.clearCache

local function generateNewRandomKicks()
    kickPercent = math.random(40, 80)
    channelKickTime = math.random(500, 700)

    return C_Timer.After(math.random(1.5, 3), generateNewRandomKicks)
end

generateNewRandomKicks()

local function getRuneCount()
    return UnitPower("player", 19)
end

local function isCastingOrChanneling(unit)
    local unit = unit.guid or unit
    local casting, _, _, startTime, endTime, _, _, interruptable = UnitCastingInfo(unit)
    if not casting then
        casting, _, _, startTime, endTime, _, interruptable = UnitChannelInfo(unit)
    end

    if casting then
        local elapsedTime = (GetTime() * 1000) - startTime
        return casting, elapsedTime, interruptable, endTime
    end

    return casting or false
end

local function IsInArena()
    local inInstance, instanceType = IsInInstance()
    return inInstance and instanceType == "arena"
end

local function UnitImmmue(unit)
    return unit:HasBuffInList(totalImmunityList) or unit:HasDeBuffInList(totalImmunityList)
end

local rosterCache  = CacheContext:getConstCacheCell()

local function getRoster()
    return rosterCache:GetOrSet("roster", function()
        local numGroupMembers = GetNumGroupMembers()
        local prefix = nil
        if IsInRaid() then
            prefix = "raid"
        else
            prefix = "party"
            numGroupMembers = numGroupMembers - 1
        end

        if not prefix or numGroupMembers <= 0 then
            if not prefix and numGroupMembers ~= 0 then
                print("Unknown group type")
            end
            return { unit:new("player") }
        end

        local roster = {}
        for i = 1, numGroupMembers do
            local unitType = unit:new(prefix .. i)
            roster[i] = unitType
        end
        return roster
    end)
end

local function getEnemies()
    return rosterCache:GetOrSet("enemies", function()
        if not IsInArena() then return { "target" } end

        local enemies = {}
        for i = 1, 3 do
            local unit = "arena" .. i
            if UnitExists(unit) and not UnitIsDeadOrGhost(unit) then
                table.insert(enemies, unit)
            end

            unit = "arenapet" .. i
            if UnitExists(unit) and not UnitIsDeadOrGhost(unit) then
                table.insert(enemies, unit)
            end
        end

        return enemies
    end)
end



local fireBreath = spell:new(357208)
local livingFlame = spell:new(361469)
local disintegrate = spell:new(356995)
local eternitySurge = spell:new(359073)
local azureStrike = spell:new(362969)
local shatteringStar = spell:new(370452)
local deepBreath = spell:new(357210)
local tipTheScales = spell:new(370553)

local verdantEmbrace = spell:new(360995)
local emeraldBloom = spell:new(355913)

local quell = spell:new(3513438, {
    offGcd = true,
})

local obsidianScales = spell:new(363916, {
    offGcd = true,
})

local buffs = {
    burnout = 375802,
    essenceBurst = 359618,
    hover = 358267
}

local deBuffs = {
    gripOfDeath = 273977,
    bloodPlague = 55078,
}

local magicNumber = bit.bor(16777216, 262144, 8388608)
local function isLoc()
    local unitFlags = UnitFlags("player") or 0

    if bit.band(unitFlags, magicNumber) > 0 then
        return true
    end
end

local function loadGlobalState()
    globalState = {
        enemyHealer = nil,
        enemyDps = {},
        enemyCanHit = {},
        enemyMaxHp = 0,
        enemyMinHp = 100,
        enemyCasters = 0,
        enemyMelee = 0,
        enemyBursting = false,

        teamHealer = nil,
        teamDps = {},
        teamTargets = {},
        teamMaxHp = 0,
        teamMinHp = 100,
        teamCasters = 0,
        teamMelee = 0,
        teamBursting = false,
        teamHasRogue = false
    }

    local enemies = getEnemies()
    local inArena = IsInArena()
    for _, enemy in ipairs(enemies) do
        if not UnitIsDeadOrGhost(enemy) and (not inArena or UnitIsPlayer(enemy)) then
            local enemyUnit = unit:new(enemy)
            local hp = enemyUnit.health

            if hp > globalState.enemyMaxHp then
                globalState.enemyMaxHp = hp
            end

            if hp < globalState.enemyMinHp then
                globalState.enemyMinHp = hp
            end

            -- Throw at healer if we can :D
            if enemyUnit.isHealer then
                globalState.enemyHealer = enemy
            else
                table.insert(globalState.enemyDps, enemy)

                if enemy.bursting then
                    globalState.enemyBursting = true
                end

                if enemyUnit.isCaster then
                    globalState.enemyCasters = globalState.enemyCasters + 1
                else
                    globalState.enemyMelee = globalState.enemyMelee + 1
                end
            end

            if enemyUnit.distance <= 40 and enemyUnit.los and not enemyUnit.inBreakableCC and not enemyUnit.immune then
                table.insert(globalState.enemyCanHit, enemy)
            end
        end
    end

    local roster = getRoster()
    for _, teamUnit in ipairs(roster) do
        if teamUnit.player then
            local hp = teamUnit.health

            if hp > globalState.teamMaxHp then
                globalState.teamMaxHp = hp
            end

            if hp < globalState.teamMinHp then
                globalState.teamMinHp = hp
            end

            -- Throw at healer if we can :D
            if teamUnit.isHealer then
                globalState.teamHealer = teamUnit
            else
                table.insert(globalState.teamDps, teamUnit)

                local currentTarget = UnitTarget(teamUnit.guid)
                if currentTarget then
                    globalState.teamTargets[currentTarget] = true
                end

                if teamUnit.bursting then
                    globalState.teamBursting = true
                end

                if teamUnit.isCaster then
                    globalState.teamCasters = globalState.teamCasters + 1
                else
                    globalState.teamMelee = globalState.teamMelee + 1
                end

                local _, _, classId = UnitClass(teamUnit.guid)
                if classId == 4 then
                    globalState.teamHasRogue = true
                end
            end
        end
    end
end

local GetEmpowerStageForCurrent = function()
    local unit = "player"
    local name, _, _, start, endTime, _, _, _, _, numStages = UnitChannelInfo(unit);

    if not name or not numStages or numStages == 0 then
        return 0, nil
    end

    local stageStart = start
    for i = 0, numStages - 1, 1 do
        local duration = GetUnitEmpowerStageDuration(unit, i)
        local ourStageStart = stageStart

        stageStart = stageStart + duration

        if stageStart / 1000 > GetTime() then
            return i, name, (((ourStageStart + 50) / 1000) > GetTime())
        end
    end

    return numStages + 1, name
end

local handleStages = function (icon)
    local empowerStage, spell, holdLonger = GetEmpowerStageForCurrent()

    if holdLonger then
        return spell, false
    end

    if empowerStage > 0 then
       return spell, true
    end

    return spell, false
end


local function defensives()
    local roster = getRoster()
    local me = unit:new("player")

    if me.health < 80 then
        if verdantEmbrace:Cast(me) then return end
    end

    if me.health < 60 then
        if emeraldBloom:Cast(me) then return end
    end

    if me.health < 50 and obsidianScales:ReadyToUse(me) then
        if obsidianScales:Cast(me) then return end
    end

end

local kickAnyone = function ()
    local enemies = getEnemies()

    for _, enemy in ipairs(enemies) do
        if isCastingOrChanneling(enemy) and enemy.los and UnitIsPlayer(enemy) then
            local casting, elapsed, notinterrupt, endTime = isCastingOrChanneling(enemy)
            if casting then
                if interruptListLookup[casting] then
                    if elapsed > channelKickTime and not notinterrupt and quell:ReadyToUse(enemy) and enemy.distance <= 15 and not UnitImmmue(enemy) then
                        ShowSpellPopup(quell.spellId, "Kicking " .. casting .. " by " .. enemy)
                        return quell:Cast(enemy)
                    end
                end
            end
        end
    end
end

local stopped = false
local stopTime = GetTime()

local function rotation()
    local me      = unit:new("player")
    local target  = unit:new("target")
    local runes   = getRuneCount()

    local spell, holdLonger = handleStages()
    if spell and not holdLonger then
        CastSpellByName(spell)
        return
    end

    if IsMounted() then return end

    if isLoc() then
        ShowSpellPopup(quell.guid, "We're in cc")
        return
    end

    if not target.canAttack then return end
    if me.casting then return end

    local imStopped = stopped and (GetTime() - stopTime) > 0.2
    local canRegularCast = imStopped or me:HasBuff(buffs.hover)

    loadGlobalState()

    if me:HasBuff(shatteringStar.name) then
        if eternitySurge:Cast(target) then return end
    end

    if kickAnyone() then return end
    if defensives() then return end

    if me:HasBuff(buffs.burnout) then
        if livingFlame:Cast(target) then return end
    end

    if target.distance > 15 and target.distance < 30 then
        if deepBreath:CastGround(target) then return end
    end

    if fireBreath:ReadyToUse(me) then
        if target.distance <= 20 and not me:HasBuff(tipTheScales.name) and tipTheScales:Cast(me) then return end
    end

    if target.distance <= 20 and (me:HasBuff(tipTheScales.name) or imStopped) and fireBreath:Cast(me) then return end

    if me:HasBuff(buffs.essenceBurst) or runes >= 3 then
        if canRegularCast and disintegrate:Cast(target) then return end
    end

    if imStopped and eternitySurge:ReadyToUse(target) then
        if shatteringStar:Cast(target) then return end
        if eternitySurge:Cast(target) then return end
    end

    if canRegularCast and livingFlame:Cast(target) then return end
    if azureStrike:Cast(target) then return end
end

local totemTracker = {}

local function trackTotems()
    totemsToHit = {}
    for i = 1, GetObjectCount(), 1 do
        local guid = GetObjectWithIndex(i)
        -- Remove 5 after done testing
        if UnitCreatureTypeId(guid) == 11 then
            if UnitCanAttack("player", guid) then
                table.insert(totemsToHit, guid)
            end

            if not totemTracker[guid] then
                totemTracker[guid] = GetTime()
            end
        end
    end

    return C_Timer.After(0.5, trackTotems)
end

local function trackerCleanup()
    local now = GetTime()
    for k, v in pairs(totemTracker) do
        if now - v > 20 then
            totemTracker[k] = nil
        end
    end

    return C_Timer.After(10, trackerCleanup)
end

local function visualRender()
    draw:ClearCanvas()

    targetDraw()

    return C_Timer.After(0.05, visualRender)
end

local function mainLoop()
    CacheContext:resetCache()
    frameworkClear()

    if GetUnitSpeed("player") == 0 then
        if not stopped then
            stopTime = GetTime()
            stopped = true
        end
    else
        stopped = false
    end

    if UnitExists("target") then
        rotation()
    end

    return C_Timer.After(math.random(0.08, 0.15), mainLoop)
end

local function doLatency()
    local _, _, homeLatency, worldLatency = GetNetStats()
    local TargetSQW = (worldLatency + 20)
    if SQW ~= TargetSQW then
        SetCVar("SpellQueueWindow", TargetSQW)
    end
end

C_Timer.NewTicker(10, doLatency)

local function Main()
    print("Jack Devoker Loaded now")
    -- WASD_Utility.TrackDR()


    mainLoop()
end

local function LoadAddon()
    if IsInWorld() then
        print("Loading Addon...")
        C_Timer.After(1, Main)
        C_Timer.After(1, visualRender)
        C_Timer.After(1, trackTotems)
        C_Timer.After(1, trackerCleanup)
        return
    end
    C_Timer.After(1, LoadAddon)
end

LoadAddon()
