local Context = ...

local CacheContext = Context.cache
local dmc = Context.dmc

local unlockList = {
    "ClassId",
    "CastSpellByName",
    "UnitCanAttack",
    "IsSpellInRange",
    "UnitHealth",
    "UnitExists",
    "UnitIsPlayer",
    "UnitIsDeadOrGhost",
    "UnitAura",
    "UnitCastingInfo",
    "UnitChannelInfo"
}

local localenv = setmetatable(
    {},
    {
      __index = function(self, func)
        return dmc[func] or _G[func]
      end
    }
)

for i = 1, #unlockList do
    local funcname = unlockList[i]
    local func = _G[funcname]
    localenv[funcname] = function(...) return dmc.SecureCode(func, ...) end
end
  
setfenv(1, localenv)

local healerSpecIds = {
    [65]   = true, -- Holy Paladin
    [105]  = true, -- Restoration Druid
    [256]  = true, -- Discipline Priest
    [257]  = true, -- <PERSON> Priest
    [264]  = true, -- Restoration Shaman
    [270]  = true, -- Mistweaver Monk
    [1468] = true, -- Pres evoker
}

local casterSpecIds = {
    [62]   = true, -- <PERSON><PERSON>
    [63]   = true, -- <PERSON> Mage
    [64]   = true, -- <PERSON> Mage
    [102]  = true, -- Balance Druid
    [105]  = true, -- Restoration Druid
    [258]  = true, -- Shadow Priest
    [262]  = true, -- Elemental Shaman
    [265]  = true, -- Affliction Warlock
    [266]  = true, -- Demonology Warlock
    [267]  = true, -- Destruction Warlock
    [1467] = true, -- Devoker
    [1473] = true, -- Augvoker
}

local burstlist = {
    doomwinds = GetSpellInfo(384352), --doomwinds
    combustion = GetSpellInfo(190319), --combustion
    meta = GetSpellInfo(191427), --meta
    meta2 = GetSpellInfo(162264), --meta2
    shadowblades = GetSpellInfo(121471), --shadowblades
    shadowDance = GetSpellInfo(185313), --shadow dance
    shadowDance2 = GetSpellInfo(185422), --shadow dance
    trueshot = GetSpellInfo(288613), --trueshot
    coordinatedAssault = GetSpellInfo(360952), --coordinated assault
    incarnFeral = GetSpellInfo(102543), --incarn feral
    incarnElune = GetSpellInfo(103560), --incarn elune
    recklessness = GetSpellInfo(1719), --recklessness
    avatar = GetSpellInfo(107574), --avatar
    adrenalineRush = GetSpellInfo(13750), --adrenaline rush
    icyVeins = GetSpellInfo(12472), --icy veins
    pillarofFrost = GetSpellInfo(51271), --pillar of frost
    abomLimb = GetSpellInfo(383269), --abom limb
    avengingWrath = GetSpellInfo(231895), -- avenging wrath
    crusade = GetSpellInfo(384392),
    crusade2 = GetSpellInfo(231895),
    serenity = GetSpellInfo(152173), --serenity
    ascendance = GetSpellInfo(114051),
    danseMacabre = GetSpellInfo(393969),
}
local totalImmunityList = {
    Cyclone = GetSpellInfo(33786),
    iceBlock = GetSpellInfo(45438),
    divineShield = GetSpellInfo(642), 
    aspectOfTheTurtle = GetSpellInfo(186265),
    imprison = GetSpellInfo(221527),
    banish = GetSpellInfo(710),
    diamondIce = GetSpellInfo(203340),
    tranquilityPVPTalent = GetSpellInfo(362486),
    cloakOfShadows = GetSpellInfo(31224),
    dispersion = GetSpellInfo(79811),
    dispersion2 = GetSpellInfo(47585),
    netherwalk = GetSpellInfo(196555),
    burrow = GetSpellInfo(409293),
    lifeCocoon = GetSpellInfo(116849),
    netherWard = GetSpellInfo(212295),
    evasion = GetSpellInfo(5277),

    -- Disorient effects
    blindingSleet = GetSpellInfo(207167),
    dragonsBreath = GetSpellInfo(31661),
    blind = GetSpellInfo(2094),
    fear = GetSpellInfo(118699),
    howlOfTerror = GetSpellInfo(5484),
    intimidatingShout1 = GetSpellInfo(5246),
    intimidatingShout2 = GetSpellInfo(316593),
    intimidatingShout3 = GetSpellInfo(316595),

    -- Incapacitate effects
    hibernate = GetSpellInfo(2637),
    incapacitatingRoar = GetSpellInfo(99),
    freezingTrap = GetSpellInfo(3355),
    freezingTrapHonorTalent = GetSpellInfo(203337),
    scatterShot = GetSpellInfo(213691),
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    ringOfFrost = GetSpellInfo(82691),
    repentance = GetSpellInfo(20066),
    shackleUndead = GetSpellInfo(9484),
    sap = GetSpellInfo(6770),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
}
local breakableCC = {
    -- Disorient effects
    blindingSleet = GetSpellInfo(207167),
    dragonsBreath = GetSpellInfo(31661),
    blind = GetSpellInfo(2094),
    fear = GetSpellInfo(118699),
    howlOfTerror = GetSpellInfo(5484),
    intimidatingShout1 = GetSpellInfo(5246),
    intimidatingShout2 = GetSpellInfo(316593),
    intimidatingShout3 = GetSpellInfo(316595),
    sigilMisery = GetSpellInfo(207684),

    -- Incapacitate effects
    hibernate = GetSpellInfo(2637),
    incapacitatingRoar = GetSpellInfo(99),
    freezingTrap = GetSpellInfo(3355),
    freezingTrapHonorTalent = GetSpellInfo(203337),
    scatterShot = GetSpellInfo(213691),
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    ringOfFrost = GetSpellInfo(82691),
    repentance = GetSpellInfo(20066),
    shackleUndead = GetSpellInfo(9484),
    sap = GetSpellInfo(6770),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
}
  

local MakuluFramwork = MakuluFramwork or {}

local unitCachePool = CacheContext:getConstCacheCell()

local UnitExists = UnitExists
local UnitGUID = UnitGUID

local UnitCanAttack                                                      = UnitCanAttack

local Unit = {}
Unit.__index = Unit

local lastSpellName = nil
local lastSpellTime = 0

local losRangeCache = CacheContext:getConstCacheCell()

local function unitDistance(unit)
    local key = unit.guid .. "RANGE"

    return losRangeCache:GetOrSet(key, function()
        return GetDistance2D("player", unit.guid) or 100
    end)
end

local function unitLos(unit, base)
    base = base or "player"
    local key = unit.guid .. "LOS"

    return losRangeCache:GetOrSet(key, function()
        local x1, y1, z1 = GetUnitPosition(base)
        local x2, y2, z2 = GetUnitPosition(unit.guid)
        if not x1 or not x2 then return false end

        local hitFlags = bit.bor(0x1, 0x10, 0x100)

        local hit,x,y,z = TraceLine(x1, y1, z1 + 2.25, x2, y2, z2 + 2.25, hitFlags)

        return hit == 0
    end)
end

local function Cast(spell, target)
    if lastSpellName == spell and GetTime() - lastSpellTime < 0.1 then
        return true
    end

    lastSpellName = spell
    lastSpellTime = GetTime()

    return CastSpellByName(spell, target) or true
end

local function CastAtPosition(oX, oY, oZ, spell)
    Cast(spell)
    local i = -100
    local mouselookup = IsMouseButtonDown(2)
    if mouselookup then MouselookStop() end
    while SpellIsTargeting() and i <= 100 do
        ClickPosition(oX, oY, oZ)
        i = i + 1
        oZ = i
    end
    if mouselookup then MouselookStart() end
    if i >= 100 and SpellIsTargeting() then
        SpellStopTargeting()
    end

    return not SpellIsTargeting()
end

local function CastGroundSpeed(spell, target)
    local oX, oY, oZ = GetUnitPosition(target)
    local speed = GetUnitSpeed(target)

    return CastAtPosition(oX, oY, oZ, spell)
end

local function CalculateFacingAngle(playerX, playerY, targetX, targetY)
    local deltaY = targetY - playerY
    local deltaX = targetX - playerX
    local angle = math.atan2(deltaY, deltaX)

    -- convert from radians to degrees
    angle = angle * (180 / math.pi)

    -- adjust for game orientation (0 degrees is south, 90 is west)
    angle = angle - 90
    if angle < 0 then
        angle = 360 + angle
    end

    return angle
end

local function IsUnitFacing(target)
    local ax, ay, az = GetUnitPosition("player")
    local bx, by, bz = GetUnitPosition(target)
    if not ax or not bx then return false end
    local dx, dy, dz = ax-bx, ay-by, az-bz
    local rotation = UnitFacing("player");
    local value = (dy*math.sin(-rotation) - dx*math.cos(-rotation)) /
    math.sqrt(dx*dx + dy*dy)
    local isFacing = value > 0.25
    return isFacing
 end

local function facehack(target)
    if target then
        local playerX, playerY, playerZ = GetUnitPosition("player")
        local targetX, targetY, targetZ = GetUnitPosition(target)
        local angle = CalculateFacingAngle(playerX, playerY, targetX, targetY)
        if true or not IsUnitFacing(target) then
            local radians = (angle + 90) * (math.pi / 180)
            FaceDirection(radians, false)
        end
    end
end

-- Stub for now
local function getUserGuid(target)
    return target
end

local function getPower(id)
    return UnitPower("player", id)
end

local idStr = "id"

function Unit:Health()
    return self.cache:GetOrSet("Health", function()
        local id = rawget(self, idStr)
        return (UnitHealth(id) / UnitHealthMax(id)) * 100
    end)
end

function Unit:CanAttack()
    return self.cache:GetOrSet("CanAttack", function()
        local id = rawget(self, idStr)
        return UnitExists(id) and not UnitIsDeadOrGhost(id) and UnitCanAttack("player", id)
    end)
end

function Unit:GetBuffs()
    return self.cache:GetOrSet("Buffs", function()
        local target = rawget(self, idStr)
        local buffPool = {}
        local _, spellName, count, spellId, duration, expiration

        for i = 1, 1000 do
            spellName, _, count, _, duration, expiration, _, _, _, spellId = UnitAura(target, i, "HELPFUL")
            if not spellName then
                break
            else
                local auraInfo = {
                    count = count,
                    duration = duration,
                    expiration = expiration,
                }

                buffPool[spellName] = auraInfo
                buffPool[spellId] = auraInfo
            end
        end

        return buffPool
    end)
end

function Unit:GetDeBuffs()
    return self.cache:GetOrSet("DeBuffs", function()
        local target = rawget(self, idStr)
        local buffPool = {}
        local _, spellName, count, spellId, duration, expiration

        for i = 1, 1000 do
            spellName, _, count, _, duration, expiration, _, _, _, spellId = UnitAura(target, i, "HARMFUL")
            if not spellName then
                break
            else
                local auraInfo = {
                    count = count,
                    duration = duration,
                    expiration = expiration,
                }

                buffPool[spellName] = auraInfo
                buffPool[spellId] = auraInfo
            end
        end

        return buffPool
    end)
end


function Unit:HasBuff(spell)
    return Unit.GetBuffs(self)[spell] ~= nil
end

function Unit:HasBuffCount(spell)
    local foundBuff = Unit.GetBuffs(self)[spell]
    return (foundBuff ~= nil and foundBuff.count) or 0
end

function Unit:HasBuffRemain(spell, remaining)
    local foundBuff = Unit.GetBuffs(self)[spell]
    if foundBuff == nil then
        return false
    end

    return foundBuff.expiration - GetTime() > remaining
end

function Unit:HasBuffInList(buffList)
    local buffs = Unit.GetBuffs(self)
    for i = 1, #buffList do
        if buffs[buffList[i]] then
            return true
        end
    end

    return false
end

function Unit:HasDeBuff(spell)
    return Unit.GetDeBuffs(self)[spell] ~= nil
end

function Unit:HasDeBuffCount(spell)
    local foundDebuff = Unit.GetDeBuffs(self)[spell]
    return (foundDebuff ~= nil and foundDebuff.count) or 0
end

function Unit:HasDeBuffRemain(spell, remaining)
    local foundDebuff = Unit.GetDeBuffs(self)[spell]
    if foundDebuff == nil then
        return false
    end

    return foundDebuff.expiration - GetTime() > remaining
end

function Unit:HasDebuffInList(debuffList)
    local debuffs = Unit.GetDeBuffs(self)
    for i = 1, #debuffList do
        if debuffs[debuffList[i]] then
            return true
        end
    end

    return false
end

function Unit:isAHealer()
    local guid = rawget(self, "guid")
    local specId = UnitSpecializationID(guid)
    return (not specId and false) or healerSpecIds[specId] or false
end

function Unit:isACaster()
    local guid = rawget(self, "guid")
    local specId = UnitSpecializationID(guid)
    return (not specId and false) or casterSpecIds[specId] or false
end

function Unit:isBursting()
    return self.cache:GetOrSet("Bursting", function()
        return Unit.HasBuffInList(self, burstlist)
    end)
end

function Unit:isInBreakableCC()
    return self.cache:GetOrSet("BreakableCC", function()
        return Unit.HasDebuffInList(self, breakableCC)
    end)
end

function Unit:isImmune()
    return self.cache:GetOrSet("Immune", function()
        return Unit.HasDebuffInList(self, totalImmunityList)
    end)
end

function Unit:isCasting(unit)
    local id = rawget(self, idStr)
    local casting, _, _, startTime, endTime, _, _, interruptable = UnitCastingInfo(id)
    if not casting then
        casting, _, _, startTime, endTime, _, interruptable = UnitChannelInfo(id)
    end

    if casting then
        local elapsedTime = (GetTime() * 1000) - startTime
        return casting, elapsedTime, interruptable, endTime
    end

    return casting or false
end

function Unit:isPlayer()
    return UnitIsPlayer(rawget(self, "id"))
end

function Unit:GetDistance()
    return unitDistance(self)
end

function Unit:GetLos()
    return unitLos(self)
end

function Unit:Exists()
    return UnitExists(rawget(self, "id"))
end

local unitActions = {
    buffs = Unit.GetBuffs,
    debuffs = Unit.GetDeBuffs,
    exists = Unit.Exists,
    moveTime = Unit.MovementTime,
    stayTime = Unit.StayingTime,
    canAttack = Unit.CanAttack,
    health = Unit.Health,
    distance = Unit.GetDistance,
    los = Unit.GetLos,
    isHealer = Unit.isAHealer,
    isCaster = Unit.isACaster,
    bursting = Unit.isBursting,
    inBreakableCC = Unit.isInBreakableCC,
    immune = Unit.isImmune,
    player = Unit.isPlayer,
    casting = Unit.isCasting,
}

local function unitIndex(unit, key)
    local property = rawget(unit, key)
    if property ~= nil then
        return property
    end

    local unitAction = unitActions[key]
    if unitAction then
        return unitAction(unit)
    else
        return Unit[key]
    end
end

function Unit:new(target)
    local targetGUID = UnitGUID(target) or target

    return unitCachePool:GetOrSet(targetGUID, function ()
        local unit = {
            cache = CacheContext:getCell(),
            callCount = 0,
            id = target,
            guid = targetGUID,
        }
        setmetatable(unit, { __index = unitIndex })  -- make Account handle lookup

        return unit
    end)
end

MakuluFramwork.Unit = Unit


local gcdInfo = CacheContext:getConstCacheCell()
local gcdSpell           = 61304

local spellState = {
    icon = nil,
    casted = false
}

local function GetSpellCooldown(e)
    return 0, 0
end

local GetSpellCooldown = _G.GetSpellCooldown or GetSpellCooldown

local function getGcd()
    return gcdInfo:GetOrSet("gcd", function()
        local gcdStart, gcdDuration = GetSpellCooldown(gcdSpell)

        return (gcdDuration == 0 and 0) or (gcdDuration - (GetTime() - gcdStart))
    end)
end

local Spell = {}

local function getCooldown(spellId)
    local start, duration = GetSpellCooldown(spellId)

    if duration and duration ~= 0 then
        return (duration - (GetTime() - start)) * 1000
    end

    return 0
end

function Spell:Cooldown()
    return self.cache:GetOrSet("Cooldown", function()
        return getCooldown(rawget(self, "spellId"))
    end)
end

function Spell:InRange(target)
    local targetGUID = rawget(target, "guid")
    local key = "InRange" .. targetGUID
    return self.cache:GetOrSet(key, function()
        local name = rawget(self, "name")
        return IsSpellInRange(name, targetGUID) == 1
    end)
end

function Spell:ReadyToUse(target)
    if spellState.casted then return false end

    if not target then target = Unit:new("target") end
    local targetGUID = rawget(target, "guid")
    local key = "IsUsable" .. targetGUID

    local name = rawget(self, "name")
    -- print('Checking spell is usable name: ' .. name)

    return self.cache:GetOrSet(key, function()
        -- First lets return early if we're on gcd no need to spam here..
        if not rawget(self, "offGcd") and getGcd() > 300 then
            return false
        end

        if not rawget(self, "resourceCheck")() then
            return false
        end

        -- If spell is on calldown then it's definintely not usable
        if Spell.Cooldown(self) > 300 then
            return false
        end

        return true
    end)
end

function Spell:Usable(target)
    return Spell.ReadyToUse(self, target)
end

function Spell:Cast(target, skipSet, spellOverride)
    target = target or Unit:new('player')
    if spellState.casted then return false end
    if not Spell.Usable(self, target) then return false end
    local name = rawget(self, "name")

    facehack(target.guid)

    return Cast(name, target.id)
end

function Spell:CastGround(target, skipSet, spellOverride)
    target = target or Unit:new('player')
    if spellState.casted then return false end
    if not Spell.Usable(self, target) then return false end
    local name = rawget(self, "name")

    return CastGroundSpeed(name, target.guid)
end

function Spell:CastTime()
    return self.cache:GetOrSet("CastTime", function()
        local _, _, _, castTime = GetSpellInfo(self.spellId)
        return castTime
    end)
end

function Spell:GetCharges()
    local spellId = rawget(self, "spellId")
    return GetSpellCharges(spellId)
end

function Spell:Callback(arg1, arg2)
    local callbackMethod = arg2 or arg1
    local key = (callbackMethod == arg2 and arg1) or nil

    if key == nil then
        self.baseCallback = callbackMethod
        return
    end

    self.callbacks[key] = callbackMethod
end

local spellActions = {
    cooldown = Spell.Cooldown,
    charges = Spell.GetCharges
}

local function spellIndex(spell, key)
    local property = rawget(spell, key)
    if property ~= nil then
        return property
    end

    local spellAction = spellActions[key]
    if spellAction then
        return spellAction(spell)
    else
        return Spell[key]
    end
end

local baseCallbackStr = "baseCallback"
local callbacksStr = "callbacks"

local function spellCall(self, ...)
    if spellState.casted then return false end

    local firstKey = select(1, ...)
    if firstKey == nil or type(firstKey) ~= "string" then
        return rawget(self, baseCallbackStr)(self, ...)
    end

    return rawget(self, callbacksStr)[firstKey](self, select(2,...))
end

function Spell:new(spellId, spellInfo)
    spellInfo = spellInfo or {}

    local powerRequirements = spellInfo.powerReq
    local resourceCheck = function()
        if not powerRequirements then return true end
        return getPower(powerRequirements.powerId) >= powerRequirements.required
    end

    local spell = {
        cache = CacheContext:getConstCacheCell(),
        spellId = spellId,
        id = spellId,
        spellInfo = spellInfo,
        offGcd = spellInfo.offGcd or false,
        spellType = spellInfo.spellType,
        damageSpell = spellInfo.damageSpell,
        name = GetSpellInfo(spellId),
        resourceCheck = resourceCheck,

        baseCallback = nil,
        callbacks = {},
    }

    setmetatable(spell, { __index = spellIndex, __call = spellCall })  -- make Account handle lookup

    self.__index = self

    return spell
end

MakuluFramwork.Spell = Spell

local function clearCache()
    CacheContext:resetCache()
end

MakuluFramwork.clearCache = clearCache

return MakuluFramwork