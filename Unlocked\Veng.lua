---@diagnostic disable: unused-local
local dmc = ...


if (not dmc) or (not dmc.IsInWorld) then
  return
end

local unlockList = {
  "ClassId",
  "CastSpellByName",
  "UnitCanAttack",
  "IsSpellInRange",
  "UnitHealth",
  "UnitExists",
  "UnitIsPlayer",
  "UnitIsDeadOrGhost",
  "UnitAura",
  "CancelUnitBuff",
  "SetTarget",
  "TargetUnit",
  "UnitName"
}

local localenv = setmetatable(
  {},
  {
    __index = function(self, func)
      return dmc[func] or _G[func]
    end
  }
)

for i = 1, #unlockList do
  local funcname = unlockList[i]
  local func = _G[funcname]
  localenv[funcname] = function(...) return dmc.SecureCode(func, ...) end
end

setfenv(1, localenv)

local ccList = {
    freezingTrapDebuff = 3355,
    zombieExplosion = 210141,
    absoluteZero = 334693,
    asphyxiateUnholy = 108194,
    asphyxiateBlood = 221562,
    gnawGhoul = 91800,
    monstrousBlowMutatedGhoul = 91797,
    deadOfWinter = 287254,
    chaosNova = 179057,
    illidansGraspPrimary = 205630,
    illidansGraspSecondary = 208618,
    felEruption = 211881,
    metamorphosisPvE = 200166,
    maim = 203123,
    rakeProwl = 163505,
    mightyBash = 5211,
    overrun = 202244,
    wildHuntsCharge = 325321,
    terrorOfTheSkies = 372245,
    bindingShot = 117526,
    consecutiveConcussion = 357021,
    intimidation = 24394,
    snowdrift = 389831,
    legSweep = 119381,
    doubleBarrel = 202346,
    exorcism = 385149,
    hammerOfJustice = 853,
    wakeOfAshes = 255941,
    psychicHorror = 64044,
    holyWordChastiseCensure = 200200,
    cheapShot = 1833,
    kidneyShot = 408,
    staticChargeCapacitorTotem = 118905,
    pulverizePrimalEarthElemental = 118345,
    lightningLasso = 305485,
    axeToss = 89766,
    meteorStrikeInfernal = 171017,
    meteorStrikeAbyssal = 171018,
    shadowfury = 30283,
    shieldCharge = 385954,
    shockwave = 46968,
    shockwaveProtection = 132168,
    shockwaveProvingGroundsPvE = 145047,
    stormBolt = 132169,
    warpath = 199085,
    warStompTauren = 20549,
    bullRushHighmountainTauren = 255723,
    imprison = 217832,
    imprisonHonorTalent = 221527,
    hibernate = 2637,
    incapacitatingRoar = 99,
    timeStop = 378441,
    freezingTrap = 3355,
    freezingTrapHonorTalent = 203337,
    scatterShot = 213691,
    massPolymorph = 383121,
    polymorph = 118,
    polymorphTurtle = 28271,
    polymorphPig = 28272,
    polymorphSnake = 61025,
    polymorphBlackCat = 61305,
    polymorphTurkey = 61780,
    polymorphRabbit = 61721,
    polymorphPorcupine = 126819,
    polymorphPolarBearCub = 161353,
    polymorphMonkey = 161354,
    polymorphPenguin = 161355,
    polymorphPeacock = 161372,
    polymorphBabyDirehorn = 277787,
    polymorphBumblebee = 277792,
    polymorphMawrat = 321395,
    polymorphDuck = 391622,
    ringOfFrost = 82691,
    paralysis = 115078,
    paralysis2PerpetualParalysis = 357768,
    repentance = 20066,
    shackleUndead = 9484,
    holyWordChastise = 200196,
    gouge = 1776,
    sap = 6770,
    sundering = 197214,
    banish = 710,
    mortalCoil = 6789,
    quakingPalmPandarenRacial = 107079,
    blindingSleet = 207167,
    sigilOfMisery = 207685,
    cyclone = 33786,
    sleepWalk = 360806,
    scareBeast = 1513,
    dragonsBreath = 31661,
    songOfChiJi = 198909,
    hotTrub = 202274,
    blindingLight = 105421,
    turnEvil = 10326,
    blind = 2094,
    seductionGrimoireOfSacrifice = 261589,
    seductionSuccubus = 6358,
    hex = 51514,
    hexVoodooTotem = 196942,
    hexRaptor = 210873,
    hexSpider = 211004,
    hexSnake = 211010,
    hexCockroach = 211015,
    hexSkeletalHatchling = 269352,
    hexLivingHoney = 309328,
    hexZandalariTendonripper = 277778,
    hexWickerMongrel = 277784,
    strangulate = 47476,
    sigilOfSilence = 202137,
    solarBeam = 78675,
    reactiveResin = 410065,
    spiderSting = 202933,
    spiderVenom = 356727,
    wailingArrow1 = 354831,
    wailingArrow2 = 355596,
    shieldOfVirtue = 217824,
    silence = 15487,
    garrote = 1330,
    unstableAfflictionSilenceEffect = 196364,
    -- Entries from playerCCList
    scatterShotDebuff = 213691,
    stormBoltDebuff = 107570,
    deepFreezeDebuff = 81429,
    maimDebuff = 22570,
    staticCharge = 135621,
    lightningLassoDebuff = 305485,
    asphyxiateDebuff = 108194,
    repentanceDebuff = 20066,
    incapacitatingRoarDebuff = 99,
    dragonsBreathDebuff = 321707,
    polymorphDebuff = 118,
    polymorphTurtle = 28271,
    polymorphPig = 28272,
    polymorphSnake = 61025,
    polymorphBlackCat = 61305,
    polymorphTurkey = 61780,
    polymorphRabbit = 61721,
    polymorphPorcupine = 126819,
    polymorphPolarBearCub = 161353,
    polymorphMonkey = 161354,
    polymorphPenguin = 161355,
    polymorphPeacock = 161372,
    polymorphBabyDirehorn = 277787,
    polymorphBumblebee = 277792,
    polymorphMawrat = 321395,
    polymorphDuck = 391622,
    blindingLightDebuff = 115750,
    intimidatingShoutDebuff = 5246,
    hexDebuff = 211015,
    psychicScreamDebuff = 8122,
    fearDebuff = 5782,
    howlOfTerrorDebuff = 5484,
    seductionDebuff = 6358,
    mortalCoilDebuff = 6789,
}


local peelModeOn = false
local shiftDown = false

local pairs = pairs
local ipairs = ipairs

local activePopups = {}
local ICON_SIZE = 36
local OFFSET_X = -100
local OFFSET_Y = 450

local function HideSpellPopup(message)
    for i, popup in ipairs(activePopups) do
        if popup.text:GetText() == message then
            popup:Hide()
            table.remove(activePopups, i)
            break
        end
    end
    for i, p in ipairs(activePopups) do
        if i == 1 then
            p:SetPoint("TOPLEFT", UIParent, "CENTER", OFFSET_X, OFFSET_Y)
        else
            p:SetPoint("TOP", activePopups[i - 1], "BOTTOM", 0, -10)
        end
    end
end

local function ShowSpellPopup(spellID, message, noautohide)
    for _, popup in ipairs(activePopups) do
        if popup.text:GetText() == message then
            return
        end
    end

    local popup = CreateFrame("Frame", "SpellPopupFrame" .. #activePopups + 1, UIParent)
    table.insert(activePopups, popup)
    
    popup:SetSize(ICON_SIZE, ICON_SIZE)
    if #activePopups == 1 then
        popup:SetPoint("TOPLEFT", UIParent, "CENTER", OFFSET_X, OFFSET_Y)
    else
        popup:SetPoint("TOP", activePopups[#activePopups - 1], "BOTTOM", 0, -10)
    end
    
    local texture = popup:CreateTexture(nil, "BACKGROUND")
    texture:SetAllPoints()
    texture:SetTexture(GetSpellTexture(spellID))
    
    local spellName = GetSpellInfo(spellID)
    
    popup.text = popup:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    popup.text:SetPoint("LEFT", texture, "RIGHT", 10, 0)
    popup.text:SetText(message)

    if noautohide then return end
    
    C_Timer.After(3, function()
        popup:Hide()
        for i, p in ipairs(activePopups) do
            if p == popup then
                table.remove(activePopups, i)
                break
            end
        end
        for i, p in ipairs(activePopups) do
            if i == 1 then
                p:SetPoint("TOPLEFT", UIParent, "CENTER", OFFSET_X, OFFSET_Y)
            else
                p:SetPoint("TOP", activePopups[i - 1], "BOTTOM", 0, -10)
            end
        end
    end)
end

local function ClearPopups()
    for i = #activePopups, 1, -1 do
        if not activePopups[i]:IsShown() then
            activePopups[i]:Hide()
            table.remove(activePopups, i)
        end
    end
end

C_Timer.NewTicker(5, function()
    ClearPopups()
end)

-- region CACHING

local CacheSection = {}
CacheSection.__index = CacheSection

local rawget, rawset = rawget, rawset
local setmetatable = setmetatable

local cacheIndex = function (self, key)
    local property = rawget(self, key)
    if property ~= nil then
        return property
    end

    return CacheSection[key]
end

function CacheSection:new(duration)
    local cacheSection = {
        dirty = false,
        data = {},
        lastUpdate = 0,
        duration = duration,
    }

    setmetatable(cacheSection, { __index = cacheIndex })
    self.__index = self

    return cacheSection
end

function CacheSection:newWeak()
    local weakData = {}
    setmetatable(weakData, {__mode = "v"})

    local cacheSection = {
        dirty = false,
        data = weakData,
    }

    setmetatable(cacheSection, self)
    self.__index = self

    return cacheSection
end

function CacheSection:Get(key)
    if rawget(self, "dirty") then
        return nil
    end

    return rawget(self, "data")[key]
end

function CacheSection:GetOrSet(key, callback)
    local dirty = rawget(self, "dirty")

    if dirty then
        rawset(self, "data", {})
        rawset(self, "dirty", false)
    end

    local data = rawget(self, "data")

    local found = data[key]
    if found ~= nil then
        return found
    end

    local value = callback()
    data[key] = value

    return value
end

local CacheContext = {
    cacheIdx = 0,
    cache = {},

    constCacheIdx = 0,
    constCache = {}, -- These are cache items which aren't cleared between calls

    perCombatIdx = 0, -- These again are const but we just only dirty the cache when out of combat
    perCombatCache = {}, -- These are only reset when we're out of combat

    weakCache = CacheSection:newWeak(),

    getCell = function (self)
        local toReturn = self.cache[self.cacheIdx]
        if toReturn ~= nil then
            toReturn.dirty = true
            self.cacheIdx = self.cacheIdx + 1

            return toReturn
        end

        local newCacheObject = CacheSection:new()
        self.cache[self.cacheIdx] = newCacheObject
        self.cacheIdx = self.cacheIdx + 1

        return newCacheObject
    end,

    getConstCacheCell = function (self)
        local newCacheObject = CacheSection:new()
        self.constCache[self.constCacheIdx] = newCacheObject
        self.constCacheIdx = self.constCacheIdx + 1

        return newCacheObject
    end,

    getCombatCacheCell = function (self)
        local newCacheObject = CacheSection:new()
        self.perCombatCache[self.perCombatIdx] = newCacheObject
        self.perCombatIdx = self.perCombatIdx + 1

        return newCacheObject
    end,

    resetCache = function(self)
        self:dirtyAllConstCells()
        self.cacheIdx = 0
    end,

    dirtyAllCells = function(self)
        for i = 0, self.cacheIdx - 1 do
            self.cache[i].dirty = true
        end
    end,

    dirtyAllConstCells = function(self)
        local time = GetTime()
        for i = 0, self.constCacheIdx - 1 do
            local cell = self.constCache[i]
            local resetTime = rawget(cell, "duration")

            if resetTime then
                local lastUpdate = rawget(cell, "lastUpdate")
                if lastUpdate == 0 or time - lastUpdate > resetTime then
                    cell.dirty = true
                    cell.lastUpdate = time
                end
            else
                cell.dirty = true
            end
        end
    end
}

-- endregion CACHING

local lastSpellName = nil
local lastSpellTime = 0

local function Cast(spell, target)
    if lastSpellName == spell and GetTime() - lastSpellTime < 0.1 then
        return true
    end

    lastSpellName = spell
    lastSpellTime = GetTime()


    return CastSpellByName(spell, target) or true
end

-- region AURATRACKING

local auraCache = CacheContext:getConstCacheCell()

local buidAuraTable = function (target, auraType)
    local key = (UnitGUID(target) or target) .. auraType

    return auraCache:GetOrSet(key, function()
        local time = GetTime()
        auraType = auraType
        local _, spellName, count, spellId, duration, expiration
        local map = {}

        for i = 1, 1000 do
            spellName, _, count, _, duration, expiration, _, _, _, spellId = UnitAura(target, i, auraType)
            if not spellName then
                break
            else
                local auraInfo = {
                    count = count,
                    duration = duration,
                    expiration = expiration,
                    elapsed = duration - (expiration - time)
                }

                map[spellName] = auraInfo
                map[spellId] = auraInfo
            end
        end
        return map
    end)
end

local function clearAuraByName(name)
    local spellName
    for i = 1, 1000 do
        spellName = UnitAura("player", i, "HELPFUL")
        if not spellName then
            break
        else
            if spellName == name then
                CancelUnitBuff("player", i, "HELPFUL")
                break
            end
        end
    end
end

local function hasBuff(target, buff)
    local auraTable = buidAuraTable(target, "HELPFUL")
    return auraTable[buff] ~= nil
end

local function hasBuffInList(target, buffList)
    local auraTable = buidAuraTable(target, "HELPFUL")

    for _, buff in pairs(buffList) do
        if auraTable[buff] ~= nil then
            return true
        end
    end

    return false
end

local function hasBuffInListElapsed(target, buffList, durataion)
    local auraTable = buidAuraTable(target, "HELPFUL")

    for _, buff in pairs(buffList) do
        local buffFound = auraTable[buff]
        if buffFound ~= nil and buffFound.elapsed > durataion then
            return true
        end
    end

    return false
end

local function hasBuffInListRemaining(target, buffList, remaining, elapsed)
    local auraTable = buidAuraTable(target, "HELPFUL")

    for _, buff in pairs(buffList) do
        local buffs = auraTable[buff]
        if buffs ~= nil and (not elapsed or (buffs.elapsed > elapsed)) then
            local remain = buffs.expiration - GetTime()
            if remain > remaining then
                return true
            end
        end
    end

    return false
end

local function hasBuffCount(target, buff)
    local auraTable = buidAuraTable(target, "HELPFUL")
    return (auraTable[buff] ~= nil and auraTable[buff].count) or 0
end

local hasBuffRemaining = function (target, aura)
    local auraTable = buidAuraTable(target, "HELPFUL")
    local buffs = auraTable[aura]
    if buffs == nil then return 0 end

    local remaining = buffs.expiration - GetTime()
    return (remaining > 0 and remaining) or 0
end

local function hasDebuff(target, debuff)
    local auraTable = buidAuraTable(target, "HARMFUL")
    return auraTable[debuff] ~= nil
end

local function hasDebuffNoCache(target, debuff)
    local auraTable = buidAuraTable(target, "HARMFUL")

    local _, spellName, count, spellId, duration, expiration
    local map = {}

    for i = 1, 1000 do
        spellName, _, count, _, duration, expiration, _, _, _, spellId = UnitAura(target, i, "HARMFUL")
        if not spellName then
            break
        else
            if spellName == debuff or spellId == debuff then
                return true
            end
        end
    end
end

local function hasDebuffInList(target, debuffList)
    local auraTable = buidAuraTable(target, "HARMFUL")
    local searchList = auraTable
    local lookupList = debuffList

    for _, debuff in pairs(debuffList) do
        if auraTable[debuff] ~= nil then
            return true
        end
    end

    return false
end

local function hasDebuffCount(target, debuff)
    local auraTable = buidAuraTable(target, "HARMFUL")
    return (auraTable[debuff] ~= nil and auraTable[debuff].count) or 0
end

local hasDebuffRemaining = function (target, aura)
    local auraTable = buidAuraTable(target, "HARMFUL")
    local buffs = auraTable[aura]
    if buffs == nil then return 0 end

    local remaining = buffs.expiration - GetTime()
    return (remaining > 0 and remaining) or 0
end
-- endregion AURATRACKING

-- region DRTRACKING
---------------------------------------------------------------------------------------DR START
local DRList = nil


local currentDrs = {}
local timers = {}

local JackTrack = CreateFrame("Frame")
JackTrack:RegisterEvent("COMBAT_LOG_EVENT_UNFILTERED")
JackTrack:RegisterEvent("GROUP_ROSTER_UPDATE")
JackTrack:SetScript("OnEvent", function(self, event)
    if not DRList then return end

    if event == "GROUP_ROSTER_UPDATE" then
        timers = {}
        currentDrs = {}
        return
    end

    -- Note: order of variables here might change
    local _, eventType, _, _, _, _, _, destGUID, _, destFlags, _, spellID, _, _, auraType = CombatLogGetCurrentEventInfo()

    -- Check all debuffs found in the combat log
    if auraType == "DEBUFF" then
        -- Get the DR category or exit immediately if spell/debuff doesn't have a DR
        -- This is the unlocalized category name, used for API functions.
        local category = DRList:GetCategoryBySpellID(spellID)
        if not category or category == "knockback" then return end

        local isPlayer = bit.band(destFlags, COMBATLOG_OBJECT_TYPE_PLAYER) ~= 0
        if not destGUID then return end

        destGUID = UnitGUID(destGUID) or destGUID

        -- CC aura has faded or refreshed, DR starts
        if eventType == "SPELL_AURA_REMOVED" or eventType == "SPELL_AURA_REFRESH" then
            local userRef = currentDrs[destGUID]
            if not userRef then
                userRef = {}
                currentDrs[destGUID] = userRef
            end

            local catRef = userRef[category] or 1

            -- If the DR is already at 0, don't do anything
            if catRef == 0 then return end

            -- If the DR is at 1, set it to 0 and start the timer
            local newDr = catRef / 2
            if newDr < 0.25 then newDr = 0 end

            userRef[category] = newDr

            local tag = destGUID .. category

            if timers[tag] then
                timers[tag].cancelled = true
            end

            local timerInfo = {
                cancelled = false,
            }
            timers[tag] = timerInfo
            C_Timer.NewTimer(DRList:GetResetTime(category), function()
                if timerInfo.cancelled then return end
                userRef[category] = 1
            end)
        end
    end
end)

local function GetDr(target, spell)
    local category = DRList:GetCategoryBySpellID(spell)
    if not category or category == "knockback" then return 1 end
    print('Found the category')

    target = UnitGUID(target) or target
    local currentDr = currentDrs[target]
    print('Found the user')
    if not currentDr then return 1 end

    local catRef = currentDr[category]
    if not catRef then return 1 end

    print('Found the user category')

    return catRef
end

local function GetDrCat(target, category)
    if not category or category == "knockback" then return 1 end

    target = UnitGUID(target) or target
    local currentDr = currentDrs[target]
    if not currentDr then return 1 end

    local catRef = currentDr[category]
    if not catRef then return 1 end

    return catRef
end

local function TrackDR()
    DRList = LibStub('DRList-1.0')
end

---------------------------------------------------------------------------------------DR END
--endregion DRTRACKING

local targetRef = nil

local totemsToHit = {}
local totemTracker = {}

-- region SPELLS

local ClassIsMelee = {
    [0] = false, -- UNKNOWN
    [1] = true, -- WARRIOR
    [2] = true, -- PALADIN
    [3] = false, -- HUNTER
    [4] = true, -- ROGUE
    [5] = false, -- PRIEST
    [6] = true, -- DEATHKNIGHT
    [7] = false, -- SHAMAN
    [8] = false, -- MAGE
    [9] = false, -- WARLOCK
    [10] = true, -- MONK
    [11] = false, -- DRUID
    [12] = true, -- DEMONHUNTER
    [13] = false, -- EVOKER
}
local ClassNeedsPeels = {
    [0] = false, -- UNKNOWN
    [1] = true, -- WARRIOR
    [2] = true, -- PALADIN
    [3] = true, -- HUNTER
    [4] = true, -- ROGUE
    [5] = false, -- PRIEST
    [6] = true, -- DEATHKNIGHT
    [7] = false, -- SHAMAN
    [8] = false, -- MAGE
    [9] = false, -- WARLOCK
    [10] = true, -- MONK
    [11] = false, -- DRUID
    [12] = true, -- DEMONHUNTER
    [13] = false, -- EVOKER
}
local reverseMagicList = {
    freezingTrap = GetSpellInfo(3355),
    freezingTrapHonorTalent = GetSpellInfo(203337),
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    ringOfFrost = GetSpellInfo(82691),
    repentance = GetSpellInfo(20066),
    HammerOfJustice = GetSpellInfo(853),  -- stun
}
local reverseMikeList = {
    ringOfFrost = GetSpellInfo(82691),
    repentance = GetSpellInfo(20066),
    blindingLight = GetSpellInfo(105421),
    hammerOfJustice = GetSpellInfo(853),
    songOfChiji = GetSpellInfo(198909),
    fear = GetSpellInfo(5782),
    howlOfTerror = GetSpellInfo(5484),
    seduction = GetSpellInfo(6358),
    sleepWalk = GetSpellInfo(360806),
    freezingTrap = GetSpellInfo(3355),
    psychicScream = GetSpellInfo(8122),
    psychicHorror = GetSpellInfo(64044),
   -- freezingTrapHonorTalent = GetSpellInfo(203337),
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
  
    Silence = GetSpellInfo(15487),
    massEntanglement = GetSpellInfo(102359), 
    soulRot = GetSpellInfo(325640), 
    frostBomb = GetSpellInfo(390612),
}
local burstlist = {
    doomwinds = GetSpellInfo(384352), --doomwinds
    combustion = GetSpellInfo(190319), --combustion
    meta = GetSpellInfo(191427), --meta
    meta2 = GetSpellInfo(162264), --meta2
    shadowblades = GetSpellInfo(121471), --shadowblades
    shadowDance = GetSpellInfo(185313), --shadow dance
    shadowDance2 = GetSpellInfo(185422), --shadow dance
    trueshot = GetSpellInfo(288613), --trueshot
    coordinatedAssault = GetSpellInfo(360952), --coordinated assault
    incarnFeral = GetSpellInfo(102543), --incarn feral
    incarnElune = GetSpellInfo(103560), --incarn elune
    recklessness = GetSpellInfo(1719), --recklessness
    avatar = GetSpellInfo(107574), --avatar
    adrenalineRush = GetSpellInfo(13750), --adrenaline rush
    icyVeins = GetSpellInfo(12472), --icy veins
    pillarofFrost = GetSpellInfo(51271), --pillar of frost
    abomLimb = GetSpellInfo(383269), --abom limb
    avengingWrath = GetSpellInfo(231895), -- avenging wrath
    crusade = GetSpellInfo(384392),
    crusade2 = GetSpellInfo(231895),
    serenity = GetSpellInfo(152173), --serenity
    ascendance = GetSpellInfo(114051),
    danseMacabre = GetSpellInfo(393969),
}
local totalImmunityList = {
    Cyclone = GetSpellInfo(33786),
    iceBlock = GetSpellInfo(45438),
    divineShield = GetSpellInfo(642), 
    aspectOfTheTurtle = GetSpellInfo(186265),
    imprison = GetSpellInfo(221527),
    banish = GetSpellInfo(710),
    diamondIce = GetSpellInfo(203340),
    tranquilityPVPTalent = GetSpellInfo(362486),
    cloakOfShadows = GetSpellInfo(31224),
    dispersion = GetSpellInfo(79811),
    dispersion2 = GetSpellInfo(47585),
    netherwalk = GetSpellInfo(196555),
    burrow = GetSpellInfo(409293),
    lifeCocoon = GetSpellInfo(116849),
    netherWard = GetSpellInfo(212295),
    evasion = GetSpellInfo(5277),
    ultimatePenitence = GetSpellInfo(421453),

    -- Disorient effects
    blindingSleet = GetSpellInfo(207167),
    dragonsBreath = GetSpellInfo(31661),
    blind = GetSpellInfo(2094),
    fear = GetSpellInfo(118699),
    howlOfTerror = GetSpellInfo(5484),
    intimidatingShout1 = GetSpellInfo(5246),
    intimidatingShout2 = GetSpellInfo(316593),
    intimidatingShout3 = GetSpellInfo(316595),

    -- Incapacitate effects
    hibernate = GetSpellInfo(2637),
    incapacitatingRoar = GetSpellInfo(99),
    freezingTrap = GetSpellInfo(3355),
    freezingTrapHonorTalent = GetSpellInfo(203337),
    scatterShot = GetSpellInfo(213691),
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    ringOfFrost = GetSpellInfo(82691),
    repentance = GetSpellInfo(20066),
    shackleUndead = GetSpellInfo(9484),
    sap = GetSpellInfo(6770),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
}
local breakableCC = {
    -- Disorient effects
    blindingSleet = GetSpellInfo(207167),
    dragonsBreath = GetSpellInfo(31661),
    blind = GetSpellInfo(2094),
    fear = GetSpellInfo(118699),
    howlOfTerror = GetSpellInfo(5484),
    intimidatingShout1 = GetSpellInfo(5246),
    intimidatingShout2 = GetSpellInfo(316593),
    intimidatingShout3 = GetSpellInfo(316595),
    sigilMisery = GetSpellInfo(207684),

    -- Incapacitate effects
    hibernate = GetSpellInfo(2637),
    incapacitatingRoar = GetSpellInfo(99),
    freezingTrap = GetSpellInfo(3355),
    freezingTrapHonorTalent = GetSpellInfo(203337),
    scatterShot = GetSpellInfo(213691),
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    ringOfFrost = GetSpellInfo(82691),
    repentance = GetSpellInfo(20066),
    shackleUndead = GetSpellInfo(9484),
    sap = GetSpellInfo(6770),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
}
local interruptList = {
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
    convoke = GetSpellInfo(323764),
    penance = GetSpellInfo(186723),
    repentance = GetSpellInfo(20066),
    ringOfFrost = GetSpellInfo(113724),
    cyclone = GetSpellInfo(33786),
    mindControl = GetSpellInfo(605),
    songOfChiji = GetSpellInfo(198898),
    fear = GetSpellInfo(5782),
    hibernate = GetSpellInfo(2637),
    banish = GetSpellInfo(710),
    turnEvil = GetSpellInfo(10326),
    sleepWalk = GetSpellInfo(360806),
    summonFelhunter = GetSpellInfo(691),
    summonImp = GetSpellInfo(688),
    summonSuccubus = GetSpellInfo(712),
    massDispel = GetSpellInfo(32375),
    shiftingPower = GetSpellInfo(314791),
    revivePet = GetSpellInfo(982),
    stormkeeper = GetSpellInfo(191634),
    kindredSpirits = GetSpellInfo(326434),
    drainLife = GetSpellInfo(234153),
    chaosBolt = GetSpellInfo(116858),
    greaterPyro = GetSpellInfo(203286),
    glacialSpike = GetSpellInfo(199786),
    soulFire = GetSpellInfo(6353),
    unstableAffliction = GetSpellInfo(342938),
    summonTyrant = GetSpellInfo(265187),
    eyeBeam = GetSpellInfo(198013),
    voidTorrent = GetSpellInfo(263165),
    soothingMist = GetSpellInfo(115175),
    vampiricTouch = GetSpellInfo(34914),
    handofGuldan = GetSpellInfo(105174),
    arcaneSurge = GetSpellInfo(365350),
    stellarFlare = GetSpellInfo(202347),
    nullifyingShroud = GetSpellInfo(378464),
    eternitySurge = GetSpellInfo(382411),
    disintegrate = GetSpellInfo(356995), --not sure if worth
    fullMoon = GetSpellInfo(274283),
    halfMoon = GetSpellInfo(274282),
    tyrsDeliverance = GetSpellInfo(200652),
    shadowFury = GetSpellInfo(30283),
    ringOfFire = GetSpellInfo(353082), --not sure if worth
    rayOfFrost = GetSpellInfo(205021),
    mindGames = GetSpellInfo(375901),
    summonfelguard = GetSpellInfo(30146),
    frostBomb = GetSpellInfo(390612),  
    callDreadStalkers = GetSpellInfo(104316),  
    fireBall = GetSpellInfo(133),  
    frostBolt = GetSpellInfo(116),
    ebonMight = GetSpellInfo(395152),
    upheavel = GetSpellInfo(396286),
    drainLife = GetSpellInfo(234153),
    maleficRapture = GetSpellInfo(324536),
    ultimatePenitence = GetSpellInfo(421453),
}

local interruptList = {
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
    repentance = GetSpellInfo(20066),
    ringOfFrost = GetSpellInfo(113724),
    cyclone = GetSpellInfo(33786),
    mindControl = GetSpellInfo(605),
    songOfChiji = GetSpellInfo(198898),
    fear = GetSpellInfo(5782),
    hibernate = GetSpellInfo(2637),
    banish = GetSpellInfo(710),
    turnEvil = GetSpellInfo(10326),
    sleepWalk = GetSpellInfo(360806),
    summonFelhunter = GetSpellInfo(691),
    summonImp = GetSpellInfo(688),
    summonSuccubus = GetSpellInfo(712),
    massDispel = GetSpellInfo(32375),
    drainLife = GetSpellInfo(234153),
    chaosBolt = GetSpellInfo(116858),
    greaterPyro = GetSpellInfo(203286),
    glacialSpike = GetSpellInfo(199786),
    soulFire = GetSpellInfo(6353),
    summonTyrant = GetSpellInfo(265187),
    voidTorrent = GetSpellInfo(263165),
    nullifyingShroud = GetSpellInfo(378464),
    disintegrate = GetSpellInfo(356995), --not sure if worth
    shadowFury = GetSpellInfo(30283),
    rayOfFrost = GetSpellInfo(205021),
    summonfelguard = GetSpellInfo(30146),
    frostBomb = GetSpellInfo(390612),  
    upheavel = GetSpellInfo(396286),
    drainLife = GetSpellInfo(234153),
    ultimatePenitence = GetSpellInfo(421453),
    searingGlare = GetSpellInfo(410201),
}
local interruptListLookup = {}

for k, v in pairs(interruptList) do
    interruptListLookup[v] = true
end

local glimpse = {
    polymorph = GetSpellInfo(118),
    polymorphTurtle = GetSpellInfo(28271),
    polymorphPig = GetSpellInfo(28272),
    polymorphSnake = GetSpellInfo(61025),
    polymorphBlackCat = GetSpellInfo(61305),
    polymorphTurkey = GetSpellInfo(61780),
    polymorphRabbit = GetSpellInfo(61721),
    polymorphPorcupine = GetSpellInfo(126819),
    polymorphPolarBearCub = GetSpellInfo(161353),
    polymorphMonkey = GetSpellInfo(161354),
    polymorphPenguin = GetSpellInfo(161355),
    polymorphPeacock = GetSpellInfo(161372),
    polymorphBabyDirehorn = GetSpellInfo(277787),
    polymorphBumblebee = GetSpellInfo(277792),
    polymorphMawrat = GetSpellInfo(321395),
    polymorphDuck = GetSpellInfo(391622),
    hex = GetSpellInfo(51514),
    hexVoodooTotem = GetSpellInfo(196942),
    hexRaptor = GetSpellInfo(210873),
    hexSpider = GetSpellInfo(211004),
    hexSnake = GetSpellInfo(211010),
    hexCockroach = GetSpellInfo(211015),
    hexSkeletalHatchling = GetSpellInfo(269352),
    hexLivingHoney = GetSpellInfo(309328),
    hexZandalariTendonripper = GetSpellInfo(277778),
    hexWickerMongrel = GetSpellInfo(277784),
    repentance = GetSpellInfo(20066),
    cyclone = GetSpellInfo(33786),
    mindControl = GetSpellInfo(605),
    songOfChiji = GetSpellInfo(198898),
    fear = GetSpellInfo(5782),
    hibernate = GetSpellInfo(2637),
    banish = GetSpellInfo(710),
    turnEvil = GetSpellInfo(10326),
    sleepWalk = GetSpellInfo(360806),
    massDispel = GetSpellInfo(32375),
    shiftingPower = GetSpellInfo(314791),
    stormkeeper = GetSpellInfo(191634),
    kindredSpirits = GetSpellInfo(326434),
}

local glimpseLookup = {}

for k, v in pairs(glimpse) do
    glimpseLookup[v] = true
end

local highPrioStompList = {
    counterstrikeTotem = 105451,
    tremorTotem = 5913,
    groundingTotem = 5925,
    skyfuryTotem = 105427,
    capacitorTotem = 61245,
    staticFieldTotem = 179867,
    spiritLinkTotem = 53006,
    healingTideTotem = 59764,
    healingStreamTotem = 3527,
    manaSpringTotem = 193620,
    warBanner = 119052,
    psyfiend = 101398,
    felObelisk = 179193,
    observer = 107100,
    earthGrabTotem = 60561,
    earthbindTotem = 2630,
    manaTideTotem = 10467,
    poisonCleansingTotem = 5923,
    stoneskinTotem = 194117,
    dragonRage = 375087
}
local highPrioPurge = {
    tipTheScales = GetSpellInfo(370553),
    blessingOfProtection = GetSpellInfo(1022),
    alterTime = GetSpellInfo(342246),
    thorns = GetSpellInfo(305497),
    powerInfusion = GetSpellInfo(10060),
    bloodlust = GetSpellInfo(2825),
    heroism = GetSpellInfo(32182),
    nullifyingShroud = GetSpellInfo(378464),
}
local purgeList = {
    tipTheScales = GetSpellInfo(370553),
    nullifyingShroud = GetSpellInfo(378464),
    naturesSwiftness = GetSpellInfo(132158),
    blessingOfSpellwarding = GetSpellInfo(204018),
    netherWard = GetSpellInfo(212295),
    felDomination = GetSpellInfo(333889),
    amplifyCurse = GetSpellInfo(328774),
    blessingOfProtection = GetSpellInfo(1022),
    divineFavor = GetSpellInfo(210294),
    temporalShield = GetSpellInfo(198111),
    powerInfusion = GetSpellInfo(10060),
    spiritwalkersGrace = GetSpellInfo(79206),
    iceFloes = GetSpellInfo(108839),
    bloodlust = GetSpellInfo(2825),
    heroism = GetSpellInfo(32182),
    holyWard = GetSpellInfo(213610),
    soulHarvest = GetSpellInfo(196098),
    thorns = GetSpellInfo(305497),
    alterTime = GetSpellInfo(342246),
    timeWarp = GetSpellInfo(80353),
    iceBarrier = GetSpellInfo(11426),
    prismaticBarrier = GetSpellInfo(235450),
    blazingBarrier = GetSpellInfo(235313),
    tempestBarrier = GetSpellInfo(382290),
    temporalVelocity = GetSpellInfo(382824),
    powerWordFortitude = GetSpellInfo(21562),
    powerWordShield = GetSpellInfo(17),
    levitate = GetSpellInfo(111759),
    bodyAndSoul = GetSpellInfo(65081),
    prayerOfMending = GetSpellInfo(41635),
    renew = GetSpellInfo(139),
    divineHymn = GetSpellInfo(64844),
    lifebloom = GetSpellInfo(33763),
    rejuvenation = GetSpellInfo(774),
    regrowth = GetSpellInfo(8936),
    wildGrowth = GetSpellInfo(48438),
    markOfTheWild = GetSpellInfo(1126),
    soulOfTheForest = GetSpellInfo(114108),
    riptide = GetSpellInfo(61295),
    earthShield = GetSpellInfo(974),
    tidalWaves = GetSpellInfo(53390),
    swirlingCurrents = GetSpellInfo(378102),
    envelopingMist = GetSpellInfo(124682),
}
local cantRootList = {
    totemGrabRoot = GetSpellInfo(116947),
    chainsOfIce = GetSpellInfo(204085),
    remorselessWinter = GetSpellInfo(233395),          
    entanglingRoots = GetSpellInfo(339),
    earthenGrasp = GetSpellInfo(235963),
    natureGrasp = GetSpellInfo(170855),
    massEntanglement = GetSpellInfo(102359),
    landslide = GetSpellInfo(355689),
    entrapment = GetSpellInfo(393456),
    steelTrap = GetSpellInfo(162480),
    steelclawTrap = GetSpellInfo(273909),
    trackerNet = GetSpellInfo(212638),
    superStickyTar = GetSpellInfo(201158),
    frostNova = GetSpellInfo(122),
    freeze = GetSpellInfo(33395),
    freezingCold = GetSpellInfo(386770),
    frostbite = GetSpellInfo(198121),
    voidTendrilGrasp = GetSpellInfo(114404),
    entrenchedinFlame = GetSpellInfo(233582),
    disable = GetSpellInfo(116706),
    clash = GetSpellInfo(324382),
    earthgrab = GetSpellInfo(64695),
    earthUnleashed = GetSpellInfo(356738),
    surgeofPower = GetSpellInfo(285515),
    thunderstruck = GetSpellInfo(199042),
    warbringer = GetSpellInfo(356356),
}
local damageReduction = {
    blur = GetSpellInfo(198589),
    unendingResolve = GetSpellInfo(104773),
    dampenHarm = GetSpellInfo(122278),
    fortifyingBrew = GetSpellInfo(243435),
    survivalInstincts = GetSpellInfo(61336),
    barkskin = GetSpellInfo(22812),
    ironbark = GetSpellInfo(102342),
    temporalShield = GetSpellInfo(198111),
    temporalShield2 = GetSpellInfo(198144),
}

local dhSpells = {
    sigilFlame = GetSpellInfo(204596),
    sigilChains = GetSpellInfo(202138),
    sigilSilence = GetSpellInfo(202137),
    sigilMisery = GetSpellInfo(207684),
    chaosNova = GetSpellInfo(179057),
    imprison = GetSpellInfo(217832),
    illidansGrasp = GetSpellInfo(205630),
    demonicTrample = GetSpellInfo(205629),
    consumeMagic = GetSpellInfo(278326),
    disrupt = GetSpellInfo(183752),
    darkness = GetSpellInfo(196718),
    meta = GetSpellInfo(187827),
    spectralSight = GetSpellInfo(188501),
    tormentor = GetSpellInfo(207029),
    torment = GetSpellInfo(185245),
    bigGrasp = GetSpellInfo(205630),

    infernalStrike = GetSpellInfo(189110),
    throwGlaive = GetSpellInfo(204157),
    immolationAura = GetSpellInfo(258920),
    felDevastation = GetSpellInfo(212084),
    fracture = GetSpellInfo(263642),
    soulCleave = GetSpellInfo(228477),
    elysianDecree = GetSpellInfo(390163),
    hunt = GetSpellInfo(370965),
    fieryBrand = GetSpellInfo(204021),
    felBlade = GetSpellInfo(232893),
    demonSpikes = GetSpellInfo(203720),
    soulCarver = GetSpellInfo(207407),
    soulBarrier = GetSpellInfo(263648),
    vengfulRetreat = GetSpellInfo(198793),
    reverseMagic = GetSpellInfo(205604),

    shadowMeld = GetSpellInfo(58984),
}

local dhDebuffs = {
    sigilFlame = GetSpellInfo(204598),
    sigilChains = GetSpellInfo(204843),
    sigilSilence = GetSpellInfo(204490),
    sigilMisery = GetSpellInfo(207685),
    masterOfGlaive = GetSpellInfo(213405),
    focusedAssault = GetSpellInfo(206891),
    frailty = GetSpellInfo(247456),
}

local dhTalents = {
    ascendingFlame = 428603,
    fieryBrand = 204021,
    fieryDemise = 389220,
    collectiveAnguish = 390152,
    stokeTheFlames = 393827,
    burningBlood = 390213,
    chaosImprint = 356510,
    tormentor = 207029,
    reverseMagic = 205604,
    bigGrasp = 205630,
    glimpse = 354489,
    demonicTrample = 205629,
    shadowMeld = 58984,
    detainment = 205596,
}

local highPrioStompList = {
    spiritLinkTotem = 53006,
    healingTideTotem = 59764,

    observer = 107100,
    counterstrikeTotem = 105451,
    tremorTotem = 5913,
    groundingTotem = 5925,
    skyfuryTotem = 105427,
    capacitorTotem = 61245,
    staticFieldTotem = 179867,
    earthGrabTotem = 60561,
    psyfiend = 101398,
    felObelisk = 179193,
    staticFieldTotem = 179867,
    warBanner = 119052,
    warBanner2 = 236320,
}

local highPrioStompListLookup = {}
for _, v in pairs(highPrioStompList) do
    highPrioStompListLookup[v] = true
end

local lowHPStompList = {
    spiritLinkTotem = 53006,
    healingTideTotem = 59764,
    counterstrikeTotem = 105451,
    tremorTotem = 5913,
    groundingTotem = 5925,
    skyfuryTotem = 105427,
    capacitorTotem = 61245,
}

local lowHPStompListLookup = {}
for _, v in pairs(lowHPStompList) do
    lowHPStompListLookup[v] = true
end

-- endregion SPELLS

local healerSpecIds = {
    [65]   = true, -- Holy Paladin
    [105]  = true, -- Restoration Druid
    [256]  = true, -- Discipline Priest
    [257]  = true, -- Holy Priest
    [264]  = true, -- Restoration Shaman
    [270]  = true, -- Mistweaver Monk
    [1468] = true, -- Pres evoker
}

local casterSpecIds = {
    [62]   = true, -- Arcane Mage
    [63]   = true, -- Fire Mage
    [64]   = true, -- Frost Mage
    [102]  = true, -- Balance Druid
    [105]  = true, -- Restoration Druid
    [258]  = true, -- Shadow Priest
    [262]  = true, -- Elemental Shaman
    [265]  = true, -- Affliction Warlock
    [266]  = true, -- Demonology Warlock
    [267]  = true, -- Destruction Warlock
    [1467] = true, -- Devoker
    [1473] = true, -- Augvoker
}

local casterLockList = {
    [dhSpells.sigilSilence] = true,
    [dhSpells.sigilMisery] = true,
    [dhSpells.imprison] = true,
}

local zugzugLockList = {
    [dhSpells.sigilChains] = true,
    [dhSpells.sigilMisery] = true,
    [dhSpells.imprison] = true,
}

local dkLockList = {
    [dhSpells.sigilSilence] = true,
    [dhSpells.sigilMisery] = true,
    [dhSpells.imprison] = true,
    [dhSpells.sigilChains] = true,
}

local hunterLockList = {
    [dhSpells.sigilMisery] = true,
    [dhSpells.imprison] = true,
}

local SpecPeelList = {
    [0] = {}, -- UNKNOWN

    -- MAGE
    [62] = casterLockList, -- Arcane
    [63] = casterLockList, -- Fire
    [64] = casterLockList, -- Frost

    -- PALADIN
    [65] = casterLockList, -- holy
    [66] = zugzugLockList, -- prot
    [70] = zugzugLockList, -- ret

    -- WARR
    [71] = zugzugLockList, -- arms
    [72] = zugzugLockList, -- fury
    [73] = zugzugLockList, -- prot

    -- DRUID
    [102] = casterLockList, -- balance
    [103] = zugzugLockList, -- feral
    [104] = zugzugLockList, -- guardian
    [105] = casterLockList, -- resto

    -- DK
    [250] = dkLockList, -- blood
    [251] = dkLockList, -- frost
    [252] = dkLockList, -- unholy

    -- Hunter
    [253] = hunterLockList, -- beast
    [254] = hunterLockList, -- mark
    [255] = hunterLockList, -- surv

    -- Priest
    [256] = casterLockList, -- disc
    [257] = casterLockList, -- holy
    [258] = casterLockList, -- shadow

    -- Rogue
    [259] = zugzugLockList, -- assa
    [260] = zugzugLockList, -- outlaw
    [261] = zugzugLockList, -- sub

    -- Shaman
    [262] = casterLockList, -- ele
    [263] = dkLockList, -- enh
    [264] = casterLockList, -- resto

    -- Warlock
    [265] = casterLockList, -- aff
    [266] = casterLockList, -- demo
    [267] = casterLockList, -- destro

    -- Monk
    [268] = zugzugLockList, -- brew
    [269] = zugzugLockList, -- ww
    [270] = casterLockList, -- mw

    -- DH
    [577] = zugzugLockList, -- havoc
    [581] = zugzugLockList, -- veng

    -- Evoker
    [1467] = casterLockList, -- Devo
    [1468] = casterLockList, -- Pres
    [1473] = casterLockList, -- Aug
}

local frame = CreateFrame("Frame", "LastSpellTrackerFrame", UIParent)
frame:Hide()  -- Hide the frame

-- Variable to store the name of the last spell
local lastSpellName = ""

local lastSigilCast = 0
local lastSilenceCast = 0
local lastMiseryCast = 0
local lastChainsCast = 0

-- Event handler function
local function OnEvent(self, event, unit, spellLineID, spellID)
    if event == "UNIT_SPELLCAST_SUCCEEDED" and unit == "player" then
        lastSpellName = GetSpellInfo(spellID)

        if lastSpellName == dhSpells.sigilChains then
            lastSigilCast = GetTime()
            lastChainsCast = GetTime()
        elseif lastSpellName == dhSpells.sigilSilence then
            lastSigilCast = GetTime()
            lastSilenceCast = GetTime()
        elseif lastSpellName == dhSpells.sigilMisery then
            lastSigilCast = GetTime()
            lastMiseryCast = GetTime()
        end

        -- You can print to the chat window to verify it's working
    end
end

-- Set the frame to listen for the UNIT_SPELLCAST_SUCCEEDED event
frame:RegisterEvent("UNIT_SPELLCAST_SUCCEEDED")
frame:SetScript("OnEvent", OnEvent)

-- Utility functions

local globalState = {
    enemyHealer = nil,
    enemyDps = {},
    enemyCanHit = {},
    enemyMaxHp = 100,
    enemyMinHp = 100,
    enemyCasters = 0,
    enemyMelee = 0,
    enemyBursting = false,

    teamHealer = nil,
    teamHealerInCC = false,
    teamDps = {},
    teamTargets = {},
    teamMaxHp = 100,
    teamMinHp = 100,
    teamCasters = 0,
    teamMelee = 0,
    teamBursting = false,
    teamHasRogue = false,
    teamHasWW = false,
}

-- region UTILITY

local function IsInArena()
    local inInstance, instanceType = IsInInstance()
    return inInstance and instanceType == "arena"
end

local getGCDonce = function()
    -- SpellID:61304 -> (Dummy)SpellName:"Global Cooldown"
    local _, duration = GetSpellCooldown(61304);
    return duration or 0
end

local function castTarget(unit)
    local castTargetUnit = nil
    if IsGuid(unit) then
        castTargetUnit = UnitCastingTarget(unit)
    else
        castTargetUnit = UnitCastingTarget(UnitGUID(unit))
    end
    if IsGuid(castTargetUnit) then
        return castTargetUnit
    else
        return nil
    end
end

local rosterCache  = CacheContext:getConstCacheCell()

local function getRoster()
    return rosterCache:GetOrSet("roster", function()
        local numGroupMembers = GetNumGroupMembers()
        local prefix = nil
        if IsInRaid() then
            prefix = "raid"
        else
            prefix = "party"
            numGroupMembers = numGroupMembers - 1
        end

        if not prefix or numGroupMembers <= 0 then
            if not prefix and numGroupMembers ~= 0 then
                print("Unknown group type")
            end
            return { "player" }
        end

        local roster = {}
        for i = 1, numGroupMembers do
            local unit = prefix .. i
            roster[i] = unit
        end
        return roster
    end)
end

local function getEnemies()
    return rosterCache:GetOrSet("enemies", function()
        if not IsInArena() then return { "target" } end

        local enemies = {}
        for i = 1, 3 do
            local unit = "arena" .. i
            if UnitExists(unit) and not UnitIsDeadOrGhost(unit) then
                table.insert(enemies, unit)
            end

            unit = "arenapet" .. i
            if UnitExists(unit) and not UnitIsDeadOrGhost(unit) then
                table.insert(enemies, unit)
            end
        end

        return enemies
    end)
end

local function unitMoving(unit)
    return GetUnitSpeed(unit) > 0
end

local function inCombat()
    return UnitAffectingCombat("player")
end

local function getFury()
    return UnitPower("player", 17)
end

local function isHealer(unit)
    local specId = UnitSpecializationID(unit)
    return (not specId and false) or healerSpecIds[specId] or false
end

local function inMelee(unit)
    return IsSpellInRange(dhSpells.soulCarver, unit) == 1
end

local losRangeCache = CacheContext:getConstCacheCell()

local function unitLos(unit, base)
    base = base or "player"
    local key = (UnitGUID(unit) or unit) .. (UnitGUID(base) or base) .. "LOS"

    return losRangeCache:GetOrSet(key, function()
        local x1, y1, z1 = GetUnitPosition(base)
        local x2, y2, z2 = GetUnitPosition(unit)
        if not x1 or not x2 then return false end

        local hitFlags = bit.bor(0x1, 0x10, 0x100)

        local x,y,z = TraceLine(x1, y1, z1 + 2.25, x2, y2, z2 + 2.25, hitFlags)

        return x == 0 and y == 0 and z == 0
    end)
end

local function unitDistanceTwo(unit, other)
    return GetDistance2D(other, unit) or 100
end

local function unitDistance(unit)
    local key = (UnitGUID(unit) or unit) .. "RANGE"

    return losRangeCache:GetOrSet(key, function()
        return GetDistance2D("player", unit) or 100
    end)
end

local function unitDistanceNoCache(unit)
    return GetDistance2D("player", unit) or 100
end

local function unitDistancePositions(x1, y1, x2, y2)
    local dx = x2 - x1
    local dy = y2 - y1
    return math.sqrt(dx * dx + dy * dy)
end

local function unitHp(unit)
    return (UnitHealth(unit) / UnitHealthMax(unit)) * 100
end

local function spellCharges(spell)
    return GetSpellCharges(spell)
end

local function spellCdLeft(spell)
    local start, duration = GetSpellCooldown(spell)
    local cdLeft = (start == 0 and 0) or (start + duration - GetTime())

    return cdLeft
end

local function CanCast(spell, charges)
    if charges and spellCharges(spell) == 0 then
        return false
    end

    return spellCdLeft(spell) < 0.2
end

local function isCaster(unit)
    local specId = UnitSpecializationID(unit)
    return (not specId and false) or casterSpecIds[specId] or healerSpecIds[specId] or false
end

local function isMelee(unit)
    local specId = UnitSpecializationID(unit)
    return (not specId and false) or (not casterSpecIds[specId] and not healerSpecIds[specId])  or false
end 

local function isTargettingCaste(unit)
    local targetUnitId = unit .. "target"
    return isCaster(targetUnitId) -- and UnitIsFriend(targetUnitId)
end

local function isCastingOrChanneling(unit)
    local casting, _, _, startTime, endTime, _, _, interruptable = UnitCastingInfo(unit)
    if not casting then
        casting, _, _, startTime, endTime, _, interruptable = UnitChannelInfo(unit)
    end

    if casting then
        local elapsedTime = (GetTime() * 1000) - startTime
        return casting, elapsedTime, interruptable, endTime
    end

    return casting or false
end

local function calculateNewPosition(x, y, z, angle, speed, time)
    -- Calculate the change in position
    local dx = speed * time * math.cos(angle)
    local dy = speed * time * math.sin(angle)
    local dz = 0 -- Assuming no change in the z-axis

    -- Update the position
    local newX = x + dx
    local newY = y + dy
    local newZ = z + dz

    return newX, newY, newZ
end

local function calculateStrafeOffset(unit)
    local flags = GetUnitMovementFlags(unit)

    if not flags or flags == 0 then return 0 end

    flags = bit.band(flags, 15)

    if flags == 9 then
        return 45
    elseif flags == 8 then
        return 90
    elseif flags == 5 then
        return -45
    elseif flags == 4 then
        return -90
    end

    return 0
end

local function movingTooFast(unit)
    return GetUnitSpeed(unit) > 15
end

local function predictTargetPostion(unit)
    local playerX, playerY, playerZ = GetUnitPosition(unit)
    local facing = UnitFacing(unit)
    local speed = GetUnitSpeed(unit)
    if not facing or not speed then return end

    local strafeOffset = calculateStrafeOffset(unit)
    strafeOffset = strafeOffset * (math.pi / 180)

    return calculateNewPosition(playerX, playerY, playerZ, facing - strafeOffset, speed, 0.5)
end

local function CastAtPosition(oX, oY, oZ, spell)
    Cast(spell)
    local i = -100
    local mouselookup = IsMouseButtonDown(2)
    if mouselookup then MouselookStop() end
    while SpellIsTargeting() and i <= 100 do
        ClickPosition(oX, oY, oZ)
        i = i + 1
        oZ = i
    end
    if mouselookup then MouselookStart() end
    if i >= 100 and SpellIsTargeting() then
        SpellStopTargeting()
    end

    return not SpellIsTargeting()
end

local function CastGroundSpeed(spell, target, withPrediction)
    local oX, oY, oZ = GetUnitPosition(target)
    local speed = GetUnitSpeed(target)
    if speed > 0 and withPrediction then
        oX, oY, oZ = predictTargetPostion(target)
    end

    return CastAtPosition(oX, oY, oZ, spell)
end

local function CastGroundFallback(spell, target)
    return CastGroundSpeed(spell, target, true) or CastGroundSpeed(spell, target, false)
end

local function CalculateFacingAngle(playerX, playerY, targetX, targetY)
    local deltaY = targetY - playerY
    local deltaX = targetX - playerX
    local angle = math.atan2(deltaY, deltaX)

    -- convert from radians to degrees
    angle = angle * (180 / math.pi)

    -- adjust for game orientation (0 degrees is south, 90 is west)
    angle = angle - 90
    if angle < 0 then
        angle = 360 + angle
    end

    return angle
end

local function IsUnitFacing(target)
    local ax, ay, az = GetUnitPosition("player")
    local bx, by, bz = GetUnitPosition(target)
    if not ax or not bx then return false end
    local dx, dy, dz = ax-bx, ay-by, az-bz
    local rotation = UnitFacing("player");
    local value = (dy*math.sin(-rotation) - dx*math.cos(-rotation)) /
    math.sqrt(dx*dx + dy*dy)
    local isFacing = value > 0.25
    return isFacing
 end

local function facehack(target)
    if target then
        local playerX, playerY, playerZ = GetUnitPosition("player")
        local targetX, targetY, targetZ = GetUnitPosition(target)
        local angle = CalculateFacingAngle(playerX, playerY, targetX, targetY)
        if true or not IsUnitFacing(target) then
            local radians = (angle + 90) * (math.pi / 180)
            FaceDirection(radians, false)
        end
    end
end

local function isTargetImmune(target)
    local key = (UnitGUID(target) or target) .. "IMMUNE"
    return auraCache:GetOrSet(key, function()
        return hasBuffInList(target, totalImmunityList) or hasDebuffInList(target, totalImmunityList)
    end)
end

local function targetHasDamageReduction(target)
    local key = (UnitGUID(target) or target) .. "DMGREDUCTION"
    return auraCache:GetOrSet(key, function()
        return hasBuffInList(target, damageReduction)
    end)
end

local function targetIsBursting(target)
    local key = (UnitGUID(target) or target) .. "BURSTING"
    return auraCache:GetOrSet(key, function()
        return hasBuffInList(target, burstlist)
    end)
end

local function inBreakableCC(target)
    local key = (UnitGUID(target) or target) .. "BREAKABLECC"
    return auraCache:GetOrSet(key, function()
        return hasDebuffInList(target, breakableCC)
    end)
end

local function isTargetImmuneRemaining(target, debuffRemaain, buffRemain)
    if not unitLos(target) then return true end
    for _, immunity in pairs(totalImmunityList) do
        local remaining = hasDebuffRemaining(target, immunity)
        if remaining > debuffRemaain then
            return true
        end

        remaining = hasBuffRemaining(target, immunity)
        if remaining > buffRemain then
            return true
        end
    end

    return false
end

local function inCC(target)
    local key = (UnitGUID(target) or target) .. "CCANY"

    return auraCache:GetOrSet(key, function()
        return hasDebuffInList(target, ccList)
    end)
end

local function isTargetInCCRemaining(target, debuffRemaain)
    for _, immunity in pairs(ccList) do
        if hasDebuffRemaining(target, immunity) > debuffRemaain then
            return true
        end
    end

    return false
end

local function maxCCRemain(target)
    local max = 0
    for _, immunity in pairs(ccList) do
        local remaining = hasDebuffRemaining(target, immunity)
        if remaining > max then
            max = remaining
        end
    end

    return max
end

local function shouldReverseMagic(target)
    for _, cc in pairs(reverseMagicList) do
        if hasDebuffRemaining(target, cc) >= 3 then
            return true
        end
    end

    return false
end

local function rooted(target)
    return hasDebuffInList(target, cantRootList)
end

local function canRoot(target)
    for _, immunity in pairs(cantRootList) do
        local remaining = hasDebuffRemaining(target, immunity)
        if remaining <= 1 then
            return true
        end

        remaining = hasDebuffRemaining(target, immunity)
        if remaining <= 1 then
            return true
        end
    end

    return false
end

local draw = Draw:New()

local function targetDraw()
    local playerX, playerY, playerZ = GetUnitPosition("player")
    local rotation = UnitFacing("player");

    if not playerX or not playerY or not playerZ or not rotation then return end
    draw:SetColor(204, 51, 255, 255)
    if UnitExists("target") then
        if inMelee("target") then
            draw:SetColor(0, 255, 0, 255)
        else
            draw:SetColor(255, 0, 0, 255)
        end
    end

    draw:SetWidth(1)

    draw:Arc(playerX, playerY, playerZ, 6, 90, rotation)
end

local function enemyHealerDraw()
    if globalState.enemyHealer and UnitExists(globalState.enemyHealer) then
        local playerX, playerY, playerZ = GetUnitPosition("player")
        local targetX, targetY, targetZ = GetUnitPosition(globalState.enemyHealer)

        if not targetX or not targetY or not targetZ then return end
        if not playerX or not playerY or not playerZ then return end
        draw:SetColor(255, 0, 0, 255)
        draw:SetWidth(3)

        if unitDistance(globalState.enemyHealer) < 30 and unitLos(globalState.enemyHealer) then
            draw:SetColor(255, 255, 0, 255)
            if unitDistance(globalState.enemyHealer) < 20 then
                draw:SetColor(0, 255, 0, 255)
            end
        end

        draw:Line(playerX, playerY, playerZ, targetX, targetY, targetZ)
    end
end


local function unitLikelyToMove(unit)
    if not UnitIsPlayer(unit) then return false end
    if rooted(unit) then return false end

    if isMelee(unit) then
        local target = UnitTarget(unit)
        if not target then return true end
        return (GetDistance2D(target, unit) or 100) <= 10
    else
        return GetUnitSpeed(unit) < 7
    end
end

local function sigilFlameLogic(spell, enemy, fieryBypass)
    if not CanCast(spell) then return false end

    local currentCharges, _, coolDownStart, coolDownDuration = GetSpellCharges(spell)
    if currentCharges > 1 then return true end

    local chargeRemaing = coolDownStart + coolDownDuration - GetTime()
    if chargeRemaing < 1.2 then return true end

    if fieryBypass and globalState.teamBursting or globalState.enemyMinHp < 80 then
        if spellCdLeft(dhSpells.fieryBrand) < 15 then
            return false
        end
    end

    if fieryBypass and hasDebuff(enemy, dhDebuffs.fieryBrand) then return true end

    if targetHasDamageReduction(enemy) then return false end
    if unitHp(enemy) < 10 then return true end

    local enemies = getEnemies()

    for _, peeps in ipairs(enemies) do
        if UnitExists(peeps) and not UnitIsUnit(enemy, peeps) and GetDistance2D(enemy, peeps) <= 10 and not unitLikelyToMove(peeps)
            and not isTargetImmune(peeps) then
                if inBreakableCC(peeps) then return false end
            return true
        end
    end
end

local function getEnemyPetToTaunt()
    local enemies = getEnemies()
    for _, v in ipairs(enemies) do
        if UnitExists(v) and not UnitIsPlayer(v) and not UnitIsUnit("target", v) and not UnitIsDeadOrGhost(v)
            and unitDistance(v) <= 30 and unitLos(v) and IsUnitFacing(v) then
            return v
        end
    end
end

local function castSpellForAoeSread(spell, target, minReq)
    return CastGroundFallback(spell, target)
end

local highPrioPurgeNear = function ()
    if not CanCast(dhSpells.consumeMagic) then return false end
    local enemies = getEnemies()

    for _, enemy in ipairs(enemies) do
        if unitDistance(enemy) <= 30 and unitLos(enemy) and not isTargetImmune(enemy) then
            if hasBuffInListElapsed(enemy, highPrioPurge, 0.4) then
                ShowSpellPopup(dhSpells.consumeMagic, "Purging " .. enemy)
                facehack(enemy)
                return Cast(dhSpells.consumeMagic, enemy)
            end
        end
    end
end

local purgeTarget = function (target)
    if not CanCast(dhSpells.consumeMagic) then return false end

    return hasBuffInListElapsed(target, purgeList, 0.4) and Cast(dhSpells.consumeMagic)
end

-- endregion UTILITY

local maintenance = function(target, fury, souls)
    local frailtyStacks = hasDebuffCount(target, dhDebuffs.frailty)
    local melee = inMelee(target)
    local useBigCds = (not targetHasDamageReduction(target)) or unitHp(target) < 30
    local canGapClose = GetUnitSpeed("player") < 20 and CanCast(dhSpells.felBlade) and unitDistance(target) < 15

    if peelModeOn and globalState.enemyHealer then
        if (unitDistanceTwo(target, globalState.enemyHealer) >= 30) then
            canGapClose = false
        end
    end

    local poolFury = false

    if unitHp("player") < 60 and CanCast(dhSpells.felDevastation) and unitDistance(target) < 20 then
        if fury > 50 then
            if CanCast(dhSpells.fracture) and melee then
                facehack(target)
                return Cast(dhSpells.fracture)
            end

            ShowSpellPopup(dhSpells.felDevastation, "Healing up up")
            facehack(target)
            return Cast(dhSpells.felDevastation)
        end

        ShowSpellPopup(dhSpells.felDevastation, "Pooling fury to heal")
        poolFury = true
    end

    if CanCast(dhSpells.soulCarver) and (melee or canGapClose) then
        if hasDebuff(target, dhDebuffs.fieryBrand) or spellCdLeft(dhSpells.fieryBrand) > 40 then
            if not melee and canGapClose then
                ShowSpellPopup(dhSpells.felBlade, "Gap closing")
                facehack(target)
                return Cast(dhSpells.felBlade)
            end

            facehack(target)
            return Cast(dhSpells.soulCarver)
        end
    end

    if globalState.teamBursting or globalState.enemyMinHp < 40 then
        if not canGapClose and useBigCds and CanCast(dhSpells.hunt) and not rooted("player") and GetUnitSpeed("player") < 20 and unitLos(target) and unitDistance(target) <= 50 then
            facehack(target)
            return Cast(dhSpells.hunt)
        end
    end

    if globalState.teamBursting or globalState.enemyMinHp < 80 then
        if CanCast(dhSpells.fieryBrand) and unitLos(target) and unitDistance(target) <= 15 then
            if hasBuffRemaining("player", dhSpells.immolationAura) > 5 then
                facehack(target)
                return Cast(dhSpells.fieryBrand)
            elseif CanCast(dhSpells.immolationAura) then
                return Cast(dhSpells.immolationAura)
            end
        end
    end

    if not movingTooFast(target) and sigilFlameLogic(dhSpells.sigilFlame, target, true) and unitDistance(target) <= 30 and (IsPlayerSpell(dhTalents.ascendingFlame) or not hasDebuff(target, dhDebuffs.sigilFlame)) then
        return castSpellForAoeSread(dhSpells.sigilFlame, target)
    end

    if CanCast(dhSpells.immolationAura) and fury <= 100 then
        return Cast(dhSpells.immolationAura)
    end

    if not melee and canGapClose then
        ShowSpellPopup(dhSpells.felBlade, "Gap closing")
        facehack(target)
        return Cast(dhSpells.felBlade)
    end

    if not poolFury and fury >= 30 and CanCast(dhSpells.soulCleave) and melee then
        facehack(target)
        return Cast(dhSpells.soulCleave)
    end

    if useBigCds and sigilFlameLogic(dhSpells.elysianDecree, target) and unitLos(target) and unitDistance(target) <= 30 then
        if castSpellForAoeSread(dhSpells.elysianDecree, target) then
            return true
        end
    end

    if purgeTarget(target) then return end

    if CanCast(dhSpells.fracture) and melee and fury <= 50 then
        facehack(target)
        return Cast(dhSpells.fracture)
    end

    return false
end

local fillerSpells = function(player, target, fury, souls)
    if CanCast(dhSpells.felBlade) and unitLos(target) and unitDistance(target) <= 15 then
        facehack(target)
        return Cast(dhSpells.felBlade)
    end

    if CanCast(dhSpells.throwGlaive) and unitLos(target) and unitDistance(target) <= 30 then
        facehack(target)
        return Cast(dhSpells.throwGlaive)
    end

    return false
end

local singleTarget = function(player, target, fury, souls)
    return fillerSpells(player, target, fury, souls)
end

local simpleFillerRotation = function (target, fury)
    if CanCast(dhSpells.fracture) and inMelee(target) then
        facehack(target)
        return Cast(dhSpells.fracture, target)
    end

    if fury >= 30 and CanCast(dhSpells.soulCleave) and inMelee(target) then
        facehack(target)
        return Cast(dhSpells.soulCleave, target)
    end

    if CanCast(dhSpells.throwGlaive) and unitLos(target) then
        facehack(target)
        return Cast(dhSpells.throwGlaive, target)
    end
end

local function findNearbyToHit()
    local nearestUnit = nil
    local nearestDistance = 1000
    local nearbyUnits = getEnemies()
    for _, unit in ipairs(nearbyUnits) do
        if UnitIsPlayer(unit) and unitDistance(unit) < nearestDistance and not isTargetImmune(unit) then
            nearestUnit = unit
            nearestDistance = unitDistance(unit)
        end
    end

    return nearestUnit
end

local function nearbyToHit(fury, souls)
    local nearestUnit = findNearbyToHit()
    if nearestUnit == nil or not inMelee(nearestUnit) then return false end

    return simpleFillerRotation(nearestUnit, fury)
end

local defensives = function (player)
    local health = unitHp(player)
    local hasDefensive = hasBuff(player, dhSpells.demonSpikes) or hasBuff(player, dhSpells.soulBarrier)

    local roster = getRoster()
    if IsPlayerSpell(dhTalents.reverseMagic) and CanCast(dhSpells.reverseMagic) then
        for _, team in ipairs(roster) do
            if shouldReverseMagic(team) and not isTargetImmune(team) then
                if unitDistance(team) <= 10 and (isHealer(team) or globalState.enemyMinHp < 40) then
                    return Cast(dhSpells.reverseMagic)
                elseif CanCast(dhSpells.infernalStrike) and unitDistance(team) <= 30 and isHealer(team) and unitLos(team) then
                    return CastGroundFallback(dhSpells.infernalStrike, team)
                end
            end
        end
    end

    if CanCast(dhSpells.darkness) then
        for _, team in ipairs(roster) do
            if unitHp(team) <= 40 and not isTargetImmune(team) and not UnitIsDeadOrGhost(team) then
                if unitDistance(team) <= 6 then
                    return Cast(dhSpells.darkness)
                elseif CanCast(dhSpells.infernalStrike) and unitHp(team) <= 30 and unitDistance(team) >= 10 and unitDistance(team) <= 30 and unitLos(team) then
                    return CastGroundFallback(dhSpells.infernalStrike, team)
                end
            end
        end

        if health < 30 then
            return Cast(dhSpells.darkness)
        end
    end

    if health < 40 and CanCast(dhSpells.meta) then
        return Cast(dhSpells.meta)
    end

    if globalState.enemyMelee > 0 and CanCast(dhSpells.demonSpikes) and not hasBuff(player, dhSpells.demonSpikes) and
        ((not hasDefensive and health < 80)
            or health < 20) then
        return Cast(dhSpells.demonSpikes)
    end

    if CanCast(dhSpells.soulBarrier) and
        ((not hasDefensive and health < 60)
            or health < 40) then
        return Cast(dhSpells.soulBarrier)
    end

    return false
end

local lastCcSigil = 0

local debuffCheckList = {
    dhDebuffs.sigilSilence,
    dhDebuffs.sigilMisery,
    dhDebuffs.sigilChains,
    dhDebuffs.imprison
}

local lockdownUnit = function (focus, checkRoots, defensive, aggressive)
    local distance = unitDistance(focus)
    if not focus or not unitLos(focus) or not UnitCanAttack("player", focus) or distance > 30 or not UnitIsPlayer(focus) then return end

    if isTargetImmuneRemaining(focus, 1.0, 0.9) then return end

    local ccRemain = maxCCRemain(focus)

    -- More than a global left
    if ccRemain > 2 then return end
    if lastSigilCast + 1.2 > GetTime() then
        return false
    end

    local maxDuration = 0
    for _, debuff in pairs(debuffCheckList) do
        local duration = hasDebuffRemaining(focus, debuff)
        if duration > maxDuration then
            maxDuration = duration
        end
    end

    local specId = UnitSpecializationID(focus) or 0
    local peelList = SpecPeelList[specId] or {}

    if peelList == {} then
        print('Incorrect spec for focus: ' .. focus)
    end

    local shouldRoot = checkRoots and isMelee(focus) and isTargettingCaste(focus)
    local tooFast = movingTooFast(focus)

    local sigilDrAmount = 0.5
    if defensive and globalState.teamMinHp < 40 then
        sigilDrAmount = 0.5
    elseif globalState.enemyMinHp < 40 then
        sigilDrAmount = 0.25
    end

    if false and aggressive then
        print('Sigil silence DR: ' .. GetDrCat(focus, "silence"))
        print('Sigil misery DR: ' .. GetDrCat(focus, "disorient"))
        print('Imprison DR: ' .. GetDrCat(focus, "incapacitate"))
        print('Grasp DR: ' .. GetDrCat(focus, "stun"))
    end

    if not tooFast and shouldRoot and CanCast(dhSpells.sigilChains) and maxDuration < 1.1 and peelList[dhSpells.sigilChains] and
        not rooted(focus) and not isTargetImmune(focus) then
        return CastGroundFallback(dhSpells.sigilChains, focus) or true
    end

    -- if check roots is 2 we only care about roots :D
    if checkRoots == 2 then return false end

    local sigilCastOrHold = function(spell)
        if ccRemain < 1.1 then
            return CastGroundFallback(spell, focus) or true
        elseif ccRemain < 2 then
            ShowSpellPopup(spell, "Holding global for cc chain")
            return true
        end
        return false
    end

    if aggressive and IsPlayerSpell(dhTalents.bigGrasp) and CanCast(dhSpells.bigGrasp)
        and GetDrCat(focus, "stun") > 0.5 then
        if maxDuration < 0.1 and distance <= 15 and GetUnitSpeed("player") < 20 then
            facehack(focus)
            return Cast(dhSpells.bigGrasp, focus) or true
        end
    end

    if not tooFast and CanCast(dhSpells.sigilSilence) and maxDuration < 1.1 and peelList[dhSpells.sigilSilence]
        and GetDrCat(focus, "silence") >= sigilDrAmount then
            return sigilCastOrHold(dhSpells.sigilSilence)
    end

    -- Below here if we're targetting the unit we don't want to use any of these
    if UnitIsUnit("target", focus) then return false end

    local focusGuid = UnitGUID(focus) or focus
    if globalState.teamTargets[focusGuid] then return false end

    if (IsPlayerSpell(dhTalents.detainment) or distance > 5) and CanCast(dhSpells.imprison) and maxDuration == 0 and peelList[dhSpells.imprison]
        and GetDrCat(focus, "incapacitate") >= sigilDrAmount then
        facehack(focus)
        return Cast(dhSpells.imprison, focus) or true
    end

    if distance > 5 and not tooFast and CanCast(dhSpells.sigilMisery) and maxDuration < 1.1 and peelList[dhSpells.sigilMisery]
        and GetDrCat(focus, "disorient") >= sigilDrAmount then
            return sigilCastOrHold(dhSpells.sigilMisery)
    end

    if aggressive and IsPlayerSpell(dhTalents.bigGrasp) and CanCast(dhSpells.bigGrasp)
        and GetDrCat(focus, "stun") >= 0.5 and not isTargetImmune(focus) then
        if maxDuration == 0 and distance <= 15 and GetUnitSpeed("player") < 20 then
            facehack(focus)
            return Cast(dhSpells.bigGrasp, focus) or true
        elseif maxDuration < 0.9 and CanCast(dhSpells.infernalStrike) and distance <= 30 and distance >= 10 and unitLos(focus) then
            ShowSpellPopup(dhSpells.infernalStrike, "Leaping to big grip")
            return CastGroundFallback(dhSpells.infernalStrike, focus) or true
        end
    end

    return false
end

local ccUtility = function ()
    local minFriendlyHp = 100
    local minFriendNeedsRoots = false

    if peelModeOn then
        if globalState.enemyHealer and lockdownUnit(globalState.enemyHealer, minFriendNeedsRoots, false, true) then return true end
    end

    local isLShitDown = (GetKeyState("LALT") == 1)
    if isLShitDown then
        ShowSpellPopup(dhSpells.sigilMisery, "Peel mode")

        for _, val in ipairs(globalState.enemyCanHit) do
            if lockdownUnit(val, minFriendNeedsRoots) then return true end
        end
    end

    if globalState.enemyHealer and unitLos(globalState.enemyHealer) 
        and (globalState.teamBursting or globalState.enemyMinHp < 30) then
        if lockdownUnit(globalState.enemyHealer, minFriendNeedsRoots) then return true end
    end

    local roster = getRoster()
    for _, team in ipairs(roster) do
        if UnitIsPlayer(team) and unitHp(team) < minFriendlyHp then
            minFriendlyHp = unitHp(team)
            minFriendNeedsRoots = false

            if (isHealer(team) or isCaster(team)) and canRoot(team) then -- If caster and can move
                minFriendNeedsRoots = true
            end
        end
    end

    local minEnemyHp = 100
    local enemyHealer = nil
    local lowestEnemy = nil

    local enemyTargets = {}
    local enemies = getEnemies()
    for _, enemy in ipairs(enemies) do
        if UnitIsPlayer(enemy) then
            if unitHp(enemy) < minEnemyHp then
                minEnemyHp = unitHp(enemy)
                lowestEnemy = enemy
            end

            if not isHealer(enemy)then
                if unitLos(enemy) then
                    local shouldLockdown = false
                    local shouldRoot = true
                    local enemyTarget = UnitTarget(enemy)

                    if enemyTarget and hasBuffInListRemaining(enemy, burstlist, 3, 0.4) then
                        if unitDistance(enemyTarget) < 10 then
                            shouldRoot = false
                        end

                        local healerInCc = false
                        if globalState.teamHealer and inCC(globalState.teamHealer) then
                            healerInCc = true
                        end

                        if globalState.teamMinHp < 30 then
                            shouldLockdown = true
                        elseif globalState.enemyMinHp < 90 and (not targetHasDamageReduction(enemyTarget) or healerInCc) then
                            shouldLockdown = true
                        end

                        if shouldLockdown then
                            -- print('Enemy ' .. enemy .. ' is bursting trying to lock them down')
                            if lockdownUnit(enemy, shouldRoot, true) then return true end
                        end
                    end

                    table.insert(enemyTargets, enemy)
                end
            else
                enemyHealer = enemy
            end
        end
    end

    if lowestEnemy and enemyHealer and not unitLos(lowestEnemy, enemyHealer) then
        if CanCast(dhSpells.sigilChains) and lastSpellName ~= dhSpells.sigilChains then
            if unitLos(enemyHealer) and unitDistance(enemyHealer) <= 30 and hasDebuffRemaining(enemyHealer, dhDebuffs.sigilChains) < 1 then
                return CastGroundSpeed(dhSpells.sigilChains, enemyHealer, false)
            end

            if unitLos(lowestEnemy) and unitDistance(lowestEnemy) <= 30 and hasDebuffRemaining(lowestEnemy, dhDebuffs.sigilChains) < 1 then
                return CastGroundSpeed(dhSpells.sigilChains, lowestEnemy, false)
            end
        end
    end

    if not enemyHealer and lowestEnemy and (minFriendlyHp < 80 or globalState.teamBursting) then
        for _, enemy in pairs(enemyTargets) do
            if enemy ~= lowestEnemy and lockdownUnit(enemy, minFriendNeedsRoots) then return true end
        end
    end

    if enemyHealer and unitLos(enemyHealer) and (globalState.enemyMinHp < 60 or globalState.teamBursting) then
        if lockdownUnit(enemyHealer, minFriendNeedsRoots) then return true end
    elseif minFriendlyHp < minEnemyHp and globalState.teamMinHp < 50 then
        for _, enemy in pairs(enemyTargets) do
            if lockdownUnit(enemy, minFriendNeedsRoots) then return true end
        end
    end
end

local chaosBrand = 1490
local throwGlaiveForBuff = function ()
    if true or not CanCast(dhSpells.throwGlaive) or not IsPlayerSpell(dhTalents.chaosImprint) then return false end

    for _, enemy in pairs(NeP.OM:Get('Enemy')) do
        if unitLos(enemy) and UnitIsPlayer(enemy) and unitDistance(enemy) <= 30 and not hasDebuff(enemy, chaosBrand) and not inBreakableCC(enemy) and not isTargetImmune(enemy) then
            facehack(enemy)
            return Cast(dhSpells.throwGlaive, enemy)
        end
    end

    return false
end

local assistTeam = function ()
    if throwGlaiveForBuff() then return true end
end

local kickPercent = 40
local channelKickTime = 600

local function generateNewRandomKicks()
    kickPercent = math.random(40, 80)
    channelKickTime = math.random(500, 700)

    return C_Timer.After(math.random(1.5, 3), generateNewRandomKicks)
end

generateNewRandomKicks()

local kickAnyone = function ()
    local enemies = getEnemies()

    for _, enemy in ipairs(enemies) do
        if isCastingOrChanneling(enemy) and unitLos(enemy) and UnitIsPlayer(enemy) then
            local casting, elapsed, notinterrupt, endTime = isCastingOrChanneling(enemy)
            if casting and interruptListLookup[casting] then
                if not notinterrupt and CanCast(dhSpells.disrupt) and elapsed > 100 and unitDistance(enemy) > 10 and unitLos(enemy) and CanCast(dhSpells.infernalStrike) and unitDistance(enemy) < 30 then
                    ShowSpellPopup(dhSpells.infernalStrike, "Leaping for kick")
                    return CastGroundFallback(dhSpells.infernalStrike, enemy)
                end

                if elapsed > channelKickTime and not notinterrupt  and CanCast(dhSpells.disrupt) and unitDistance(enemy) <= 10 and isTargetImmune(enemy) == false then
                    ShowSpellPopup(dhSpells.disrupt, "Kicking " .. casting .. " by " .. enemy)
                    return Cast(dhSpells.disrupt, enemy)
                elseif glimpseLookup[casting] and not rooted("player") then
                    local timeLeft = endTime - (GetTime() * 1000)
                    local target = castTarget(enemy)
                    if target and target == UnitGUID("player") then
                        if timeLeft < 200 and IsPlayerSpell(dhTalents.glimpse) and CanCast(dhSpells.vengfulRetreat) then
                            ShowSpellPopup(dhSpells.vengfulRetreat, "Retreating to avoid CC")
                            return Cast(dhSpells.vengfulRetreat)
                        elseif timeLeft < 1000 then
                            if IsPlayerSpell(dhTalents.shadowMeld) and CanCast(dhSpells.shadowMeld) then
                                if timeLeft < 200 then
                                    ShowSpellPopup(dhSpells.shadowMeld, "Shadowmelding to avoid CC")
                                    return Cast(dhSpells.shadowMeld)
                                else
                                    ShowSpellPopup(dhSpells.shadowMeld, "Holding global for meld")
                                    return true
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

local function bigGripRotation()
    local target = nil

    local enemies = getEnemies()
    for _, enemy in ipairs(enemies) do
        if unitLos(enemy) and UnitIsPlayer(enemy) and not inCC(enemy) and not isTargetImmune(enemy)
            and GetDrCat(enemy, "stun") >= 0.5 then
            if isHealer(enemy) then
                if unitDistance(enemy) <= 15 then
                    target = enemy
                    break
                elseif CanCast(dhSpells.infernalStrike) and unitDistance(enemy) <= 30 then
                    ShowSpellPopup(dhSpells.bigGrasp, "Leap for grip offensive")
                    return CastGroundSpeed(dhSpells.infernalStrike, enemy, false) or true
                end
            end
        end
    end

    if not target then
        local enemy = "target"
        if unitDistance(enemy) <= 15 and unitLos(enemy) and UnitIsPlayer(enemy) and not inCC(enemy) and not isTargetImmune(enemy) and
            GetDrCat(enemy, "stun") >= 0.5 then
            target = enemy
        else
            return false
        end
    end

    ShowSpellPopup(dhSpells.bigGrasp, "Gripping enemy offensively!")

    return Cast(dhSpells.bigGrasp, target)
end

local function defensiveGripOnBurst()
    if not globalState.teamHealerInCC then return false end
    local target = nil

    local enemies = getEnemies()
    for _, enemy in ipairs(enemies) do
        if unitDistance(enemy) <= 15 and unitLos(enemy) and UnitIsPlayer(enemy) and not isHealer(enemy) and not inCC(enemy) and not isTargetImmune(enemy) then

            if targetIsBursting(enemy) then
                target = enemy
                break
            end
        end
    end

    if not target then
        return false
    end

    ShowSpellPopup(dhSpells.bigGrasp, "Gripping enemy defensively!")

    return Cast(dhSpells.bigGrasp, target)
end

local function defensiveBigGrip()
    local maxDrTarget = nil
    local maxDR = 0

    for _, enemy in ipairs(globalState.enemyCanHit) do
        if not isHealer(enemy) and unitDistance(enemy) < 10 then
            local drLevel = GetDrCat(enemy, "stun")

            if maxDR < drLevel then
                maxDR = drLevel
                maxDrTarget = enemy
            end

            if  drLevel > 0.5 and not isHealer(enemy) then
                ShowSpellPopup(dhSpells.bigGrasp, "Gripping enemy defensively!")
                return Cast(dhSpells.bigGrasp, enemy) or true
            end
        end
    end

    -- if maxDR >= 0.5 and maxDrTarget ~= nil then
    --     if CanCast(dhSpells.infernalStrike) then
    --         print('Leap for grip to save team')
    --         return CastGroundSpeed(dhSpells.infernalStrike, maxDrTarget, false) or true
    --     end
    -- end
end

local function bigGripFinisher(endTime)
    local remaining = endTime - (GetTime() * 1000)
    if remaining > 1000 then return end

    if SpellIsTargeting() then
        return
    else
        print('Spell not targetting')
    end

    local target = nil
    local enemies = getEnemies()
    for _, enemy in ipairs(enemies) do
        if unitDistance(enemy) <= 40 and unitLos(enemy) and UnitIsPlayer(enemy) and not inBreakableCC(enemy) and not isTargetImmune(enemy)
            and not hasDebuff(enemy, dhSpells.bigGrasp) then
            target = enemy

            -- Throw at healer if we can :D
            if isHealer(enemy) then
                break
            end
        end
    end

    if not target then
        target = "player"
    end

    return CastGroundSpeed(dhSpells.bigGrasp, target, false)
end

local magicNumber = bit.bor(16777216, 262144, 8388608)
local function isLoc()
    local unitFlags = UnitFlags("player") or 0

    if bit.band(unitFlags, magicNumber) > 0 then
        return true
    end

    -- if bit.band(unitFlags, 2097152) > 0 then
    --     print('Player disarmed')
    -- end

    -- if bit.band(unitFlags, 262144) > 0 then
    --     print('Player stunned')
    -- end

    -- if bit.band(unitFlags, 8388608) > 0 then
    --     print('Player fleeing')
    -- end

    -- if bit.band(unitFlags, 16777216) > 0 then
    --     print('Player confused')
    -- end

end

local function totemStomp()
    for _, totem in ipairs(totemsToHit) do
        if ObjectExists(totem) and unitLos(totem) then
            local totemId = ObjectID(totem)
            local firstSeen = totemTracker[totem] or 100

            if GetTime() - firstSeen > 0.5 then
                if (highPrioStompListLookup[totemId] or lowHPStompListLookup[totemId]) and IsUnitFacing(totem) and inMelee(totem) then
                    return simpleFillerRotation(totem, getFury())
                end

                if lowHPStompListLookup[totemId] and CanCast(dhSpells.throwGlaive) and IsUnitFacing(totem) and unitDistanceNoCache(totem) <= 30 then
                    return Cast(dhSpells.throwGlaive, totem)
                end
            end
        end
    end
end

local function loadGlobalState()
    globalState = {
        enemyHealer = nil,
        enemyDps = {},
        enemyCanHit = {},
        enemyMaxHp = 0,
        enemyMinHp = 100,
        enemyCasters = 0,
        enemyMelee = 0,
        enemyBursting = false,

        teamHealer = nil,
        teamHealerInCC = false,
        teamDps = {},
        teamTargets = {},
        teamMaxHp = 0,
        teamMinHp = 100,
        teamCasters = 0,
        teamMelee = 0,
        teamBursting = false,
        teamHasRogue = false,
        teamHasWW = false,
    }

    local enemies = getEnemies()
    local inArena = IsInArena()
    for _, enemy in ipairs(enemies) do
        if not UnitIsDeadOrGhost(enemy) and (not inArena or UnitIsPlayer(enemy)) then
            local hp = unitHp(enemy)

            if hp > globalState.enemyMaxHp then
                globalState.enemyMaxHp = hp
            end

            if hp < globalState.enemyMinHp then
                globalState.enemyMinHp = hp
            end

            -- Throw at healer if we can :D
            if isHealer(enemy) then
                globalState.enemyHealer = enemy
            else
                table.insert(globalState.enemyDps, enemy)

                if targetIsBursting(enemy) then
                    globalState.enemyBursting = true
                end

                if isCaster(enemy) then
                    globalState.enemyCasters = globalState.enemyCasters + 1
                else
                    globalState.enemyMelee = globalState.enemyMelee + 1
                end
            end

            if unitDistance(enemy) <= 40 and unitLos(enemy) and not inBreakableCC(enemy) and not isTargetImmune(enemy) then
                table.insert(globalState.enemyCanHit, enemy)
            end
        end
    end

    local roster = getRoster()
    for _, team in ipairs(roster) do
        if not UnitIsDeadOrGhost(team) and UnitIsPlayer(team) then
            local hp = unitHp(team)

            if hp > globalState.teamMaxHp then
                globalState.teamMaxHp = hp
            end

            if hp < globalState.teamMinHp then
                globalState.teamMinHp = hp
            end

            -- Throw at healer if we can :D
            if isHealer(team) then
                globalState.teamHealer = team
                globalState.teamHealerInCC = inCC(team)
            else
                table.insert(globalState.teamDps, team)

                local currentTarget = UnitTarget(team)
                if currentTarget then
                    globalState.teamTargets[currentTarget] = true
                end

                if targetIsBursting(team) then
                    globalState.teamBursting = true
                end

                if isCaster(team) then
                    globalState.teamCasters = globalState.teamCasters + 1
                else
                    globalState.teamMelee = globalState.teamMelee + 1
                end

                local _, _, classId = UnitClass(team)
                if classId == 4 then
                    globalState.teamHasRogue = true
                else
                    local spec = UnitSpecializationID(team) or 0

                    if spec == 269 then
                        globalState.teamHasWW = true
                    end
                end
            end
        end
    end
end

local function rotation()
    local player = "player"
    local target = "target"

    if isLoc() then
        ShowSpellPopup(dhSpells.sigilMisery, "We're in cc")
        return
    end

    if IsMounted() then return false end

    if IsPlayerSpell(dhTalents.demonicTrample) and hasBuff(player, dhSpells.demonicTrample) then
        ShowSpellPopup(dhSpells.demonicTrample, "We're trampling")

        if UnitExists(target) and inMelee(target) then
            clearAuraByName(dhSpells.demonicTrample)
            return
        end
        return
    end

    if hasBuff(player, dhSpells.shadowMeld) then return end

    local playerCasting, _, _, endTime = isCastingOrChanneling(player)
    if playerCasting then
        if playerCasting == dhSpells.bigGrasp then
            return bigGripFinisher(endTime)
        end

        return false
    end

    if not UnitExists(target) then return end

    -- print('Target is player ' .. (UnitIsPlayer(UnitGUID(target)) and 'true' or 'false'))

    if not UnitCanAttack(player, target) then return end

    if not inCombat() then
        -- Get rogues outa stealth plz
        if hasBuff(player, dhSpells.spectralSight) and CanCast(dhSpells.throwGlaive) then
            if unitLos(target) and unitDistance(target) <= 30 then
                return Cast(dhSpells.throwGlaive)
            end

            return false
        end

        if not IsInArena() then return end
    end

    loadGlobalState()

    if hasDebuff(player, 410201) then
        ShowSpellPopup(410201, "Searing Glare")
        return false
    end

    if IsPlayerSpell(dhTalents.demonicTrample) and CanCast(dhSpells.demonicTrample) and unitDistance(target) > 10 and rooted(player) then
        ShowSpellPopup(dhSpells.demonicTrample, "Trampling to remove root")
        return Cast(dhSpells.demonicTrample)
    end

    local fury = getFury()
    local souls = hasBuffCount(player, 203981)

    if IsPlayerSpell(dhTalents.bigGrasp) and CanCast(dhSpells.bigGrasp) then
        if globalState.teamMinHp < 30 and defensiveBigGrip() then return end
        if globalState.enemyMinHp < 30 and bigGripRotation() then return end
        if globalState.teamMinHp < 50 and defensiveGripOnBurst() then return end
    end

    if kickAnyone() then return end
    if highPrioPurgeNear() then return end

    if defensives(player) then return end
    if ccUtility() then return end
    if assistTeam() then return end

    -- if CanCast(dhSpells.throwGlaive) then
    --     for _, enemy in pairs(NeP.OM:Get('Critters')) do
    --         if UnitCanAttack("player", enemy) and not UnitIsPlayer(enemy) and unitDistance(enemy) <= 40 and unitLos(enemy) and not UnitIsDeadOrGhost(enemy) then
    --             for _, stompItem in pairs(highPrioStompList) do
    --                 if enemy.id == stompItem then
    --                     print('Throwing glaive totem')
    --                     facehack(enemy)
    --                     return Cast(dhSpells.throwGlaive, enemy)
    --                 end
    --             end
    --         end
    --     end
    -- end

    if totemStomp() then return end

    if IsInArena() and not UnitIsPlayer(target) then
        return simpleFillerRotation(target, fury)
    end

    if isTargetImmune(target) then
        ShowSpellPopup(dhSpells.soulCleave, "Target is immune")
        return
    end

    if IsPlayerSpell(dhTalents.tormentor) then
       if unitDistance(target) <= 10 and CanCast(dhSpells.tormentor) and not hasDebuff(target, dhDebuffs.focusedAssault) then
            return Cast(dhSpells.tormentor)
        end
    end

    -- if unitHp(target) <= 40 and CanCast(dhSpells.meta) then
    --     return Cast(dhSpells.meta)
    -- end

    if unitHp(target) >= 50 and targetHasDamageReduction(target) then
        local enemies = getEnemies()
        for _, enemy in ipairs(enemies) do
            if unitDistance(enemy) <= 8 and UnitIsPlayer(enemy) and IsUnitFacing(enemy) and not inBreakableCC(enemy) and not isTargetImmune(enemy) and not targetHasDamageReduction(enemy) then
                ShowSpellPopup(dhSpells.soulCleave, "Hitting another unit for damage")

                target = enemy
                TargetUnit(target)
                break
            end
        end
    end

    -- DPS stuff --
    -- if IsPlayerSpell(dhTalents.fieryDemise) and IsPlayerSpell(dhTalents.fieryBrand) and hasDebuff(target, dhDebuffs.fieryBrand) then
    --     if fieryDemiseRotation(player, target, fury, souls) then return end
    -- end

    local aoeCount = 0
    local healerNearby = false
    local noNova = false
    local bursting = false

    local enemies = getEnemies()
    for _, enemy in ipairs(enemies) do
        if unitDistance(enemy) <= 8 and UnitIsPlayer(enemy) and not isTargetImmune(enemy) then
            if inBreakableCC(enemy) or inCC(enemy) then
                noNova = true
            end

            if globalState.teamHasWW and not isHealer(enemy) then
                noNova = true
            end

            if globalState.teamHasRogue then
                noNova = true
            end

            if targetIsBursting(enemy) and GetDrCat(enemy, "stun") >= 0.25 then
                bursting = true
            end

            if GetDrCat(enemy, "stun") >= 0.5 then
                aoeCount = aoeCount + 1

                if isHealer(enemy) then
                    healerNearby = true
                end
            end
        end
    end

    if not noNova and (aoeCount >= 3 or healerNearby or bursting) then
        if CanCast(dhSpells.chaosNova) then
            return Cast(dhSpells.chaosNova)
        end
    end

    if maintenance(target, fury, souls) then return end
    if singleTarget(player, target, fury, souls) then return end
    if nearbyToHit(fury, souls) then return end

    if not IsPlayerSpell(dhTalents.tormentor) and CanCast(dhSpells.torment) then
        local petToTaunt = getEnemyPetToTaunt()

        if petToTaunt then
            local petName = ObjectName(petToTaunt) or "pet"
            ShowSpellPopup(dhSpells.torment, "Taunting " .. petName)
            return Cast(dhSpells.torment, petToTaunt)
        end
    end

    return false
end

local function trackTotems()
    totemsToHit = {}
    for i = 1, GetObjectCount(), 1 do
        local guid = GetObjectWithIndex(i)
        -- Remove 5 after done testing
        if UnitCreatureTypeId(guid) == 11 then
            if UnitCanAttack("player", guid) then
                table.insert(totemsToHit, guid)
            end

            if not totemTracker[guid] then
                totemTracker[guid] = GetTime()
            end
        end
    end

    return C_Timer.After(0.5, trackTotems)
end

local function trackerCleanup()
    local now = GetTime()
    for k, v in pairs(totemTracker) do
        if now - v > 20 then
            totemTracker[k] = nil
        end
    end

    return C_Timer.After(10, trackerCleanup)
end

local function visualRender()
    draw:ClearCanvas()

    targetDraw()
    enemyHealerDraw()

    return C_Timer.After(0.05, visualRender)
end

local function mainLoop()
    CacheContext:resetCache()

    if GetKeyState("LSHIFT") == 1 then
        if not shiftDown then
            shiftDown = true

            peelModeOn = not peelModeOn
        end
    else
        shiftDown = false
    end

    if peelModeOn then
        ShowSpellPopup(dhSpells.sigilMisery, "CC healer mode")
    else
        HideSpellPopup("CC healer mode")
    end

    if UnitExists("target") then
        --print('Dr level is: ' .. GetDrCat("target", "silence"))
        rotation()
    end

    return C_Timer.After(math.random(0.08, 0.1), mainLoop)
    -- return C_Timer.After(0.5, mainLoop)
end

local function doLatency()
    local _, _, homeLatency, worldLatency = GetNetStats()
    local TargetSQW = (worldLatency + 20)
    if SQW ~= TargetSQW then
        SetCVar("SpellQueueWindow", TargetSQW)
    end
end

C_Timer.NewTicker(10, doLatency)

local function Main()
    print("Jack Veng Loaded now")
    TrackDR()

    mainLoop()
end

local function LoadAddon()
  if IsInWorld() then
    print("Loading Addon...")
    C_Timer.After(1, Main)
    C_Timer.After(1, visualRender)
    C_Timer.After(1, trackTotems)
    C_Timer.After(1, trackerCleanup)
    return
  end
  C_Timer.After(1, LoadAddon)
end

LoadAddon()