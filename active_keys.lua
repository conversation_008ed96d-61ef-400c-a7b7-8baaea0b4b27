LPH_JIT_MAX = function(...) return ... end
LPH_NO_VIRTUALIZE = function(...) return ... end
LPH_ENCNUM = function(val) return val end

Makulu_magic_number = LPH_ENCNUM(345781245632342333)

function MakuluValidCheck()
    local date = C_DateAndTime.GetCurrentCalendarTime()
    local today = tonumber(string.format("%04d%02d%02d", date.year, date.month, date.monthDay))
    if today > LPH_ENCNUM(20240206) then
        print("The rotation is locked due to an outdated version.")
        return false
    end
    Makulu_magic_number = LPH_ENCNUM(2347956243324)
    return true
end

local ProfileSession = _G.Action.ProfileSession
local normaltime = "2022-12-31-23-59-59"
local lifetime 	 = "2100-01-01-23-59-59"
local trialtime  = "trial-07"
local full_profiles = {
	["Makulu - Druid"] = true,
	["Makulu - Rogue"] = true,
    ["<PERSON><PERSON><PERSON> - Shaman"] = true,
    ["<PERSON><PERSON><PERSON> - <PERSON>"] = true,
    ["<PERSON><PERSON><PERSON> - Paladin"] = true,
    ["<PERSON><PERSON><PERSON> - <PERSON>"] = true,
    ["<PERSON>ku<PERSON> - Ma<PERSON>"] = true,
    ["<PERSON><PERSON>lu - <PERSON>lock"] = true,
    ["Makulu - Hunter"] = true,
    ["Makulu - Death Knight"] = true,
    ["Makulu - Monk"] = true,
    ["Makulu - Demon Hunter"] = true
}           
ProfileSession:Setup("cdb0db7b6f82440a270838ce2951853facbc017a9eb1c8b6b7c4f8ebc42d8e23", { users = {["9e91369348389ae0f96706b91b9fe13d8e205baffae76f076cb1a4bf847014fb"] = {expiration = "2024-03-04-00-00-00", profiles = full_profiles},["db21ac7dce022bbaa7d20aaf21c0dd7fcf7dafcd9b30b427cf4ca65320d1f381"] = {expiration = "2024-03-05-00-00-00", profiles = full_profiles},["3fac36a729c6e0236cb4c821254080f607105204c37d8a2d43aab949979b9089"] = {expiration = "2024-03-05-00-00-00", profiles = full_profiles},["4a3620f100fb170a09ce4f32d7f9f718935416da62381de2606a005f4634ad1a"] = {expiration = "2024-02-09-00-00-00", profiles = {["Makulu - Shaman"] = true,["Makulu - Druid"] = false,["Makulu - Rogue"] = false,["Makulu - Warrior"] = false,["Makulu - Paladin"] = false,["Makulu - Priest"] = false,["Makulu - Mage"] = false,["Makulu - Warlock"] = false,["Makulu - Hunter"] = false,["Makulu - Death Knight"] = false,["Makulu - Monk"] = false,["Makulu - Demon Hunter"] = false,}},["94c1532ddf3bd4335a5c0d503b803ce8fbdb5d2a02c01e7d3cf11615eec09b58"] = {expiration = "2024-03-07-00-00-00", profiles = {["Makulu - Priest"] = true,["Makulu - Druid"] = false,["Makulu - Rogue"] = false,["Makulu - Shaman"] = false,["Makulu - Warrior"] = false,["Makulu - Paladin"] = false,["Makulu - Mage"] = false,["Makulu - Warlock"] = false,["Makulu - Hunter"] = false,["Makulu - Death Knight"] = false,["Makulu - Monk"] = false,["Makulu - Demon Hunter"] = false,}},["ABC123"] = {expiration = "2024-02-06-00-00-00", profiles = {["Makulu - Hunter"] = true,["Makulu - Druid"] = false,["Makulu - Rogue"] = false,["Makulu - Shaman"] = false,["Makulu - Warrior"] = false,["Makulu - Paladin"] = false,["Makulu - Priest"] = false,["Makulu - Mage"] = false,["Makulu - Warlock"] = false,["Makulu - Death Knight"] = false,["Makulu - Monk"] = false,["Makulu - Demon Hunter"] = false,}},}})